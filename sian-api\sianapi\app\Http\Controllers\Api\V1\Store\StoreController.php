<?php

namespace App\Http\Controllers\Api\V1\Store;


use App\Models\Procedures\SpGetStoresWithSales;
use App\Models\Store;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;


class StoreController extends Controller {
    /**
     *
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        try {
            $stores = DB::table('store as S')
                ->select('S.*', 'W.warehouse_id')
                ->join('warehouse as W', function ($join) {
                    $join->on('W.store_id', '=', 'S.store_id')
                        ->where('W.store_default', '=', 1);
                })
                ->where('S.status', '=', 1)
                ->where('S.physical', '=', 1)
                ->get();


            return response()->json([
                'success' => true,
                'data' => $stores
            ]);


        } catch (\Throwable $th) {
            Log::error($th->getMessage());
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }


    public function getStoresWithSales() {
        try {
            $stores = SpGetStoresWithSales::execute();
            return response()->json([
                'success' => true,
                'data' => $stores
            ]);


        } catch (\Throwable $th) {
            Log::error($th->getMessage());
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }
}
