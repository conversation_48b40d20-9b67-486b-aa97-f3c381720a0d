<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class LockerRequest
 * 
 * @property int $locker_request_id
 * @property Carbon $request_date
 * @property bool $status
 * @property string $state
 * @property int $locker_id
 * @property int|null $api_user_id
 * @property int|null $person_client_id
 * @property int|null $person_deliverer_id
 * @property int $request_count
 * @property int $article_quantity
 * @property int $deliverer_count
 * @property bool $client_count
 * @property bool|null $rating
 * @property bool $is_editable
 * @property bool $is_back_fillable
 * @property bool $is_fillable
 * @property bool $is_not_fillable
 * @property bool $is_back_pickable
 * @property bool $is_returnable
 * @property bool $is_back_returnable
 * @property bool $is_nullable
 * @property bool $is_removable
 * 
 * @property ApiUser|null $api_user
 * @property Locker $locker
 * @property Person|null $person
 * @property Collection|LockerAccess[] $locker_accesses
 * @property Collection|LockerBoxRequest[] $locker_box_requests
 *
 * @package App\Models
 */
class LockerRequest extends Model
{
	protected $table = 'locker_request';
	protected $primaryKey = 'locker_request_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'locker_id' => 'int',
		'api_user_id' => 'int',
		'person_client_id' => 'int',
		'person_deliverer_id' => 'int',
		'request_count' => 'int',
		'article_quantity' => 'int',
		'deliverer_count' => 'int',
		'client_count' => 'bool',
		'rating' => 'bool',
		'is_editable' => 'bool',
		'is_back_fillable' => 'bool',
		'is_fillable' => 'bool',
		'is_not_fillable' => 'bool',
		'is_back_pickable' => 'bool',
		'is_returnable' => 'bool',
		'is_back_returnable' => 'bool',
		'is_nullable' => 'bool',
		'is_removable' => 'bool'
	];

	protected $dates = [
		'request_date'
	];

	protected $fillable = [
		'request_date',
		'status',
		'state',
		'locker_id',
		'api_user_id',
		'person_client_id',
		'person_deliverer_id',
		'request_count',
		'article_quantity',
		'deliverer_count',
		'client_count',
		'rating',
		'is_editable',
		'is_back_fillable',
		'is_fillable',
		'is_not_fillable',
		'is_back_pickable',
		'is_returnable',
		'is_back_returnable',
		'is_nullable',
		'is_removable'
	];

	public function api_user()
	{
		return $this->belongsTo(ApiUser::class);
	}

	public function locker()
	{
		return $this->belongsTo(Locker::class);
	}

	public function person()
	{
		return $this->belongsTo(Person::class, 'person_deliverer_id');
	}

	public function locker_accesses()
	{
		return $this->hasMany(LockerAccess::class);
	}

	public function locker_box_requests()
	{
		return $this->hasMany(LockerBoxRequest::class);
	}
}
