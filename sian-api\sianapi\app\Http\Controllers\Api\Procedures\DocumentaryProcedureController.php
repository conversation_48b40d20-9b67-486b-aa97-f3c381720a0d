<?php

namespace App\Http\Controllers\Api\Procedures;

use App\Http\Controllers\Controller;
use App\Models\Procedures\DocumentaryProcedure;
use Illuminate\Http\Request;

use App\Http\Resources\Procedures\DocumentaryProcedureResource;
use App\Http\Resources\Procedures\DocumentaryProcedureCollection;

class DocumentaryProcedureController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // $page = 1;
        $request->validate([
            'page' => 'int',
            'limit' => 'int',
        ]);

        $params = $request->only('page', 'limit');

        $limit = isset($params['limit']) ? $params['limit'] : 5;
        // if (isset($params['page'])) {
        //     $page = $params['page'];
        // }

        return new DocumentaryProcedureCollection(DocumentaryProcedure::latest()->paginate($limit));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\DocumentaryProcedure  $documentaryProcedure
     * @return \Illuminate\Http\Response
     */
    public function show(DocumentaryProcedure $documentaryProcedure)
    {
        return new DocumentaryProcedureResource($documentaryProcedure);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\DocumentaryProcedure  $documentaryProcedure
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, DocumentaryProcedure $documentaryProcedure)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\DocumentaryProcedure  $documentaryProcedure
     * @return \Illuminate\Http\Response
     */
    public function destroy(DocumentaryProcedure $documentaryProcedure)
    {
        //
    }
}
