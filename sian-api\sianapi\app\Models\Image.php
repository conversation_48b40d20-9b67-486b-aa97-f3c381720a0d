<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Image
 *
 * @property int $image_id
 * @property string $image
 * @property int $object_id
 *
 * @property ObjectModel $object
 *
 * @package App\Models
 */
class Image extends Model
{
	protected $table = 'image';
	protected $primaryKey = 'image_id';
	public $timestamps = false;

	protected $casts = [
		'object_id' => 'int'
	];

	protected $fillable = [
		'image',
		'object_id'
	];

	public function object()
	{
		return $this->belongsTo(ObjectModel::class);
	}
}
