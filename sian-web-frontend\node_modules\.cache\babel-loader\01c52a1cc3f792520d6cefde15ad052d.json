{"ast": null, "code": "var _jsxFileName = \"D:\\\\www\\\\SWF\\\\sian-web-frontend\\\\src\\\\views\\\\logistic\\\\reposition\\\\others\\\\RotationDetail.jsx\",\n    _s2 = $RefreshSig$(),\n    _s3 = $RefreshSig$(),\n    _s6 = $RefreshSig$();\n\nimport { Box, IconButton, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Tooltip, Typography } from '@mui/material';\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'store';\nimport StoreIcon from '@mui/icons-material/Store';\nimport AddCircleIcon from '@mui/icons-material/AddCircle';\nimport RemoveCircleIcon from '@mui/icons-material/RemoveCircle';\nimport { addToCart, editToCart, getRepositionDataByProduct, openRotationModal, removeFromCart, setSelectedProduct, updateFormDataItem, updateFormSupplyDataItem } from 'store/slices/reposition/reposition';\nimport ErrorIcon from '@mui/icons-material/Error';\nimport { addDays, parseDateToLocaleString } from 'utils/dates';\nimport Grid from 'ui-component/grid/Grid';\nimport AutoModeIcon from '@mui/icons-material/AutoMode';\nimport useLoading from 'hooks/useLoading';\nimport { BlockLoader } from 'ui-component/loaders/loaders';\nimport SIANLink from 'ui-component/SIAN/SIANLink';\nimport { FOOD_VALUE, RAW_MATERIAL } from 'models/Reposition';\nimport RightAlignedNumber from 'ui-component/grid/RightAlignedNumber';\nimport { stickyColumn } from 'ui-component/grid/Grid';\nimport DisplayCurrency from 'ui-component/display/DisplayCurrency';\nimport { UNIT_EQUIVALENCE } from 'models/Presentation';\nimport { MenuItem } from '@mui/material';\nimport NestedGrid from 'ui-component/grid/NestedGrid';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n\nconst findDerivedProductRecursively = (derivedProducts, targetProductId) => {\n  if (!derivedProducts || !Array.isArray(derivedProducts)) return null;\n\n  for (const derived of derivedProducts) {\n    if (derived.product_id === targetProductId) {\n      return derived;\n    }\n\n    if (derived.derivedProducts && derived.derivedProducts.length > 0) {\n      const found = findDerivedProductRecursively(derived.derivedProducts, targetProductId);\n      if (found) return found;\n    }\n  }\n\n  return null;\n};\n\nconst findMainProductWithDerivedProduct = (data, targetProductId) => {\n  if (!data || !Array.isArray(data)) return null;\n\n  for (const item of data) {\n    const found = findDerivedProductRecursively(item.derivedProducts, targetProductId);\n    if (found) return item;\n  }\n\n  return null;\n};\n\nconst NumberAlert = _ref => {\n  let {\n    condition = false,\n    title = ''\n  } = _ref;\n\n  if (!condition) {\n    return null;\n  }\n\n  return /*#__PURE__*/_jsxDEV(Tooltip, {\n    title: title,\n    children: /*#__PURE__*/_jsxDEV(IconButton, {\n      color: \"error\",\n      children: /*#__PURE__*/_jsxDEV(ErrorIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 9\n  }, this);\n};\n\n_c = NumberAlert;\n\nconst AlertRotation = _ref2 => {\n  let {\n    rotationScale\n  } = _ref2;\n\n  switch (rotationScale) {\n    case 'AR':\n      return /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"El producto tiene ALTA ROTACI\\xD3N\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 20,\n            height: 20,\n            borderRadius: '99999px',\n            backgroundColor: '#c6e0b4'\n          },\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 17\n      }, this);\n\n    case 'MR':\n      return /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"El producto tiene MEDIA ROTACI\\xD3N\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 20,\n            height: 20,\n            borderRadius: '99999px',\n            backgroundColor: '#ffe699'\n          },\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 17\n      }, this);\n\n    case 'BR':\n      return /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"El producto tiene BAJA ROTACI\\xD3N\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 20,\n            height: 20,\n            borderRadius: '99999px',\n            backgroundColor: '#b11226'\n          },\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 17\n      }, this);\n\n    default:\n      return /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"El producto NO tiene ROTACI\\xD3N\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 20,\n            height: 20,\n            borderRadius: '99999px',\n            backgroundColor: 'white',\n            border: '1px solid'\n          },\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 17\n      }, this);\n  }\n};\n\n_c2 = AlertRotation;\n\nconst AlertBreak = _ref3 => {\n  let {\n    break_scale = 'QB'\n  } = _ref3;\n\n  switch (break_scale) {\n    case 'SS':\n      return /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"El punto de Quiebre es SOBRE STOCK\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 20,\n            height: 20,\n            borderRadius: '99999px',\n            backgroundColor: '#a777dd'\n          },\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 17\n      }, this);\n\n    case 'OPT':\n      return /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"El punto de Quiebre es OPTIMO\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 20,\n            height: 20,\n            borderRadius: '99999px',\n            backgroundColor: '#c6e0b4'\n          },\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 17\n      }, this);\n\n    case 'MOD':\n      return /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"El punto de Quiebre es MODERADO\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 20,\n            height: 20,\n            borderRadius: '99999px',\n            backgroundColor: '#ffe699'\n          },\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 17\n      }, this);\n\n    case 'CRI':\n      return /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"El punto de Quiebre es CR\\xCDTICO\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 20,\n            height: 20,\n            borderRadius: '99999px',\n            backgroundColor: '#b11226'\n          },\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 17\n      }, this);\n\n    case 'QB':\n      return /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"El punto de Quiebre esta QUEBRADO\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 20,\n            height: 20,\n            borderRadius: '99999px',\n            backgroundColor: 'white',\n            border: '1px solid'\n          },\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 17\n      }, this);\n\n    default:\n      return /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"El punto de Quiebre esta QUEBRADO\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 20,\n            height: 20,\n            borderRadius: '99999px',\n            backgroundColor: 'white',\n            border: '1px solid'\n          },\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 17\n      }, this);\n  }\n};\n\n_c3 = AlertBreak;\n\nconst processAnalisys = (productData, stores) => {\n  if (!productData || !productData.analisys) return null;\n  const listData = productData.analisys.map((item, index) => {\n    const pk = `${item.product_id}-${item.store_id}`;\n    return { ...item,\n      rowIndex: index,\n      quantity: parseFloat(item.unit_quantity_order).toFixed(4),\n      unit_quantity: parseFloat(item.unit_quantity_order),\n      unit_price: parseFloat(item.unit_price),\n      default_unit_price: parseFloat(item.unit_price),\n      provider: item.provider,\n      pk\n    };\n  });\n  stores.forEach(store => {\n    const existingItem = listData.find(item => item.store_id === store.store_id);\n\n    if (!existingItem) {\n      listData.push({\n        notAvailable: true,\n        indicator_calculation_id: 4,\n        store_id: store.store_id,\n        store_name: store.store_name,\n        product_id: productData.product_id,\n        product_name: productData.product_name,\n        measure_name: productData.measure_name,\n        equivalence_default: productData.equivalence_default,\n        measure_default: productData.measure_default,\n        provider_id: productData.provider_id,\n        provider_number: productData.provider_number,\n        provider: productData.provider,\n        warehouse_id: store.warehouse_id,\n        unit_price: productData.unit_price,\n        expires: 0,\n        vcto_alert: '-',\n        rotation_scale: '-',\n        rotation_value: 0,\n        rotation_indicator: 'NR(0)',\n        rotation_color: '',\n        obsolete: 0,\n        obsolete_indicator: '-',\n        pareto_percentage_sale: '0.0',\n        pareto_percentage_utility: '0.0',\n        stock: '0.00',\n        to_enter: '0.00',\n        to_dispatch: '0.00',\n        purchase_stock: '0.00',\n        average_quantity: '0.00',\n        average_diary: '0.0000',\n        inventory_days: 0,\n        break_value: 0,\n        break_scale: '-',\n        min_stock: 0,\n        reposition_stock: 0,\n        unit_quantity_order: 0,\n        rowIndex: 0,\n        quantity: '0',\n        unit_quantity: 0,\n        default_unit_price: productData.default_unit_price,\n        pk: `${productData.product_id}-${store.store_name}`\n      });\n    }\n  });\n  listData.sort((a, b) => a.warehouse_id - b.warehouse_id);\n  return listData;\n};\n\nconst getDerivedProducts = productData => {\n  if (!productData || !productData.derivedProducts) return [];\n  return productData.derivedProducts.map((item, index) => ({ ...item,\n    pk: item.product_id,\n    globalIndex: index\n  }));\n};\n\nconst NestedCard = _ref4 => {\n  let {\n    children,\n    width = '50%'\n  } = _ref4;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 1,\n      backgroundColor: 'white',\n      borderRadius: '1rem',\n      border: '1px solid #e0e0e0',\n      width\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 269,\n    columnNumber: 5\n  }, this);\n};\n\n_c4 = NestedCard;\n\nconst DerivedProductAnalysis = _ref5 => {\n  _s2();\n\n  var _s = $RefreshSig$();\n\n  let {\n    row,\n    columns\n  } = _ref5;\n  const productId = row[0];\n  const {\n    data\n  } = useSelector(state => state.reposition);\n  const {\n    data: storeData\n  } = useSelector(state => state.store);\n  const mainProductData = findMainProductWithDerivedProduct(data, productId);\n  const derivedProduct = findDerivedProductRecursively(mainProductData === null || mainProductData === void 0 ? void 0 : mainProductData.derivedProducts, productId);\n  const derivedAnalysis = derivedProduct !== null && derivedProduct !== void 0 && derivedProduct.analisys ? processAnalisys(derivedProduct, storeData) : null;\n\n  if (!derivedAnalysis || derivedAnalysis.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        textAlign: 'center',\n        color: 'text.secondary'\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: \"No hay an\\xE1lisis disponible para este producto derivado\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 13\n    }, this);\n  }\n\n  const DerivedAnalysisGrid = () => {\n    _s();\n\n    const [repositionDerivedProduct] = useState(derivedAnalysis);\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      columns: columns,\n      title: \"An\\xE1lisis por Tienda\",\n      data: repositionDerivedProduct,\n      options: {\n        search: false,\n        download: false,\n        print: false,\n        sort: false,\n        viewColumns: false,\n        filter: false,\n        responsive: 'vertical',\n        fixedHeader: false,\n        selectableRows: 'none',\n        pagination: false,\n        toolbar: false,\n        setTableProps: () => ({\n          size: 'small',\n          sx: {\n            '& .MuiTableHead-root .MuiTableCell-root': {\n              fontSize: '0.875rem',\n              fontWeight: 'bold',\n              padding: '8px 16px'\n            },\n            '& .MuiTableBody-root .MuiTableCell-root': {\n              fontSize: '1.2rem',\n              padding: '36px 16px'\n            }\n          }\n        })\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 13\n    }, this);\n  };\n\n  _s(DerivedAnalysisGrid, \"WHhaXpbZMWrrNuCIsZJ/nfj+zQA=\");\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%',\n      height: 'fit-content'\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        backgroundColor: 'white',\n        borderRadius: '1rem',\n        border: '1px solid #e0e0e0',\n        '& .MuiTableHead-root .MuiTableCell-root': {\n          fontSize: '0.875rem',\n          fontWeight: 'bold',\n          padding: '8px 16px'\n        },\n        '& .MuiTableBody-root .MuiTableCell-root': {\n          fontSize: '1.2rem',\n          padding: '36px 16px'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(DerivedAnalysisGrid, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 328,\n    columnNumber: 9\n  }, this);\n};\n\n_s2(DerivedProductAnalysis, \"qeA+H7VVIvd47UjTttqhZeJWxQQ=\", false, function () {\n  return [useSelector, useSelector];\n});\n\n_c5 = DerivedProductAnalysis;\n\nconst DerivedProductNestedContent = _ref6 => {\n  let {\n    row,\n    data,\n    derivedAnalysisColumns,\n    simplifiedDerivedProductColumns\n  } = _ref6;\n  const productId = row[0];\n  const mainProductData = findMainProductWithDerivedProduct(data, productId);\n  const derivedProduct = findDerivedProductRecursively(mainProductData === null || mainProductData === void 0 ? void 0 : mainProductData.derivedProducts, productId);\n  const hasSubDerived = (derivedProduct === null || derivedProduct === void 0 ? void 0 : derivedProduct.derivedProducts) && derivedProduct.derivedProducts.length > 0;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      flexDirection: 'row',\n      gap: 2,\n      py: 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: hasSubDerived ? '25%' : '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(DerivedProductAnalysis, {\n        row: row,\n        columns: derivedAnalysisColumns\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 13\n    }, this), hasSubDerived && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '75%'\n      },\n      children: /*#__PURE__*/_jsxDEV(SubDerivedProducts, {\n        row: row,\n        columns: simplifiedDerivedProductColumns,\n        analysisColumns: derivedAnalysisColumns\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 359,\n    columnNumber: 9\n  }, this);\n};\n\n_c6 = DerivedProductNestedContent;\n\nconst SubDerivedProducts = _ref7 => {\n  _s3();\n\n  let {\n    row,\n    columns,\n    analysisColumns\n  } = _ref7;\n  const productId = row[0];\n  const {\n    data\n  } = useSelector(state => state.reposition);\n  const mainProductData = findMainProductWithDerivedProduct(data, productId);\n  const derivedProduct = findDerivedProductRecursively(mainProductData === null || mainProductData === void 0 ? void 0 : mainProductData.derivedProducts, productId);\n  const rawSubDerivedProducts = (derivedProduct === null || derivedProduct === void 0 ? void 0 : derivedProduct.derivedProducts) || [];\n  const subDerivedProducts = rawSubDerivedProducts.map((item, index) => ({ ...item,\n    pk: item.product_id,\n    globalIndex: index\n  }));\n\n  if (subDerivedProducts.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        textAlign: 'center',\n        color: 'text.secondary'\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: \"No hay productos derivados adicionales\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 13\n    }, this);\n  }\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%',\n      height: 'fit-content'\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 1,\n        backgroundColor: 'white',\n        borderRadius: '1rem',\n        border: '1px solid #e0e0e0',\n        width: '100%',\n        '& .MuiTable-root': {\n          width: '100% !important',\n          tableLayout: 'fixed'\n        },\n        '& .MuiTableCell-root': {\n          padding: '8px 16px'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(NestedGrid, {\n        columns: columns,\n        data: subDerivedProducts,\n        RenderNestedContent: props => /*#__PURE__*/_jsxDEV(DerivedProductAnalysis, { ...props,\n          columns: analysisColumns || columns\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 53\n        }, this),\n        options: {\n          search: false,\n          download: false,\n          print: false,\n          sort: false,\n          viewColumns: false,\n          filter: false,\n          responsive: 'vertical',\n          fixedHeader: false,\n          selectableRows: 'none',\n          pagination: false,\n          toolbar: false,\n          setTableProps: () => ({\n            size: 'small'\n          })\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 394,\n    columnNumber: 9\n  }, this);\n};\n\n_s3(SubDerivedProducts, \"/Hwr0EusckN6Yz4QGI98pPT/utc=\", false, function () {\n  return [useSelector];\n});\n\n_c7 = SubDerivedProducts;\nexport default function RotationDetail(_ref8) {\n  _s6();\n\n  var _s4 = $RefreshSig$(),\n      _s5 = $RefreshSig$();\n\n  let {\n    row,\n    isFromProyection = null,\n    foodMode = null\n  } = _ref8;\n  const dispatch = useDispatch();\n  const {\n    data,\n    formData,\n    cart,\n    filters,\n    merchandiseFoodData,\n    formSupplyData\n  } = useSelector(state => state.reposition);\n  const {\n    data: storeData\n  } = useSelector(state => state.store);\n\n  const isRowDataAvailable = rowData => {\n    return rowData && !rowData.notAvailable;\n  };\n\n  const getRowDataSafely = pk => {\n    try {\n      const rowData = repositionProduct.find(item => item.pk === pk);\n      const result = rowData || {\n        notAvailable: true\n      };\n      return result;\n    } catch (error) {\n      return {\n        notAvailable: true\n      };\n    }\n  };\n\n  const renderSafeContent = (pk, renderFunction) => {\n    try {\n      const rowData = getRowDataSafely(pk);\n\n      if (!isRowDataAvailable(rowData)) {\n        return /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"gray\",\n          children: \"-\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 24\n        }, this);\n      }\n\n      return renderFunction(rowData);\n    } catch (error) {\n      console.warn('Error al renderizar contenido para pk:', pk, error);\n      return /*#__PURE__*/_jsxDEV(Typography, {\n        color: \"gray\",\n        children: \"-\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 20\n      }, this);\n    }\n  };\n\n  const supplyAnalisys = processAnalisys(isFromProyection ? merchandiseFoodData.find(item => item.product_id === row[0]) : data.find(item => item.product_id === row[0]), storeData);\n  const productData = isFromProyection ? merchandiseFoodData.find(item => item.product_id === row[0]) : data.find(item => item.product_id === row[0]);\n  const derivedProducts = getDerivedProducts(productData);\n  const [repositionProduct, setRepositionProduct] = useState(supplyAnalisys || []);\n  const [isAsync] = useState(!supplyAnalisys);\n  const [loading, startLoading, endLoading] = useLoading(isAsync);\n\n  const openModal = () => dispatch(openRotationModal());\n\n  const setSelected = data => dispatch(setSelectedProduct(data));\n\n  const reload = () => {\n    if (isAsync) {\n      startLoading();\n      dispatch(getRepositionDataByProduct(row[0], { ...filters,\n        mode: isFromProyection ? 'Merc2' : filters.mode\n      }, storeData)).then(data => {\n        setRepositionProduct(data);\n        endLoading();\n      });\n    }\n  };\n\n  const handleAddToCart = item => {\n    dispatch(addToCart(item));\n  };\n\n  const handleRemoveFromCart = pk => {\n    dispatch(removeFromCart(pk));\n  };\n\n  const handleEditCart = (pk, updatedData) => {\n    dispatch(editToCart(pk, updatedData));\n  };\n\n  useEffect(() => {\n    reload();\n  }, []);\n\n  const QuantityInput = _ref9 => {\n    _s4();\n\n    var _rowData$presentation, _rowData$presentation2;\n\n    let {\n      tableMeta: {\n        rowData: rowMetadata\n      },\n      keyword = 'quantity_oc'\n    } = _ref9;\n    const pk = rowMetadata[0];\n    const rowData = filters.mode === FOOD_VALUE ? formSupplyData[pk] : formData[pk];\n    const cartRowData = cart[pk];\n    const repositionProductData = repositionProduct.find(item => item.pk === pk);\n\n    const getQuantity = (cartRowData, rowData, keyword) => {\n      if (cartRowData && cartRowData[keyword] !== undefined) {\n        return cartRowData[keyword];\n      }\n\n      if (rowData && rowData[keyword] !== undefined) {\n        return rowData[keyword];\n      }\n\n      return 0;\n    };\n\n    const [numberInput, setNumberInput] = useState(getQuantity(cartRowData, rowData, keyword));\n    useEffect(() => {\n      const quantity = getQuantity(cartRowData, rowData, keyword);\n      setNumberInput(parseFloat(quantity).toFixed(2));\n    }, [rowData === null || rowData === void 0 ? void 0 : rowData[keyword], cartRowData === null || cartRowData === void 0 ? void 0 : cartRowData[keyword]]);\n\n    const handleBlur = () => {\n      var _repositionProductDat;\n\n      const newValue = (repositionProductData === null || repositionProductData === void 0 ? void 0 : (_repositionProductDat = repositionProductData.presentation) === null || _repositionProductDat === void 0 ? void 0 : _repositionProductDat.allowDecimals) === 1 ? numberInput : parseFloat(numberInput);\n      const fixedValue = Math.floor(newValue).toFixed(2) || 0;\n      setNumberInput(fixedValue);\n      const updateAction = filters.mode === FOOD_VALUE ? updateFormSupplyDataItem : updateFormDataItem;\n      dispatch(updateAction(pk, {\n        [keyword]: fixedValue,\n        hasTouch: true,\n        product_id: repositionProductData.product_id,\n        store_id: repositionProductData.store_id\n      }));\n\n      if (cartRowData) {\n        handleEditCart(pk, {\n          [keyword]: fixedValue\n        });\n      }\n    };\n\n    const handleChange = _ref10 => {\n      let {\n        target: {\n          value\n        }\n      } = _ref10;\n      const newValue = parseFloat(value) || 0;\n      setNumberInput(newValue);\n    };\n\n    const autocomplete = () => {\n      var _productData$presenta, _productData$presenta2;\n\n      const productData = data.find(item => item.pk === row[0]);\n      const repositionRowData = repositionProduct.find(item => item.pk === pk);\n      const estimated = parseFloat(repositionRowData.unit_quantity) / parseFloat((_productData$presenta = productData === null || productData === void 0 ? void 0 : (_productData$presenta2 = productData.presentation) === null || _productData$presenta2 === void 0 ? void 0 : _productData$presenta2.equivalence) !== null && _productData$presenta !== void 0 ? _productData$presenta : 1);\n\n      if (estimated > 0) {\n        let keyOtherValue = '';\n        let newValue = estimated;\n\n        if (rowData) {\n          var _rowData$keyOtherValu;\n\n          if (keyword === 'quantity_oc') {\n            keyOtherValue = 'quantity_ota';\n          } else {\n            keyOtherValue = 'quantity_oc';\n          }\n\n          newValue = estimated - ((_rowData$keyOtherValu = rowData[keyOtherValue]) !== null && _rowData$keyOtherValu !== void 0 ? _rowData$keyOtherValu : 0);\n        }\n\n        if (newValue > 0) {\n          const updateAction = filters.mode === FOOD_VALUE ? updateFormSupplyDataItem : updateFormDataItem;\n          dispatch(updateAction(pk, {\n            [keyword]: newValue,\n            hasTouch: true,\n            store_id: repositionProductData.store_id\n          }));\n\n          if (cartRowData) {\n            handleEditCart(pk, {\n              [keyword]: newValue\n            });\n          }\n        }\n      }\n    };\n\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(TextField, {\n        variant: \"outlined\",\n        sx: {\n          width: '8rem'\n        },\n        size: \"small\",\n        type: \"number\",\n        value: numberInput,\n        onChange: handleChange,\n        onBlur: handleBlur,\n        InputProps: {\n          inputProps: {\n            style: {\n              textAlign: 'right'\n            },\n            step: (rowData === null || rowData === void 0 ? void 0 : (_rowData$presentation = rowData.presentation) === null || _rowData$presentation === void 0 ? void 0 : _rowData$presentation.allowDecimals) === 1 ? 0.1 : 1,\n            min: 0,\n            inputMode: (rowData === null || rowData === void 0 ? void 0 : (_rowData$presentation2 = rowData.presentation) === null || _rowData$presentation2 === void 0 ? void 0 : _rowData$presentation2.allowDecimals) === 1 ? 'decimal' : 'numeric',\n            pattern: '[0-9]*'\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 17\n      }, this), filters.mode !== FOOD_VALUE && !repositionProductData.notAvailable && /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Volver al Valor Sugerido\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"primary\",\n          \"aria-label\": \"Volver al Valor Original\",\n          onClick: autocomplete,\n          children: /*#__PURE__*/_jsxDEV(AutoModeIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 593,\n      columnNumber: 13\n    }, this);\n  };\n\n  _s4(QuantityInput, \"tNlF9XDcyZVFibIB6e1a29+KlA8=\");\n\n  const SelectProduct = _ref11 => {\n    _s5();\n\n    var _rowItem$quantity_oc, _rowItem$quantity_ota;\n\n    let {\n      tableMeta\n    } = _ref11;\n    const pk = tableMeta.rowData[0];\n    const product_id = row[0];\n    const rowItem = filters.mode === FOOD_VALUE ? formSupplyData[pk] : formData[pk];\n    const productData = data.find(item => item.pk === product_id);\n    const cartSelected = cart[pk];\n    const sum_quantity = parseFloat((_rowItem$quantity_oc = rowItem === null || rowItem === void 0 ? void 0 : rowItem.quantity_oc) !== null && _rowItem$quantity_oc !== void 0 ? _rowItem$quantity_oc : 0) + parseFloat((_rowItem$quantity_ota = rowItem === null || rowItem === void 0 ? void 0 : rowItem.quantity_ota) !== null && _rowItem$quantity_ota !== void 0 ? _rowItem$quantity_ota : 0);\n\n    const handleClick = () => {\n      if (cartSelected) {\n        handleRemoveFromCart(pk);\n      } else {\n        handleAddToCart({ ...rowItem,\n          store_id: tableMeta.rowData[1],\n          store_name: tableMeta.rowData[2],\n          product_name: productData.product_name,\n          provider: productData.provider,\n          provider_id: productData.provider_id,\n          provider_number: productData.provider_number,\n          equivalence: productData.presentation.equivalence,\n          presentation: productData.presentation,\n          product_id: row[0],\n          unit_price: productData.unit_price\n        });\n      }\n    };\n\n    useEffect(() => {\n      if (cartSelected && sum_quantity <= 0) {\n        handleRemoveFromCart(pk);\n      }\n    }, [sum_quantity, cartSelected]);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center'\n      },\n      children: rowItem && sum_quantity > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: cartSelected ? /*#__PURE__*/_jsxDEV(RemoveCircleIcon, {\n          color: \"error\",\n          fontSize: \"large\",\n          onClick: handleClick,\n          style: {\n            cursor: 'pointer'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 29\n        }, this) : /*#__PURE__*/_jsxDEV(AddCircleIcon, {\n          color: \"primary\",\n          fontSize: \"large\",\n          onClick: handleClick,\n          style: {\n            cursor: 'pointer'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 29\n        }, this)\n      }, void 0, false) : /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"La cantidad es insuficiente para a\\xF1adir al carrito\",\n        sx: {\n          color: '#bdbdbd'\n        },\n        children: /*#__PURE__*/_jsxDEV(AddCircleIcon, {\n          color: \"inherit\",\n          fontSize: \"large\",\n          style: {\n            cursor: 'pointer'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 669,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 659,\n      columnNumber: 13\n    }, this);\n  };\n\n  _s5(SelectProduct, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n\n  const columns = [{\n    name: 'pk',\n    label: 'PK',\n    options: {\n      filter: false,\n      sort: false,\n      display: false\n    }\n  }, {\n    name: 'store_id',\n    label: 'ID',\n    options: {\n      filter: true,\n      sort: true,\n      display: false\n    }\n  }, {\n    name: 'store_name',\n    label: 'TIENDA',\n    options: {\n      filter: true,\n      sort: true\n    }\n  }, {\n    name: 'inventory_days',\n    label: 'DIAS INV',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (!isRowDataAvailable(rowData)) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 32\n          }, this);\n        }\n\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [value, /*#__PURE__*/_jsxDEV(NumberAlert, {\n            condition: parseFloat(value) < 1,\n            title: \"No hay stock para cumplir con la demanda requerida\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 25\n        }, this);\n      }\n    }\n  }, {\n    name: 'break_value',\n    label: 'P.QUIEBRE',\n    options: {\n      filter: true,\n      sort: true,\n      display: true,\n      customBodyRender: (value, tableMeta) => {\n        var _rowData$break_scale, _rowData$break_scale2;\n\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (!isRowDataAvailable(rowData)) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 32\n          }, this);\n        }\n\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1,\n            flexDirection: 'row',\n            alignItems: 'center',\n            justifyContent: 'center',\n            width: '100%',\n            cursor: 'pointer'\n          },\n          children: [`${(_rowData$break_scale = rowData === null || rowData === void 0 ? void 0 : rowData.break_scale) !== null && _rowData$break_scale !== void 0 ? _rowData$break_scale : 'QB'}(${parseFloat(value) > 0 ? value : '-'})`, /*#__PURE__*/_jsxDEV(AlertBreak, {\n            break_scale: (_rowData$break_scale2 = rowData === null || rowData === void 0 ? void 0 : rowData.break_scale) !== null && _rowData$break_scale2 !== void 0 ? _rowData$break_scale2 : 'QB'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 25\n        }, this);\n      }\n    }\n  }, {\n    name: 'inventory_days',\n    label: 'F.QUIEBRE',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = getRowDataSafely(pk);\n\n        if (!isRowDataAvailable(rowData)) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 32\n          }, this);\n        }\n\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [parseDateToLocaleString(addDays(new Date(), parseInt(value, 10))), /*#__PURE__*/_jsxDEV(NumberAlert, {\n            condition: parseInt(value, 10) < 1,\n            title: \"El quiebre es Cr\\xEDtico\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 774,\n          columnNumber: 25\n        }, this);\n      }\n    }\n  }, {\n    name: 'break_value',\n    label: 'F.LIMITE PEDIDO',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        return renderSafeContent(pk, rowData => {\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            },\n            children: [parseDateToLocaleString(addDays(new Date(), parseInt(value, 10))), /*#__PURE__*/_jsxDEV(NumberAlert, {\n              condition: parseInt(value, 10) < 1,\n              title: \"El quiebre es Cr\\xEDtico\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 793,\n            columnNumber: 29\n          }, this);\n        });\n      }\n    }\n  }, {\n    name: 'vcto_alert',\n    label: 'VCTO',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData && rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 813,\n            columnNumber: 32\n          }, this);\n        }\n\n        return value;\n      }\n    }\n  }, {\n    name: 'rotation_scale',\n    label: 'ROTACIÓN',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 830,\n            columnNumber: 32\n          }, this);\n        }\n\n        return value;\n      }\n    }\n  }, {\n    name: 'rotation_value',\n    label: 'INDICE DE ROTACIÓN',\n    options: {\n      filter: true,\n      sort: true,\n      display: true,\n      customBodyRender: (value, tableMeta) => {\n        var _rowData$rotation_ind, _rowData$rotation_sca;\n\n        const pk = tableMeta.rowData[0];\n        const store = tableMeta.rowData[1];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 849,\n            columnNumber: 32\n          }, this);\n        }\n\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1,\n            flexDirection: 'row',\n            alignItems: 'center',\n            justifyContent: 'center',\n            width: '100%',\n            cursor: 'pointer'\n          },\n          onClick: () => {\n            setSelected({ ...rowData,\n              store\n            });\n            openModal();\n          },\n          children: [(_rowData$rotation_ind = rowData === null || rowData === void 0 ? void 0 : rowData.rotation_indicator) !== null && _rowData$rotation_ind !== void 0 ? _rowData$rotation_ind : '-', /*#__PURE__*/_jsxDEV(AlertRotation, {\n            rotationScale: (_rowData$rotation_sca = rowData === null || rowData === void 0 ? void 0 : rowData.rotation_scale) !== null && _rowData$rotation_sca !== void 0 ? _rowData$rotation_sca : 'NR'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 869,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 853,\n          columnNumber: 25\n        }, this);\n      }\n    }\n  }, {\n    name: 'obsolete_indicator',\n    label: '% OBSOLETO',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 886,\n            columnNumber: 32\n          }, this);\n        }\n\n        return `${value}%`;\n      }\n    }\n  }, {\n    name: 'pareto_percentage_sale',\n    label: 'PRTO % VOL',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 904,\n            columnNumber: 32\n          }, this);\n        }\n\n        return `${value}%`;\n      }\n    }\n  }, {\n    name: 'pareto_percentage_utility',\n    label: 'PRTO % UTL',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 922,\n            columnNumber: 32\n          }, this);\n        }\n\n        return `${value}%`;\n      }\n    }\n  }, {\n    name: 'stock',\n    label: 'STOCK LOCAL',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 940,\n            columnNumber: 32\n          }, this);\n        }\n\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [value, \" \", /*#__PURE__*/_jsxDEV(NumberAlert, {\n            condition: parseFloat(value) < 1,\n            title: \"No hay stock en el Local\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 945,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 944,\n          columnNumber: 25\n        }, this);\n      }\n    }\n  }, {\n    name: 'to_enter',\n    label: 'CANT X ING',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 962,\n            columnNumber: 32\n          }, this);\n        }\n\n        return value;\n      }\n    }\n  }, {\n    name: 'to_dispatch',\n    label: 'CANT X DES',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 980,\n            columnNumber: 32\n          }, this);\n        }\n\n        return value;\n      }\n    }\n  }, {\n    name: 'purchase_stock',\n    label: 'STOCK DISPON',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: (value, tableMeta) => parseFloat(value).toFixed(2)\n    }\n  }, {\n    name: 'min_stock',\n    label: 'STOCK MIN',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1006,\n            columnNumber: 32\n          }, this);\n        }\n\n        return parseFloat(value).toFixed(2);\n      }\n    }\n  }, {\n    name: 'average_quantity',\n    label: 'PROM X 30 DÍAS',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1022,\n            columnNumber: 32\n          }, this);\n        }\n\n        return parseFloat(value).toFixed(2);\n      }\n    }\n  }, {\n    name: 'unit_quantity',\n    label: 'SUGERIDO',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: (value, tableMeta) => {\n        var _productData$presenta3, _productData$presenta4;\n\n        const pk = row[0];\n        const productData = data.find(item => item.pk === pk);\n        const result = value / parseFloat((_productData$presenta3 = productData === null || productData === void 0 ? void 0 : (_productData$presenta4 = productData.presentation) === null || _productData$presenta4 === void 0 ? void 0 : _productData$presenta4.equivalence) !== null && _productData$presenta3 !== void 0 ? _productData$presenta3 : 1);\n        const product_id = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === product_id);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1042,\n            columnNumber: 32\n          }, this);\n        }\n\n        return Math.ceil(result).toFixed(2);\n      }\n    }\n  }, {\n    name: 'quantity_oc',\n    label: 'CANT A PEDIR',\n    options: {\n      filter: true,\n      sort: false,\n      display: true,\n      customBodyRender: (value, tableMeta) => /*#__PURE__*/_jsxDEV(QuantityInput, {\n        tableMeta: tableMeta\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1056,\n        columnNumber: 57\n      }, this)\n    }\n  }, {\n    name: 'quantity_ota',\n    label: 'CANT A TRANSFERIR',\n    options: {\n      filter: true,\n      sort: false,\n      display: false,\n      customBodyRender: (value, tableMeta) => /*#__PURE__*/_jsxDEV(QuantityInput, {\n        tableMeta: tableMeta,\n        keyword: \"quantity_ota\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1066,\n        columnNumber: 57\n      }, this)\n    }\n  }, {\n    name: 'measure_name',\n    label: 'PRES',\n    options: {\n      filter: true,\n      sort: true,\n      display: true,\n      customBodyRender: () => {\n        var _rowData$presentation3, _rowData$presentation4;\n\n        const pk = row[0];\n        const rowData = data.find(item => item.pk === pk);\n        return (_rowData$presentation3 = rowData === null || rowData === void 0 ? void 0 : (_rowData$presentation4 = rowData.presentation) === null || _rowData$presentation4 === void 0 ? void 0 : _rowData$presentation4.measure_name) !== null && _rowData$presentation3 !== void 0 ? _rowData$presentation3 : '';\n      }\n    }\n  }, {\n    name: 'other',\n    label: '-',\n    options: {\n      filter: false,\n      sort: false,\n      customBodyRender: (_, tableMeta) => /*#__PURE__*/_jsxDEV(SelectProduct, {\n        tableMeta: tableMeta\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1089,\n        columnNumber: 53\n      }, this)\n    }\n  }];\n  const merchandiseFoodColumns = [{\n    name: 'pk',\n    label: 'PK',\n    options: {\n      filter: false,\n      sort: false,\n      display: false\n    }\n  }, {\n    name: 'store_id',\n    label: 'ID',\n    options: {\n      filter: true,\n      sort: true,\n      display: false\n    }\n  }, {\n    name: 'store_name',\n    label: 'TIENDA',\n    options: {\n      filter: true,\n      sort: true\n    }\n  }, {\n    name: 'rotation_scale',\n    label: 'ROTACIÓN',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1132,\n            columnNumber: 32\n          }, this);\n        }\n\n        return value;\n      }\n    }\n  }, {\n    name: 'rotation_value',\n    label: 'INDICE DE ROTACIÓN',\n    options: {\n      filter: true,\n      sort: true,\n      display: true,\n      customBodyRender: (value, tableMeta) => {\n        var _rowData$rotation_ind2, _rowData$rotation_sca2;\n\n        const pk = tableMeta.rowData[0];\n        const store = tableMeta.rowData[1];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1151,\n            columnNumber: 32\n          }, this);\n        }\n\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1,\n            flexDirection: 'row',\n            alignItems: 'center',\n            justifyContent: 'center',\n            width: '100%',\n            cursor: 'pointer'\n          },\n          onClick: () => {\n            setSelected({ ...rowData,\n              store\n            });\n            openModal();\n          },\n          children: [(_rowData$rotation_ind2 = rowData === null || rowData === void 0 ? void 0 : rowData.rotation_indicator) !== null && _rowData$rotation_ind2 !== void 0 ? _rowData$rotation_ind2 : '-', /*#__PURE__*/_jsxDEV(AlertRotation, {\n            rotationScale: (_rowData$rotation_sca2 = rowData === null || rowData === void 0 ? void 0 : rowData.rotation_scale) !== null && _rowData$rotation_sca2 !== void 0 ? _rowData$rotation_sca2 : 'NR'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1171,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1155,\n          columnNumber: 25\n        }, this);\n      }\n    }\n  }, {\n    name: 'obsolete_indicator',\n    label: '% OBSOLETO',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1188,\n            columnNumber: 32\n          }, this);\n        }\n\n        return `${value}%`;\n      }\n    }\n  }, {\n    name: 'pareto_percentage_sale',\n    label: 'PRTO % VOL',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1206,\n            columnNumber: 32\n          }, this);\n        }\n\n        return `${value}%`;\n      }\n    }\n  }, {\n    name: 'pareto_percentage_utility',\n    label: 'PRTO % UTL',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1224,\n            columnNumber: 32\n          }, this);\n        }\n\n        return `${value}%`;\n      }\n    }\n  }, {\n    name: 'to_enter',\n    label: 'CANT X ING',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1242,\n            columnNumber: 32\n          }, this);\n        }\n\n        return value;\n      }\n    }\n  }, {\n    name: 'to_dispatch',\n    label: 'CANT X DES',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1260,\n            columnNumber: 32\n          }, this);\n        }\n\n        return value;\n      }\n    }\n  }, {\n    name: 'average_quantity',\n    label: 'PROM X 30 DÍAS',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1277,\n            columnNumber: 32\n          }, this);\n        }\n\n        return parseFloat(value).toFixed(2);\n      }\n    }\n  }, {\n    name: 'unit_quantity',\n    label: 'PROYECTADO A VENDER',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: (value, tableMeta) => {\n        var _productData$presenta5, _productData$presenta6, _rowData$presentation5, _rowData$presentation6;\n\n        const pk = row[0];\n        const productData = data.find(item => item.pk === pk);\n        const result = value / parseFloat((_productData$presenta5 = productData === null || productData === void 0 ? void 0 : (_productData$presenta6 = productData.presentation) === null || _productData$presenta6 === void 0 ? void 0 : _productData$presenta6.equivalence) !== null && _productData$presenta5 !== void 0 ? _productData$presenta5 : 1);\n        const rowData = merchandiseFoodData.find(item => item.pk === pk);\n        const measure_name = (_rowData$presentation5 = rowData === null || rowData === void 0 ? void 0 : (_rowData$presentation6 = rowData.presentation) === null || _rowData$presentation6 === void 0 ? void 0 : _rowData$presentation6.measure_name) !== null && _rowData$presentation5 !== void 0 ? _rowData$presentation5 : '';\n        return `${Math.ceil(result).toFixed(2)} ${measure_name}`;\n      }\n    }\n  }];\n  const supplyColumns = [{\n    name: 'pk',\n    label: 'PK',\n    options: {\n      filter: false,\n      sort: false,\n      display: false\n    }\n  }, {\n    name: 'store_id',\n    label: 'ID',\n    options: {\n      filter: true,\n      sort: true,\n      display: false\n    }\n  }, {\n    name: 'store_name',\n    label: 'TIENDA',\n    options: {\n      filter: true,\n      sort: true\n    }\n  }, {\n    name: 'unit_quantity_proyected',\n    label: 'C.PROYECTADA',\n    options: {\n      filter: true,\n      sort: true,\n      display: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n        value: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1335,\n        columnNumber: 46\n      }, this)\n    }\n  }, {\n    name: 'purchase_stock',\n    label: 'STOCK',\n    options: {\n      filter: true,\n      sort: true,\n      display: true,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = getRowDataSafely(pk);\n\n        if (!isRowDataAvailable(rowData)) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1349,\n            columnNumber: 32\n          }, this);\n        }\n\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'end',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n            value: value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1353,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(NumberAlert, {\n            condition: parseFloat(value) < 1,\n            title: \"No hay stock en el Local\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1354,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1352,\n          columnNumber: 25\n        }, this);\n      }\n    }\n  }, {\n    name: 'to_enter',\n    label: 'CANT X ING',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = getRowDataSafely(pk);\n\n        if (!isRowDataAvailable(rowData)) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1371,\n            columnNumber: 32\n          }, this);\n        }\n\n        return /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n          value: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1374,\n          columnNumber: 28\n        }, this);\n      }\n    }\n  }, {\n    name: 'to_dispatch',\n    label: 'CANT X DES',\n    options: {\n      filter: true,\n      sort: true,\n      display: false,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = repositionProduct.find(item => item.pk === pk);\n\n        if (rowData.notAvailable) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1389,\n            columnNumber: 32\n          }, this);\n        }\n\n        return /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n          value: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1392,\n          columnNumber: 28\n        }, this);\n      }\n    }\n  }, {\n    name: 'quantity_oc',\n    label: 'CANTIDAD REPONER',\n    options: {\n      filter: true,\n      sort: false,\n      display: true,\n      customBodyRender: (value, tableMeta) => /*#__PURE__*/_jsxDEV(QuantityInput, {\n        tableMeta: tableMeta,\n        keyword: \"quantity_ota\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1403,\n        columnNumber: 57\n      }, this)\n    }\n  }];\n  const recipeColumns = [{\n    name: 'recipe_id',\n    label: 'ID',\n    options: {\n      display: false\n    }\n  }, {\n    name: 'recipe_name',\n    label: 'RECETA'\n  }, {\n    name: 'recipe_quantity',\n    label: 'CANTIDAD DE RECETA'\n  }, {\n    name: 'supply_quantity',\n    label: 'CANTIDAD DEL INSUMO'\n  }];\n  const simplifiedDerivedProductColumns = [{\n    name: 'product_id',\n    label: 'ID',\n    options: {\n      filter: true,\n      sort: true,\n      setCellHeaderProps: () => ({\n        style: {\n          width: '80px',\n          maxWidth: '80px'\n        }\n      }),\n      setCellProps: () => ({\n        style: {\n          width: '80px',\n          maxWidth: '80px'\n        }\n      })\n    }\n  }, {\n    name: 'product_name',\n    label: 'PRODUCTO',\n    options: {\n      filter: true,\n      sort: true,\n      setCellHeaderProps: () => ({\n        style: {\n          minWidth: '450px',\n          whiteSpace: 'nowrap'\n        }\n      }),\n      setCellProps: () => ({\n        style: {\n          minWidth: '450px',\n          whiteSpace: 'nowrap'\n        }\n      }),\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          whiteSpace: 'nowrap'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1451,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1450,\n        columnNumber: 21\n      }, this)\n    }\n  }, {\n    name: 'waste_info',\n    label: 'MERMA %',\n    options: {\n      filter: true,\n      sort: true,\n      setCellHeaderProps: () => ({\n        style: {\n          minWidth: '100px',\n          whiteSpace: 'nowrap'\n        }\n      }),\n      setCellProps: () => ({\n        style: {\n          minWidth: '100px',\n          whiteSpace: 'nowrap'\n        }\n      }),\n      customBodyRender: value => {\n        const wasteInfo = value;\n\n        if (!wasteInfo || !wasteInfo.waste_percentage_total) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              whiteSpace: 'nowrap',\n              textAlign: 'center'\n            },\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1468,\n            columnNumber: 32\n          }, this);\n        }\n\n        const percentage = parseFloat(wasteInfo.waste_percentage_total);\n        return /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            whiteSpace: 'nowrap',\n            textAlign: 'center'\n          },\n          children: [percentage.toFixed(2), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1473,\n          columnNumber: 28\n        }, this);\n      }\n    }\n  }, {\n    name: 'purchase_stock',\n    label: 'STOCK EN TIENDAS',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n        value: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1483,\n        columnNumber: 46\n      }, this)\n    }\n  }, // {\n  //     name: 'purchase_stock',\n  //     label: 'STOCK DE UNIDADES EN TIENDAS',\n  //     options: {\n  //         filter: true,\n  //         sort: true,\n  //         customBodyRender: (value, { rowData }) => {\n  //             const product_id = rowData[0];\n  //             const productData = data.find((item) => item.pk === product_id);\n  //             const result = value / parseFloat(productData?.equivalence_default ?? 1);\n  //             return <RightAlignedNumber value={result} />;\n  //         }\n  //     }\n  // },\n  {\n    name: 'supplying_stock',\n    label: 'STOCK A.PRINCIPAL',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n        value: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1506,\n        columnNumber: 46\n      }, this)\n    }\n  }, {\n    name: 'measure_name',\n    label: 'PRESENTACIÓN',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(Typography, {\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1515,\n        columnNumber: 46\n      }, this)\n    }\n  }, {\n    name: 'measure_default',\n    label: 'PRESENTACIÓN UNIDAD',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(Typography, {\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1524,\n        columnNumber: 46\n      }, this)\n    }\n  }];\n  const derivedAnalysisColumns = [{\n    name: 'pk',\n    label: 'PK',\n    options: {\n      filter: false,\n      sort: false,\n      display: false\n    }\n  }, {\n    name: 'store_name',\n    label: 'TIENDA',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          fontSize: '1.2rem',\n          fontWeight: 'bold'\n        },\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1545,\n        columnNumber: 46\n      }, this)\n    }\n  }, {\n    name: 'stock',\n    label: 'STOCK',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          fontSize: '1.2rem',\n          fontWeight: 'medium',\n          textAlign: 'right'\n        },\n        children: parseFloat(value).toFixed(2)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1555,\n        columnNumber: 21\n      }, this)\n    }\n  }];\n\n  const getColumns = () => {\n    if (foodMode === RAW_MATERIAL) {\n      return derivedAnalysisColumns;\n    }\n\n    if (isFromProyection) {\n      return merchandiseFoodColumns;\n    }\n\n    if (isAsync) {\n      return columns;\n    }\n\n    return supplyColumns;\n  };\n\n  const getCardWidth = () => {\n    if (isFromProyection && isAsync) {\n      return '100%';\n    }\n\n    if (isFromProyection && !isAsync) {\n      return '60%';\n    }\n\n    if (!isFromProyection && isAsync) {\n      return '90%';\n    }\n\n    return '45%';\n  };\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      pt: 2,\n      pl: 2,\n      pr: 2,\n      pb: 4,\n      backgroundColor: '#f5f5f5'\n    },\n    children: /*#__PURE__*/_jsxDEV(BlockLoader, {\n      loading: loading,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          flexDirection: 'row',\n          width: '100%',\n          justifyContent: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(NestedCard, {\n          width: foodMode === RAW_MATERIAL ? '30%' : getCardWidth(),\n          sx: {\n            '& .MuiTable-root': {\n              width: '100% !important',\n              tableLayout: 'fixed'\n            },\n            '& .MuiTableCell-root': {\n              padding: '8px 16px'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            columns: getColumns(),\n            data: repositionProduct,\n            title: \"An\\xE1lisis por Tienda\",\n            options: {\n              search: false,\n              download: false,\n              print: false,\n              sort: false,\n              viewColumns: true,\n              filter: false,\n              filterType: 'multiselect',\n              responsive: 'vertical',\n              fixedHeader: true,\n              fixedSelectColumn: true,\n              jumpToPage: false,\n              resizableColumns: false,\n              draggableColumns: {\n                enabled: true\n              },\n              serverSide: true,\n              selectableRows: 'none',\n              selectableRowsOnClick: false,\n              pagination: false,\n              confirmFilters: false,\n              rowHover: true,\n              toolbar: false\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1606,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1594,\n          columnNumber: 21\n        }, this), foodMode === RAW_MATERIAL && /*#__PURE__*/_jsxDEV(NestedCard, {\n          width: \"70%\",\n          sx: {\n            '& .MuiTable-root': {\n              width: '100% !important',\n              tableLayout: 'fixed'\n            },\n            '& .MuiTableCell-root': {\n              padding: '8px 16px'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(NestedGrid, {\n            columns: simplifiedDerivedProductColumns,\n            data: derivedProducts,\n            title: \"Productos Derivados\",\n            RenderNestedContent: props => /*#__PURE__*/_jsxDEV(DerivedProductNestedContent, { ...props,\n              data: data,\n              derivedAnalysisColumns: derivedAnalysisColumns,\n              simplifiedDerivedProductColumns: simplifiedDerivedProductColumns\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1654,\n              columnNumber: 37\n            }, this),\n            options: {\n              search: false,\n              download: false,\n              print: false,\n              sort: false,\n              viewColumns: true,\n              filter: false,\n              filterType: 'multiselect',\n              responsive: 'vertical',\n              fixedHeader: true,\n              fixedSelectColumn: true,\n              jumpToPage: false,\n              resizableColumns: false,\n              draggableColumns: {\n                enabled: true\n              },\n              serverSide: false,\n              selectableRows: 'none',\n              selectableRowsOnClick: false,\n              pagination: false,\n              confirmFilters: false,\n              rowHover: true,\n              toolbar: false,\n              setTableProps: () => ({\n                size: 'small'\n              })\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1649,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1637,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1593,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1592,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1591,\n    columnNumber: 9\n  }, this);\n}\n/* istanbul ignore next */\n\n/* c8 ignore start */\n\n/* eslint-disable */\n\n_s6(RotationDetail, \"wwjX0k+SKjyLJpwjzcztJo/dXJw=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useLoading];\n});\n\n_c8 = RotationDetail;\n;\n\nfunction oo_cm() {\n  try {\n    return (0, eval)(\"globalThis._console_ninja\") || (0, eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x418f23=_0x33f3;(function(_0x2c70e5,_0x70d422){var _0x45fe32=_0x33f3,_0x244e11=_0x2c70e5();while(!![]){try{var _0xe599a4=parseInt(_0x45fe32(0xb0))/0x1*(parseInt(_0x45fe32(0xa1))/0x2)+-parseInt(_0x45fe32(0x15e))/0x3+-parseInt(_0x45fe32(0x109))/0x4*(parseInt(_0x45fe32(0xc2))/0x5)+parseInt(_0x45fe32(0x191))/0x6+-parseInt(_0x45fe32(0x11d))/0x7*(parseInt(_0x45fe32(0x9c))/0x8)+parseInt(_0x45fe32(0xe1))/0x9+-parseInt(_0x45fe32(0x15f))/0xa*(-parseInt(_0x45fe32(0x148))/0xb);if(_0xe599a4===_0x70d422)break;else _0x244e11['push'](_0x244e11['shift']());}catch(_0x630c67){_0x244e11['push'](_0x244e11['shift']());}}}(_0x4e19,0xaaec1));var G=Object[_0x418f23(0xe5)],V=Object[_0x418f23(0x103)],ee=Object['getOwnPropertyDescriptor'],te=Object[_0x418f23(0xdf)],ne=Object[_0x418f23(0xd9)],re=Object[_0x418f23(0x119)][_0x418f23(0xf2)],ie=(_0x24c79a,_0x5c1c97,_0x1147c3,_0x2138d8)=>{var _0x36a3cf=_0x418f23;if(_0x5c1c97&&typeof _0x5c1c97==_0x36a3cf(0x117)||typeof _0x5c1c97==_0x36a3cf(0x13e)){for(let _0x5c0210 of te(_0x5c1c97))!re[_0x36a3cf(0xda)](_0x24c79a,_0x5c0210)&&_0x5c0210!==_0x1147c3&&V(_0x24c79a,_0x5c0210,{'get':()=>_0x5c1c97[_0x5c0210],'enumerable':!(_0x2138d8=ee(_0x5c1c97,_0x5c0210))||_0x2138d8[_0x36a3cf(0x14e)]});}return _0x24c79a;},j=(_0x1f84af,_0x39bbd1,_0xf2cf2e)=>(_0xf2cf2e=_0x1f84af!=null?G(ne(_0x1f84af)):{},ie(_0x39bbd1||!_0x1f84af||!_0x1f84af['__es'+'Module']?V(_0xf2cf2e,'default',{'value':_0x1f84af,'enumerable':!0x0}):_0xf2cf2e,_0x1f84af)),q=class{constructor(_0x14d9ea,_0x61266e,_0x21d732,_0x659164,_0x2ce13a,_0x1b0c0c){var _0x4b2850=_0x418f23,_0x34d24c,_0x26bffd,_0xeab781,_0x4b345e;this['global']=_0x14d9ea,this[_0x4b2850(0xe0)]=_0x61266e,this['port']=_0x21d732,this[_0x4b2850(0x173)]=_0x659164,this[_0x4b2850(0x131)]=_0x2ce13a,this['eventReceivedCallback']=_0x1b0c0c,this[_0x4b2850(0x159)]=!0x0,this['_allowedToConnectOnSend']=!0x0,this[_0x4b2850(0xee)]=!0x1,this[_0x4b2850(0xa0)]=!0x1,this[_0x4b2850(0x160)]=((_0x26bffd=(_0x34d24c=_0x14d9ea['process'])==null?void 0x0:_0x34d24c[_0x4b2850(0x116)])==null?void 0x0:_0x26bffd['NEXT_RUNTIME'])===_0x4b2850(0x9e),this[_0x4b2850(0x174)]=!((_0x4b345e=(_0xeab781=this[_0x4b2850(0xf8)][_0x4b2850(0x104)])==null?void 0x0:_0xeab781['versions'])!=null&&_0x4b345e[_0x4b2850(0xc8)])&&!this[_0x4b2850(0x160)],this[_0x4b2850(0xe6)]=null,this[_0x4b2850(0xfc)]=0x0,this[_0x4b2850(0xf1)]=0x14,this['_webSocketErrorDocsLink']=_0x4b2850(0xcd),this[_0x4b2850(0xb8)]=(this[_0x4b2850(0x174)]?_0x4b2850(0xaf):_0x4b2850(0x9f))+this[_0x4b2850(0xc9)];}async[_0x418f23(0xac)](){var _0x4a1673=_0x418f23,_0x2d8a6c,_0x2fabb9;if(this[_0x4a1673(0xe6)])return this[_0x4a1673(0xe6)];let _0x338282;if(this[_0x4a1673(0x174)]||this[_0x4a1673(0x160)])_0x338282=this[_0x4a1673(0xf8)][_0x4a1673(0x17c)];else{if((_0x2d8a6c=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])!=null&&_0x2d8a6c[_0x4a1673(0xcc)])_0x338282=(_0x2fabb9=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])==null?void 0x0:_0x2fabb9[_0x4a1673(0xcc)];else try{let _0x6adc18=await import(_0x4a1673(0x17f));_0x338282=(await import((await import(_0x4a1673(0x14c)))[_0x4a1673(0xb2)](_0x6adc18['join'](this[_0x4a1673(0x173)],_0x4a1673(0x9a)))['toString']()))[_0x4a1673(0x164)];}catch{try{_0x338282=require(require(_0x4a1673(0x17f))['join'](this[_0x4a1673(0x173)],'ws'));}catch{throw new Error('failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket');}}}return this[_0x4a1673(0xe6)]=_0x338282,_0x338282;}[_0x418f23(0xe2)](){var _0x560a95=_0x418f23;this[_0x560a95(0xa0)]||this[_0x560a95(0xee)]||this[_0x560a95(0xfc)]>=this[_0x560a95(0xf1)]||(this['_allowedToConnectOnSend']=!0x1,this['_connecting']=!0x0,this[_0x560a95(0xfc)]++,this['_ws']=new Promise((_0x48a2aa,_0x1b9b87)=>{var _0x3507cc=_0x560a95;this[_0x3507cc(0xac)]()['then'](_0x2d9634=>{var _0x4649cf=_0x3507cc;let _0x18b292=new _0x2d9634(_0x4649cf(0x185)+(!this['_inBrowser']&&this['dockerizedApp']?_0x4649cf(0x15a):this[_0x4649cf(0xe0)])+':'+this['port']);_0x18b292[_0x4649cf(0x16f)]=()=>{var _0x37af5c=_0x4649cf;this['_allowedToSend']=!0x1,this[_0x37af5c(0x162)](_0x18b292),this['_attemptToReconnectShortly'](),_0x1b9b87(new Error('logger\\\\x20websocket\\\\x20error'));},_0x18b292[_0x4649cf(0xf5)]=()=>{var _0x5c5b5c=_0x4649cf;this[_0x5c5b5c(0x174)]||_0x18b292[_0x5c5b5c(0xe7)]&&_0x18b292[_0x5c5b5c(0xe7)]['unref']&&_0x18b292[_0x5c5b5c(0xe7)]['unref'](),_0x48a2aa(_0x18b292);},_0x18b292[_0x4649cf(0xb6)]=()=>{this['_allowedToConnectOnSend']=!0x0,this['_disposeWebsocket'](_0x18b292),this['_attemptToReconnectShortly']();},_0x18b292[_0x4649cf(0x121)]=_0xf360ec=>{var _0x34c0e1=_0x4649cf;try{if(!(_0xf360ec!=null&&_0xf360ec[_0x34c0e1(0x99)])||!this[_0x34c0e1(0x12f)])return;let _0x5a655a=JSON[_0x34c0e1(0x13d)](_0xf360ec[_0x34c0e1(0x99)]);this['eventReceivedCallback'](_0x5a655a['method'],_0x5a655a[_0x34c0e1(0xab)],this[_0x34c0e1(0xf8)],this[_0x34c0e1(0x174)]);}catch{}};})['then'](_0x382d9b=>(this['_connected']=!0x0,this[_0x3507cc(0xa0)]=!0x1,this[_0x3507cc(0x12c)]=!0x1,this[_0x3507cc(0x159)]=!0x0,this['_connectAttemptCount']=0x0,_0x382d9b))['catch'](_0x469147=>(this[_0x3507cc(0xee)]=!0x1,this[_0x3507cc(0xa0)]=!0x1,console[_0x3507cc(0xed)](_0x3507cc(0x169)+this[_0x3507cc(0xc9)]),_0x1b9b87(new Error(_0x3507cc(0x12a)+(_0x469147&&_0x469147[_0x3507cc(0xb4)])))));}));}[_0x418f23(0x162)](_0x391e4c){var _0x18bf98=_0x418f23;this[_0x18bf98(0xee)]=!0x1,this[_0x18bf98(0xa0)]=!0x1;try{_0x391e4c['onclose']=null,_0x391e4c[_0x18bf98(0x16f)]=null,_0x391e4c[_0x18bf98(0xf5)]=null;}catch{}try{_0x391e4c[_0x18bf98(0xb9)]<0x2&&_0x391e4c[_0x18bf98(0x141)]();}catch{}}['_attemptToReconnectShortly'](){var _0x4846b6=_0x418f23;clearTimeout(this[_0x4846b6(0xa3)]),!(this[_0x4846b6(0xfc)]>=this[_0x4846b6(0xf1)])&&(this['_reconnectTimeout']=setTimeout(()=>{var _0xc0d1ae=_0x4846b6,_0x3b3b8b;this[_0xc0d1ae(0xee)]||this[_0xc0d1ae(0xa0)]||(this[_0xc0d1ae(0xe2)](),(_0x3b3b8b=this[_0xc0d1ae(0xd2)])==null||_0x3b3b8b[_0xc0d1ae(0x120)](()=>this['_attemptToReconnectShortly']()));},0x1f4),this['_reconnectTimeout'][_0x4846b6(0x188)]&&this[_0x4846b6(0xa3)][_0x4846b6(0x188)]());}async[_0x418f23(0x11e)](_0x592dff){var _0x123097=_0x418f23;try{if(!this[_0x123097(0x159)])return;this[_0x123097(0x12c)]&&this[_0x123097(0xe2)](),(await this['_ws'])[_0x123097(0x11e)](JSON[_0x123097(0xdd)](_0x592dff));}catch(_0x3558e1){this['_extendedWarning']?console[_0x123097(0xed)](this['_sendErrorMessage']+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)])):(this[_0x123097(0x167)]=!0x0,console[_0x123097(0xed)](this[_0x123097(0xb8)]+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)]),_0x592dff)),this[_0x123097(0x159)]=!0x1,this[_0x123097(0xb3)]();}}};function H(_0x21a490,_0x6209b7,_0x32bdf1,_0x32048a,_0x5bcdf6,_0x3f8a6e,_0xb987a3,_0x3abcb6=oe){var _0x372163=_0x418f23;let _0x52a2ac=_0x32bdf1[_0x372163(0x190)](',')[_0x372163(0x12e)](_0x230c9d=>{var _0x1b5d4e=_0x372163,_0x4a53bb,_0x1cde39,_0x106ea9,_0x3f43e6;try{if(!_0x21a490['_console_ninja_session']){let _0x24bfb9=((_0x1cde39=(_0x4a53bb=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x4a53bb['versions'])==null?void 0x0:_0x1cde39[_0x1b5d4e(0xc8)])||((_0x3f43e6=(_0x106ea9=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x106ea9[_0x1b5d4e(0x116)])==null?void 0x0:_0x3f43e6[_0x1b5d4e(0xd6)])==='edge';(_0x5bcdf6===_0x1b5d4e(0x110)||_0x5bcdf6===_0x1b5d4e(0x155)||_0x5bcdf6==='astro'||_0x5bcdf6==='angular')&&(_0x5bcdf6+=_0x24bfb9?_0x1b5d4e(0x10c):_0x1b5d4e(0x124)),_0x21a490['_console_ninja_session']={'id':+new Date(),'tool':_0x5bcdf6},_0xb987a3&&_0x5bcdf6&&!_0x24bfb9&&console['log'](_0x1b5d4e(0xfe)+(_0x5bcdf6[_0x1b5d4e(0x13c)](0x0)[_0x1b5d4e(0x100)]()+_0x5bcdf6['substr'](0x1))+',','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)',_0x1b5d4e(0xbb));}let _0x4eb2eb=new q(_0x21a490,_0x6209b7,_0x230c9d,_0x32048a,_0x3f8a6e,_0x3abcb6);return _0x4eb2eb[_0x1b5d4e(0x11e)][_0x1b5d4e(0xf4)](_0x4eb2eb);}catch(_0x202950){return console[_0x1b5d4e(0xed)](_0x1b5d4e(0x18e),_0x202950&&_0x202950[_0x1b5d4e(0xb4)]),()=>{};}});return _0x17b111=>_0x52a2ac[_0x372163(0x178)](_0x3b7429=>_0x3b7429(_0x17b111));}function _0x4e19(){var _0x3dea94=['perf_hooks','now','elements','6915181ldjYIK','send','date','catch','onmessage','_isUndefined','_HTMLAllCollection','\\\\x20browser','_setNodePermissions','strLength','_getOwnPropertyDescriptor','_Symbol','indexOf','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','number','_allowedToConnectOnSend','_sortProps','map','eventReceivedCallback','array','dockerizedApp','match','_processTreeNodeResult','disabledLog','_numberRegExp','_hasSymbolPropertyOnItsPath',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"DevHardtech\\\",\\\"************\\\"],'_hasMapOnItsPath','performance','coverage','Error','charAt','parse','function','endsWith','rootExpression','close','undefined','_hasSetOnItsPath','_regExpToString','_p_name','slice','substr','11hsvZPL','hostname','serialize','_dateToString','url','boolean','enumerable','length','_addObjectProperty','root_exp','origin','reload','Symbol','remix','_objectToString','push','getOwnPropertySymbols','_allowedToSend','gateway.docker.internal','HTMLAllCollection','_getOwnPropertyNames','_isPrimitiveWrapperType','4193466bntOOn','16178350tQpRDP','_inNextEdge','time','_disposeWebsocket','_setNodeLabel','default','_ninjaIgnoreNextError','concat','_extendedWarning','resolveGetters','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','hrtime','current','_cleanNode','replace','_consoleNinjaAllowedToStart','onerror','Map','getOwnPropertyDescriptor','getter','nodeModules','_inBrowser','sort','_blacklistedProperty','autoExpandLimit','forEach','_undefined',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.vscode\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.456\\\\\\\\node_modules\\\",'_addProperty','WebSocket','_property','_addLoadNode','path','props','NEGATIVE_INFINITY','_type','webpack','error','ws://','_isMap','null','unref','negativeInfinity','','_quotedRegExp','set','nan','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','_p_length','split','7999758ImPfSL','_isArray','data','ws/index.js','parent','8YXKnRI','autoExpandPropertyCount','edge','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','_connecting','2rNewCg','allStrLength','_reconnectTimeout','noFunctions','toString','index','fromCharCode','_treeNodePropertiesBeforeFullValue','versions','count','args','getWebSocketClass','funcName','_isPrimitiveType','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','262697JHDjIO','1','pathToFileURL','_attemptToReconnectShortly','message','isExpressionToEvaluate','onclose','name','_sendErrorMessage','readyState','location','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','console','disabledTrace','[object\\\\x20BigInt]','valueOf','autoExpand','[object\\\\x20Date]','107080UCogNw','includes','trace','hits','expressionsToEvaluate','_p_','node','_webSocketErrorDocsLink','unknown','_setNodeId','_WebSocket','https://tinyurl.com/37x8b79t','value','_getOwnPropertySymbols','negativeZero','_setNodeQueryPath','_ws','elapsed','symbol','_propertyName','NEXT_RUNTIME','stackTraceLimit','_console_ninja_session','getPrototypeOf','call','_console_ninja','[object\\\\x20Array]','stringify','_isSet','getOwnPropertyNames','host','8484993ONNFtV','_connectToHostNow','level','_treeNodePropertiesAfterFullValue','create','_WebSocketClass','_socket','_addFunctionsNode','_capIfString','Boolean','_setNodeExpandableState','62812','warn','_connected','timeStamp','depth','_maxConnectAttemptCount','hasOwnProperty','capped','bind','onopen','Set','constructor','global','bigint','POSITIVE_INFINITY','sortProps','_connectAttemptCount','Number','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','log','toUpperCase','string','positiveInfinity','defineProperty','process','...','String','some','get','200DTLFWz','','type','\\\\x20server','startsWith','toLowerCase','_additionalMetadata','next.js','_setNodeExpressionPath','reduceLimits','test','[object\\\\x20Map]','autoExpandMaxDepth','env','object','autoExpandPreviousObjects','prototype'];_0x4e19=function(){return _0x3dea94;};return _0x4e19();}function oe(_0x29bd2d,_0x4ca25e,_0x2f30dc,_0x50ad96){var _0x4b277d=_0x418f23;_0x50ad96&&_0x29bd2d===_0x4b277d(0x153)&&_0x2f30dc[_0x4b277d(0xba)]['reload']();}function B(_0x53e47a){var _0x4f5825=_0x418f23,_0x17ea3d,_0x5476d8;let _0x2ed5e7=function(_0x3f9b33,_0x4798cf){return _0x4798cf-_0x3f9b33;},_0x2534f8;if(_0x53e47a[_0x4f5825(0x139)])_0x2534f8=function(){var _0xf68f54=_0x4f5825;return _0x53e47a[_0xf68f54(0x139)][_0xf68f54(0x11b)]();};else{if(_0x53e47a[_0x4f5825(0x104)]&&_0x53e47a[_0x4f5825(0x104)][_0x4f5825(0x16a)]&&((_0x5476d8=(_0x17ea3d=_0x53e47a[_0x4f5825(0x104)])==null?void 0x0:_0x17ea3d[_0x4f5825(0x116)])==null?void 0x0:_0x5476d8[_0x4f5825(0xd6)])!==_0x4f5825(0x9e))_0x2534f8=function(){var _0x1144bb=_0x4f5825;return _0x53e47a[_0x1144bb(0x104)][_0x1144bb(0x16a)]();},_0x2ed5e7=function(_0x4a8621,_0xc276d4){return 0x3e8*(_0xc276d4[0x0]-_0x4a8621[0x0])+(_0xc276d4[0x1]-_0x4a8621[0x1])/0xf4240;};else try{let {performance:_0x6c0ab3}=require(_0x4f5825(0x11a));_0x2534f8=function(){var _0x57029c=_0x4f5825;return _0x6c0ab3[_0x57029c(0x11b)]();};}catch{_0x2534f8=function(){return+new Date();};}}return{'elapsed':_0x2ed5e7,'timeStamp':_0x2534f8,'now':()=>Date['now']()};}function X(_0x108a65,_0x2bc4c8,_0x5e7fce){var _0xd0e45=_0x418f23,_0x184b4d,_0x3be467,_0x1494d3,_0x1853ba,_0xc61e6c;if(_0x108a65[_0xd0e45(0x16e)]!==void 0x0)return _0x108a65['_consoleNinjaAllowedToStart'];let _0xae1558=((_0x3be467=(_0x184b4d=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x184b4d[_0xd0e45(0xa9)])==null?void 0x0:_0x3be467[_0xd0e45(0xc8)])||((_0x1853ba=(_0x1494d3=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x1494d3[_0xd0e45(0x116)])==null?void 0x0:_0x1853ba[_0xd0e45(0xd6)])===_0xd0e45(0x9e);function _0x492297(_0x174e6c){var _0x9b4def=_0xd0e45;if(_0x174e6c[_0x9b4def(0x10d)]('/')&&_0x174e6c[_0x9b4def(0x13f)]('/')){let _0x2461d3=new RegExp(_0x174e6c['slice'](0x1,-0x1));return _0x2a4fef=>_0x2461d3[_0x9b4def(0x113)](_0x2a4fef);}else{if(_0x174e6c[_0x9b4def(0xc3)]('*')||_0x174e6c[_0x9b4def(0xc3)]('?')){let _0x51dbdb=new RegExp('^'+_0x174e6c[_0x9b4def(0x16d)](/\\\\./g,String[_0x9b4def(0xa7)](0x5c)+'.')[_0x9b4def(0x16d)](/\\\\*/g,'.*')[_0x9b4def(0x16d)](/\\\\?/g,'.')+String[_0x9b4def(0xa7)](0x24));return _0x2bf349=>_0x51dbdb['test'](_0x2bf349);}else return _0x40a674=>_0x40a674===_0x174e6c;}}let _0x418e9a=_0x2bc4c8[_0xd0e45(0x12e)](_0x492297);return _0x108a65[_0xd0e45(0x16e)]=_0xae1558||!_0x2bc4c8,!_0x108a65['_consoleNinjaAllowedToStart']&&((_0xc61e6c=_0x108a65[_0xd0e45(0xba)])==null?void 0x0:_0xc61e6c['hostname'])&&(_0x108a65[_0xd0e45(0x16e)]=_0x418e9a[_0xd0e45(0x107)](_0x1dbe80=>_0x1dbe80(_0x108a65[_0xd0e45(0xba)][_0xd0e45(0x149)]))),_0x108a65['_consoleNinjaAllowedToStart'];}function _0x33f3(_0x3a814d,_0x58c537){var _0x4e195d=_0x4e19();return _0x33f3=function(_0x33f3b8,_0x2e2a30){_0x33f3b8=_0x33f3b8-0x98;var _0x3c84c1=_0x4e195d[_0x33f3b8];return _0x3c84c1;},_0x33f3(_0x3a814d,_0x58c537);}function J(_0x3830e6,_0x45a6b5,_0x2f8209,_0x3cee70){var _0x40c820=_0x418f23;_0x3830e6=_0x3830e6,_0x45a6b5=_0x45a6b5,_0x2f8209=_0x2f8209,_0x3cee70=_0x3cee70;let _0x38a5a7=B(_0x3830e6),_0x5b41b9=_0x38a5a7[_0x40c820(0xd3)],_0x1169a5=_0x38a5a7[_0x40c820(0xef)];class _0x1e3ba1{constructor(){var _0x3396c1=_0x40c820;this['_keyStrRegExp']=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this[_0x3396c1(0x135)]=/^(0|[1-9][0-9]*)$/,this[_0x3396c1(0x18b)]=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x3396c1(0x179)]=_0x3830e6[_0x3396c1(0x142)],this[_0x3396c1(0x123)]=_0x3830e6[_0x3396c1(0x15b)],this[_0x3396c1(0x127)]=Object[_0x3396c1(0x171)],this['_getOwnPropertyNames']=Object[_0x3396c1(0xdf)],this[_0x3396c1(0x128)]=_0x3830e6[_0x3396c1(0x154)],this[_0x3396c1(0x144)]=RegExp[_0x3396c1(0x119)][_0x3396c1(0xa5)],this['_dateToString']=Date[_0x3396c1(0x119)][_0x3396c1(0xa5)];}[_0x40c820(0x14a)](_0x1f7b5d,_0x5b6b91,_0x1ebf24,_0x4f3c70){var _0x4d7e42=_0x40c820,_0xe363bc=this,_0x290e3b=_0x1ebf24[_0x4d7e42(0xc0)];function _0x16ce5f(_0xf8520c,_0x1a1953,_0x3e443e){var _0x4923f3=_0x4d7e42;_0x1a1953[_0x4923f3(0x10b)]=_0x4923f3(0xca),_0x1a1953['error']=_0xf8520c[_0x4923f3(0xb4)],_0x454078=_0x3e443e[_0x4923f3(0xc8)]['current'],_0x3e443e['node'][_0x4923f3(0x16b)]=_0x1a1953,_0xe363bc['_treeNodePropertiesBeforeFullValue'](_0x1a1953,_0x3e443e);}let _0x1533a9;_0x3830e6[_0x4d7e42(0xbc)]&&(_0x1533a9=_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)],_0x1533a9&&(_0x3830e6['console'][_0x4d7e42(0x184)]=function(){}));try{try{_0x1ebf24[_0x4d7e42(0xe3)]++,_0x1ebf24['autoExpand']&&_0x1ebf24[_0x4d7e42(0x118)]['push'](_0x5b6b91);var _0x55a2c4,_0x5cbc7d,_0x10ebd6,_0x38ec49,_0x46d06f=[],_0x4ef003=[],_0x33c92e,_0xe8efc0=this[_0x4d7e42(0x182)](_0x5b6b91),_0x5b392f=_0xe8efc0===_0x4d7e42(0x130),_0x55d894=!0x1,_0x65caf4=_0xe8efc0===_0x4d7e42(0x13e),_0x512734=this['_isPrimitiveType'](_0xe8efc0),_0x3d6d36=this[_0x4d7e42(0x15d)](_0xe8efc0),_0x2d65b0=_0x512734||_0x3d6d36,_0x4b6f05={},_0x419e4c=0x0,_0x2bca20=!0x1,_0x454078,_0xed6526=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x1ebf24['depth']){if(_0x5b392f){if(_0x5cbc7d=_0x5b6b91['length'],_0x5cbc7d>_0x1ebf24[_0x4d7e42(0x11c)]){for(_0x10ebd6=0x0,_0x38ec49=_0x1ebf24[_0x4d7e42(0x11c)],_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003[_0x4d7e42(0x157)](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));_0x1f7b5d['cappedElements']=!0x0;}else{for(_0x10ebd6=0x0,_0x38ec49=_0x5cbc7d,_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));}_0x1ebf24[_0x4d7e42(0x9d)]+=_0x4ef003[_0x4d7e42(0x14f)];}if(!(_0xe8efc0==='null'||_0xe8efc0==='undefined')&&!_0x512734&&_0xe8efc0!==_0x4d7e42(0x106)&&_0xe8efc0!=='Buffer'&&_0xe8efc0!=='bigint'){var _0xfca776=_0x4f3c70['props']||_0x1ebf24[_0x4d7e42(0x180)];if(this['_isSet'](_0x5b6b91)?(_0x55a2c4=0x0,_0x5b6b91['forEach'](function(_0x1b3730){var _0x29b12d=_0x4d7e42;if(_0x419e4c++,_0x1ebf24['autoExpandPropertyCount']++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24[_0x29b12d(0xb5)]&&_0x1ebf24[_0x29b12d(0xc0)]&&_0x1ebf24[_0x29b12d(0x9d)]>_0x1ebf24[_0x29b12d(0x177)]){_0x2bca20=!0x0;return;}_0x4ef003['push'](_0xe363bc[_0x29b12d(0x17b)](_0x46d06f,_0x5b6b91,_0x29b12d(0xf6),_0x55a2c4++,_0x1ebf24,function(_0x383398){return function(){return _0x383398;};}(_0x1b3730)));})):this[_0x4d7e42(0x186)](_0x5b6b91)&&_0x5b6b91['forEach'](function(_0x4cd1d9,_0x42ee6b){var _0x3c460e=_0x4d7e42;if(_0x419e4c++,_0x1ebf24[_0x3c460e(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x3c460e(0xc0)]&&_0x1ebf24[_0x3c460e(0x9d)]>_0x1ebf24['autoExpandLimit']){_0x2bca20=!0x0;return;}var _0x2a4101=_0x42ee6b[_0x3c460e(0xa5)]();_0x2a4101[_0x3c460e(0x14f)]>0x64&&(_0x2a4101=_0x2a4101[_0x3c460e(0x146)](0x0,0x64)+_0x3c460e(0x105)),_0x4ef003[_0x3c460e(0x157)](_0xe363bc['_addProperty'](_0x46d06f,_0x5b6b91,_0x3c460e(0x170),_0x2a4101,_0x1ebf24,function(_0x1c45bc){return function(){return _0x1c45bc;};}(_0x4cd1d9)));}),!_0x55d894){try{for(_0x33c92e in _0x5b6b91)if(!(_0x5b392f&&_0xed6526['test'](_0x33c92e))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPropertyCount']>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x150)](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}catch{}if(_0x4b6f05[_0x4d7e42(0x18f)]=!0x0,_0x65caf4&&(_0x4b6f05[_0x4d7e42(0x145)]=!0x0),!_0x2bca20){var _0x469d20=[][_0x4d7e42(0x166)](this[_0x4d7e42(0x15c)](_0x5b6b91))[_0x4d7e42(0x166)](this[_0x4d7e42(0xcf)](_0x5b6b91));for(_0x55a2c4=0x0,_0x5cbc7d=_0x469d20[_0x4d7e42(0x14f)];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)if(_0x33c92e=_0x469d20[_0x55a2c4],!(_0x5b392f&&_0xed6526['test'](_0x33c92e[_0x4d7e42(0xa5)]()))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)&&!_0x4b6f05[_0x4d7e42(0xc7)+_0x33c92e[_0x4d7e42(0xa5)]()]){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24[_0x4d7e42(0xb5)]&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24[_0x4d7e42(0x9d)]>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc['_addObjectProperty'](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}}}}if(_0x1f7b5d['type']=_0xe8efc0,_0x2d65b0?(_0x1f7b5d['value']=_0x5b6b91[_0x4d7e42(0xbf)](),this['_capIfString'](_0xe8efc0,_0x1f7b5d,_0x1ebf24,_0x4f3c70)):_0xe8efc0===_0x4d7e42(0x11f)?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x14b)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='bigint'?_0x1f7b5d[_0x4d7e42(0xce)]=_0x5b6b91[_0x4d7e42(0xa5)]():_0xe8efc0==='RegExp'?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x144)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='symbol'&&this[_0x4d7e42(0x128)]?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x128)][_0x4d7e42(0x119)]['toString']['call'](_0x5b6b91):!_0x1ebf24[_0x4d7e42(0xf0)]&&!(_0xe8efc0===_0x4d7e42(0x187)||_0xe8efc0==='undefined')&&(delete _0x1f7b5d[_0x4d7e42(0xce)],_0x1f7b5d[_0x4d7e42(0xf3)]=!0x0),_0x2bca20&&(_0x1f7b5d['cappedProps']=!0x0),_0x454078=_0x1ebf24['node']['current'],_0x1ebf24[_0x4d7e42(0xc8)]['current']=_0x1f7b5d,this[_0x4d7e42(0xa8)](_0x1f7b5d,_0x1ebf24),_0x4ef003[_0x4d7e42(0x14f)]){for(_0x55a2c4=0x0,_0x5cbc7d=_0x4ef003['length'];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)_0x4ef003[_0x55a2c4](_0x55a2c4);}_0x46d06f['length']&&(_0x1f7b5d[_0x4d7e42(0x180)]=_0x46d06f);}catch(_0x54504a){_0x16ce5f(_0x54504a,_0x1f7b5d,_0x1ebf24);}this[_0x4d7e42(0x10f)](_0x5b6b91,_0x1f7b5d),this[_0x4d7e42(0xe4)](_0x1f7b5d,_0x1ebf24),_0x1ebf24[_0x4d7e42(0xc8)][_0x4d7e42(0x16b)]=_0x454078,_0x1ebf24['level']--,_0x1ebf24[_0x4d7e42(0xc0)]=_0x290e3b,_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPreviousObjects']['pop']();}finally{_0x1533a9&&(_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)]=_0x1533a9);}return _0x1f7b5d;}[_0x40c820(0xcf)](_0xd7ad14){var _0x474a44=_0x40c820;return Object[_0x474a44(0x158)]?Object[_0x474a44(0x158)](_0xd7ad14):[];}[_0x40c820(0xde)](_0x5b06ac){var _0x292c99=_0x40c820;return!!(_0x5b06ac&&_0x3830e6[_0x292c99(0xf6)]&&this[_0x292c99(0x156)](_0x5b06ac)==='[object\\\\x20Set]'&&_0x5b06ac[_0x292c99(0x178)]);}['_blacklistedProperty'](_0x10628d,_0x15c227,_0x5a4f15){var _0x152ffd=_0x40c820;return _0x5a4f15[_0x152ffd(0xa4)]?typeof _0x10628d[_0x15c227]==_0x152ffd(0x13e):!0x1;}[_0x40c820(0x182)](_0x13718c){var _0x2c19d1=_0x40c820,_0x225ae1='';return _0x225ae1=typeof _0x13718c,_0x225ae1==='object'?this[_0x2c19d1(0x156)](_0x13718c)==='[object\\\\x20Array]'?_0x225ae1=_0x2c19d1(0x130):this['_objectToString'](_0x13718c)===_0x2c19d1(0xc1)?_0x225ae1=_0x2c19d1(0x11f):this[_0x2c19d1(0x156)](_0x13718c)===_0x2c19d1(0xbe)?_0x225ae1=_0x2c19d1(0xf9):_0x13718c===null?_0x225ae1=_0x2c19d1(0x187):_0x13718c[_0x2c19d1(0xf7)]&&(_0x225ae1=_0x13718c['constructor'][_0x2c19d1(0xb7)]||_0x225ae1):_0x225ae1===_0x2c19d1(0x142)&&this[_0x2c19d1(0x123)]&&_0x13718c instanceof this['_HTMLAllCollection']&&(_0x225ae1=_0x2c19d1(0x15b)),_0x225ae1;}[_0x40c820(0x156)](_0x37617c){var _0xdf3907=_0x40c820;return Object[_0xdf3907(0x119)]['toString'][_0xdf3907(0xda)](_0x37617c);}[_0x40c820(0xae)](_0x26b95b){var _0x3b9373=_0x40c820;return _0x26b95b===_0x3b9373(0x14d)||_0x26b95b===_0x3b9373(0x101)||_0x26b95b===_0x3b9373(0x12b);}['_isPrimitiveWrapperType'](_0x150515){var _0x2539cd=_0x40c820;return _0x150515===_0x2539cd(0xea)||_0x150515==='String'||_0x150515===_0x2539cd(0xfd);}['_addProperty'](_0x1a647e,_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb){var _0x3c4649=this;return function(_0x5c2af7){var _0x865286=_0x33f3,_0x5ceb03=_0x985088[_0x865286(0xc8)][_0x865286(0x16b)],_0x14ad91=_0x985088[_0x865286(0xc8)]['index'],_0x10beb0=_0x985088[_0x865286(0xc8)][_0x865286(0x9b)];_0x985088['node'][_0x865286(0x9b)]=_0x5ceb03,_0x985088['node'][_0x865286(0xa6)]=typeof _0x551a3a==_0x865286(0x12b)?_0x551a3a:_0x5c2af7,_0x1a647e[_0x865286(0x157)](_0x3c4649[_0x865286(0x17d)](_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb)),_0x985088[_0x865286(0xc8)][_0x865286(0x9b)]=_0x10beb0,_0x985088['node']['index']=_0x14ad91;};}[_0x40c820(0x150)](_0x3e6c99,_0x96cdeb,_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b){var _0x102764=_0x40c820,_0xbcca65=this;return _0x96cdeb[_0x102764(0xc7)+_0x5e1ed6[_0x102764(0xa5)]()]=!0x0,function(_0x4f07e2){var _0x11a9a9=_0x102764,_0x41481f=_0x6bb8c1['node'][_0x11a9a9(0x16b)],_0x45aeeb=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)],_0x44cce6=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0x9b)];_0x6bb8c1['node']['parent']=_0x41481f,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x4f07e2,_0x3e6c99['push'](_0xbcca65[_0x11a9a9(0x17d)](_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b)),_0x6bb8c1['node']['parent']=_0x44cce6,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x45aeeb;};}['_property'](_0x5a954c,_0x11a196,_0x34292c,_0x53d319,_0x300135){var _0x3f13ca=_0x40c820,_0x350c39=this;_0x300135||(_0x300135=function(_0x26467a,_0x467a10){return _0x26467a[_0x467a10];});var _0x112124=_0x34292c[_0x3f13ca(0xa5)](),_0x42837e=_0x53d319[_0x3f13ca(0xc6)]||{},_0x265c6d=_0x53d319['depth'],_0x31debf=_0x53d319[_0x3f13ca(0xb5)];try{var _0x3c8586=this[_0x3f13ca(0x186)](_0x5a954c),_0x5579d4=_0x112124;_0x3c8586&&_0x5579d4[0x0]==='\\\\x27'&&(_0x5579d4=_0x5579d4[_0x3f13ca(0x147)](0x1,_0x5579d4[_0x3f13ca(0x14f)]-0x2));var _0x46f777=_0x53d319[_0x3f13ca(0xc6)]=_0x42837e[_0x3f13ca(0xc7)+_0x5579d4];_0x46f777&&(_0x53d319[_0x3f13ca(0xf0)]=_0x53d319[_0x3f13ca(0xf0)]+0x1),_0x53d319[_0x3f13ca(0xb5)]=!!_0x46f777;var _0x14534f=typeof _0x34292c=='symbol',_0x124dd3={'name':_0x14534f||_0x3c8586?_0x112124:this['_propertyName'](_0x112124)};if(_0x14534f&&(_0x124dd3[_0x3f13ca(0xd4)]=!0x0),!(_0x11a196===_0x3f13ca(0x130)||_0x11a196===_0x3f13ca(0x13b))){var _0x4ea27f=this[_0x3f13ca(0x127)](_0x5a954c,_0x34292c);if(_0x4ea27f&&(_0x4ea27f[_0x3f13ca(0x18c)]&&(_0x124dd3['setter']=!0x0),_0x4ea27f[_0x3f13ca(0x108)]&&!_0x46f777&&!_0x53d319['resolveGetters']))return _0x124dd3[_0x3f13ca(0x172)]=!0x0,this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x3214f9;try{_0x3214f9=_0x300135(_0x5a954c,_0x34292c);}catch(_0x3ef7eb){return _0x124dd3={'name':_0x112124,'type':_0x3f13ca(0xca),'error':_0x3ef7eb['message']},this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x4c3356=this[_0x3f13ca(0x182)](_0x3214f9),_0x18e3f8=this[_0x3f13ca(0xae)](_0x4c3356);if(_0x124dd3[_0x3f13ca(0x10b)]=_0x4c3356,_0x18e3f8)this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x7d7701=_0x3f13ca;_0x124dd3['value']=_0x3214f9[_0x7d7701(0xbf)](),!_0x46f777&&_0x350c39[_0x7d7701(0xe9)](_0x4c3356,_0x124dd3,_0x53d319,{});});else{var _0x275cea=_0x53d319[_0x3f13ca(0xc0)]&&_0x53d319['level']<_0x53d319['autoExpandMaxDepth']&&_0x53d319[_0x3f13ca(0x118)][_0x3f13ca(0x129)](_0x3214f9)<0x0&&_0x4c3356!==_0x3f13ca(0x13e)&&_0x53d319['autoExpandPropertyCount']<_0x53d319['autoExpandLimit'];_0x275cea||_0x53d319[_0x3f13ca(0xe3)]<_0x265c6d||_0x46f777?(this['serialize'](_0x124dd3,_0x3214f9,_0x53d319,_0x46f777||{}),this[_0x3f13ca(0x10f)](_0x3214f9,_0x124dd3)):this['_processTreeNodeResult'](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x2b8765=_0x3f13ca;_0x4c3356===_0x2b8765(0x187)||_0x4c3356===_0x2b8765(0x142)||(delete _0x124dd3[_0x2b8765(0xce)],_0x124dd3[_0x2b8765(0xf3)]=!0x0);});}return _0x124dd3;}finally{_0x53d319['expressionsToEvaluate']=_0x42837e,_0x53d319[_0x3f13ca(0xf0)]=_0x265c6d,_0x53d319['isExpressionToEvaluate']=_0x31debf;}}[_0x40c820(0xe9)](_0x3711dd,_0x3273d6,_0x30712a,_0x2bfd2c){var _0x22c791=_0x40c820,_0x3ed3d6=_0x2bfd2c['strLength']||_0x30712a[_0x22c791(0x126)];if((_0x3711dd==='string'||_0x3711dd==='String')&&_0x3273d6[_0x22c791(0xce)]){let _0xffdbb0=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x14f)];_0x30712a['allStrLength']+=_0xffdbb0,_0x30712a[_0x22c791(0xa2)]>_0x30712a['totalStrLength']?(_0x3273d6[_0x22c791(0xf3)]='',delete _0x3273d6[_0x22c791(0xce)]):_0xffdbb0>_0x3ed3d6&&(_0x3273d6[_0x22c791(0xf3)]=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x147)](0x0,_0x3ed3d6),delete _0x3273d6[_0x22c791(0xce)]);}}['_isMap'](_0x261c40){var _0xd2ec63=_0x40c820;return!!(_0x261c40&&_0x3830e6['Map']&&this[_0xd2ec63(0x156)](_0x261c40)===_0xd2ec63(0x114)&&_0x261c40[_0xd2ec63(0x178)]);}[_0x40c820(0xd5)](_0x24e250){var _0x49be73=_0x40c820;if(_0x24e250[_0x49be73(0x132)](/^\\\\d+$/))return _0x24e250;var _0x1d38d;try{_0x1d38d=JSON['stringify'](''+_0x24e250);}catch{_0x1d38d='\\\\x22'+this['_objectToString'](_0x24e250)+'\\\\x22';}return _0x1d38d['match'](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x1d38d=_0x1d38d[_0x49be73(0x147)](0x1,_0x1d38d[_0x49be73(0x14f)]-0x2):_0x1d38d=_0x1d38d[_0x49be73(0x16d)](/'/g,'\\\\x5c\\\\x27')[_0x49be73(0x16d)](/\\\\\\\\\\\"/g,'\\\\x22')[_0x49be73(0x16d)](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x1d38d;}[_0x40c820(0x133)](_0x1b7571,_0x8f439b,_0x2c2981,_0x188fd5){var _0xf97f10=_0x40c820;this[_0xf97f10(0xa8)](_0x1b7571,_0x8f439b),_0x188fd5&&_0x188fd5(),this[_0xf97f10(0x10f)](_0x2c2981,_0x1b7571),this['_treeNodePropertiesAfterFullValue'](_0x1b7571,_0x8f439b);}[_0x40c820(0xa8)](_0x231eb3,_0x5ea482){var _0x31f690=_0x40c820;this['_setNodeId'](_0x231eb3,_0x5ea482),this[_0x31f690(0xd1)](_0x231eb3,_0x5ea482),this[_0x31f690(0x111)](_0x231eb3,_0x5ea482),this[_0x31f690(0x125)](_0x231eb3,_0x5ea482);}[_0x40c820(0xcb)](_0x10d2e1,_0x3c8083){}[_0x40c820(0xd1)](_0x53d949,_0x188c67){}[_0x40c820(0x163)](_0x3f8259,_0x16e80a){}[_0x40c820(0x122)](_0x25a3a3){return _0x25a3a3===this['_undefined'];}[_0x40c820(0xe4)](_0x44987b,_0x4ed592){var _0x37fb3e=_0x40c820;this[_0x37fb3e(0x163)](_0x44987b,_0x4ed592),this[_0x37fb3e(0xeb)](_0x44987b),_0x4ed592[_0x37fb3e(0xfb)]&&this[_0x37fb3e(0x12d)](_0x44987b),this['_addFunctionsNode'](_0x44987b,_0x4ed592),this[_0x37fb3e(0x17e)](_0x44987b,_0x4ed592),this[_0x37fb3e(0x16c)](_0x44987b);}['_additionalMetadata'](_0x1c2784,_0x4c1dde){var _0x4f21c1=_0x40c820;try{_0x1c2784&&typeof _0x1c2784[_0x4f21c1(0x14f)]==_0x4f21c1(0x12b)&&(_0x4c1dde[_0x4f21c1(0x14f)]=_0x1c2784[_0x4f21c1(0x14f)]);}catch{}if(_0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x12b)||_0x4c1dde[_0x4f21c1(0x10b)]==='Number'){if(isNaN(_0x4c1dde[_0x4f21c1(0xce)]))_0x4c1dde[_0x4f21c1(0x18d)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];else switch(_0x4c1dde[_0x4f21c1(0xce)]){case Number[_0x4f21c1(0xfa)]:_0x4c1dde[_0x4f21c1(0x102)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case Number['NEGATIVE_INFINITY']:_0x4c1dde[_0x4f21c1(0x189)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case 0x0:this['_isNegativeZero'](_0x4c1dde[_0x4f21c1(0xce)])&&(_0x4c1dde[_0x4f21c1(0xd0)]=!0x0);break;}}else _0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x13e)&&typeof _0x1c2784['name']==_0x4f21c1(0x101)&&_0x1c2784[_0x4f21c1(0xb7)]&&_0x4c1dde['name']&&_0x1c2784[_0x4f21c1(0xb7)]!==_0x4c1dde[_0x4f21c1(0xb7)]&&(_0x4c1dde[_0x4f21c1(0xad)]=_0x1c2784[_0x4f21c1(0xb7)]);}['_isNegativeZero'](_0x289882){var _0x1b66c9=_0x40c820;return 0x1/_0x289882===Number[_0x1b66c9(0x181)];}['_sortProps'](_0x3992ee){var _0x3db550=_0x40c820;!_0x3992ee[_0x3db550(0x180)]||!_0x3992ee[_0x3db550(0x180)][_0x3db550(0x14f)]||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0x130)||_0x3992ee[_0x3db550(0x10b)]==='Map'||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0xf6)||_0x3992ee[_0x3db550(0x180)][_0x3db550(0x175)](function(_0x57a739,_0x31b40b){var _0x5dcaae=_0x3db550,_0x3d0d50=_0x57a739[_0x5dcaae(0xb7)][_0x5dcaae(0x10e)](),_0xd6d4fc=_0x31b40b[_0x5dcaae(0xb7)]['toLowerCase']();return _0x3d0d50<_0xd6d4fc?-0x1:_0x3d0d50>_0xd6d4fc?0x1:0x0;});}[_0x40c820(0xe8)](_0x12537a,_0x57f3dc){var _0x2884a4=_0x40c820;if(!(_0x57f3dc[_0x2884a4(0xa4)]||!_0x12537a['props']||!_0x12537a[_0x2884a4(0x180)][_0x2884a4(0x14f)])){for(var _0x53c006=[],_0x347d6e=[],_0x52e85a=0x0,_0x273297=_0x12537a['props']['length'];_0x52e85a<_0x273297;_0x52e85a++){var _0x1ee5b3=_0x12537a[_0x2884a4(0x180)][_0x52e85a];_0x1ee5b3[_0x2884a4(0x10b)]==='function'?_0x53c006[_0x2884a4(0x157)](_0x1ee5b3):_0x347d6e['push'](_0x1ee5b3);}if(!(!_0x347d6e[_0x2884a4(0x14f)]||_0x53c006[_0x2884a4(0x14f)]<=0x1)){_0x12537a[_0x2884a4(0x180)]=_0x347d6e;var _0x15f515={'functionsNode':!0x0,'props':_0x53c006};this[_0x2884a4(0xcb)](_0x15f515,_0x57f3dc),this[_0x2884a4(0x163)](_0x15f515,_0x57f3dc),this[_0x2884a4(0xeb)](_0x15f515),this[_0x2884a4(0x125)](_0x15f515,_0x57f3dc),_0x15f515['id']+='\\\\x20f',_0x12537a['props']['unshift'](_0x15f515);}}}['_addLoadNode'](_0x5bea6e,_0x14049e){}[_0x40c820(0xeb)](_0x199084){}[_0x40c820(0x98)](_0xf50c17){var _0x35cb98=_0x40c820;return Array['isArray'](_0xf50c17)||typeof _0xf50c17==_0x35cb98(0x117)&&this['_objectToString'](_0xf50c17)===_0x35cb98(0xdc);}[_0x40c820(0x125)](_0x3ea390,_0x54c209){}[_0x40c820(0x16c)](_0x25cdb9){var _0x1aa0a5=_0x40c820;delete _0x25cdb9[_0x1aa0a5(0x136)],delete _0x25cdb9[_0x1aa0a5(0x143)],delete _0x25cdb9[_0x1aa0a5(0x138)];}['_setNodeExpressionPath'](_0x17f351,_0x40c77e){}}let _0x459cb0=new _0x1e3ba1(),_0x218fe5={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x11fc4c={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x482c8e(_0x50675f,_0x2f7559,_0x19c481,_0x2c8a95,_0x245f16,_0x24484e){var _0x4c67af=_0x40c820;let _0x3ce9b8,_0x1af844;try{_0x1af844=_0x1169a5(),_0x3ce9b8=_0x2f8209[_0x2f7559],!_0x3ce9b8||_0x1af844-_0x3ce9b8['ts']>0x1f4&&_0x3ce9b8[_0x4c67af(0xaa)]&&_0x3ce9b8['time']/_0x3ce9b8[_0x4c67af(0xaa)]<0x64?(_0x2f8209[_0x2f7559]=_0x3ce9b8={'count':0x0,'time':0x0,'ts':_0x1af844},_0x2f8209[_0x4c67af(0xc5)]={}):_0x1af844-_0x2f8209[_0x4c67af(0xc5)]['ts']>0x32&&_0x2f8209['hits']['count']&&_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x161)]/_0x2f8209[_0x4c67af(0xc5)]['count']<0x64&&(_0x2f8209[_0x4c67af(0xc5)]={});let _0x157126=[],_0x1464d6=_0x3ce9b8[_0x4c67af(0x112)]||_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]?_0x11fc4c:_0x218fe5,_0x553948=_0x2ff7b4=>{var _0x3a1316=_0x4c67af;let _0x5d676c={};return _0x5d676c[_0x3a1316(0x180)]=_0x2ff7b4[_0x3a1316(0x180)],_0x5d676c['elements']=_0x2ff7b4['elements'],_0x5d676c[_0x3a1316(0x126)]=_0x2ff7b4[_0x3a1316(0x126)],_0x5d676c['totalStrLength']=_0x2ff7b4['totalStrLength'],_0x5d676c['autoExpandLimit']=_0x2ff7b4[_0x3a1316(0x177)],_0x5d676c[_0x3a1316(0x115)]=_0x2ff7b4['autoExpandMaxDepth'],_0x5d676c['sortProps']=!0x1,_0x5d676c['noFunctions']=!_0x45a6b5,_0x5d676c['depth']=0x1,_0x5d676c[_0x3a1316(0xe3)]=0x0,_0x5d676c['expId']='root_exp_id',_0x5d676c[_0x3a1316(0x140)]=_0x3a1316(0x151),_0x5d676c['autoExpand']=!0x0,_0x5d676c[_0x3a1316(0x118)]=[],_0x5d676c[_0x3a1316(0x9d)]=0x0,_0x5d676c[_0x3a1316(0x168)]=!0x0,_0x5d676c['allStrLength']=0x0,_0x5d676c[_0x3a1316(0xc8)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0x5d676c;};for(var _0x33de8f=0x0;_0x33de8f<_0x245f16[_0x4c67af(0x14f)];_0x33de8f++)_0x157126[_0x4c67af(0x157)](_0x459cb0[_0x4c67af(0x14a)]({'timeNode':_0x50675f===_0x4c67af(0x161)||void 0x0},_0x245f16[_0x33de8f],_0x553948(_0x1464d6),{}));if(_0x50675f==='trace'||_0x50675f===_0x4c67af(0x184)){let _0x1d9735=Error[_0x4c67af(0xd7)];try{Error[_0x4c67af(0xd7)]=0x1/0x0,_0x157126[_0x4c67af(0x157)](_0x459cb0['serialize']({'stackNode':!0x0},new Error()['stack'],_0x553948(_0x1464d6),{'strLength':0x1/0x0}));}finally{Error[_0x4c67af(0xd7)]=_0x1d9735;}}return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':_0x157126,'id':_0x2f7559,'context':_0x24484e}]};}catch(_0x2d5a77){return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':[{'type':_0x4c67af(0xca),'error':_0x2d5a77&&_0x2d5a77[_0x4c67af(0xb4)]}],'id':_0x2f7559,'context':_0x24484e}]};}finally{try{if(_0x3ce9b8&&_0x1af844){let _0xff386f=_0x1169a5();_0x3ce9b8[_0x4c67af(0xaa)]++,_0x3ce9b8['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x3ce9b8['ts']=_0xff386f,_0x2f8209[_0x4c67af(0xc5)]['count']++,_0x2f8209[_0x4c67af(0xc5)]['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x2f8209['hits']['ts']=_0xff386f,(_0x3ce9b8[_0x4c67af(0xaa)]>0x32||_0x3ce9b8[_0x4c67af(0x161)]>0x64)&&(_0x3ce9b8['reduceLimits']=!0x0),(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0xaa)]>0x3e8||_0x2f8209[_0x4c67af(0xc5)]['time']>0x12c)&&(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]=!0x0);}}catch{}}}return _0x482c8e;}((_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x296e29,_0x567fe9,_0x14adfa,_0x6b3989,_0x593945,_0x42f609)=>{var _0x543ef9=_0x418f23;if(_0x12a02f[_0x543ef9(0xdb)])return _0x12a02f[_0x543ef9(0xdb)];if(!X(_0x12a02f,_0x14adfa,_0x1164b7))return _0x12a02f[_0x543ef9(0xdb)]={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x12a02f[_0x543ef9(0xdb)];let _0x5a7d78=B(_0x12a02f),_0x236b4f=_0x5a7d78[_0x543ef9(0xd3)],_0x57b9d9=_0x5a7d78['timeStamp'],_0x163b61=_0x5a7d78[_0x543ef9(0x11b)],_0x384cd9={'hits':{},'ts':{}},_0x9c7997=J(_0x12a02f,_0x6b3989,_0x384cd9,_0x296e29),_0x3ffb36=_0xa1ec34=>{_0x384cd9['ts'][_0xa1ec34]=_0x57b9d9();},_0x4ce4d2=(_0x173258,_0x2e0c6d)=>{var _0x3b6e53=_0x543ef9;let _0x2b64c1=_0x384cd9['ts'][_0x2e0c6d];if(delete _0x384cd9['ts'][_0x2e0c6d],_0x2b64c1){let _0x557981=_0x236b4f(_0x2b64c1,_0x57b9d9());_0x593a90(_0x9c7997(_0x3b6e53(0x161),_0x173258,_0x163b61(),_0x50e896,[_0x557981],_0x2e0c6d));}},_0x46c5f3=_0x1f105e=>{var _0x1152c8=_0x543ef9,_0x4a2783;return _0x1164b7===_0x1152c8(0x110)&&_0x12a02f[_0x1152c8(0x152)]&&((_0x4a2783=_0x1f105e==null?void 0x0:_0x1f105e[_0x1152c8(0xab)])==null?void 0x0:_0x4a2783[_0x1152c8(0x14f)])&&(_0x1f105e[_0x1152c8(0xab)][0x0][_0x1152c8(0x152)]=_0x12a02f[_0x1152c8(0x152)]),_0x1f105e;};_0x12a02f[_0x543ef9(0xdb)]={'consoleLog':(_0x204f4b,_0x3e1804)=>{var _0x309615=_0x543ef9;_0x12a02f['console'][_0x309615(0xff)][_0x309615(0xb7)]!==_0x309615(0x134)&&_0x593a90(_0x9c7997(_0x309615(0xff),_0x204f4b,_0x163b61(),_0x50e896,_0x3e1804));},'consoleTrace':(_0x267a3f,_0x51c339)=>{var _0x4c4943=_0x543ef9,_0x33fd8c,_0x24e61f;_0x12a02f[_0x4c4943(0xbc)][_0x4c4943(0xff)]['name']!==_0x4c4943(0xbd)&&((_0x24e61f=(_0x33fd8c=_0x12a02f[_0x4c4943(0x104)])==null?void 0x0:_0x33fd8c['versions'])!=null&&_0x24e61f[_0x4c4943(0xc8)]&&(_0x12a02f[_0x4c4943(0x165)]=!0x0),_0x593a90(_0x46c5f3(_0x9c7997(_0x4c4943(0xc4),_0x267a3f,_0x163b61(),_0x50e896,_0x51c339))));},'consoleError':(_0xf7f1fc,_0x1384d7)=>{var _0x28d83c=_0x543ef9;_0x12a02f['_ninjaIgnoreNextError']=!0x0,_0x593a90(_0x46c5f3(_0x9c7997(_0x28d83c(0x184),_0xf7f1fc,_0x163b61(),_0x50e896,_0x1384d7)));},'consoleTime':_0x2ad865=>{_0x3ffb36(_0x2ad865);},'consoleTimeEnd':(_0x3c91cf,_0x308c8b)=>{_0x4ce4d2(_0x308c8b,_0x3c91cf);},'autoLog':(_0x4bbc9f,_0x3599a3)=>{var _0x598cfa=_0x543ef9;_0x593a90(_0x9c7997(_0x598cfa(0xff),_0x3599a3,_0x163b61(),_0x50e896,[_0x4bbc9f]));},'autoLogMany':(_0x158592,_0x29b77d)=>{var _0x425f64=_0x543ef9;_0x593a90(_0x9c7997(_0x425f64(0xff),_0x158592,_0x163b61(),_0x50e896,_0x29b77d));},'autoTrace':(_0x3f5f9d,_0xc378ab)=>{var _0x377a7d=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x377a7d(0xc4),_0xc378ab,_0x163b61(),_0x50e896,[_0x3f5f9d])));},'autoTraceMany':(_0x2c6f73,_0x35405b)=>{var _0x4f4e7f=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x4f4e7f(0xc4),_0x2c6f73,_0x163b61(),_0x50e896,_0x35405b)));},'autoTime':(_0x4915d6,_0xaaf0db,_0x4c1f1e)=>{_0x3ffb36(_0x4c1f1e);},'autoTimeEnd':(_0x397624,_0x436d57,_0x47b9b8)=>{_0x4ce4d2(_0x436d57,_0x47b9b8);},'coverage':_0x45a646=>{var _0x1e9860=_0x543ef9;_0x593a90({'method':_0x1e9860(0x13a),'version':_0x296e29,'args':[{'id':_0x45a646}]});}};let _0x593a90=H(_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x593945,_0x42f609),_0x50e896=_0x12a02f[_0x543ef9(0xd8)];return _0x12a02f[_0x543ef9(0xdb)];})(globalThis,'127.0.0.1',_0x418f23(0xec),_0x418f23(0x17a),_0x418f23(0x183),'1.0.0','1751470503293',_0x418f23(0x137),_0x418f23(0x18a),_0x418f23(0x10a),_0x418f23(0xb1));\");\n  } catch (e) {}\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_oo(\n/**@type{any}**/\ni) {\n  for (var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    v[_key - 1] = arguments[_key];\n  }\n\n  try {\n    oo_cm().consoleLog(i, v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_tr(\n/**@type{any}**/\ni) {\n  for (var _len2 = arguments.length, v = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    v[_key2 - 1] = arguments[_key2];\n  }\n\n  try {\n    oo_cm().consoleTrace(i, v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_tx(\n/**@type{any}**/\ni) {\n  for (var _len3 = arguments.length, v = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n    v[_key3 - 1] = arguments[_key3];\n  }\n\n  try {\n    oo_cm().consoleError(i, v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_ts(\n/**@type{any}**/\nv) {\n  try {\n    oo_cm().consoleTime(v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_te(\n/**@type{any}**/\nv,\n/**@type{any}**/\ni) {\n  try {\n    oo_cm().consoleTimeEnd(v, i);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/\n\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n\n$RefreshReg$(_c, \"NumberAlert\");\n$RefreshReg$(_c2, \"AlertRotation\");\n$RefreshReg$(_c3, \"AlertBreak\");\n$RefreshReg$(_c4, \"NestedCard\");\n$RefreshReg$(_c5, \"DerivedProductAnalysis\");\n$RefreshReg$(_c6, \"DerivedProductNestedContent\");\n$RefreshReg$(_c7, \"SubDerivedProducts\");\n$RefreshReg$(_c8, \"RotationDetail\");", "map": {"version": 3, "sources": ["D:/www/SWF/sian-web-frontend/src/views/logistic/reposition/others/RotationDetail.jsx"], "names": ["Box", "IconButton", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TextField", "<PERSON><PERSON><PERSON>", "Typography", "React", "useEffect", "useState", "useDispatch", "useSelector", "StoreIcon", "AddCircleIcon", "RemoveCircleIcon", "addToCart", "editToCart", "getRepositionDataByProduct", "openRotationModal", "removeFromCart", "setSelectedProduct", "updateFormDataItem", "updateFormSupplyDataItem", "ErrorIcon", "addDays", "parseDateToLocaleString", "Grid", "AutoModeIcon", "useLoading", "<PERSON><PERSON><PERSON><PERSON>", "SIANLink", "FOOD_VALUE", "RAW_MATERIAL", "RightAlignedNumber", "stickyColumn", "DisplayCurrency", "UNIT_EQUIVALENCE", "MenuItem", "NestedGrid", "findDerivedProductRecursively", "derivedProducts", "targetProductId", "Array", "isArray", "derived", "product_id", "length", "found", "findMainProductWithDerivedProduct", "data", "item", "<PERSON><PERSON><PERSON><PERSON>", "condition", "title", "AlertRotation", "rotationScale", "width", "height", "borderRadius", "backgroundColor", "border", "AlertBreak", "break_scale", "processAnalisys", "productData", "stores", "analisys", "listData", "map", "index", "pk", "store_id", "rowIndex", "quantity", "parseFloat", "unit_quantity_order", "toFixed", "unit_quantity", "unit_price", "default_unit_price", "provider", "for<PERSON>ach", "store", "existingItem", "find", "push", "notAvailable", "indicator_calculation_id", "store_name", "product_name", "measure_name", "equivalence_default", "measure_default", "provider_id", "provider_number", "warehouse_id", "expires", "vcto_alert", "rotation_scale", "rotation_value", "rotation_indicator", "rotation_color", "obsolete", "obsolete_indicator", "pareto_percentage_sale", "pareto_percentage_utility", "stock", "to_enter", "to_dispatch", "purchase_stock", "average_quantity", "average_diary", "inventory_days", "break_value", "min_stock", "reposition_stock", "sort", "a", "b", "getDerivedProducts", "globalIndex", "NestedCard", "children", "p", "DerivedProductAnalysis", "row", "columns", "productId", "state", "reposition", "storeData", "mainProductData", "derivedProduct", "derivedAnalysis", "textAlign", "color", "DerivedAnalysisGrid", "repositionDerivedProduct", "search", "download", "print", "viewColumns", "filter", "responsive", "fixedHeader", "selectableRows", "pagination", "toolbar", "setTableProps", "size", "sx", "fontSize", "fontWeight", "padding", "DerivedProductNestedContent", "derivedAnalysisColumns", "simplifiedDerivedProductColumns", "hasSubDerived", "display", "flexDirection", "gap", "py", "SubDerivedProducts", "analysisColumns", "rawSubDerivedProducts", "subDerivedProducts", "tableLayout", "props", "RotationDetail", "isFromProyection", "foodMode", "dispatch", "formData", "cart", "filters", "merchandiseFoodData", "formSupplyData", "isRowDataAvailable", "rowData", "getRowDataSafely", "repositionProduct", "result", "error", "renderSafeContent", "renderFunction", "console", "warn", "supplyAnalisys", "setRepositionProduct", "isAsync", "loading", "startLoading", "endLoading", "openModal", "setSelected", "reload", "mode", "then", "handleAddToCart", "handleRemoveFromCart", "handleEditCart", "updatedData", "QuantityInput", "tableMeta", "rowMetadata", "keyword", "cartRowData", "repositionProductData", "getQuantity", "undefined", "numberInput", "setNumberInput", "handleBlur", "newValue", "presentation", "allowDecimals", "fixedValue", "Math", "floor", "updateAction", "has<PERSON><PERSON><PERSON>", "handleChange", "target", "value", "autocomplete", "repositionRowData", "estimated", "equivalence", "key<PERSON>ther<PERSON><PERSON><PERSON>", "inputProps", "style", "step", "min", "inputMode", "pattern", "SelectProduct", "rowItem", "cartSelected", "sum_quantity", "quantity_oc", "quantity_ota", "handleClick", "justifyContent", "alignItems", "cursor", "name", "label", "options", "customBodyRender", "Date", "parseInt", "ceil", "_", "merchandiseFoodColumns", "supplyColumns", "recipeColumns", "setCellHeaderProps", "max<PERSON><PERSON><PERSON>", "setCellProps", "min<PERSON><PERSON><PERSON>", "whiteSpace", "wasteInfo", "waste_percentage_total", "percentage", "getColumns", "getCardWidth", "pt", "pl", "pr", "pb", "filterType", "fixedSelectColumn", "jumpToPage", "resizableColumns", "draggableColumns", "enabled", "serverSide", "selectableRowsOnClick", "confirmFilters", "rowHover", "oo_cm", "eval", "e", "oo_oo", "i", "v", "consoleLog", "oo_tr", "consoleTrace", "oo_tx", "consoleError", "oo_ts", "consoleTime", "oo_te", "consoleTimeEnd"], "mappings": ";;;;;AAAA,SACIA,GADJ,EAEIC,UAFJ,EAGIC,KAHJ,EAIIC,KAJJ,EAKIC,SALJ,EAMIC,SANJ,EAOIC,cAPJ,EAQIC,SARJ,EASIC,QATJ,EAUIC,SAVJ,EAWIC,OAXJ,EAYIC,UAZJ,QAaO,eAbP;AAcA,OAAOC,KAAP,IAAgBC,SAAhB,EAA2BC,QAA3B,QAA2C,OAA3C;AACA,SAASC,WAAT,EAAsBC,WAAtB,QAAyC,OAAzC;AACA,OAAOC,SAAP,MAAsB,2BAAtB;AACA,OAAOC,aAAP,MAA0B,+BAA1B;AACA,OAAOC,gBAAP,MAA6B,kCAA7B;AACA,SACIC,SADJ,EAEIC,UAFJ,EAGIC,0BAHJ,EAIIC,iBAJJ,EAKIC,cALJ,EAMIC,kBANJ,EAOIC,kBAPJ,EAQIC,wBARJ,QASO,oCATP;AAUA,OAAOC,SAAP,MAAsB,2BAAtB;AACA,SAASC,OAAT,EAAkBC,uBAAlB,QAAiD,aAAjD;AACA,OAAOC,IAAP,MAAiB,wBAAjB;AACA,OAAOC,YAAP,MAAyB,8BAAzB;AACA,OAAOC,UAAP,MAAuB,kBAAvB;AACA,SAASC,WAAT,QAA4B,8BAA5B;AACA,OAAOC,QAAP,MAAqB,4BAArB;AACA,SAASC,UAAT,EAAqBC,YAArB,QAAyC,mBAAzC;AACA,OAAOC,kBAAP,MAA+B,sCAA/B;AACA,SAASC,YAAT,QAA6B,wBAA7B;AACA,OAAOC,eAAP,MAA4B,sCAA5B;AACA,SAASC,gBAAT,QAAiC,qBAAjC;AACA,SAASC,QAAT,QAAyB,eAAzB;AACA,OAAOC,UAAP,MAAuB,8BAAvB;;;;AAEA,MAAMC,6BAA6B,GAAG,CAACC,eAAD,EAAkBC,eAAlB,KAAsC;AACxE,MAAI,CAACD,eAAD,IAAoB,CAACE,KAAK,CAACC,OAAN,CAAcH,eAAd,CAAzB,EAAyD,OAAO,IAAP;;AAEzD,OAAK,MAAMI,OAAX,IAAsBJ,eAAtB,EAAuC;AACnC,QAAII,OAAO,CAACC,UAAR,KAAuBJ,eAA3B,EAA4C;AACxC,aAAOG,OAAP;AACH;;AAED,QAAIA,OAAO,CAACJ,eAAR,IAA2BI,OAAO,CAACJ,eAAR,CAAwBM,MAAxB,GAAiC,CAAhE,EAAmE;AAC/D,YAAMC,KAAK,GAAGR,6BAA6B,CAACK,OAAO,CAACJ,eAAT,EAA0BC,eAA1B,CAA3C;AACA,UAAIM,KAAJ,EAAW,OAAOA,KAAP;AACd;AACJ;;AAED,SAAO,IAAP;AACH,CAfD;;AAiBA,MAAMC,iCAAiC,GAAG,CAACC,IAAD,EAAOR,eAAP,KAA2B;AACjE,MAAI,CAACQ,IAAD,IAAS,CAACP,KAAK,CAACC,OAAN,CAAcM,IAAd,CAAd,EAAmC,OAAO,IAAP;;AAEnC,OAAK,MAAMC,IAAX,IAAmBD,IAAnB,EAAyB;AACrB,UAAMF,KAAK,GAAGR,6BAA6B,CAACW,IAAI,CAACV,eAAN,EAAuBC,eAAvB,CAA3C;AACA,QAAIM,KAAJ,EAAW,OAAOG,IAAP;AACd;;AAED,SAAO,IAAP;AACH,CATD;;AAWA,MAAMC,WAAW,GAAG,QAAuC;AAAA,MAAtC;AAAEC,IAAAA,SAAS,GAAG,KAAd;AAAqBC,IAAAA,KAAK,GAAG;AAA7B,GAAsC;;AACvD,MAAI,CAACD,SAAL,EAAgB;AACZ,WAAO,IAAP;AACH;;AACD,sBACI,QAAC,OAAD;AAAS,IAAA,KAAK,EAAEC,KAAhB;AAAA,2BACI,QAAC,UAAD;AAAY,MAAA,KAAK,EAAC,OAAlB;AAAA,6BACI,QAAC,SAAD;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,UADJ;AAOH,CAXD;;KAAMF,W;;AAaN,MAAMG,aAAa,GAAG,SAAuB;AAAA,MAAtB;AAAEC,IAAAA;AAAF,GAAsB;;AACzC,UAAQA,aAAR;AACI,SAAK,IAAL;AACI,0BACI,QAAC,OAAD;AAAS,QAAA,KAAK,EAAC,oCAAf;AAAA,+BACI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAEC,YAAAA,KAAK,EAAE,EAAT;AAAaC,YAAAA,MAAM,EAAE,EAArB;AAAyBC,YAAAA,YAAY,EAAE,SAAvC;AAAkDC,YAAAA,eAAe,EAAE;AAAnE,WAAT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,cADJ;;AAKJ,SAAK,IAAL;AACI,0BACI,QAAC,OAAD;AAAS,QAAA,KAAK,EAAC,qCAAf;AAAA,+BACI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAEH,YAAAA,KAAK,EAAE,EAAT;AAAaC,YAAAA,MAAM,EAAE,EAArB;AAAyBC,YAAAA,YAAY,EAAE,SAAvC;AAAkDC,YAAAA,eAAe,EAAE;AAAnE,WAAT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,cADJ;;AAKJ,SAAK,IAAL;AACI,0BACI,QAAC,OAAD;AAAS,QAAA,KAAK,EAAC,oCAAf;AAAA,+BACI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAEH,YAAAA,KAAK,EAAE,EAAT;AAAaC,YAAAA,MAAM,EAAE,EAArB;AAAyBC,YAAAA,YAAY,EAAE,SAAvC;AAAkDC,YAAAA,eAAe,EAAE;AAAnE,WAAT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,cADJ;;AAKJ;AACI,0BACI,QAAC,OAAD;AAAS,QAAA,KAAK,EAAC,kCAAf;AAAA,+BACI,QAAC,GAAD;AACI,UAAA,EAAE,EAAE;AACAH,YAAAA,KAAK,EAAE,EADP;AAEAC,YAAAA,MAAM,EAAE,EAFR;AAGAC,YAAAA,YAAY,EAAE,SAHd;AAIAC,YAAAA,eAAe,EAAE,OAJjB;AAKAC,YAAAA,MAAM,EAAE;AALR,WADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,cADJ;AApBR;AAoCH,CArCD;;MAAMN,a;;AAuCN,MAAMO,UAAU,GAAG,SAA4B;AAAA,MAA3B;AAAEC,IAAAA,WAAW,GAAG;AAAhB,GAA2B;;AAC3C,UAAQA,WAAR;AACI,SAAK,IAAL;AACI,0BACI,QAAC,OAAD;AAAS,QAAA,KAAK,EAAC,oCAAf;AAAA,+BACI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAEN,YAAAA,KAAK,EAAE,EAAT;AAAaC,YAAAA,MAAM,EAAE,EAArB;AAAyBC,YAAAA,YAAY,EAAE,SAAvC;AAAkDC,YAAAA,eAAe,EAAE;AAAnE,WAAT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,cADJ;;AAKJ,SAAK,KAAL;AACI,0BACI,QAAC,OAAD;AAAS,QAAA,KAAK,EAAC,+BAAf;AAAA,+BACI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAEH,YAAAA,KAAK,EAAE,EAAT;AAAaC,YAAAA,MAAM,EAAE,EAArB;AAAyBC,YAAAA,YAAY,EAAE,SAAvC;AAAkDC,YAAAA,eAAe,EAAE;AAAnE,WAAT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,cADJ;;AAKJ,SAAK,KAAL;AACI,0BACI,QAAC,OAAD;AAAS,QAAA,KAAK,EAAC,iCAAf;AAAA,+BACI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAEH,YAAAA,KAAK,EAAE,EAAT;AAAaC,YAAAA,MAAM,EAAE,EAArB;AAAyBC,YAAAA,YAAY,EAAE,SAAvC;AAAkDC,YAAAA,eAAe,EAAE;AAAnE,WAAT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,cADJ;;AAKJ,SAAK,KAAL;AACI,0BACI,QAAC,OAAD;AAAS,QAAA,KAAK,EAAC,mCAAf;AAAA,+BACI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAEH,YAAAA,KAAK,EAAE,EAAT;AAAaC,YAAAA,MAAM,EAAE,EAArB;AAAyBC,YAAAA,YAAY,EAAE,SAAvC;AAAkDC,YAAAA,eAAe,EAAE;AAAnE,WAAT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,cADJ;;AAKJ,SAAK,IAAL;AACI,0BACI,QAAC,OAAD;AAAS,QAAA,KAAK,EAAC,mCAAf;AAAA,+BACI,QAAC,GAAD;AACI,UAAA,EAAE,EAAE;AACAH,YAAAA,KAAK,EAAE,EADP;AAEAC,YAAAA,MAAM,EAAE,EAFR;AAGAC,YAAAA,YAAY,EAAE,SAHd;AAIAC,YAAAA,eAAe,EAAE,OAJjB;AAKAC,YAAAA,MAAM,EAAE;AALR,WADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,cADJ;;AAeJ;AACI,0BACI,QAAC,OAAD;AAAS,QAAA,KAAK,EAAC,mCAAf;AAAA,+BACI,QAAC,GAAD;AACI,UAAA,EAAE,EAAE;AACAJ,YAAAA,KAAK,EAAE,EADP;AAEAC,YAAAA,MAAM,EAAE,EAFR;AAGAC,YAAAA,YAAY,EAAE,SAHd;AAIAC,YAAAA,eAAe,EAAE,OAJjB;AAKAC,YAAAA,MAAM,EAAE;AALR,WADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,cADJ;AA1CR;AA0DH,CA3DD;;MAAMC,U;;AA6DN,MAAME,eAAe,GAAG,CAACC,WAAD,EAAcC,MAAd,KAAyB;AAC7C,MAAI,CAACD,WAAD,IAAgB,CAACA,WAAW,CAACE,QAAjC,EAA2C,OAAO,IAAP;AAE3C,QAAMC,QAAQ,GAAGH,WAAW,CAACE,QAAZ,CAAqBE,GAArB,CAAyB,CAAClB,IAAD,EAAOmB,KAAP,KAAiB;AACvD,UAAMC,EAAE,GAAI,GAAEpB,IAAI,CAACL,UAAW,IAAGK,IAAI,CAACqB,QAAS,EAA/C;AACA,WAAO,EACH,GAAGrB,IADA;AAEHsB,MAAAA,QAAQ,EAAEH,KAFP;AAGHI,MAAAA,QAAQ,EAAEC,UAAU,CAACxB,IAAI,CAACyB,mBAAN,CAAV,CAAqCC,OAArC,CAA6C,CAA7C,CAHP;AAIHC,MAAAA,aAAa,EAAEH,UAAU,CAACxB,IAAI,CAACyB,mBAAN,CAJtB;AAKHG,MAAAA,UAAU,EAAEJ,UAAU,CAACxB,IAAI,CAAC4B,UAAN,CALnB;AAMHC,MAAAA,kBAAkB,EAAEL,UAAU,CAACxB,IAAI,CAAC4B,UAAN,CAN3B;AAOHE,MAAAA,QAAQ,EAAE9B,IAAI,CAAC8B,QAPZ;AAQHV,MAAAA;AARG,KAAP;AAUH,GAZgB,CAAjB;AAcAL,EAAAA,MAAM,CAACgB,OAAP,CAAgBC,KAAD,IAAW;AACtB,UAAMC,YAAY,GAAGhB,QAAQ,CAACiB,IAAT,CAAelC,IAAD,IAAUA,IAAI,CAACqB,QAAL,KAAkBW,KAAK,CAACX,QAAhD,CAArB;;AAEA,QAAI,CAACY,YAAL,EAAmB;AACfhB,MAAAA,QAAQ,CAACkB,IAAT,CAAc;AACVC,QAAAA,YAAY,EAAE,IADJ;AAEVC,QAAAA,wBAAwB,EAAE,CAFhB;AAGVhB,QAAAA,QAAQ,EAAEW,KAAK,CAACX,QAHN;AAIViB,QAAAA,UAAU,EAAEN,KAAK,CAACM,UAJR;AAKV3C,QAAAA,UAAU,EAAEmB,WAAW,CAACnB,UALd;AAMV4C,QAAAA,YAAY,EAAEzB,WAAW,CAACyB,YANhB;AAOVC,QAAAA,YAAY,EAAE1B,WAAW,CAAC0B,YAPhB;AAQVC,QAAAA,mBAAmB,EAAE3B,WAAW,CAAC2B,mBARvB;AASVC,QAAAA,eAAe,EAAE5B,WAAW,CAAC4B,eATnB;AAUVC,QAAAA,WAAW,EAAE7B,WAAW,CAAC6B,WAVf;AAWVC,QAAAA,eAAe,EAAE9B,WAAW,CAAC8B,eAXnB;AAYVd,QAAAA,QAAQ,EAAEhB,WAAW,CAACgB,QAZZ;AAaVe,QAAAA,YAAY,EAAEb,KAAK,CAACa,YAbV;AAcVjB,QAAAA,UAAU,EAAEd,WAAW,CAACc,UAdd;AAeVkB,QAAAA,OAAO,EAAE,CAfC;AAgBVC,QAAAA,UAAU,EAAE,GAhBF;AAiBVC,QAAAA,cAAc,EAAE,GAjBN;AAkBVC,QAAAA,cAAc,EAAE,CAlBN;AAmBVC,QAAAA,kBAAkB,EAAE,OAnBV;AAoBVC,QAAAA,cAAc,EAAE,EApBN;AAqBVC,QAAAA,QAAQ,EAAE,CArBA;AAsBVC,QAAAA,kBAAkB,EAAE,GAtBV;AAuBVC,QAAAA,sBAAsB,EAAE,KAvBd;AAwBVC,QAAAA,yBAAyB,EAAE,KAxBjB;AAyBVC,QAAAA,KAAK,EAAE,MAzBG;AA0BVC,QAAAA,QAAQ,EAAE,MA1BA;AA2BVC,QAAAA,WAAW,EAAE,MA3BH;AA4BVC,QAAAA,cAAc,EAAE,MA5BN;AA6BVC,QAAAA,gBAAgB,EAAE,MA7BR;AA8BVC,QAAAA,aAAa,EAAE,QA9BL;AA+BVC,QAAAA,cAAc,EAAE,CA/BN;AAgCVC,QAAAA,WAAW,EAAE,CAhCH;AAiCVnD,QAAAA,WAAW,EAAE,GAjCH;AAkCVoD,QAAAA,SAAS,EAAE,CAlCD;AAmCVC,QAAAA,gBAAgB,EAAE,CAnCR;AAoCVxC,QAAAA,mBAAmB,EAAE,CApCX;AAqCVH,QAAAA,QAAQ,EAAE,CArCA;AAsCVC,QAAAA,QAAQ,EAAE,GAtCA;AAuCVI,QAAAA,aAAa,EAAE,CAvCL;AAwCVE,QAAAA,kBAAkB,EAAEf,WAAW,CAACe,kBAxCtB;AAyCVT,QAAAA,EAAE,EAAG,GAAEN,WAAW,CAACnB,UAAW,IAAGqC,KAAK,CAACM,UAAW;AAzCxC,OAAd;AA2CH;AACJ,GAhDD;AAkDArB,EAAAA,QAAQ,CAACiD,IAAT,CAAc,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACtB,YAAF,GAAiBuB,CAAC,CAACvB,YAA3C;AAEA,SAAO5B,QAAP;AACH,CAtED;;AAwEA,MAAMoD,kBAAkB,GAAIvD,WAAD,IAAiB;AACxC,MAAI,CAACA,WAAD,IAAgB,CAACA,WAAW,CAACxB,eAAjC,EAAkD,OAAO,EAAP;AAElD,SAAOwB,WAAW,CAACxB,eAAZ,CAA4B4B,GAA5B,CAAgC,CAAClB,IAAD,EAAOmB,KAAP,MAAkB,EACrD,GAAGnB,IADkD;AAErDoB,IAAAA,EAAE,EAAEpB,IAAI,CAACL,UAF4C;AAGrD2E,IAAAA,WAAW,EAAEnD;AAHwC,GAAlB,CAAhC,CAAP;AAKH,CARD;;AAUA,MAAMoD,UAAU,GAAG;AAAA,MAAC;AAAEC,IAAAA,QAAF;AAAYlE,IAAAA,KAAK,GAAG;AAApB,GAAD;AAAA,sBACf,QAAC,GAAD;AAAK,IAAA,EAAE,EAAE;AAAEmE,MAAAA,CAAC,EAAE,CAAL;AAAQhE,MAAAA,eAAe,EAAE,OAAzB;AAAkCD,MAAAA,YAAY,EAAE,MAAhD;AAAwDE,MAAAA,MAAM,EAAE,mBAAhE;AAAqFJ,MAAAA;AAArF,KAAT;AAAA,cAAwGkE;AAAxG;AAAA;AAAA;AAAA;AAAA,UADe;AAAA,CAAnB;;MAAMD,U;;AAIN,MAAMG,sBAAsB,GAAG,SAAsB;AAAA;;AAAA;;AAAA,MAArB;AAAEC,IAAAA,GAAF;AAAOC,IAAAA;AAAP,GAAqB;AACjD,QAAMC,SAAS,GAAGF,GAAG,CAAC,CAAD,CAArB;AACA,QAAM;AAAE5E,IAAAA;AAAF,MAAWtC,WAAW,CAAEqH,KAAD,IAAWA,KAAK,CAACC,UAAlB,CAA5B;AACA,QAAM;AAAEhF,IAAAA,IAAI,EAAEiF;AAAR,MAAsBvH,WAAW,CAAEqH,KAAD,IAAWA,KAAK,CAAC9C,KAAlB,CAAvC;AACA,QAAMiD,eAAe,GAAGnF,iCAAiC,CAACC,IAAD,EAAO8E,SAAP,CAAzD;AACA,QAAMK,cAAc,GAAG7F,6BAA6B,CAAC4F,eAAD,aAACA,eAAD,uBAACA,eAAe,CAAE3F,eAAlB,EAAmCuF,SAAnC,CAApD;AACA,QAAMM,eAAe,GAAGD,cAAc,SAAd,IAAAA,cAAc,WAAd,IAAAA,cAAc,CAAElE,QAAhB,GAA2BH,eAAe,CAACqE,cAAD,EAAiBF,SAAjB,CAA1C,GAAwE,IAAhG;;AAEA,MAAI,CAACG,eAAD,IAAoBA,eAAe,CAACvF,MAAhB,KAA2B,CAAnD,EAAsD;AAClD,wBACI,QAAC,GAAD;AAAK,MAAA,EAAE,EAAE;AAAE6E,QAAAA,CAAC,EAAE,CAAL;AAAQW,QAAAA,SAAS,EAAE,QAAnB;AAA6BC,QAAAA,KAAK,EAAE;AAApC,OAAT;AAAA,6BACI,QAAC,UAAD;AAAY,QAAA,OAAO,EAAC,OAApB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,YADJ;AAKH;;AAED,QAAMC,mBAAmB,GAAG,MAAM;AAAA;;AAC9B,UAAM,CAACC,wBAAD,IAA6BhI,QAAQ,CAAC4H,eAAD,CAA3C;AAEA,wBACI,QAAC,IAAD;AACI,MAAA,OAAO,EAAEP,OADb;AAEI,MAAA,KAAK,EAAC,wBAFV;AAGI,MAAA,IAAI,EAAEW,wBAHV;AAII,MAAA,OAAO,EAAE;AACLC,QAAAA,MAAM,EAAE,KADH;AAELC,QAAAA,QAAQ,EAAE,KAFL;AAGLC,QAAAA,KAAK,EAAE,KAHF;AAILxB,QAAAA,IAAI,EAAE,KAJD;AAKLyB,QAAAA,WAAW,EAAE,KALR;AAMLC,QAAAA,MAAM,EAAE,KANH;AAOLC,QAAAA,UAAU,EAAE,UAPP;AAQLC,QAAAA,WAAW,EAAE,KARR;AASLC,QAAAA,cAAc,EAAE,MATX;AAULC,QAAAA,UAAU,EAAE,KAVP;AAWLC,QAAAA,OAAO,EAAE,KAXJ;AAYLC,QAAAA,aAAa,EAAE,OAAO;AAClBC,UAAAA,IAAI,EAAE,OADY;AAElBC,UAAAA,EAAE,EAAE;AACA,uDAA2C;AACvCC,cAAAA,QAAQ,EAAE,UAD6B;AAEvCC,cAAAA,UAAU,EAAE,MAF2B;AAGvCC,cAAAA,OAAO,EAAE;AAH8B,aAD3C;AAMA,uDAA2C;AACvCF,cAAAA,QAAQ,EAAE,QAD6B;AAEvCE,cAAAA,OAAO,EAAE;AAF8B;AAN3C;AAFc,SAAP;AAZV;AAJb;AAAA;AAAA;AAAA;AAAA,YADJ;AAkCH,GArCD;;AAhBiD,KAgB3CjB,mBAhB2C;;AAuDjD,sBACI,QAAC,GAAD;AAAK,IAAA,EAAE,EAAE;AAAEhF,MAAAA,KAAK,EAAE,MAAT;AAAiBC,MAAAA,MAAM,EAAE;AAAzB,KAAT;AAAA,2BACI,QAAC,GAAD;AACI,MAAA,EAAE,EAAE;AACAkE,QAAAA,CAAC,EAAE,CADH;AAEAhE,QAAAA,eAAe,EAAE,OAFjB;AAGAD,QAAAA,YAAY,EAAE,MAHd;AAIAE,QAAAA,MAAM,EAAE,mBAJR;AAKA,mDAA2C;AACvC2F,UAAAA,QAAQ,EAAE,UAD6B;AAEvCC,UAAAA,UAAU,EAAE,MAF2B;AAGvCC,UAAAA,OAAO,EAAE;AAH8B,SAL3C;AAUA,mDAA2C;AACvCF,UAAAA,QAAQ,EAAE,QAD6B;AAEvCE,UAAAA,OAAO,EAAE;AAF8B;AAV3C,OADR;AAAA,6BAiBI,QAAC,mBAAD;AAAA;AAAA;AAAA;AAAA;AAjBJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,UADJ;AAuBH,CA9ED;;IAAM7B,sB;UAEejH,W,EACWA,W;;;MAH1BiH,sB;;AAgFN,MAAM8B,2BAA2B,GAAG,SAA4E;AAAA,MAA3E;AAAE7B,IAAAA,GAAF;AAAO5E,IAAAA,IAAP;AAAa0G,IAAAA,sBAAb;AAAqCC,IAAAA;AAArC,GAA2E;AAC5G,QAAM7B,SAAS,GAAGF,GAAG,CAAC,CAAD,CAArB;AACA,QAAMM,eAAe,GAAGnF,iCAAiC,CAACC,IAAD,EAAO8E,SAAP,CAAzD;AACA,QAAMK,cAAc,GAAG7F,6BAA6B,CAAC4F,eAAD,aAACA,eAAD,uBAACA,eAAe,CAAE3F,eAAlB,EAAmCuF,SAAnC,CAApD;AACA,QAAM8B,aAAa,GAAG,CAAAzB,cAAc,SAAd,IAAAA,cAAc,WAAd,YAAAA,cAAc,CAAE5F,eAAhB,KAAmC4F,cAAc,CAAC5F,eAAf,CAA+BM,MAA/B,GAAwC,CAAjG;AAEA,sBACI,QAAC,GAAD;AAAK,IAAA,EAAE,EAAE;AAAEgH,MAAAA,OAAO,EAAE,MAAX;AAAmBC,MAAAA,aAAa,EAAE,KAAlC;AAAyCC,MAAAA,GAAG,EAAE,CAA9C;AAAiDC,MAAAA,EAAE,EAAE;AAArD,KAAT;AAAA,4BACI,QAAC,GAAD;AAAK,MAAA,EAAE,EAAE;AAAEzG,QAAAA,KAAK,EAAEqG,aAAa,GAAG,KAAH,GAAW;AAAjC,OAAT;AAAA,6BACI,QAAC,sBAAD;AAAwB,QAAA,GAAG,EAAEhC,GAA7B;AAAkC,QAAA,OAAO,EAAE8B;AAA3C;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,YADJ,EAIKE,aAAa,iBACV,QAAC,GAAD;AAAK,MAAA,EAAE,EAAE;AAAErG,QAAAA,KAAK,EAAE;AAAT,OAAT;AAAA,6BACI,QAAC,kBAAD;AAAoB,QAAA,GAAG,EAAEqE,GAAzB;AAA8B,QAAA,OAAO,EAAE+B,+BAAvC;AAAwE,QAAA,eAAe,EAAED;AAAzF;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,YALR;AAAA;AAAA;AAAA;AAAA;AAAA,UADJ;AAYH,CAlBD;;MAAMD,2B;;AAoBN,MAAMQ,kBAAkB,GAAG,SAAuC;AAAA;;AAAA,MAAtC;AAAErC,IAAAA,GAAF;AAAOC,IAAAA,OAAP;AAAgBqC,IAAAA;AAAhB,GAAsC;AAC9D,QAAMpC,SAAS,GAAGF,GAAG,CAAC,CAAD,CAArB;AACA,QAAM;AAAE5E,IAAAA;AAAF,MAAWtC,WAAW,CAAEqH,KAAD,IAAWA,KAAK,CAACC,UAAlB,CAA5B;AACA,QAAME,eAAe,GAAGnF,iCAAiC,CAACC,IAAD,EAAO8E,SAAP,CAAzD;AACA,QAAMK,cAAc,GAAG7F,6BAA6B,CAAC4F,eAAD,aAACA,eAAD,uBAACA,eAAe,CAAE3F,eAAlB,EAAmCuF,SAAnC,CAApD;AACA,QAAMqC,qBAAqB,GAAG,CAAAhC,cAAc,SAAd,IAAAA,cAAc,WAAd,YAAAA,cAAc,CAAE5F,eAAhB,KAAmC,EAAjE;AAEA,QAAM6H,kBAAkB,GAAGD,qBAAqB,CAAChG,GAAtB,CAA0B,CAAClB,IAAD,EAAOmB,KAAP,MAAkB,EACnE,GAAGnB,IADgE;AAEnEoB,IAAAA,EAAE,EAAEpB,IAAI,CAACL,UAF0D;AAGnE2E,IAAAA,WAAW,EAAEnD;AAHsD,GAAlB,CAA1B,CAA3B;;AAMA,MAAIgG,kBAAkB,CAACvH,MAAnB,KAA8B,CAAlC,EAAqC;AACjC,wBACI,QAAC,GAAD;AAAK,MAAA,EAAE,EAAE;AAAE6E,QAAAA,CAAC,EAAE,CAAL;AAAQW,QAAAA,SAAS,EAAE,QAAnB;AAA6BC,QAAAA,KAAK,EAAE;AAApC,OAAT;AAAA,6BACI,QAAC,UAAD;AAAY,QAAA,OAAO,EAAC,OAApB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,YADJ;AAKH;;AAED,sBACI,QAAC,GAAD;AAAK,IAAA,EAAE,EAAE;AAAE/E,MAAAA,KAAK,EAAE,MAAT;AAAiBC,MAAAA,MAAM,EAAE;AAAzB,KAAT;AAAA,2BACI,QAAC,GAAD;AACI,MAAA,EAAE,EAAE;AACAkE,QAAAA,CAAC,EAAE,CADH;AAEAhE,QAAAA,eAAe,EAAE,OAFjB;AAGAD,QAAAA,YAAY,EAAE,MAHd;AAIAE,QAAAA,MAAM,EAAE,mBAJR;AAKAJ,QAAAA,KAAK,EAAE,MALP;AAMA,4BAAoB;AAChBA,UAAAA,KAAK,EAAE,iBADS;AAEhB8G,UAAAA,WAAW,EAAE;AAFG,SANpB;AAUA,gCAAwB;AACpBb,UAAAA,OAAO,EAAE;AADW;AAVxB,OADR;AAAA,6BAgBI,QAAC,UAAD;AACI,QAAA,OAAO,EAAE3B,OADb;AAEI,QAAA,IAAI,EAAEuC,kBAFV;AAGI,QAAA,mBAAmB,EAAGE,KAAD,iBAAW,QAAC,sBAAD,OAA4BA,KAA5B;AAAmC,UAAA,OAAO,EAAEJ,eAAe,IAAIrC;AAA/D;AAAA;AAAA;AAAA;AAAA,gBAHpC;AAII,QAAA,OAAO,EAAE;AACLY,UAAAA,MAAM,EAAE,KADH;AAELC,UAAAA,QAAQ,EAAE,KAFL;AAGLC,UAAAA,KAAK,EAAE,KAHF;AAILxB,UAAAA,IAAI,EAAE,KAJD;AAKLyB,UAAAA,WAAW,EAAE,KALR;AAMLC,UAAAA,MAAM,EAAE,KANH;AAOLC,UAAAA,UAAU,EAAE,UAPP;AAQLC,UAAAA,WAAW,EAAE,KARR;AASLC,UAAAA,cAAc,EAAE,MATX;AAULC,UAAAA,UAAU,EAAE,KAVP;AAWLC,UAAAA,OAAO,EAAE,KAXJ;AAYLC,UAAAA,aAAa,EAAE,OAAO;AAClBC,YAAAA,IAAI,EAAE;AADY,WAAP;AAZV;AAJb;AAAA;AAAA;AAAA;AAAA;AAhBJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,UADJ;AA0CH,CA/DD;;IAAMa,kB;UAEevJ,W;;;MAFfuJ,kB;AAiEN,eAAe,SAASM,cAAT,QAA2E;AAAA;;AAAA;AAAA;;AAAA,MAAnD;AAAE3C,IAAAA,GAAF;AAAO4C,IAAAA,gBAAgB,GAAG,IAA1B;AAAgCC,IAAAA,QAAQ,GAAG;AAA3C,GAAmD;AACtF,QAAMC,QAAQ,GAAGjK,WAAW,EAA5B;AACA,QAAM;AAAEuC,IAAAA,IAAF;AAAQ2H,IAAAA,QAAR;AAAkBC,IAAAA,IAAlB;AAAwBC,IAAAA,OAAxB;AAAiCC,IAAAA,mBAAjC;AAAsDC,IAAAA;AAAtD,MAAyErK,WAAW,CAAEqH,KAAD,IAAWA,KAAK,CAACC,UAAlB,CAA1F;AACA,QAAM;AAAEhF,IAAAA,IAAI,EAAEiF;AAAR,MAAsBvH,WAAW,CAAEqH,KAAD,IAAWA,KAAK,CAAC9C,KAAlB,CAAvC;;AAEA,QAAM+F,kBAAkB,GAAIC,OAAD,IAAa;AACpC,WAAOA,OAAO,IAAI,CAACA,OAAO,CAAC5F,YAA3B;AACH,GAFD;;AAIA,QAAM6F,gBAAgB,GAAI7G,EAAD,IAAQ;AAC7B,QAAI;AACA,YAAM4G,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;AACA,YAAM+G,MAAM,GAAGH,OAAO,IAAI;AAAE5F,QAAAA,YAAY,EAAE;AAAhB,OAA1B;AACA,aAAO+F,MAAP;AACH,KAJD,CAIE,OAAOC,KAAP,EAAc;AACZ,aAAO;AAAEhG,QAAAA,YAAY,EAAE;AAAhB,OAAP;AACH;AACJ,GARD;;AAUA,QAAMiG,iBAAiB,GAAG,CAACjH,EAAD,EAAKkH,cAAL,KAAwB;AAC9C,QAAI;AACA,YAAMN,OAAO,GAAGC,gBAAgB,CAAC7G,EAAD,CAAhC;;AACA,UAAI,CAAC2G,kBAAkB,CAACC,OAAD,CAAvB,EAAkC;AAC9B,4BAAO,QAAC,UAAD;AAAY,UAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAAP;AACH;;AACD,aAAOM,cAAc,CAACN,OAAD,CAArB;AACH,KAND,CAME,OAAOI,KAAP,EAAc;AACZG,MAAAA,OAAO,CAACC,IAAR,CAAa,wCAAb,EAAuDpH,EAAvD,EAA2DgH,KAA3D;AACA,0BAAO,QAAC,UAAD;AAAY,QAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAAP;AACH;AACJ,GAXD;;AAaA,QAAMK,cAAc,GAAG5H,eAAe,CAClC0G,gBAAgB,GAAGM,mBAAmB,CAAC3F,IAApB,CAA0BlC,IAAD,IAAUA,IAAI,CAACL,UAAL,KAAoBgF,GAAG,CAAC,CAAD,CAA1D,CAAH,GAAoE5E,IAAI,CAACmC,IAAL,CAAWlC,IAAD,IAAUA,IAAI,CAACL,UAAL,KAAoBgF,GAAG,CAAC,CAAD,CAA3C,CADlD,EAElCK,SAFkC,CAAtC;AAIA,QAAMlE,WAAW,GAAGyG,gBAAgB,GAC9BM,mBAAmB,CAAC3F,IAApB,CAA0BlC,IAAD,IAAUA,IAAI,CAACL,UAAL,KAAoBgF,GAAG,CAAC,CAAD,CAA1D,CAD8B,GAE9B5E,IAAI,CAACmC,IAAL,CAAWlC,IAAD,IAAUA,IAAI,CAACL,UAAL,KAAoBgF,GAAG,CAAC,CAAD,CAA3C,CAFN;AAGA,QAAMrF,eAAe,GAAG+E,kBAAkB,CAACvD,WAAD,CAA1C;AAEA,QAAM,CAACoH,iBAAD,EAAoBQ,oBAApB,IAA4CnL,QAAQ,CAACkL,cAAc,IAAI,EAAnB,CAA1D;AACA,QAAM,CAACE,OAAD,IAAYpL,QAAQ,CAAC,CAACkL,cAAF,CAA1B;AACA,QAAM,CAACG,OAAD,EAAUC,YAAV,EAAwBC,UAAxB,IAAsCpK,UAAU,CAACiK,OAAD,CAAtD;;AAEA,QAAMI,SAAS,GAAG,MAAMtB,QAAQ,CAACzJ,iBAAiB,EAAlB,CAAhC;;AACA,QAAMgL,WAAW,GAAIjJ,IAAD,IAAU0H,QAAQ,CAACvJ,kBAAkB,CAAC6B,IAAD,CAAnB,CAAtC;;AAEA,QAAMkJ,MAAM,GAAG,MAAM;AACjB,QAAIN,OAAJ,EAAa;AACTE,MAAAA,YAAY;AACZpB,MAAAA,QAAQ,CAAC1J,0BAA0B,CAAC4G,GAAG,CAAC,CAAD,CAAJ,EAAS,EAAE,GAAGiD,OAAL;AAAcsB,QAAAA,IAAI,EAAE3B,gBAAgB,GAAG,OAAH,GAAaK,OAAO,CAACsB;AAAzD,OAAT,EAA0ElE,SAA1E,CAA3B,CAAR,CAAyHmE,IAAzH,CACKpJ,IAAD,IAAU;AACN2I,QAAAA,oBAAoB,CAAC3I,IAAD,CAApB;AACA+I,QAAAA,UAAU;AACb,OAJL;AAMH;AACJ,GAVD;;AAYA,QAAMM,eAAe,GAAIpJ,IAAD,IAAU;AAC9ByH,IAAAA,QAAQ,CAAC5J,SAAS,CAACmC,IAAD,CAAV,CAAR;AACH,GAFD;;AAIA,QAAMqJ,oBAAoB,GAAIjI,EAAD,IAAQ;AACjCqG,IAAAA,QAAQ,CAACxJ,cAAc,CAACmD,EAAD,CAAf,CAAR;AACH,GAFD;;AAIA,QAAMkI,cAAc,GAAG,CAAClI,EAAD,EAAKmI,WAAL,KAAqB;AACxC9B,IAAAA,QAAQ,CAAC3J,UAAU,CAACsD,EAAD,EAAKmI,WAAL,CAAX,CAAR;AACH,GAFD;;AAIAjM,EAAAA,SAAS,CAAC,MAAM;AACZ2L,IAAAA,MAAM;AACT,GAFQ,EAEN,EAFM,CAAT;;AAIA,QAAMO,aAAa,GAAG,SAAsE;AAAA;;AAAA;;AAAA,QAArE;AAAEC,MAAAA,SAAS,EAAE;AAAEzB,QAAAA,OAAO,EAAE0B;AAAX,OAAb;AAAuCC,MAAAA,OAAO,GAAG;AAAjD,KAAqE;AACxF,UAAMvI,EAAE,GAAGsI,WAAW,CAAC,CAAD,CAAtB;AAEA,UAAM1B,OAAO,GAAGJ,OAAO,CAACsB,IAAR,KAAiBrK,UAAjB,GAA8BiJ,cAAc,CAAC1G,EAAD,CAA5C,GAAmDsG,QAAQ,CAACtG,EAAD,CAA3E;AACA,UAAMwI,WAAW,GAAGjC,IAAI,CAACvG,EAAD,CAAxB;AACA,UAAMyI,qBAAqB,GAAG3B,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAA9B;;AAEA,UAAM0I,WAAW,GAAG,CAACF,WAAD,EAAc5B,OAAd,EAAuB2B,OAAvB,KAAmC;AACnD,UAAIC,WAAW,IAAIA,WAAW,CAACD,OAAD,CAAX,KAAyBI,SAA5C,EAAuD;AACnD,eAAOH,WAAW,CAACD,OAAD,CAAlB;AACH;;AACD,UAAI3B,OAAO,IAAIA,OAAO,CAAC2B,OAAD,CAAP,KAAqBI,SAApC,EAA+C;AAC3C,eAAO/B,OAAO,CAAC2B,OAAD,CAAd;AACH;;AACD,aAAO,CAAP;AACH,KARD;;AAUA,UAAM,CAACK,WAAD,EAAcC,cAAd,IAAgC1M,QAAQ,CAACuM,WAAW,CAACF,WAAD,EAAc5B,OAAd,EAAuB2B,OAAvB,CAAZ,CAA9C;AAEArM,IAAAA,SAAS,CAAC,MAAM;AACZ,YAAMiE,QAAQ,GAAGuI,WAAW,CAACF,WAAD,EAAc5B,OAAd,EAAuB2B,OAAvB,CAA5B;AACAM,MAAAA,cAAc,CAACzI,UAAU,CAACD,QAAD,CAAV,CAAqBG,OAArB,CAA6B,CAA7B,CAAD,CAAd;AACH,KAHQ,EAGN,CAACsG,OAAD,aAACA,OAAD,uBAACA,OAAO,CAAG2B,OAAH,CAAR,EAAqBC,WAArB,aAAqBA,WAArB,uBAAqBA,WAAW,CAAGD,OAAH,CAAhC,CAHM,CAAT;;AAKA,UAAMO,UAAU,GAAG,MAAM;AAAA;;AACrB,YAAMC,QAAQ,GAAG,CAAAN,qBAAqB,SAArB,IAAAA,qBAAqB,WAArB,qCAAAA,qBAAqB,CAAEO,YAAvB,gFAAqCC,aAArC,MAAuD,CAAvD,GAA2DL,WAA3D,GAAyExI,UAAU,CAACwI,WAAD,CAApG;AACA,YAAMM,UAAU,GAAGC,IAAI,CAACC,KAAL,CAAWL,QAAX,EAAqBzI,OAArB,CAA6B,CAA7B,KAAmC,CAAtD;AACAuI,MAAAA,cAAc,CAACK,UAAD,CAAd;AAEA,YAAMG,YAAY,GAAG7C,OAAO,CAACsB,IAAR,KAAiBrK,UAAjB,GAA8BT,wBAA9B,GAAyDD,kBAA9E;AACAsJ,MAAAA,QAAQ,CACJgD,YAAY,CAACrJ,EAAD,EAAK;AACb,SAACuI,OAAD,GAAWW,UADE;AAEbI,QAAAA,QAAQ,EAAE,IAFG;AAGb/K,QAAAA,UAAU,EAAEkK,qBAAqB,CAAClK,UAHrB;AAIb0B,QAAAA,QAAQ,EAAEwI,qBAAqB,CAACxI;AAJnB,OAAL,CADR,CAAR;;AASA,UAAIuI,WAAJ,EAAiB;AACbN,QAAAA,cAAc,CAAClI,EAAD,EAAK;AAAE,WAACuI,OAAD,GAAWW;AAAb,SAAL,CAAd;AACH;AACJ,KAlBD;;AAoBA,UAAMK,YAAY,GAAG,UAA2B;AAAA,UAA1B;AAAEC,QAAAA,MAAM,EAAE;AAAEC,UAAAA;AAAF;AAAV,OAA0B;AAC5C,YAAMV,QAAQ,GAAG3I,UAAU,CAACqJ,KAAD,CAAV,IAAqB,CAAtC;AACAZ,MAAAA,cAAc,CAACE,QAAD,CAAd;AACH,KAHD;;AAKA,UAAMW,YAAY,GAAG,MAAM;AAAA;;AACvB,YAAMhK,WAAW,GAAGf,IAAI,CAACmC,IAAL,CAAWlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYuD,GAAG,CAAC,CAAD,CAAnC,CAApB;AACA,YAAMoG,iBAAiB,GAAG7C,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAA1B;AACA,YAAM4J,SAAS,GAAGxJ,UAAU,CAACuJ,iBAAiB,CAACpJ,aAAnB,CAAV,GAA8CH,UAAU,0BAACV,WAAD,aAACA,WAAD,iDAACA,WAAW,CAAEsJ,YAAd,2DAAC,uBAA2Ba,WAA5B,yEAA2C,CAA3C,CAA1E;;AAEA,UAAID,SAAS,GAAG,CAAhB,EAAmB;AACf,YAAIE,aAAa,GAAG,EAApB;AACA,YAAIf,QAAQ,GAAGa,SAAf;;AAEA,YAAIhD,OAAJ,EAAa;AAAA;;AACT,cAAI2B,OAAO,KAAK,aAAhB,EAA+B;AAC3BuB,YAAAA,aAAa,GAAG,cAAhB;AACH,WAFD,MAEO;AACHA,YAAAA,aAAa,GAAG,aAAhB;AACH;;AAEDf,UAAAA,QAAQ,GAAGa,SAAS,6BAAIhD,OAAO,CAACkD,aAAD,CAAX,yEAA8B,CAA9B,CAApB;AACH;;AAED,YAAIf,QAAQ,GAAG,CAAf,EAAkB;AACd,gBAAMM,YAAY,GAAG7C,OAAO,CAACsB,IAAR,KAAiBrK,UAAjB,GAA8BT,wBAA9B,GAAyDD,kBAA9E;AACAsJ,UAAAA,QAAQ,CAACgD,YAAY,CAACrJ,EAAD,EAAK;AAAE,aAACuI,OAAD,GAAWQ,QAAb;AAAuBO,YAAAA,QAAQ,EAAE,IAAjC;AAAuCrJ,YAAAA,QAAQ,EAAEwI,qBAAqB,CAACxI;AAAvE,WAAL,CAAb,CAAR;;AAEA,cAAIuI,WAAJ,EAAiB;AACbN,YAAAA,cAAc,CAAClI,EAAD,EAAK;AAAE,eAACuI,OAAD,GAAWQ;AAAb,aAAL,CAAd;AACH;AACJ;AACJ;AACJ,KA5BD;;AA8BA,wBACI,QAAC,GAAD;AAAA,8BACI,QAAC,SAAD;AACI,QAAA,OAAO,EAAC,UADZ;AAEI,QAAA,EAAE,EAAE;AAAE7J,UAAAA,KAAK,EAAE;AAAT,SAFR;AAGI,QAAA,IAAI,EAAC,OAHT;AAII,QAAA,IAAI,EAAC,QAJT;AAKI,QAAA,KAAK,EAAE0J,WALX;AAMI,QAAA,QAAQ,EAAEW,YANd;AAOI,QAAA,MAAM,EAAET,UAPZ;AAQI,QAAA,UAAU,EAAE;AACRiB,UAAAA,UAAU,EAAE;AACRC,YAAAA,KAAK,EAAE;AAAEhG,cAAAA,SAAS,EAAE;AAAb,aADC;AAERiG,YAAAA,IAAI,EAAE,CAAArD,OAAO,SAAP,IAAAA,OAAO,WAAP,qCAAAA,OAAO,CAAEoC,YAAT,gFAAuBC,aAAvB,MAAyC,CAAzC,GAA6C,GAA7C,GAAmD,CAFjD;AAGRiB,YAAAA,GAAG,EAAE,CAHG;AAIRC,YAAAA,SAAS,EAAE,CAAAvD,OAAO,SAAP,IAAAA,OAAO,WAAP,sCAAAA,OAAO,CAAEoC,YAAT,kFAAuBC,aAAvB,MAAyC,CAAzC,GAA6C,SAA7C,GAAyD,SAJ5D;AAKRmB,YAAAA,OAAO,EAAE;AALD;AADJ;AARhB;AAAA;AAAA;AAAA;AAAA,cADJ,EAmBK5D,OAAO,CAACsB,IAAR,KAAiBrK,UAAjB,IAA+B,CAACgL,qBAAqB,CAACzH,YAAtD,iBACG,QAAC,OAAD;AAAS,QAAA,KAAK,EAAC,0BAAf;AAAA,+BACI,QAAC,UAAD;AAAY,UAAA,KAAK,EAAC,SAAlB;AAA4B,wBAAW,0BAAvC;AAAkE,UAAA,OAAO,EAAE0I,YAA3E;AAAA,iCACI,QAAC,YAAD;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,cApBR;AAAA;AAAA;AAAA;AAAA;AAAA,YADJ;AA6BH,GA5GD;;AA5EsF,MA4EhFtB,aA5EgF;;AA0LtF,QAAMiC,aAAa,GAAG,UAAmB;AAAA;;AAAA;;AAAA,QAAlB;AAAEhC,MAAAA;AAAF,KAAkB;AACrC,UAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,UAAMrI,UAAU,GAAGgF,GAAG,CAAC,CAAD,CAAtB;AAEA,UAAM+G,OAAO,GAAG9D,OAAO,CAACsB,IAAR,KAAiBrK,UAAjB,GAA8BiJ,cAAc,CAAC1G,EAAD,CAA5C,GAAmDsG,QAAQ,CAACtG,EAAD,CAA3E;AACA,UAAMN,WAAW,GAAGf,IAAI,CAACmC,IAAL,CAAWlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYzB,UAAhC,CAApB;AACA,UAAMgM,YAAY,GAAGhE,IAAI,CAACvG,EAAD,CAAzB;AACA,UAAMwK,YAAY,GAAGpK,UAAU,yBAACkK,OAAD,aAACA,OAAD,uBAACA,OAAO,CAAEG,WAAV,uEAAyB,CAAzB,CAAV,GAAwCrK,UAAU,0BAACkK,OAAD,aAACA,OAAD,uBAACA,OAAO,CAAEI,YAAV,yEAA0B,CAA1B,CAAvE;;AAEA,UAAMC,WAAW,GAAG,MAAM;AACtB,UAAIJ,YAAJ,EAAkB;AACdtC,QAAAA,oBAAoB,CAACjI,EAAD,CAApB;AACH,OAFD,MAEO;AACHgI,QAAAA,eAAe,CAAC,EACZ,GAAGsC,OADS;AAEZrK,UAAAA,QAAQ,EAAEoI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAFE;AAGZ1F,UAAAA,UAAU,EAAEmH,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAHA;AAIZzF,UAAAA,YAAY,EAAEzB,WAAW,CAACyB,YAJd;AAKZT,UAAAA,QAAQ,EAAEhB,WAAW,CAACgB,QALV;AAMZa,UAAAA,WAAW,EAAE7B,WAAW,CAAC6B,WANb;AAOZC,UAAAA,eAAe,EAAE9B,WAAW,CAAC8B,eAPjB;AAQZqI,UAAAA,WAAW,EAAEnK,WAAW,CAACsJ,YAAZ,CAAyBa,WAR1B;AASZb,UAAAA,YAAY,EAAEtJ,WAAW,CAACsJ,YATd;AAUZzK,UAAAA,UAAU,EAAEgF,GAAG,CAAC,CAAD,CAVH;AAWZ/C,UAAAA,UAAU,EAAEd,WAAW,CAACc;AAXZ,SAAD,CAAf;AAaH;AACJ,KAlBD;;AAoBAtE,IAAAA,SAAS,CAAC,MAAM;AACZ,UAAIqO,YAAY,IAAIC,YAAY,IAAI,CAApC,EAAuC;AACnCvC,QAAAA,oBAAoB,CAACjI,EAAD,CAApB;AACH;AACJ,KAJQ,EAIN,CAACwK,YAAD,EAAeD,YAAf,CAJM,CAAT;AAMA,wBACI,QAAC,GAAD;AAAK,MAAA,EAAE,EAAE;AAAE/E,QAAAA,OAAO,EAAE,MAAX;AAAmBoF,QAAAA,cAAc,EAAE,QAAnC;AAA6CC,QAAAA,UAAU,EAAE;AAAzD,OAAT;AAAA,gBACKP,OAAO,IAAIE,YAAY,GAAG,CAA1B,gBACG;AAAA,kBACKD,YAAY,gBACT,QAAC,gBAAD;AAAkB,UAAA,KAAK,EAAC,OAAxB;AAAgC,UAAA,QAAQ,EAAC,OAAzC;AAAiD,UAAA,OAAO,EAAEI,WAA1D;AAAuE,UAAA,KAAK,EAAE;AAAEG,YAAAA,MAAM,EAAE;AAAV;AAA9E;AAAA;AAAA;AAAA;AAAA,gBADS,gBAGT,QAAC,aAAD;AAAe,UAAA,KAAK,EAAC,SAArB;AAA+B,UAAA,QAAQ,EAAC,OAAxC;AAAgD,UAAA,OAAO,EAAEH,WAAzD;AAAsE,UAAA,KAAK,EAAE;AAAEG,YAAAA,MAAM,EAAE;AAAV;AAA7E;AAAA;AAAA;AAAA;AAAA;AAJR,uBADH,gBASG,QAAC,OAAD;AAAS,QAAA,KAAK,EAAC,uDAAf;AAAoE,QAAA,EAAE,EAAE;AAAE7G,UAAAA,KAAK,EAAE;AAAT,SAAxE;AAAA,+BACI,QAAC,aAAD;AAAe,UAAA,KAAK,EAAC,SAArB;AAA+B,UAAA,QAAQ,EAAC,OAAxC;AAAgD,UAAA,KAAK,EAAE;AAAE6G,YAAAA,MAAM,EAAE;AAAV;AAAvD;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AAVR;AAAA;AAAA;AAAA;AAAA,YADJ;AAiBH,GApDD;;AA1LsF,MA0LhFT,aA1LgF;;AAgPtF,QAAM7G,OAAO,GAAG,CACZ;AACIuH,IAAAA,IAAI,EAAE,IADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,KADH;AAEL1B,MAAAA,IAAI,EAAE,KAFD;AAGL0C,MAAAA,OAAO,EAAE;AAHJ;AAHb,GADY,EAUZ;AACIuF,IAAAA,IAAI,EAAE,UADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE;AAHJ;AAHb,GAVY,EAmBZ;AACIuF,IAAAA,IAAI,EAAE,YADV;AAEIC,IAAAA,KAAK,EAAE,QAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE;AAFD;AAHb,GAnBY,EA2BZ;AACIiI,IAAAA,IAAI,EAAE,gBADV;AAEIC,IAAAA,KAAK,EAAE,UAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGLoI,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI,CAAC2G,kBAAkB,CAACC,OAAD,CAAvB,EAAkC;AAC9B,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,4BACI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAEpB,YAAAA,OAAO,EAAE,MAAX;AAAmBqF,YAAAA,UAAU,EAAE,QAA/B;AAAyCD,YAAAA,cAAc,EAAE;AAAzD,WAAT;AAAA,qBACKnB,KADL,eAEI,QAAC,WAAD;AAAa,YAAA,SAAS,EAAErJ,UAAU,CAACqJ,KAAD,CAAV,GAAoB,CAA5C;AAA+C,YAAA,KAAK,EAAC;AAArD;AAAA;AAAA;AAAA;AAAA,kBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBADJ;AAMH;AAhBI;AAHb,GA3BY,EAiDZ;AACIsB,IAAAA,IAAI,EAAE,aADV;AAEIC,IAAAA,KAAK,EAAE,WAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,IAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AAAA;;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AAEA,YAAI,CAAC2G,kBAAkB,CAACC,OAAD,CAAvB,EAAkC;AAC9B,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,4BACI,QAAC,GAAD;AACI,UAAA,EAAE,EAAE;AACApB,YAAAA,OAAO,EAAE,MADT;AAEAE,YAAAA,GAAG,EAAE,CAFL;AAGAD,YAAAA,aAAa,EAAE,KAHf;AAIAoF,YAAAA,UAAU,EAAE,QAJZ;AAKAD,YAAAA,cAAc,EAAE,QALhB;AAMA1L,YAAAA,KAAK,EAAE,MANP;AAOA4L,YAAAA,MAAM,EAAE;AAPR,WADR;AAAA,qBAWM,GAAD,wBAAGlE,OAAH,aAAGA,OAAH,uBAAGA,OAAO,CAAEpH,WAAZ,uEAA2B,IAAK,IAAGY,UAAU,CAACqJ,KAAD,CAAV,GAAoB,CAApB,GAAwBA,KAAxB,GAAgC,GAAI,GAX5E,eAYI,QAAC,UAAD;AAAY,YAAA,WAAW,2BAAE7C,OAAF,aAAEA,OAAF,uBAAEA,OAAO,CAAEpH,WAAX,yEAA0B;AAAjD;AAAA;AAAA;AAAA;AAAA,kBAZJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBADJ;AAgBH;AA5BI;AAHb,GAjDY,EAmFZ;AACIuL,IAAAA,IAAI,EAAE,gBADV;AAEIC,IAAAA,KAAK,EAAE,WAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGC,gBAAgB,CAAC7G,EAAD,CAAhC;;AACA,YAAI,CAAC2G,kBAAkB,CAACC,OAAD,CAAvB,EAAkC;AAC9B,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AACD,4BACI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAEpB,YAAAA,OAAO,EAAE,MAAX;AAAmBqF,YAAAA,UAAU,EAAE,QAA/B;AAAyCD,YAAAA,cAAc,EAAE;AAAzD,WAAT;AAAA,qBACKzN,uBAAuB,CAACD,OAAO,CAAC,IAAIiO,IAAJ,EAAD,EAAaC,QAAQ,CAAC3B,KAAD,EAAQ,EAAR,CAArB,CAAR,CAD5B,eAEI,QAAC,WAAD;AAAa,YAAA,SAAS,EAAE2B,QAAQ,CAAC3B,KAAD,EAAQ,EAAR,CAAR,GAAsB,CAA9C;AAAiD,YAAA,KAAK,EAAC;AAAvD;AAAA;AAAA;AAAA;AAAA,kBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBADJ;AAMH;AAhBI;AAHb,GAnFY,EAyGZ;AACIsB,IAAAA,IAAI,EAAE,aADV;AAEIC,IAAAA,KAAK,EAAE,iBAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,eAAOK,iBAAiB,CAACjH,EAAD,EAAM4G,OAAD,IAAa;AACtC,8BACI,QAAC,GAAD;AAAK,YAAA,EAAE,EAAE;AAAEpB,cAAAA,OAAO,EAAE,MAAX;AAAmBqF,cAAAA,UAAU,EAAE,QAA/B;AAAyCD,cAAAA,cAAc,EAAE;AAAzD,aAAT;AAAA,uBACKzN,uBAAuB,CAACD,OAAO,CAAC,IAAIiO,IAAJ,EAAD,EAAaC,QAAQ,CAAC3B,KAAD,EAAQ,EAAR,CAArB,CAAR,CAD5B,eAEI,QAAC,WAAD;AAAa,cAAA,SAAS,EAAE2B,QAAQ,CAAC3B,KAAD,EAAQ,EAAR,CAAR,GAAsB,CAA9C;AAAiD,cAAA,KAAK,EAAC;AAAvD;AAAA;AAAA;AAAA;AAAA,oBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,kBADJ;AAMH,SAPuB,CAAxB;AAQH;AAdI;AAHb,GAzGY,EA6HZ;AACIsB,IAAAA,IAAI,EAAE,YADV;AAEIC,IAAAA,KAAK,EAAE,MAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,IAAIA,OAAO,CAAC5F,YAAvB,EAAqC;AACjC,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AACD,eAAOyI,KAAP;AACH;AAXI;AAHb,GA7HY,EA8IZ;AACIsB,IAAAA,IAAI,EAAE,gBADV;AAEIC,IAAAA,KAAK,EAAE,UAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AACD,eAAOyI,KAAP;AACH;AAXI;AAHb,GA9IY,EA+JZ;AACIsB,IAAAA,IAAI,EAAE,gBADV;AAEIC,IAAAA,KAAK,EAAE,oBAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,IAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AAAA;;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMhG,KAAK,GAAGyH,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAd;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AAEA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,4BACI,QAAC,GAAD;AACI,UAAA,EAAE,EAAE;AACAwE,YAAAA,OAAO,EAAE,MADT;AAEAE,YAAAA,GAAG,EAAE,CAFL;AAGAD,YAAAA,aAAa,EAAE,KAHf;AAIAoF,YAAAA,UAAU,EAAE,QAJZ;AAKAD,YAAAA,cAAc,EAAE,QALhB;AAMA1L,YAAAA,KAAK,EAAE,MANP;AAOA4L,YAAAA,MAAM,EAAE;AAPR,WADR;AAUI,UAAA,OAAO,EAAE,MAAM;AACXlD,YAAAA,WAAW,CAAC,EAAE,GAAGhB,OAAL;AAAchG,cAAAA;AAAd,aAAD,CAAX;AACA+G,YAAAA,SAAS;AACZ,WAbL;AAAA,8CAeKf,OAfL,aAeKA,OAfL,uBAeKA,OAAO,CAAE9E,kBAfd,yEAeoC,GAfpC,eAgBI,QAAC,aAAD;AAAe,YAAA,aAAa,2BAAE8E,OAAF,aAAEA,OAAF,uBAAEA,OAAO,CAAEhF,cAAX,yEAA6B;AAAzD;AAAA;AAAA;AAAA;AAAA,kBAhBJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBADJ;AAoBH;AAjCI;AAHb,GA/JY,EAsMZ;AACImJ,IAAAA,IAAI,EAAE,oBADV;AAEIC,IAAAA,KAAK,EAAE,YAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,eAAQ,GAAEyI,KAAM,GAAhB;AACH;AAZI;AAHb,GAtMY,EAwNZ;AACIsB,IAAAA,IAAI,EAAE,wBADV;AAEIC,IAAAA,KAAK,EAAE,YAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,eAAQ,GAAEyI,KAAM,GAAhB;AACH;AAZI;AAHb,GAxNY,EA0OZ;AACIsB,IAAAA,IAAI,EAAE,2BADV;AAEIC,IAAAA,KAAK,EAAE,YAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,eAAQ,GAAEyI,KAAM,GAAhB;AACH;AAZI;AAHb,GA1OY,EA4PZ;AACIsB,IAAAA,IAAI,EAAE,OADV;AAEIC,IAAAA,KAAK,EAAE,aAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,4BACI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAEwE,YAAAA,OAAO,EAAE,MAAX;AAAmBqF,YAAAA,UAAU,EAAE;AAA/B,WAAT;AAAA,qBACKpB,KADL,oBACY,QAAC,WAAD;AAAa,YAAA,SAAS,EAAErJ,UAAU,CAACqJ,KAAD,CAAV,GAAoB,CAA5C;AAA+C,YAAA,KAAK,EAAC;AAArD;AAAA;AAAA;AAAA;AAAA,kBADZ;AAAA;AAAA;AAAA;AAAA;AAAA,gBADJ;AAKH;AAhBI;AAHb,GA5PY,EAkRZ;AACIsB,IAAAA,IAAI,EAAE,UADV;AAEIC,IAAAA,KAAK,EAAE,YAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,eAAOyI,KAAP;AACH;AAZI;AAHb,GAlRY,EAoSZ;AACIsB,IAAAA,IAAI,EAAE,aADV;AAEIC,IAAAA,KAAK,EAAE,YAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,eAAOyI,KAAP;AACH;AAZI;AAHb,GApSY,EAsTZ;AACIsB,IAAAA,IAAI,EAAE,gBADV;AAEIC,IAAAA,KAAK,EAAE,cAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGLoI,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsBjI,UAAU,CAACqJ,KAAD,CAAV,CAAkBnJ,OAAlB,CAA0B,CAA1B;AAHnC;AAHb,GAtTY,EA+TZ;AACIyK,IAAAA,IAAI,EAAE,WADV;AAEIC,IAAAA,KAAK,EAAE,WAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGLoI,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AACD,eAAOZ,UAAU,CAACqJ,KAAD,CAAV,CAAkBnJ,OAAlB,CAA0B,CAA1B,CAAP;AACH;AAVI;AAHb,GA/TY,EA+UZ;AACIyK,IAAAA,IAAI,EAAE,kBADV;AAEIC,IAAAA,KAAK,EAAE,gBAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGLoI,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AACD,eAAOZ,UAAU,CAACqJ,KAAD,CAAV,CAAkBnJ,OAAlB,CAA0B,CAA1B,CAAP;AACH;AAVI;AAHb,GA/UY,EA+VZ;AACIyK,IAAAA,IAAI,EAAE,eADV;AAEIC,IAAAA,KAAK,EAAE,UAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGLoI,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AAAA;;AACpC,cAAMrI,EAAE,GAAGuD,GAAG,CAAC,CAAD,CAAd;AACA,cAAM7D,WAAW,GAAGf,IAAI,CAACmC,IAAL,CAAWlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAAhC,CAApB;AACA,cAAM+G,MAAM,GAAG0C,KAAK,GAAGrJ,UAAU,2BAACV,WAAD,aAACA,WAAD,iDAACA,WAAW,CAAEsJ,YAAd,2DAAC,uBAA2Ba,WAA5B,2EAA2C,CAA3C,CAAjC;AAEA,cAAMtL,UAAU,GAAG8J,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAnB;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYzB,UAA7C,CAAhB;;AACA,YAAIqI,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,eAAOmI,IAAI,CAACkC,IAAL,CAAUtE,MAAV,EAAkBzG,OAAlB,CAA0B,CAA1B,CAAP;AACH;AAfI;AAHb,GA/VY,EAoXZ;AACIyK,IAAAA,IAAI,EAAE,aADV;AAEIC,IAAAA,KAAK,EAAE,cAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,KAFD;AAGL0C,MAAAA,OAAO,EAAE,IAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,kBAAsB,QAAC,aAAD;AAAe,QAAA,SAAS,EAAEA;AAA1B;AAAA;AAAA;AAAA;AAAA;AAJnC;AAHb,GApXY,EA8XZ;AACI0C,IAAAA,IAAI,EAAE,cADV;AAEIC,IAAAA,KAAK,EAAE,mBAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,KAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,kBAAsB,QAAC,aAAD;AAAe,QAAA,SAAS,EAAEA,SAA1B;AAAqC,QAAA,OAAO,EAAC;AAA7C;AAAA;AAAA;AAAA;AAAA;AAJnC;AAHb,GA9XY,EAwYZ;AACI0C,IAAAA,IAAI,EAAE,cADV;AAEIC,IAAAA,KAAK,EAAE,MAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,IAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,MAAM;AAAA;;AACpB,cAAMlL,EAAE,GAAGuD,GAAG,CAAC,CAAD,CAAd;AACA,cAAMqD,OAAO,GAAGjI,IAAI,CAACmC,IAAL,CAAWlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAAhC,CAAhB;AACA,yCAAO4G,OAAP,aAAOA,OAAP,iDAAOA,OAAO,CAAEoC,YAAhB,2DAAO,uBAAuB5H,YAA9B,2EAA8C,EAA9C;AACH;AARI;AAHb,GAxYY,EAsZZ;AACI2J,IAAAA,IAAI,EAAE,OADV;AAEIC,IAAAA,KAAK,EAAE,GAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,KADH;AAEL1B,MAAAA,IAAI,EAAE,KAFD;AAGLoI,MAAAA,gBAAgB,EAAE,CAACI,CAAD,EAAIjD,SAAJ,kBAAkB,QAAC,aAAD;AAAe,QAAA,SAAS,EAAEA;AAA1B;AAAA;AAAA;AAAA;AAAA;AAH/B;AAHb,GAtZY,CAAhB;AAiaA,QAAMkD,sBAAsB,GAAG,CAC3B;AACIR,IAAAA,IAAI,EAAE,IADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,KADH;AAEL1B,MAAAA,IAAI,EAAE,KAFD;AAGL0C,MAAAA,OAAO,EAAE;AAHJ;AAHb,GAD2B,EAU3B;AACIuF,IAAAA,IAAI,EAAE,UADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE;AAHJ;AAHb,GAV2B,EAmB3B;AACIuF,IAAAA,IAAI,EAAE,YADV;AAEIC,IAAAA,KAAK,EAAE,QAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE;AAFD;AAHb,GAnB2B,EA2B3B;AACIiI,IAAAA,IAAI,EAAE,gBADV;AAEIC,IAAAA,KAAK,EAAE,UAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AACD,eAAOyI,KAAP;AACH;AAXI;AAHb,GA3B2B,EA4C3B;AACIsB,IAAAA,IAAI,EAAE,gBADV;AAEIC,IAAAA,KAAK,EAAE,oBAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,IAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AAAA;;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMhG,KAAK,GAAGyH,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAd;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AAEA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,4BACI,QAAC,GAAD;AACI,UAAA,EAAE,EAAE;AACAwE,YAAAA,OAAO,EAAE,MADT;AAEAE,YAAAA,GAAG,EAAE,CAFL;AAGAD,YAAAA,aAAa,EAAE,KAHf;AAIAoF,YAAAA,UAAU,EAAE,QAJZ;AAKAD,YAAAA,cAAc,EAAE,QALhB;AAMA1L,YAAAA,KAAK,EAAE,MANP;AAOA4L,YAAAA,MAAM,EAAE;AAPR,WADR;AAUI,UAAA,OAAO,EAAE,MAAM;AACXlD,YAAAA,WAAW,CAAC,EAAE,GAAGhB,OAAL;AAAchG,cAAAA;AAAd,aAAD,CAAX;AACA+G,YAAAA,SAAS;AACZ,WAbL;AAAA,+CAeKf,OAfL,aAeKA,OAfL,uBAeKA,OAAO,CAAE9E,kBAfd,2EAeoC,GAfpC,eAgBI,QAAC,aAAD;AAAe,YAAA,aAAa,4BAAE8E,OAAF,aAAEA,OAAF,uBAAEA,OAAO,CAAEhF,cAAX,2EAA6B;AAAzD;AAAA;AAAA;AAAA;AAAA,kBAhBJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBADJ;AAoBH;AAjCI;AAHb,GA5C2B,EAmF3B;AACImJ,IAAAA,IAAI,EAAE,oBADV;AAEIC,IAAAA,KAAK,EAAE,YAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,eAAQ,GAAEyI,KAAM,GAAhB;AACH;AAZI;AAHb,GAnF2B,EAqG3B;AACIsB,IAAAA,IAAI,EAAE,wBADV;AAEIC,IAAAA,KAAK,EAAE,YAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,eAAQ,GAAEyI,KAAM,GAAhB;AACH;AAZI;AAHb,GArG2B,EAuH3B;AACIsB,IAAAA,IAAI,EAAE,2BADV;AAEIC,IAAAA,KAAK,EAAE,YAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,eAAQ,GAAEyI,KAAM,GAAhB;AACH;AAZI;AAHb,GAvH2B,EAyI3B;AACIsB,IAAAA,IAAI,EAAE,UADV;AAEIC,IAAAA,KAAK,EAAE,YAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,eAAOyI,KAAP;AACH;AAZI;AAHb,GAzI2B,EA2J3B;AACIsB,IAAAA,IAAI,EAAE,aADV;AAEIC,IAAAA,KAAK,EAAE,YAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,eAAOyI,KAAP;AACH;AAZI;AAHb,GA3J2B,EA6K3B;AACIsB,IAAAA,IAAI,EAAE,kBADV;AAEIC,IAAAA,KAAK,EAAE,gBAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGLoI,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AACD,eAAOZ,UAAU,CAACqJ,KAAD,CAAV,CAAkBnJ,OAAlB,CAA0B,CAA1B,CAAP;AACH;AAVI;AAHb,GA7K2B,EA6L3B;AACIyK,IAAAA,IAAI,EAAE,eADV;AAEIC,IAAAA,KAAK,EAAE,qBAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGLoI,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AAAA;;AACpC,cAAMrI,EAAE,GAAGuD,GAAG,CAAC,CAAD,CAAd;AACA,cAAM7D,WAAW,GAAGf,IAAI,CAACmC,IAAL,CAAWlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAAhC,CAApB;AACA,cAAM+G,MAAM,GAAG0C,KAAK,GAAGrJ,UAAU,2BAACV,WAAD,aAACA,WAAD,iDAACA,WAAW,CAAEsJ,YAAd,2DAAC,uBAA2Ba,WAA5B,2EAA2C,CAA3C,CAAjC;AACA,cAAMjD,OAAO,GAAGH,mBAAmB,CAAC3F,IAApB,CAA0BlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA/C,CAAhB;AACA,cAAMoB,YAAY,6BAAGwF,OAAH,aAAGA,OAAH,iDAAGA,OAAO,CAAEoC,YAAZ,2DAAG,uBAAuB5H,YAA1B,2EAA0C,EAA5D;AACA,eAAQ,GAAE+H,IAAI,CAACkC,IAAL,CAAUtE,MAAV,EAAkBzG,OAAlB,CAA0B,CAA1B,CAA6B,IAAGc,YAAa,EAAvD;AACH;AAVI;AAHb,GA7L2B,CAA/B;AA+MA,QAAMoK,aAAa,GAAG,CAClB;AACIT,IAAAA,IAAI,EAAE,IADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,KADH;AAEL1B,MAAAA,IAAI,EAAE,KAFD;AAGL0C,MAAAA,OAAO,EAAE;AAHJ;AAHb,GADkB,EAUlB;AACIuF,IAAAA,IAAI,EAAE,UADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE;AAHJ;AAHb,GAVkB,EAmBlB;AACIuF,IAAAA,IAAI,EAAE,YADV;AAEIC,IAAAA,KAAK,EAAE,QAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE;AAFD;AAHb,GAnBkB,EA2BlB;AACIiI,IAAAA,IAAI,EAAE,yBADV;AAEIC,IAAAA,KAAK,EAAE,cAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,IAHJ;AAIL0F,MAAAA,gBAAgB,EAAGzB,KAAD,iBAAW,QAAC,kBAAD;AAAoB,QAAA,KAAK,EAAEA;AAA3B;AAAA;AAAA;AAAA;AAAA;AAJxB;AAHb,GA3BkB,EAqClB;AACIsB,IAAAA,IAAI,EAAE,gBADV;AAEIC,IAAAA,KAAK,EAAE,OAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,IAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGC,gBAAgB,CAAC7G,EAAD,CAAhC;;AACA,YAAI,CAAC2G,kBAAkB,CAACC,OAAD,CAAvB,EAAkC;AAC9B,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AACD,4BACI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAEpB,YAAAA,OAAO,EAAE,MAAX;AAAmBqF,YAAAA,UAAU,EAAE,QAA/B;AAAyCD,YAAAA,cAAc,EAAE,KAAzD;AAAgElF,YAAAA,GAAG,EAAE;AAArE,WAAT;AAAA,kCACI,QAAC,kBAAD;AAAoB,YAAA,KAAK,EAAE+D;AAA3B;AAAA;AAAA;AAAA;AAAA,kBADJ,eAEI,QAAC,WAAD;AAAa,YAAA,SAAS,EAAErJ,UAAU,CAACqJ,KAAD,CAAV,GAAoB,CAA5C;AAA+C,YAAA,KAAK,EAAC;AAArD;AAAA;AAAA;AAAA;AAAA,kBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBADJ;AAMH;AAhBI;AAHb,GArCkB,EA2DlB;AACIsB,IAAAA,IAAI,EAAE,UADV;AAEIC,IAAAA,KAAK,EAAE,YAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGC,gBAAgB,CAAC7G,EAAD,CAAhC;;AACA,YAAI,CAAC2G,kBAAkB,CAACC,OAAD,CAAvB,EAAkC;AAC9B,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,4BAAO,QAAC,kBAAD;AAAoB,UAAA,KAAK,EAAE6C;AAA3B;AAAA;AAAA;AAAA;AAAA,gBAAP;AACH;AAZI;AAHb,GA3DkB,EA6ElB;AACIsB,IAAAA,IAAI,EAAE,aADV;AAEIC,IAAAA,KAAK,EAAE,YAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL0C,MAAAA,OAAO,EAAE,KAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,KAAsB;AACpC,cAAMrI,EAAE,GAAGqI,SAAS,CAACzB,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGE,iBAAiB,CAAChG,IAAlB,CAAwBlC,IAAD,IAAUA,IAAI,CAACoB,EAAL,KAAYA,EAA7C,CAAhB;;AACA,YAAI4G,OAAO,CAAC5F,YAAZ,EAA0B;AACtB,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,4BAAO,QAAC,kBAAD;AAAoB,UAAA,KAAK,EAAEyI;AAA3B;AAAA;AAAA;AAAA;AAAA,gBAAP;AACH;AAZI;AAHb,GA7EkB,EA+FlB;AACIsB,IAAAA,IAAI,EAAE,aADV;AAEIC,IAAAA,KAAK,EAAE,kBAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,KAFD;AAGL0C,MAAAA,OAAO,EAAE,IAHJ;AAIL0F,MAAAA,gBAAgB,EAAE,CAACzB,KAAD,EAAQpB,SAAR,kBAAsB,QAAC,aAAD;AAAe,QAAA,SAAS,EAAEA,SAA1B;AAAqC,QAAA,OAAO,EAAC;AAA7C;AAAA;AAAA;AAAA;AAAA;AAJnC;AAHb,GA/FkB,CAAtB;AA2GA,QAAMoD,aAAa,GAAG,CAClB;AACIV,IAAAA,IAAI,EAAE,WADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzF,MAAAA,OAAO,EAAE;AADJ;AAHb,GADkB,EAQlB;AACIuF,IAAAA,IAAI,EAAE,aADV;AAEIC,IAAAA,KAAK,EAAE;AAFX,GARkB,EAYlB;AACID,IAAAA,IAAI,EAAE,iBADV;AAEIC,IAAAA,KAAK,EAAE;AAFX,GAZkB,EAgBlB;AACID,IAAAA,IAAI,EAAE,iBADV;AAEIC,IAAAA,KAAK,EAAE;AAFX,GAhBkB,CAAtB;AAsBA,QAAM1F,+BAA+B,GAAG,CACpC;AACIyF,IAAAA,IAAI,EAAE,YADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL4I,MAAAA,kBAAkB,EAAE,OAAO;AAAE1B,QAAAA,KAAK,EAAE;AAAE9K,UAAAA,KAAK,EAAE,MAAT;AAAiByM,UAAAA,QAAQ,EAAE;AAA3B;AAAT,OAAP,CAHf;AAILC,MAAAA,YAAY,EAAE,OAAO;AAAE5B,QAAAA,KAAK,EAAE;AAAE9K,UAAAA,KAAK,EAAE,MAAT;AAAiByM,UAAAA,QAAQ,EAAE;AAA3B;AAAT,OAAP;AAJT;AAHb,GADoC,EAWpC;AACIZ,IAAAA,IAAI,EAAE,cADV;AAEIC,IAAAA,KAAK,EAAE,UAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL4I,MAAAA,kBAAkB,EAAE,OAAO;AAAE1B,QAAAA,KAAK,EAAE;AAAE6B,UAAAA,QAAQ,EAAE,OAAZ;AAAqBC,UAAAA,UAAU,EAAE;AAAjC;AAAT,OAAP,CAHf;AAILF,MAAAA,YAAY,EAAE,OAAO;AAAE5B,QAAAA,KAAK,EAAE;AAAE6B,UAAAA,QAAQ,EAAE,OAAZ;AAAqBC,UAAAA,UAAU,EAAE;AAAjC;AAAT,OAAP,CAJT;AAKLZ,MAAAA,gBAAgB,EAAGzB,KAAD,iBACd,QAAC,UAAD;AAAY,QAAA,EAAE,EAAE;AAAEqC,UAAAA,UAAU,EAAE;AAAd,SAAhB;AAAA,+BACI;AAAA,oBAASrC;AAAT;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AANC;AAHb,GAXoC,EA0BpC;AACIsB,IAAAA,IAAI,EAAE,YADV;AAEIC,IAAAA,KAAK,EAAE,SAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGL4I,MAAAA,kBAAkB,EAAE,OAAO;AAAE1B,QAAAA,KAAK,EAAE;AAAE6B,UAAAA,QAAQ,EAAE,OAAZ;AAAqBC,UAAAA,UAAU,EAAE;AAAjC;AAAT,OAAP,CAHf;AAILF,MAAAA,YAAY,EAAE,OAAO;AAAE5B,QAAAA,KAAK,EAAE;AAAE6B,UAAAA,QAAQ,EAAE,OAAZ;AAAqBC,UAAAA,UAAU,EAAE;AAAjC;AAAT,OAAP,CAJT;AAKLZ,MAAAA,gBAAgB,EAAGzB,KAAD,IAAW;AACzB,cAAMsC,SAAS,GAAGtC,KAAlB;;AAEA,YAAI,CAACsC,SAAD,IAAc,CAACA,SAAS,CAACC,sBAA7B,EAAqD;AACjD,8BAAO,QAAC,UAAD;AAAY,YAAA,EAAE,EAAE;AAAEF,cAAAA,UAAU,EAAE,QAAd;AAAwB9H,cAAAA,SAAS,EAAE;AAAnC,aAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,cAAMiI,UAAU,GAAG7L,UAAU,CAAC2L,SAAS,CAACC,sBAAX,CAA7B;AAEA,4BAAO,QAAC,UAAD;AAAY,UAAA,EAAE,EAAE;AAAEF,YAAAA,UAAU,EAAE,QAAd;AAAwB9H,YAAAA,SAAS,EAAE;AAAnC,WAAhB;AAAA,qBAAgEiI,UAAU,CAAC3L,OAAX,CAAmB,CAAnB,CAAhE;AAAA;AAAA;AAAA;AAAA;AAAA,gBAAP;AACH;AAfI;AAHb,GA1BoC,EA+CpC;AACIyK,IAAAA,IAAI,EAAE,gBADV;AAEIC,IAAAA,KAAK,EAAE,kBAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGLoI,MAAAA,gBAAgB,EAAGzB,KAAD,iBAAW,QAAC,kBAAD;AAAoB,QAAA,KAAK,EAAEA;AAA3B;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GA/CoC,EAwDpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIsB,IAAAA,IAAI,EAAE,iBADV;AAEIC,IAAAA,KAAK,EAAE,mBAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGLoI,MAAAA,gBAAgB,EAAGzB,KAAD,iBAAW,QAAC,kBAAD;AAAoB,QAAA,KAAK,EAAEA;AAA3B;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GAtEoC,EA+EpC;AACIsB,IAAAA,IAAI,EAAE,cADV;AAEIC,IAAAA,KAAK,EAAE,cAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGLoI,MAAAA,gBAAgB,EAAGzB,KAAD,iBAAW,QAAC,UAAD;AAAA,kBAAaA;AAAb;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GA/EoC,EAwFpC;AACIsB,IAAAA,IAAI,EAAE,iBADV;AAEIC,IAAAA,KAAK,EAAE,qBAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGLoI,MAAAA,gBAAgB,EAAGzB,KAAD,iBAAW,QAAC,UAAD;AAAA,kBAAaA;AAAb;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GAxFoC,CAAxC;AAmGA,QAAMpE,sBAAsB,GAAG,CAC3B;AACI0F,IAAAA,IAAI,EAAE,IADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,KADH;AAEL1B,MAAAA,IAAI,EAAE,KAFD;AAGL0C,MAAAA,OAAO,EAAE;AAHJ;AAHb,GAD2B,EAU3B;AACIuF,IAAAA,IAAI,EAAE,YADV;AAEIC,IAAAA,KAAK,EAAE,QAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGLoI,MAAAA,gBAAgB,EAAGzB,KAAD,iBAAW,QAAC,UAAD;AAAY,QAAA,EAAE,EAAE;AAAExE,UAAAA,QAAQ,EAAE,QAAZ;AAAsBC,UAAAA,UAAU,EAAE;AAAlC,SAAhB;AAAA,kBAA6DuE;AAA7D;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GAV2B,EAmB3B;AACIsB,IAAAA,IAAI,EAAE,OADV;AAEIC,IAAAA,KAAK,EAAE,OAFX;AAGIC,IAAAA,OAAO,EAAE;AACLzG,MAAAA,MAAM,EAAE,IADH;AAEL1B,MAAAA,IAAI,EAAE,IAFD;AAGLoI,MAAAA,gBAAgB,EAAGzB,KAAD,iBACd,QAAC,UAAD;AAAY,QAAA,EAAE,EAAE;AAAExE,UAAAA,QAAQ,EAAE,QAAZ;AAAsBC,UAAAA,UAAU,EAAE,QAAlC;AAA4ClB,UAAAA,SAAS,EAAE;AAAvD,SAAhB;AAAA,kBACK5D,UAAU,CAACqJ,KAAD,CAAV,CAAkBnJ,OAAlB,CAA0B,CAA1B;AADL;AAAA;AAAA;AAAA;AAAA;AAJC;AAHb,GAnB2B,CAA/B;;AAkCA,QAAM4L,UAAU,GAAG,MAAM;AACrB,QAAI9F,QAAQ,KAAK1I,YAAjB,EAA+B;AAC3B,aAAO2H,sBAAP;AACH;;AAED,QAAIc,gBAAJ,EAAsB;AAClB,aAAOoF,sBAAP;AACH;;AACD,QAAIhE,OAAJ,EAAa;AACT,aAAO/D,OAAP;AACH;;AACD,WAAOgI,aAAP;AACH,GAZD;;AAcA,QAAMW,YAAY,GAAG,MAAM;AACvB,QAAIhG,gBAAgB,IAAIoB,OAAxB,EAAiC;AAC7B,aAAO,MAAP;AACH;;AACD,QAAIpB,gBAAgB,IAAI,CAACoB,OAAzB,EAAkC;AAC9B,aAAO,KAAP;AACH;;AACD,QAAI,CAACpB,gBAAD,IAAqBoB,OAAzB,EAAkC;AAC9B,aAAO,KAAP;AACH;;AACD,WAAO,KAAP;AACH,GAXD;;AAaA,sBACI,QAAC,GAAD;AAAK,IAAA,EAAE,EAAE;AAAE6E,MAAAA,EAAE,EAAE,CAAN;AAASC,MAAAA,EAAE,EAAE,CAAb;AAAgBC,MAAAA,EAAE,EAAE,CAApB;AAAuBC,MAAAA,EAAE,EAAE,CAA3B;AAA8BlN,MAAAA,eAAe,EAAE;AAA/C,KAAT;AAAA,2BACI,QAAC,WAAD;AAAa,MAAA,OAAO,EAAEmI,OAAtB;AAAA,6BACI,QAAC,GAAD;AAAK,QAAA,EAAE,EAAE;AAAEhC,UAAAA,OAAO,EAAE,MAAX;AAAmBE,UAAAA,GAAG,EAAE,CAAxB;AAA2BD,UAAAA,aAAa,EAAE,KAA1C;AAAiDvG,UAAAA,KAAK,EAAE,MAAxD;AAAgE0L,UAAAA,cAAc,EAAE;AAAhF,SAAT;AAAA,gCACI,QAAC,UAAD;AACI,UAAA,KAAK,EAAExE,QAAQ,KAAK1I,YAAb,GAA4B,KAA5B,GAAoCyO,YAAY,EAD3D;AAEI,UAAA,EAAE,EAAE;AACA,gCAAoB;AAChBjN,cAAAA,KAAK,EAAE,iBADS;AAEhB8G,cAAAA,WAAW,EAAE;AAFG,aADpB;AAKA,oCAAwB;AACpBb,cAAAA,OAAO,EAAE;AADW;AALxB,WAFR;AAAA,iCAYI,QAAC,IAAD;AACI,YAAA,OAAO,EAAE+G,UAAU,EADvB;AAEI,YAAA,IAAI,EAAEpF,iBAFV;AAGI,YAAA,KAAK,EAAC,wBAHV;AAII,YAAA,OAAO,EAAE;AACL1C,cAAAA,MAAM,EAAE,KADH;AAELC,cAAAA,QAAQ,EAAE,KAFL;AAGLC,cAAAA,KAAK,EAAE,KAHF;AAILxB,cAAAA,IAAI,EAAE,KAJD;AAKLyB,cAAAA,WAAW,EAAE,IALR;AAMLC,cAAAA,MAAM,EAAE,KANH;AAOLgI,cAAAA,UAAU,EAAE,aAPP;AAQL/H,cAAAA,UAAU,EAAE,UARP;AASLC,cAAAA,WAAW,EAAE,IATR;AAUL+H,cAAAA,iBAAiB,EAAE,IAVd;AAWLC,cAAAA,UAAU,EAAE,KAXP;AAYLC,cAAAA,gBAAgB,EAAE,KAZb;AAaLC,cAAAA,gBAAgB,EAAE;AACdC,gBAAAA,OAAO,EAAE;AADK,eAbb;AAgBLC,cAAAA,UAAU,EAAE,IAhBP;AAiBLnI,cAAAA,cAAc,EAAE,MAjBX;AAkBLoI,cAAAA,qBAAqB,EAAE,KAlBlB;AAmBLnI,cAAAA,UAAU,EAAE,KAnBP;AAoBLoI,cAAAA,cAAc,EAAE,KApBX;AAqBLC,cAAAA,QAAQ,EAAE,IArBL;AAsBLpI,cAAAA,OAAO,EAAE;AAtBJ;AAJb;AAAA;AAAA;AAAA;AAAA;AAZJ;AAAA;AAAA;AAAA;AAAA,gBADJ,EA2CKuB,QAAQ,KAAK1I,YAAb,iBACG,QAAC,UAAD;AACI,UAAA,KAAK,EAAC,KADV;AAEI,UAAA,EAAE,EAAE;AACA,gCAAoB;AAChBwB,cAAAA,KAAK,EAAE,iBADS;AAEhB8G,cAAAA,WAAW,EAAE;AAFG,aADpB;AAKA,oCAAwB;AACpBb,cAAAA,OAAO,EAAE;AADW;AALxB,WAFR;AAAA,iCAYI,QAAC,UAAD;AACI,YAAA,OAAO,EAAEG,+BADb;AAEI,YAAA,IAAI,EAAEpH,eAFV;AAGI,YAAA,KAAK,EAAC,qBAHV;AAII,YAAA,mBAAmB,EAAG+H,KAAD,iBACjB,QAAC,2BAAD,OACQA,KADR;AAEI,cAAA,IAAI,EAAEtH,IAFV;AAGI,cAAA,sBAAsB,EAAE0G,sBAH5B;AAII,cAAA,+BAA+B,EAAEC;AAJrC;AAAA;AAAA;AAAA;AAAA,oBALR;AAYI,YAAA,OAAO,EAAE;AACLlB,cAAAA,MAAM,EAAE,KADH;AAELC,cAAAA,QAAQ,EAAE,KAFL;AAGLC,cAAAA,KAAK,EAAE,KAHF;AAILxB,cAAAA,IAAI,EAAE,KAJD;AAKLyB,cAAAA,WAAW,EAAE,IALR;AAMLC,cAAAA,MAAM,EAAE,KANH;AAOLgI,cAAAA,UAAU,EAAE,aAPP;AAQL/H,cAAAA,UAAU,EAAE,UARP;AASLC,cAAAA,WAAW,EAAE,IATR;AAUL+H,cAAAA,iBAAiB,EAAE,IAVd;AAWLC,cAAAA,UAAU,EAAE,KAXP;AAYLC,cAAAA,gBAAgB,EAAE,KAZb;AAaLC,cAAAA,gBAAgB,EAAE;AACdC,gBAAAA,OAAO,EAAE;AADK,eAbb;AAgBLC,cAAAA,UAAU,EAAE,KAhBP;AAiBLnI,cAAAA,cAAc,EAAE,MAjBX;AAkBLoI,cAAAA,qBAAqB,EAAE,KAlBlB;AAmBLnI,cAAAA,UAAU,EAAE,KAnBP;AAoBLoI,cAAAA,cAAc,EAAE,KApBX;AAqBLC,cAAAA,QAAQ,EAAE,IArBL;AAsBLpI,cAAAA,OAAO,EAAE,KAtBJ;AAuBLC,cAAAA,aAAa,EAAE,OAAO;AAClBC,gBAAAA,IAAI,EAAE;AADY,eAAP;AAvBV;AAZb;AAAA;AAAA;AAAA;AAAA;AAZJ;AAAA;AAAA;AAAA;AAAA,gBA5CR;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,UADJ;AAyGH;AACD;;AAA0B;;AAAqB;;IA3uCvBmB,c;UACH9J,W,EAC8DC,W,EACnDA,W,EAwCgBiB,U;;;MA3CxB4I,c;AA2uC2C;;AAAC,SAASgH,KAAT,GAAgB;AAAC,MAAG;AAAC,WAAO,CAAC,GAAEC,IAAH,EAAS,2BAAT,KAAyC,CAAC,GAAEA,IAAH,EAAS,onuCAAT,CAAhD;AAAgruC,GAApruC,CAAoruC,OAAMC,CAAN,EAAQ,CAAE;AAAC;;AAAA;AAAC;;AAA0B,SAASC,KAAT;AAAe;AAAgBC,CAA/B,EAAsD;AAAA,oCAAFC,CAAE;AAAFA,IAAAA,CAAE;AAAA;;AAAC,MAAG;AAACL,IAAAA,KAAK,GAAGM,UAAR,CAAmBF,CAAnB,EAAsBC,CAAtB;AAA0B,GAA9B,CAA8B,OAAMH,CAAN,EAAQ,CAAE;;AAAC,SAAOG,CAAP;AAAS;;AAAA;AAAC;;AAA0B,SAASE,KAAT;AAAe;AAAgBH,CAA/B,EAAsD;AAAA,qCAAFC,CAAE;AAAFA,IAAAA,CAAE;AAAA;;AAAC,MAAG;AAACL,IAAAA,KAAK,GAAGQ,YAAR,CAAqBJ,CAArB,EAAwBC,CAAxB;AAA4B,GAAhC,CAAgC,OAAMH,CAAN,EAAQ,CAAE;;AAAC,SAAOG,CAAP;AAAS;;AAAA;AAAC;;AAA0B,SAASI,KAAT;AAAe;AAAgBL,CAA/B,EAAsD;AAAA,qCAAFC,CAAE;AAAFA,IAAAA,CAAE;AAAA;;AAAC,MAAG;AAACL,IAAAA,KAAK,GAAGU,YAAR,CAAqBN,CAArB,EAAwBC,CAAxB;AAA4B,GAAhC,CAAgC,OAAMH,CAAN,EAAQ,CAAE;;AAAC,SAAOG,CAAP;AAAS;;AAAA;AAAC;;AAA0B,SAASM,KAAT;AAAe;AAAgBN,CAA/B,EAAiC;AAAC,MAAG;AAACL,IAAAA,KAAK,GAAGY,WAAR,CAAoBP,CAApB;AAAwB,GAA5B,CAA4B,OAAMH,CAAN,EAAQ,CAAE;;AAAC,SAAOG,CAAP;AAAU;;AAAA;AAAC;;AAA0B,SAASQ,KAAT;AAAe;AAAgBR,CAA/B;AAAkC;AAAgBD,CAAlD,EAAoD;AAAC,MAAG;AAACJ,IAAAA,KAAK,GAAGc,cAAR,CAAuBT,CAAvB,EAA0BD,CAA1B;AAA8B,GAAlC,CAAkC,OAAMF,CAAN,EAAQ,CAAE;;AAAC,SAAOG,CAAP;AAAU;;AAAA;AAAC", "sourcesContent": ["import {\r\n    Box,\r\n    IconButton,\r\n    Paper,\r\n    Table,\r\n    TableBody,\r\n    TableCell,\r\n    TableContainer,\r\n    TableHead,\r\n    TableRow,\r\n    TextField,\r\n    Tooltip,\r\n    Typography\r\n} from '@mui/material';\r\nimport React, { useEffect, useState } from 'react';\r\nimport { useDispatch, useSelector } from 'store';\r\nimport StoreIcon from '@mui/icons-material/Store';\r\nimport AddCircleIcon from '@mui/icons-material/AddCircle';\r\nimport RemoveCircleIcon from '@mui/icons-material/RemoveCircle';\r\nimport {\r\n    addToCart,\r\n    editToCart,\r\n    getRepositionDataByProduct,\r\n    openRotationModal,\r\n    removeFromCart,\r\n    setSelectedProduct,\r\n    updateFormDataItem,\r\n    updateFormSupplyDataItem\r\n} from 'store/slices/reposition/reposition';\r\nimport ErrorIcon from '@mui/icons-material/Error';\r\nimport { addDays, parseDateToLocaleString } from 'utils/dates';\r\nimport Grid from 'ui-component/grid/Grid';\r\nimport AutoModeIcon from '@mui/icons-material/AutoMode';\r\nimport useLoading from 'hooks/useLoading';\r\nimport { BlockLoader } from 'ui-component/loaders/loaders';\r\nimport SIANLink from 'ui-component/SIAN/SIANLink';\r\nimport { FOOD_VALUE, RAW_MATERIAL } from 'models/Reposition';\r\nimport RightAlignedNumber from 'ui-component/grid/RightAlignedNumber';\r\nimport { stickyColumn } from 'ui-component/grid/Grid';\r\nimport DisplayCurrency from 'ui-component/display/DisplayCurrency';\r\nimport { UNIT_EQUIVALENCE } from 'models/Presentation';\r\nimport { MenuItem } from '@mui/material';\r\nimport NestedGrid from 'ui-component/grid/NestedGrid';\r\n\r\nconst findDerivedProductRecursively = (derivedProducts, targetProductId) => {\r\n    if (!derivedProducts || !Array.isArray(derivedProducts)) return null;\r\n\r\n    for (const derived of derivedProducts) {\r\n        if (derived.product_id === targetProductId) {\r\n            return derived;\r\n        }\r\n\r\n        if (derived.derivedProducts && derived.derivedProducts.length > 0) {\r\n            const found = findDerivedProductRecursively(derived.derivedProducts, targetProductId);\r\n            if (found) return found;\r\n        }\r\n    }\r\n\r\n    return null;\r\n};\r\n\r\nconst findMainProductWithDerivedProduct = (data, targetProductId) => {\r\n    if (!data || !Array.isArray(data)) return null;\r\n\r\n    for (const item of data) {\r\n        const found = findDerivedProductRecursively(item.derivedProducts, targetProductId);\r\n        if (found) return item;\r\n    }\r\n\r\n    return null;\r\n};\r\n\r\nconst NumberAlert = ({ condition = false, title = '' }) => {\r\n    if (!condition) {\r\n        return null;\r\n    }\r\n    return (\r\n        <Tooltip title={title}>\r\n            <IconButton color=\"error\">\r\n                <ErrorIcon />\r\n            </IconButton>\r\n        </Tooltip>\r\n    );\r\n};\r\n\r\nconst AlertRotation = ({ rotationScale }) => {\r\n    switch (rotationScale) {\r\n        case 'AR':\r\n            return (\r\n                <Tooltip title=\"El producto tiene ALTA ROTACIÓN\">\r\n                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#c6e0b4' }}>&nbsp;</Box>\r\n                </Tooltip>\r\n            );\r\n        case 'MR':\r\n            return (\r\n                <Tooltip title=\"El producto tiene MEDIA ROTACIÓN\">\r\n                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#ffe699' }}>&nbsp;</Box>\r\n                </Tooltip>\r\n            );\r\n        case 'BR':\r\n            return (\r\n                <Tooltip title=\"El producto tiene BAJA ROTACIÓN\">\r\n                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#b11226' }}>&nbsp;</Box>\r\n                </Tooltip>\r\n            );\r\n        default:\r\n            return (\r\n                <Tooltip title=\"El producto NO tiene ROTACIÓN\">\r\n                    <Box\r\n                        sx={{\r\n                            width: 20,\r\n                            height: 20,\r\n                            borderRadius: '99999px',\r\n                            backgroundColor: 'white',\r\n                            border: '1px solid'\r\n                        }}\r\n                    >\r\n                        &nbsp;\r\n                    </Box>\r\n                </Tooltip>\r\n            );\r\n    }\r\n};\r\n\r\nconst AlertBreak = ({ break_scale = 'QB' }) => {\r\n    switch (break_scale) {\r\n        case 'SS':\r\n            return (\r\n                <Tooltip title=\"El punto de Quiebre es SOBRE STOCK\">\r\n                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#a777dd' }}>&nbsp;</Box>\r\n                </Tooltip>\r\n            );\r\n        case 'OPT':\r\n            return (\r\n                <Tooltip title=\"El punto de Quiebre es OPTIMO\">\r\n                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#c6e0b4' }}>&nbsp;</Box>\r\n                </Tooltip>\r\n            );\r\n        case 'MOD':\r\n            return (\r\n                <Tooltip title=\"El punto de Quiebre es MODERADO\">\r\n                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#ffe699' }}>&nbsp;</Box>\r\n                </Tooltip>\r\n            );\r\n        case 'CRI':\r\n            return (\r\n                <Tooltip title=\"El punto de Quiebre es CRÍTICO\">\r\n                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#b11226' }}>&nbsp;</Box>\r\n                </Tooltip>\r\n            );\r\n        case 'QB':\r\n            return (\r\n                <Tooltip title=\"El punto de Quiebre esta QUEBRADO\">\r\n                    <Box\r\n                        sx={{\r\n                            width: 20,\r\n                            height: 20,\r\n                            borderRadius: '99999px',\r\n                            backgroundColor: 'white',\r\n                            border: '1px solid'\r\n                        }}\r\n                    >\r\n                        &nbsp;\r\n                    </Box>\r\n                </Tooltip>\r\n            );\r\n        default:\r\n            return (\r\n                <Tooltip title=\"El punto de Quiebre esta QUEBRADO\">\r\n                    <Box\r\n                        sx={{\r\n                            width: 20,\r\n                            height: 20,\r\n                            borderRadius: '99999px',\r\n                            backgroundColor: 'white',\r\n                            border: '1px solid'\r\n                        }}\r\n                    >\r\n                        &nbsp;\r\n                    </Box>\r\n                </Tooltip>\r\n            );\r\n    }\r\n};\r\n\r\nconst processAnalisys = (productData, stores) => {\r\n    if (!productData || !productData.analisys) return null;\r\n\r\n    const listData = productData.analisys.map((item, index) => {\r\n        const pk = `${item.product_id}-${item.store_id}`;\r\n        return {\r\n            ...item,\r\n            rowIndex: index,\r\n            quantity: parseFloat(item.unit_quantity_order).toFixed(4),\r\n            unit_quantity: parseFloat(item.unit_quantity_order),\r\n            unit_price: parseFloat(item.unit_price),\r\n            default_unit_price: parseFloat(item.unit_price),\r\n            provider: item.provider,\r\n            pk\r\n        };\r\n    });\r\n\r\n    stores.forEach((store) => {\r\n        const existingItem = listData.find((item) => item.store_id === store.store_id);\r\n\r\n        if (!existingItem) {\r\n            listData.push({\r\n                notAvailable: true,\r\n                indicator_calculation_id: 4,\r\n                store_id: store.store_id,\r\n                store_name: store.store_name,\r\n                product_id: productData.product_id,\r\n                product_name: productData.product_name,\r\n                measure_name: productData.measure_name,\r\n                equivalence_default: productData.equivalence_default,\r\n                measure_default: productData.measure_default,\r\n                provider_id: productData.provider_id,\r\n                provider_number: productData.provider_number,\r\n                provider: productData.provider,\r\n                warehouse_id: store.warehouse_id,\r\n                unit_price: productData.unit_price,\r\n                expires: 0,\r\n                vcto_alert: '-',\r\n                rotation_scale: '-',\r\n                rotation_value: 0,\r\n                rotation_indicator: 'NR(0)',\r\n                rotation_color: '',\r\n                obsolete: 0,\r\n                obsolete_indicator: '-',\r\n                pareto_percentage_sale: '0.0',\r\n                pareto_percentage_utility: '0.0',\r\n                stock: '0.00',\r\n                to_enter: '0.00',\r\n                to_dispatch: '0.00',\r\n                purchase_stock: '0.00',\r\n                average_quantity: '0.00',\r\n                average_diary: '0.0000',\r\n                inventory_days: 0,\r\n                break_value: 0,\r\n                break_scale: '-',\r\n                min_stock: 0,\r\n                reposition_stock: 0,\r\n                unit_quantity_order: 0,\r\n                rowIndex: 0,\r\n                quantity: '0',\r\n                unit_quantity: 0,\r\n                default_unit_price: productData.default_unit_price,\r\n                pk: `${productData.product_id}-${store.store_name}`\r\n            });\r\n        }\r\n    });\r\n\r\n    listData.sort((a, b) => a.warehouse_id - b.warehouse_id);\r\n\r\n    return listData;\r\n};\r\n\r\nconst getDerivedProducts = (productData) => {\r\n    if (!productData || !productData.derivedProducts) return [];\r\n\r\n    return productData.derivedProducts.map((item, index) => ({\r\n        ...item,\r\n        pk: item.product_id,\r\n        globalIndex: index\r\n    }));\r\n};\r\n\r\nconst NestedCard = ({ children, width = '50%' }) => (\r\n    <Box sx={{ p: 1, backgroundColor: 'white', borderRadius: '1rem', border: '1px solid #e0e0e0', width }}>{children}</Box>\r\n);\r\n\r\nconst DerivedProductAnalysis = ({ row, columns }) => {\r\n    const productId = row[0];\r\n    const { data } = useSelector((state) => state.reposition);\r\n    const { data: storeData } = useSelector((state) => state.store);\r\n    const mainProductData = findMainProductWithDerivedProduct(data, productId);\r\n    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);\r\n    const derivedAnalysis = derivedProduct?.analisys ? processAnalisys(derivedProduct, storeData) : null;\r\n\r\n    if (!derivedAnalysis || derivedAnalysis.length === 0) {\r\n        return (\r\n            <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>\r\n                <Typography variant=\"body2\">No hay análisis disponible para este producto derivado</Typography>\r\n            </Box>\r\n        );\r\n    }\r\n\r\n    const DerivedAnalysisGrid = () => {\r\n        const [repositionDerivedProduct] = useState(derivedAnalysis);\r\n\r\n        return (\r\n            <Grid\r\n                columns={columns}\r\n                title=\"Análisis por Tienda\"\r\n                data={repositionDerivedProduct}\r\n                options={{\r\n                    search: false,\r\n                    download: false,\r\n                    print: false,\r\n                    sort: false,\r\n                    viewColumns: false,\r\n                    filter: false,\r\n                    responsive: 'vertical',\r\n                    fixedHeader: false,\r\n                    selectableRows: 'none',\r\n                    pagination: false,\r\n                    toolbar: false,\r\n                    setTableProps: () => ({\r\n                        size: 'small',\r\n                        sx: {\r\n                            '& .MuiTableHead-root .MuiTableCell-root': {\r\n                                fontSize: '0.875rem',\r\n                                fontWeight: 'bold',\r\n                                padding: '8px 16px'\r\n                            },\r\n                            '& .MuiTableBody-root .MuiTableCell-root': {\r\n                                fontSize: '1.2rem',\r\n                                padding: '36px 16px'\r\n                            }\r\n                        }\r\n                    })\r\n                }}\r\n            />\r\n        );\r\n    };\r\n\r\n    return (\r\n        <Box sx={{ width: '100%', height: 'fit-content' }}>\r\n            <Box\r\n                sx={{\r\n                    p: 2,\r\n                    backgroundColor: 'white',\r\n                    borderRadius: '1rem',\r\n                    border: '1px solid #e0e0e0',\r\n                    '& .MuiTableHead-root .MuiTableCell-root': {\r\n                        fontSize: '0.875rem',\r\n                        fontWeight: 'bold',\r\n                        padding: '8px 16px'\r\n                    },\r\n                    '& .MuiTableBody-root .MuiTableCell-root': {\r\n                        fontSize: '1.2rem',\r\n                        padding: '36px 16px'\r\n                    }\r\n                }}\r\n            >\r\n                <DerivedAnalysisGrid />\r\n            </Box>\r\n        </Box>\r\n    );\r\n};\r\n\r\nconst DerivedProductNestedContent = ({ row, data, derivedAnalysisColumns, simplifiedDerivedProductColumns }) => {\r\n    const productId = row[0];\r\n    const mainProductData = findMainProductWithDerivedProduct(data, productId);\r\n    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);\r\n    const hasSubDerived = derivedProduct?.derivedProducts && derivedProduct.derivedProducts.length > 0;\r\n\r\n    return (\r\n        <Box sx={{ display: 'flex', flexDirection: 'row', gap: 2, py: 1 }}>\r\n            <Box sx={{ width: hasSubDerived ? '25%' : '100%' }}>\r\n                <DerivedProductAnalysis row={row} columns={derivedAnalysisColumns} />\r\n            </Box>\r\n            {hasSubDerived && (\r\n                <Box sx={{ width: '75%' }}>\r\n                    <SubDerivedProducts row={row} columns={simplifiedDerivedProductColumns} analysisColumns={derivedAnalysisColumns} />\r\n                </Box>\r\n            )}\r\n        </Box>\r\n    );\r\n};\r\n\r\nconst SubDerivedProducts = ({ row, columns, analysisColumns }) => {\r\n    const productId = row[0];\r\n    const { data } = useSelector((state) => state.reposition);\r\n    const mainProductData = findMainProductWithDerivedProduct(data, productId);\r\n    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);\r\n    const rawSubDerivedProducts = derivedProduct?.derivedProducts || [];\r\n\r\n    const subDerivedProducts = rawSubDerivedProducts.map((item, index) => ({\r\n        ...item,\r\n        pk: item.product_id,\r\n        globalIndex: index\r\n    }));\r\n\r\n    if (subDerivedProducts.length === 0) {\r\n        return (\r\n            <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>\r\n                <Typography variant=\"body2\">No hay productos derivados adicionales</Typography>\r\n            </Box>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <Box sx={{ width: '100%', height: 'fit-content' }}>\r\n            <Box\r\n                sx={{\r\n                    p: 1,\r\n                    backgroundColor: 'white',\r\n                    borderRadius: '1rem',\r\n                    border: '1px solid #e0e0e0',\r\n                    width: '100%',\r\n                    '& .MuiTable-root': {\r\n                        width: '100% !important',\r\n                        tableLayout: 'fixed'\r\n                    },\r\n                    '& .MuiTableCell-root': {\r\n                        padding: '8px 16px'\r\n                    }\r\n                }}\r\n            >\r\n                <NestedGrid\r\n                    columns={columns}\r\n                    data={subDerivedProducts}\r\n                    RenderNestedContent={(props) => <DerivedProductAnalysis {...props} columns={analysisColumns || columns} />}\r\n                    options={{\r\n                        search: false,\r\n                        download: false,\r\n                        print: false,\r\n                        sort: false,\r\n                        viewColumns: false,\r\n                        filter: false,\r\n                        responsive: 'vertical',\r\n                        fixedHeader: false,\r\n                        selectableRows: 'none',\r\n                        pagination: false,\r\n                        toolbar: false,\r\n                        setTableProps: () => ({\r\n                            size: 'small'\r\n                        })\r\n                    }}\r\n                />\r\n            </Box>\r\n        </Box>\r\n    );\r\n};\r\n\r\nexport default function RotationDetail({ row, isFromProyection = null, foodMode = null }) {\r\n    const dispatch = useDispatch();\r\n    const { data, formData, cart, filters, merchandiseFoodData, formSupplyData } = useSelector((state) => state.reposition);\r\n    const { data: storeData } = useSelector((state) => state.store);\r\n\r\n    const isRowDataAvailable = (rowData) => {\r\n        return rowData && !rowData.notAvailable;\r\n    };\r\n\r\n    const getRowDataSafely = (pk) => {\r\n        try {\r\n            const rowData = repositionProduct.find((item) => item.pk === pk);\r\n            const result = rowData || { notAvailable: true };\r\n            return result;\r\n        } catch (error) {\r\n            return { notAvailable: true };\r\n        }\r\n    };\r\n\r\n    const renderSafeContent = (pk, renderFunction) => {\r\n        try {\r\n            const rowData = getRowDataSafely(pk);\r\n            if (!isRowDataAvailable(rowData)) {\r\n                return <Typography color=\"gray\">-</Typography>;\r\n            }\r\n            return renderFunction(rowData);\r\n        } catch (error) {\r\n            console.warn('Error al renderizar contenido para pk:', pk, error);\r\n            return <Typography color=\"gray\">-</Typography>;\r\n        }\r\n    };\r\n\r\n    const supplyAnalisys = processAnalisys(\r\n        isFromProyection ? merchandiseFoodData.find((item) => item.product_id === row[0]) : data.find((item) => item.product_id === row[0]),\r\n        storeData\r\n    );\r\n    const productData = isFromProyection\r\n        ? merchandiseFoodData.find((item) => item.product_id === row[0])\r\n        : data.find((item) => item.product_id === row[0]);\r\n    const derivedProducts = getDerivedProducts(productData);\r\n\r\n    const [repositionProduct, setRepositionProduct] = useState(supplyAnalisys || []);\r\n    const [isAsync] = useState(!supplyAnalisys);\r\n    const [loading, startLoading, endLoading] = useLoading(isAsync);\r\n\r\n    const openModal = () => dispatch(openRotationModal());\r\n    const setSelected = (data) => dispatch(setSelectedProduct(data));\r\n\r\n    const reload = () => {\r\n        if (isAsync) {\r\n            startLoading();\r\n            dispatch(getRepositionDataByProduct(row[0], { ...filters, mode: isFromProyection ? 'Merc2' : filters.mode }, storeData)).then(\r\n                (data) => {\r\n                    setRepositionProduct(data);\r\n                    endLoading();\r\n                }\r\n            );\r\n        }\r\n    };\r\n\r\n    const handleAddToCart = (item) => {\r\n        dispatch(addToCart(item));\r\n    };\r\n\r\n    const handleRemoveFromCart = (pk) => {\r\n        dispatch(removeFromCart(pk));\r\n    };\r\n\r\n    const handleEditCart = (pk, updatedData) => {\r\n        dispatch(editToCart(pk, updatedData));\r\n    };\r\n\r\n    useEffect(() => {\r\n        reload();\r\n    }, []);\r\n\r\n    const QuantityInput = ({ tableMeta: { rowData: rowMetadata }, keyword = 'quantity_oc' }) => {\r\n        const pk = rowMetadata[0];\r\n\r\n        const rowData = filters.mode === FOOD_VALUE ? formSupplyData[pk] : formData[pk];\r\n        const cartRowData = cart[pk];\r\n        const repositionProductData = repositionProduct.find((item) => item.pk === pk);\r\n\r\n        const getQuantity = (cartRowData, rowData, keyword) => {\r\n            if (cartRowData && cartRowData[keyword] !== undefined) {\r\n                return cartRowData[keyword];\r\n            }\r\n            if (rowData && rowData[keyword] !== undefined) {\r\n                return rowData[keyword];\r\n            }\r\n            return 0;\r\n        };\r\n\r\n        const [numberInput, setNumberInput] = useState(getQuantity(cartRowData, rowData, keyword));\r\n\r\n        useEffect(() => {\r\n            const quantity = getQuantity(cartRowData, rowData, keyword);\r\n            setNumberInput(parseFloat(quantity).toFixed(2));\r\n        }, [rowData?.[keyword], cartRowData?.[keyword]]);\r\n\r\n        const handleBlur = () => {\r\n            const newValue = repositionProductData?.presentation?.allowDecimals === 1 ? numberInput : parseFloat(numberInput);\r\n            const fixedValue = Math.floor(newValue).toFixed(2) || 0;\r\n            setNumberInput(fixedValue);\r\n\r\n            const updateAction = filters.mode === FOOD_VALUE ? updateFormSupplyDataItem : updateFormDataItem;\r\n            dispatch(\r\n                updateAction(pk, {\r\n                    [keyword]: fixedValue,\r\n                    hasTouch: true,\r\n                    product_id: repositionProductData.product_id,\r\n                    store_id: repositionProductData.store_id\r\n                })\r\n            );\r\n\r\n            if (cartRowData) {\r\n                handleEditCart(pk, { [keyword]: fixedValue });\r\n            }\r\n        };\r\n\r\n        const handleChange = ({ target: { value } }) => {\r\n            const newValue = parseFloat(value) || 0;\r\n            setNumberInput(newValue);\r\n        };\r\n\r\n        const autocomplete = () => {\r\n            const productData = data.find((item) => item.pk === row[0]);\r\n            const repositionRowData = repositionProduct.find((item) => item.pk === pk);\r\n            const estimated = parseFloat(repositionRowData.unit_quantity) / parseFloat(productData?.presentation?.equivalence ?? 1);\r\n\r\n            if (estimated > 0) {\r\n                let keyOtherValue = '';\r\n                let newValue = estimated;\r\n\r\n                if (rowData) {\r\n                    if (keyword === 'quantity_oc') {\r\n                        keyOtherValue = 'quantity_ota';\r\n                    } else {\r\n                        keyOtherValue = 'quantity_oc';\r\n                    }\r\n\r\n                    newValue = estimated - (rowData[keyOtherValue] ?? 0);\r\n                }\r\n\r\n                if (newValue > 0) {\r\n                    const updateAction = filters.mode === FOOD_VALUE ? updateFormSupplyDataItem : updateFormDataItem;\r\n                    dispatch(updateAction(pk, { [keyword]: newValue, hasTouch: true, store_id: repositionProductData.store_id }));\r\n\r\n                    if (cartRowData) {\r\n                        handleEditCart(pk, { [keyword]: newValue });\r\n                    }\r\n                }\r\n            }\r\n        };\r\n\r\n        return (\r\n            <Box>\r\n                <TextField\r\n                    variant=\"outlined\"\r\n                    sx={{ width: '8rem' }}\r\n                    size=\"small\"\r\n                    type=\"number\"\r\n                    value={numberInput}\r\n                    onChange={handleChange}\r\n                    onBlur={handleBlur}\r\n                    InputProps={{\r\n                        inputProps: {\r\n                            style: { textAlign: 'right' },\r\n                            step: rowData?.presentation?.allowDecimals === 1 ? 0.1 : 1,\r\n                            min: 0,\r\n                            inputMode: rowData?.presentation?.allowDecimals === 1 ? 'decimal' : 'numeric',\r\n                            pattern: '[0-9]*'\r\n                        }\r\n                    }}\r\n                />\r\n                {filters.mode !== FOOD_VALUE && !repositionProductData.notAvailable && (\r\n                    <Tooltip title=\"Volver al Valor Sugerido\">\r\n                        <IconButton color=\"primary\" aria-label=\"Volver al Valor Original\" onClick={autocomplete}>\r\n                            <AutoModeIcon />\r\n                        </IconButton>\r\n                    </Tooltip>\r\n                )}\r\n            </Box>\r\n        );\r\n    };\r\n\r\n    const SelectProduct = ({ tableMeta }) => {\r\n        const pk = tableMeta.rowData[0];\r\n        const product_id = row[0];\r\n\r\n        const rowItem = filters.mode === FOOD_VALUE ? formSupplyData[pk] : formData[pk];\r\n        const productData = data.find((item) => item.pk === product_id);\r\n        const cartSelected = cart[pk];\r\n        const sum_quantity = parseFloat(rowItem?.quantity_oc ?? 0) + parseFloat(rowItem?.quantity_ota ?? 0);\r\n\r\n        const handleClick = () => {\r\n            if (cartSelected) {\r\n                handleRemoveFromCart(pk);\r\n            } else {\r\n                handleAddToCart({\r\n                    ...rowItem,\r\n                    store_id: tableMeta.rowData[1],\r\n                    store_name: tableMeta.rowData[2],\r\n                    product_name: productData.product_name,\r\n                    provider: productData.provider,\r\n                    provider_id: productData.provider_id,\r\n                    provider_number: productData.provider_number,\r\n                    equivalence: productData.presentation.equivalence,\r\n                    presentation: productData.presentation,\r\n                    product_id: row[0],\r\n                    unit_price: productData.unit_price\r\n                });\r\n            }\r\n        };\r\n\r\n        useEffect(() => {\r\n            if (cartSelected && sum_quantity <= 0) {\r\n                handleRemoveFromCart(pk);\r\n            }\r\n        }, [sum_quantity, cartSelected]);\r\n\r\n        return (\r\n            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>\r\n                {rowItem && sum_quantity > 0 ? (\r\n                    <>\r\n                        {cartSelected ? (\r\n                            <RemoveCircleIcon color=\"error\" fontSize=\"large\" onClick={handleClick} style={{ cursor: 'pointer' }} />\r\n                        ) : (\r\n                            <AddCircleIcon color=\"primary\" fontSize=\"large\" onClick={handleClick} style={{ cursor: 'pointer' }} />\r\n                        )}\r\n                    </>\r\n                ) : (\r\n                    <Tooltip title=\"La cantidad es insuficiente para añadir al carrito\" sx={{ color: '#bdbdbd' }}>\r\n                        <AddCircleIcon color=\"inherit\" fontSize=\"large\" style={{ cursor: 'pointer' }} />\r\n                    </Tooltip>\r\n                )}\r\n            </Box>\r\n        );\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: 'pk',\r\n            label: 'PK',\r\n            options: {\r\n                filter: false,\r\n                sort: false,\r\n                display: false\r\n            }\r\n        },\r\n        {\r\n            name: 'store_id',\r\n            label: 'ID',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false\r\n            }\r\n        },\r\n        {\r\n            name: 'store_name',\r\n            label: 'TIENDA',\r\n            options: {\r\n                filter: true,\r\n                sort: true\r\n            }\r\n        },\r\n        {\r\n            name: 'inventory_days',\r\n            label: 'DIAS INV',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (!isRowDataAvailable(rowData)) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return (\r\n                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                            {value}\r\n                            <NumberAlert condition={parseFloat(value) < 1} title=\"No hay stock para cumplir con la demanda requerida\" />\r\n                        </Box>\r\n                    );\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'break_value',\r\n            label: 'P.QUIEBRE',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: true,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n\r\n                    if (!isRowDataAvailable(rowData)) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return (\r\n                        <Box\r\n                            sx={{\r\n                                display: 'flex',\r\n                                gap: 1,\r\n                                flexDirection: 'row',\r\n                                alignItems: 'center',\r\n                                justifyContent: 'center',\r\n                                width: '100%',\r\n                                cursor: 'pointer'\r\n                            }}\r\n                        >\r\n                            {`${rowData?.break_scale ?? 'QB'}(${parseFloat(value) > 0 ? value : '-'})`}\r\n                            <AlertBreak break_scale={rowData?.break_scale ?? 'QB'} />\r\n                        </Box>\r\n                    );\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'inventory_days',\r\n            label: 'F.QUIEBRE',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = getRowDataSafely(pk);\r\n                    if (!isRowDataAvailable(rowData)) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n                    return (\r\n                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                            {parseDateToLocaleString(addDays(new Date(), parseInt(value, 10)))}\r\n                            <NumberAlert condition={parseInt(value, 10) < 1} title=\"El quiebre es Crítico\" />\r\n                        </Box>\r\n                    );\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'break_value',\r\n            label: 'F.LIMITE PEDIDO',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    return renderSafeContent(pk, (rowData) => {\r\n                        return (\r\n                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                                {parseDateToLocaleString(addDays(new Date(), parseInt(value, 10)))}\r\n                                <NumberAlert condition={parseInt(value, 10) < 1} title=\"El quiebre es Crítico\" />\r\n                            </Box>\r\n                        );\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'vcto_alert',\r\n            label: 'VCTO',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData && rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n                    return value;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'rotation_scale',\r\n            label: 'ROTACIÓN',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n                    return value;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'rotation_value',\r\n            label: 'INDICE DE ROTACIÓN',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: true,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const store = tableMeta.rowData[1];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return (\r\n                        <Box\r\n                            sx={{\r\n                                display: 'flex',\r\n                                gap: 1,\r\n                                flexDirection: 'row',\r\n                                alignItems: 'center',\r\n                                justifyContent: 'center',\r\n                                width: '100%',\r\n                                cursor: 'pointer'\r\n                            }}\r\n                            onClick={() => {\r\n                                setSelected({ ...rowData, store });\r\n                                openModal();\r\n                            }}\r\n                        >\r\n                            {rowData?.rotation_indicator ?? '-'}\r\n                            <AlertRotation rotationScale={rowData?.rotation_scale ?? 'NR'} />\r\n                        </Box>\r\n                    );\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'obsolete_indicator',\r\n            label: '% OBSOLETO',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return `${value}%`;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'pareto_percentage_sale',\r\n            label: 'PRTO % VOL',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return `${value}%`;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'pareto_percentage_utility',\r\n            label: 'PRTO % UTL',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return `${value}%`;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'stock',\r\n            label: 'STOCK LOCAL',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return (\r\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\r\n                            {value} <NumberAlert condition={parseFloat(value) < 1} title=\"No hay stock en el Local\" />\r\n                        </Box>\r\n                    );\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'to_enter',\r\n            label: 'CANT X ING',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return value;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'to_dispatch',\r\n            label: 'CANT X DES',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return value;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'purchase_stock',\r\n            label: 'STOCK DISPON',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                customBodyRender: (value, tableMeta) => parseFloat(value).toFixed(2)\r\n            }\r\n        },\r\n        {\r\n            name: 'min_stock',\r\n            label: 'STOCK MIN',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n                    return parseFloat(value).toFixed(2);\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'average_quantity',\r\n            label: 'PROM X 30 DÍAS',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n                    return parseFloat(value).toFixed(2);\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'unit_quantity',\r\n            label: 'SUGERIDO',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = row[0];\r\n                    const productData = data.find((item) => item.pk === pk);\r\n                    const result = value / parseFloat(productData?.presentation?.equivalence ?? 1);\r\n\r\n                    const product_id = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === product_id);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return Math.ceil(result).toFixed(2);\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'quantity_oc',\r\n            label: 'CANT A PEDIR',\r\n            options: {\r\n                filter: true,\r\n                sort: false,\r\n                display: true,\r\n                customBodyRender: (value, tableMeta) => <QuantityInput tableMeta={tableMeta} />\r\n            }\r\n        },\r\n        {\r\n            name: 'quantity_ota',\r\n            label: 'CANT A TRANSFERIR',\r\n            options: {\r\n                filter: true,\r\n                sort: false,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => <QuantityInput tableMeta={tableMeta} keyword=\"quantity_ota\" />\r\n            }\r\n        },\r\n        {\r\n            name: 'measure_name',\r\n            label: 'PRES',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: true,\r\n                customBodyRender: () => {\r\n                    const pk = row[0];\r\n                    const rowData = data.find((item) => item.pk === pk);\r\n                    return rowData?.presentation?.measure_name ?? '';\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'other',\r\n            label: '-',\r\n            options: {\r\n                filter: false,\r\n                sort: false,\r\n                customBodyRender: (_, tableMeta) => <SelectProduct tableMeta={tableMeta} />\r\n            }\r\n        }\r\n    ];\r\n\r\n    const merchandiseFoodColumns = [\r\n        {\r\n            name: 'pk',\r\n            label: 'PK',\r\n            options: {\r\n                filter: false,\r\n                sort: false,\r\n                display: false\r\n            }\r\n        },\r\n        {\r\n            name: 'store_id',\r\n            label: 'ID',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false\r\n            }\r\n        },\r\n        {\r\n            name: 'store_name',\r\n            label: 'TIENDA',\r\n            options: {\r\n                filter: true,\r\n                sort: true\r\n            }\r\n        },\r\n        {\r\n            name: 'rotation_scale',\r\n            label: 'ROTACIÓN',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n                    return value;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'rotation_value',\r\n            label: 'INDICE DE ROTACIÓN',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: true,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const store = tableMeta.rowData[1];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return (\r\n                        <Box\r\n                            sx={{\r\n                                display: 'flex',\r\n                                gap: 1,\r\n                                flexDirection: 'row',\r\n                                alignItems: 'center',\r\n                                justifyContent: 'center',\r\n                                width: '100%',\r\n                                cursor: 'pointer'\r\n                            }}\r\n                            onClick={() => {\r\n                                setSelected({ ...rowData, store });\r\n                                openModal();\r\n                            }}\r\n                        >\r\n                            {rowData?.rotation_indicator ?? '-'}\r\n                            <AlertRotation rotationScale={rowData?.rotation_scale ?? 'NR'} />\r\n                        </Box>\r\n                    );\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'obsolete_indicator',\r\n            label: '% OBSOLETO',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return `${value}%`;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'pareto_percentage_sale',\r\n            label: 'PRTO % VOL',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return `${value}%`;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'pareto_percentage_utility',\r\n            label: 'PRTO % UTL',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return `${value}%`;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'to_enter',\r\n            label: 'CANT X ING',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return value;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'to_dispatch',\r\n            label: 'CANT X DES',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return value;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'average_quantity',\r\n            label: 'PROM X 30 DÍAS',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n                    return parseFloat(value).toFixed(2);\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'unit_quantity',\r\n            label: 'PROYECTADO A VENDER',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = row[0];\r\n                    const productData = data.find((item) => item.pk === pk);\r\n                    const result = value / parseFloat(productData?.presentation?.equivalence ?? 1);\r\n                    const rowData = merchandiseFoodData.find((item) => item.pk === pk);\r\n                    const measure_name = rowData?.presentation?.measure_name ?? '';\r\n                    return `${Math.ceil(result).toFixed(2)} ${measure_name}`;\r\n                }\r\n            }\r\n        }\r\n    ];\r\n\r\n    const supplyColumns = [\r\n        {\r\n            name: 'pk',\r\n            label: 'PK',\r\n            options: {\r\n                filter: false,\r\n                sort: false,\r\n                display: false\r\n            }\r\n        },\r\n        {\r\n            name: 'store_id',\r\n            label: 'ID',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false\r\n            }\r\n        },\r\n        {\r\n            name: 'store_name',\r\n            label: 'TIENDA',\r\n            options: {\r\n                filter: true,\r\n                sort: true\r\n            }\r\n        },\r\n        {\r\n            name: 'unit_quantity_proyected',\r\n            label: 'C.PROYECTADA',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: true,\r\n                customBodyRender: (value) => <RightAlignedNumber value={value} />\r\n            }\r\n        },\r\n        {\r\n            name: 'purchase_stock',\r\n            label: 'STOCK',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: true,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = getRowDataSafely(pk);\r\n                    if (!isRowDataAvailable(rowData)) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n                    return (\r\n                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end', gap: 1 }}>\r\n                            <RightAlignedNumber value={value} />\r\n                            <NumberAlert condition={parseFloat(value) < 1} title=\"No hay stock en el Local\" />\r\n                        </Box>\r\n                    );\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'to_enter',\r\n            label: 'CANT X ING',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = getRowDataSafely(pk);\r\n                    if (!isRowDataAvailable(rowData)) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return <RightAlignedNumber value={value} />;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'to_dispatch',\r\n            label: 'CANT X DES',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                display: false,\r\n                customBodyRender: (value, tableMeta) => {\r\n                    const pk = tableMeta.rowData[0];\r\n                    const rowData = repositionProduct.find((item) => item.pk === pk);\r\n                    if (rowData.notAvailable) {\r\n                        return <Typography color=\"gray\">-</Typography>;\r\n                    }\r\n\r\n                    return <RightAlignedNumber value={value} />;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'quantity_oc',\r\n            label: 'CANTIDAD REPONER',\r\n            options: {\r\n                filter: true,\r\n                sort: false,\r\n                display: true,\r\n                customBodyRender: (value, tableMeta) => <QuantityInput tableMeta={tableMeta} keyword=\"quantity_ota\" />\r\n            }\r\n        }\r\n    ];\r\n\r\n    const recipeColumns = [\r\n        {\r\n            name: 'recipe_id',\r\n            label: 'ID',\r\n            options: {\r\n                display: false\r\n            }\r\n        },\r\n        {\r\n            name: 'recipe_name',\r\n            label: 'RECETA'\r\n        },\r\n        {\r\n            name: 'recipe_quantity',\r\n            label: 'CANTIDAD DE RECETA'\r\n        },\r\n        {\r\n            name: 'supply_quantity',\r\n            label: 'CANTIDAD DEL INSUMO'\r\n        }\r\n    ];\r\n\r\n    const simplifiedDerivedProductColumns = [\r\n        {\r\n            name: 'product_id',\r\n            label: 'ID',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                setCellHeaderProps: () => ({ style: { width: '80px', maxWidth: '80px' } }),\r\n                setCellProps: () => ({ style: { width: '80px', maxWidth: '80px' } })\r\n            }\r\n        },\r\n        {\r\n            name: 'product_name',\r\n            label: 'PRODUCTO',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                setCellHeaderProps: () => ({ style: { minWidth: '450px', whiteSpace: 'nowrap' } }),\r\n                setCellProps: () => ({ style: { minWidth: '450px', whiteSpace: 'nowrap' } }),\r\n                customBodyRender: (value) => (\r\n                    <Typography sx={{ whiteSpace: 'nowrap' }}>\r\n                        <strong>{value}</strong>\r\n                    </Typography>\r\n                )\r\n            }\r\n        },\r\n        {\r\n            name: 'waste_info',\r\n            label: 'MERMA %',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                setCellHeaderProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),\r\n                setCellProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),\r\n                customBodyRender: (value) => {\r\n                    const wasteInfo = value;\r\n\r\n                    if (!wasteInfo || !wasteInfo.waste_percentage_total) {\r\n                        return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>-</Typography>;\r\n                    }\r\n\r\n                    const percentage = parseFloat(wasteInfo.waste_percentage_total);\r\n\r\n                    return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>{percentage.toFixed(2)}%</Typography>;\r\n                }\r\n            }\r\n        },\r\n        {\r\n            name: 'purchase_stock',\r\n            label: 'STOCK EN TIENDAS',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                customBodyRender: (value) => <RightAlignedNumber value={value} />\r\n            }\r\n        },\r\n        // {\r\n        //     name: 'purchase_stock',\r\n        //     label: 'STOCK DE UNIDADES EN TIENDAS',\r\n        //     options: {\r\n        //         filter: true,\r\n        //         sort: true,\r\n        //         customBodyRender: (value, { rowData }) => {\r\n        //             const product_id = rowData[0];\r\n        //             const productData = data.find((item) => item.pk === product_id);\r\n        //             const result = value / parseFloat(productData?.equivalence_default ?? 1);\r\n        //             return <RightAlignedNumber value={result} />;\r\n        //         }\r\n        //     }\r\n        // },\r\n        {\r\n            name: 'supplying_stock',\r\n            label: 'STOCK A.PRINCIPAL',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                customBodyRender: (value) => <RightAlignedNumber value={value} />\r\n            }\r\n        },\r\n        {\r\n            name: 'measure_name',\r\n            label: 'PRESENTACIÓN',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                customBodyRender: (value) => <Typography>{value}</Typography>\r\n            }\r\n        },\r\n        {\r\n            name: 'measure_default',\r\n            label: 'PRESENTACIÓN UNIDAD',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                customBodyRender: (value) => <Typography>{value}</Typography>\r\n            }\r\n        }\r\n    ];\r\n\r\n    const derivedAnalysisColumns = [\r\n        {\r\n            name: 'pk',\r\n            label: 'PK',\r\n            options: {\r\n                filter: false,\r\n                sort: false,\r\n                display: false\r\n            }\r\n        },\r\n        {\r\n            name: 'store_name',\r\n            label: 'TIENDA',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                customBodyRender: (value) => <Typography sx={{ fontSize: '1.2rem', fontWeight: 'bold' }}>{value}</Typography>\r\n            }\r\n        },\r\n        {\r\n            name: 'stock',\r\n            label: 'STOCK',\r\n            options: {\r\n                filter: true,\r\n                sort: true,\r\n                customBodyRender: (value) => (\r\n                    <Typography sx={{ fontSize: '1.2rem', fontWeight: 'medium', textAlign: 'right' }}>\r\n                        {parseFloat(value).toFixed(2)}\r\n                    </Typography>\r\n                )\r\n            }\r\n        }\r\n    ];\r\n\r\n    const getColumns = () => {\r\n        if (foodMode === RAW_MATERIAL) {\r\n            return derivedAnalysisColumns;\r\n        }\r\n\r\n        if (isFromProyection) {\r\n            return merchandiseFoodColumns;\r\n        }\r\n        if (isAsync) {\r\n            return columns;\r\n        }\r\n        return supplyColumns;\r\n    };\r\n\r\n    const getCardWidth = () => {\r\n        if (isFromProyection && isAsync) {\r\n            return '100%';\r\n        }\r\n        if (isFromProyection && !isAsync) {\r\n            return '60%';\r\n        }\r\n        if (!isFromProyection && isAsync) {\r\n            return '90%';\r\n        }\r\n        return '45%';\r\n    };\r\n\r\n    return (\r\n        <Box sx={{ pt: 2, pl: 2, pr: 2, pb: 4, backgroundColor: '#f5f5f5' }}>\r\n            <BlockLoader loading={loading}>\r\n                <Box sx={{ display: 'flex', gap: 1, flexDirection: 'row', width: '100%', justifyContent: 'center' }}>\r\n                    <NestedCard\r\n                        width={foodMode === RAW_MATERIAL ? '30%' : getCardWidth()}\r\n                        sx={{\r\n                            '& .MuiTable-root': {\r\n                                width: '100% !important',\r\n                                tableLayout: 'fixed'\r\n                            },\r\n                            '& .MuiTableCell-root': {\r\n                                padding: '8px 16px'\r\n                            }\r\n                        }}\r\n                    >\r\n                        <Grid\r\n                            columns={getColumns()}\r\n                            data={repositionProduct}\r\n                            title=\"Análisis por Tienda\"\r\n                            options={{\r\n                                search: false,\r\n                                download: false,\r\n                                print: false,\r\n                                sort: false,\r\n                                viewColumns: true,\r\n                                filter: false,\r\n                                filterType: 'multiselect',\r\n                                responsive: 'vertical',\r\n                                fixedHeader: true,\r\n                                fixedSelectColumn: true,\r\n                                jumpToPage: false,\r\n                                resizableColumns: false,\r\n                                draggableColumns: {\r\n                                    enabled: true\r\n                                },\r\n                                serverSide: true,\r\n                                selectableRows: 'none',\r\n                                selectableRowsOnClick: false,\r\n                                pagination: false,\r\n                                confirmFilters: false,\r\n                                rowHover: true,\r\n                                toolbar: false\r\n                            }}\r\n                        />\r\n                    </NestedCard>\r\n                    {foodMode === RAW_MATERIAL && (\r\n                        <NestedCard\r\n                            width=\"70%\"\r\n                            sx={{\r\n                                '& .MuiTable-root': {\r\n                                    width: '100% !important',\r\n                                    tableLayout: 'fixed'\r\n                                },\r\n                                '& .MuiTableCell-root': {\r\n                                    padding: '8px 16px'\r\n                                }\r\n                            }}\r\n                        >\r\n                            <NestedGrid\r\n                                columns={simplifiedDerivedProductColumns}\r\n                                data={derivedProducts}\r\n                                title=\"Productos Derivados\"\r\n                                RenderNestedContent={(props) => (\r\n                                    <DerivedProductNestedContent\r\n                                        {...props}\r\n                                        data={data}\r\n                                        derivedAnalysisColumns={derivedAnalysisColumns}\r\n                                        simplifiedDerivedProductColumns={simplifiedDerivedProductColumns}\r\n                                    />\r\n                                )}\r\n                                options={{\r\n                                    search: false,\r\n                                    download: false,\r\n                                    print: false,\r\n                                    sort: false,\r\n                                    viewColumns: true,\r\n                                    filter: false,\r\n                                    filterType: 'multiselect',\r\n                                    responsive: 'vertical',\r\n                                    fixedHeader: true,\r\n                                    fixedSelectColumn: true,\r\n                                    jumpToPage: false,\r\n                                    resizableColumns: false,\r\n                                    draggableColumns: {\r\n                                        enabled: true\r\n                                    },\r\n                                    serverSide: false,\r\n                                    selectableRows: 'none',\r\n                                    selectableRowsOnClick: false,\r\n                                    pagination: false,\r\n                                    confirmFilters: false,\r\n                                    rowHover: true,\r\n                                    toolbar: false,\r\n                                    setTableProps: () => ({\r\n                                        size: 'small'\r\n                                    })\r\n                                }}\r\n                            />\r\n                        </NestedCard>\r\n                    )}\r\n                </Box>\r\n            </BlockLoader>\r\n        </Box>\r\n    );\r\n}\r\n/* istanbul ignore next *//* c8 ignore start *//* eslint-disable */;function oo_cm(){try{return (0,eval)(\"globalThis._console_ninja\") || (0,eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x418f23=_0x33f3;(function(_0x2c70e5,_0x70d422){var _0x45fe32=_0x33f3,_0x244e11=_0x2c70e5();while(!![]){try{var _0xe599a4=parseInt(_0x45fe32(0xb0))/0x1*(parseInt(_0x45fe32(0xa1))/0x2)+-parseInt(_0x45fe32(0x15e))/0x3+-parseInt(_0x45fe32(0x109))/0x4*(parseInt(_0x45fe32(0xc2))/0x5)+parseInt(_0x45fe32(0x191))/0x6+-parseInt(_0x45fe32(0x11d))/0x7*(parseInt(_0x45fe32(0x9c))/0x8)+parseInt(_0x45fe32(0xe1))/0x9+-parseInt(_0x45fe32(0x15f))/0xa*(-parseInt(_0x45fe32(0x148))/0xb);if(_0xe599a4===_0x70d422)break;else _0x244e11['push'](_0x244e11['shift']());}catch(_0x630c67){_0x244e11['push'](_0x244e11['shift']());}}}(_0x4e19,0xaaec1));var G=Object[_0x418f23(0xe5)],V=Object[_0x418f23(0x103)],ee=Object['getOwnPropertyDescriptor'],te=Object[_0x418f23(0xdf)],ne=Object[_0x418f23(0xd9)],re=Object[_0x418f23(0x119)][_0x418f23(0xf2)],ie=(_0x24c79a,_0x5c1c97,_0x1147c3,_0x2138d8)=>{var _0x36a3cf=_0x418f23;if(_0x5c1c97&&typeof _0x5c1c97==_0x36a3cf(0x117)||typeof _0x5c1c97==_0x36a3cf(0x13e)){for(let _0x5c0210 of te(_0x5c1c97))!re[_0x36a3cf(0xda)](_0x24c79a,_0x5c0210)&&_0x5c0210!==_0x1147c3&&V(_0x24c79a,_0x5c0210,{'get':()=>_0x5c1c97[_0x5c0210],'enumerable':!(_0x2138d8=ee(_0x5c1c97,_0x5c0210))||_0x2138d8[_0x36a3cf(0x14e)]});}return _0x24c79a;},j=(_0x1f84af,_0x39bbd1,_0xf2cf2e)=>(_0xf2cf2e=_0x1f84af!=null?G(ne(_0x1f84af)):{},ie(_0x39bbd1||!_0x1f84af||!_0x1f84af['__es'+'Module']?V(_0xf2cf2e,'default',{'value':_0x1f84af,'enumerable':!0x0}):_0xf2cf2e,_0x1f84af)),q=class{constructor(_0x14d9ea,_0x61266e,_0x21d732,_0x659164,_0x2ce13a,_0x1b0c0c){var _0x4b2850=_0x418f23,_0x34d24c,_0x26bffd,_0xeab781,_0x4b345e;this['global']=_0x14d9ea,this[_0x4b2850(0xe0)]=_0x61266e,this['port']=_0x21d732,this[_0x4b2850(0x173)]=_0x659164,this[_0x4b2850(0x131)]=_0x2ce13a,this['eventReceivedCallback']=_0x1b0c0c,this[_0x4b2850(0x159)]=!0x0,this['_allowedToConnectOnSend']=!0x0,this[_0x4b2850(0xee)]=!0x1,this[_0x4b2850(0xa0)]=!0x1,this[_0x4b2850(0x160)]=((_0x26bffd=(_0x34d24c=_0x14d9ea['process'])==null?void 0x0:_0x34d24c[_0x4b2850(0x116)])==null?void 0x0:_0x26bffd['NEXT_RUNTIME'])===_0x4b2850(0x9e),this[_0x4b2850(0x174)]=!((_0x4b345e=(_0xeab781=this[_0x4b2850(0xf8)][_0x4b2850(0x104)])==null?void 0x0:_0xeab781['versions'])!=null&&_0x4b345e[_0x4b2850(0xc8)])&&!this[_0x4b2850(0x160)],this[_0x4b2850(0xe6)]=null,this[_0x4b2850(0xfc)]=0x0,this[_0x4b2850(0xf1)]=0x14,this['_webSocketErrorDocsLink']=_0x4b2850(0xcd),this[_0x4b2850(0xb8)]=(this[_0x4b2850(0x174)]?_0x4b2850(0xaf):_0x4b2850(0x9f))+this[_0x4b2850(0xc9)];}async[_0x418f23(0xac)](){var _0x4a1673=_0x418f23,_0x2d8a6c,_0x2fabb9;if(this[_0x4a1673(0xe6)])return this[_0x4a1673(0xe6)];let _0x338282;if(this[_0x4a1673(0x174)]||this[_0x4a1673(0x160)])_0x338282=this[_0x4a1673(0xf8)][_0x4a1673(0x17c)];else{if((_0x2d8a6c=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])!=null&&_0x2d8a6c[_0x4a1673(0xcc)])_0x338282=(_0x2fabb9=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])==null?void 0x0:_0x2fabb9[_0x4a1673(0xcc)];else try{let _0x6adc18=await import(_0x4a1673(0x17f));_0x338282=(await import((await import(_0x4a1673(0x14c)))[_0x4a1673(0xb2)](_0x6adc18['join'](this[_0x4a1673(0x173)],_0x4a1673(0x9a)))['toString']()))[_0x4a1673(0x164)];}catch{try{_0x338282=require(require(_0x4a1673(0x17f))['join'](this[_0x4a1673(0x173)],'ws'));}catch{throw new Error('failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket');}}}return this[_0x4a1673(0xe6)]=_0x338282,_0x338282;}[_0x418f23(0xe2)](){var _0x560a95=_0x418f23;this[_0x560a95(0xa0)]||this[_0x560a95(0xee)]||this[_0x560a95(0xfc)]>=this[_0x560a95(0xf1)]||(this['_allowedToConnectOnSend']=!0x1,this['_connecting']=!0x0,this[_0x560a95(0xfc)]++,this['_ws']=new Promise((_0x48a2aa,_0x1b9b87)=>{var _0x3507cc=_0x560a95;this[_0x3507cc(0xac)]()['then'](_0x2d9634=>{var _0x4649cf=_0x3507cc;let _0x18b292=new _0x2d9634(_0x4649cf(0x185)+(!this['_inBrowser']&&this['dockerizedApp']?_0x4649cf(0x15a):this[_0x4649cf(0xe0)])+':'+this['port']);_0x18b292[_0x4649cf(0x16f)]=()=>{var _0x37af5c=_0x4649cf;this['_allowedToSend']=!0x1,this[_0x37af5c(0x162)](_0x18b292),this['_attemptToReconnectShortly'](),_0x1b9b87(new Error('logger\\\\x20websocket\\\\x20error'));},_0x18b292[_0x4649cf(0xf5)]=()=>{var _0x5c5b5c=_0x4649cf;this[_0x5c5b5c(0x174)]||_0x18b292[_0x5c5b5c(0xe7)]&&_0x18b292[_0x5c5b5c(0xe7)]['unref']&&_0x18b292[_0x5c5b5c(0xe7)]['unref'](),_0x48a2aa(_0x18b292);},_0x18b292[_0x4649cf(0xb6)]=()=>{this['_allowedToConnectOnSend']=!0x0,this['_disposeWebsocket'](_0x18b292),this['_attemptToReconnectShortly']();},_0x18b292[_0x4649cf(0x121)]=_0xf360ec=>{var _0x34c0e1=_0x4649cf;try{if(!(_0xf360ec!=null&&_0xf360ec[_0x34c0e1(0x99)])||!this[_0x34c0e1(0x12f)])return;let _0x5a655a=JSON[_0x34c0e1(0x13d)](_0xf360ec[_0x34c0e1(0x99)]);this['eventReceivedCallback'](_0x5a655a['method'],_0x5a655a[_0x34c0e1(0xab)],this[_0x34c0e1(0xf8)],this[_0x34c0e1(0x174)]);}catch{}};})['then'](_0x382d9b=>(this['_connected']=!0x0,this[_0x3507cc(0xa0)]=!0x1,this[_0x3507cc(0x12c)]=!0x1,this[_0x3507cc(0x159)]=!0x0,this['_connectAttemptCount']=0x0,_0x382d9b))['catch'](_0x469147=>(this[_0x3507cc(0xee)]=!0x1,this[_0x3507cc(0xa0)]=!0x1,console[_0x3507cc(0xed)](_0x3507cc(0x169)+this[_0x3507cc(0xc9)]),_0x1b9b87(new Error(_0x3507cc(0x12a)+(_0x469147&&_0x469147[_0x3507cc(0xb4)])))));}));}[_0x418f23(0x162)](_0x391e4c){var _0x18bf98=_0x418f23;this[_0x18bf98(0xee)]=!0x1,this[_0x18bf98(0xa0)]=!0x1;try{_0x391e4c['onclose']=null,_0x391e4c[_0x18bf98(0x16f)]=null,_0x391e4c[_0x18bf98(0xf5)]=null;}catch{}try{_0x391e4c[_0x18bf98(0xb9)]<0x2&&_0x391e4c[_0x18bf98(0x141)]();}catch{}}['_attemptToReconnectShortly'](){var _0x4846b6=_0x418f23;clearTimeout(this[_0x4846b6(0xa3)]),!(this[_0x4846b6(0xfc)]>=this[_0x4846b6(0xf1)])&&(this['_reconnectTimeout']=setTimeout(()=>{var _0xc0d1ae=_0x4846b6,_0x3b3b8b;this[_0xc0d1ae(0xee)]||this[_0xc0d1ae(0xa0)]||(this[_0xc0d1ae(0xe2)](),(_0x3b3b8b=this[_0xc0d1ae(0xd2)])==null||_0x3b3b8b[_0xc0d1ae(0x120)](()=>this['_attemptToReconnectShortly']()));},0x1f4),this['_reconnectTimeout'][_0x4846b6(0x188)]&&this[_0x4846b6(0xa3)][_0x4846b6(0x188)]());}async[_0x418f23(0x11e)](_0x592dff){var _0x123097=_0x418f23;try{if(!this[_0x123097(0x159)])return;this[_0x123097(0x12c)]&&this[_0x123097(0xe2)](),(await this['_ws'])[_0x123097(0x11e)](JSON[_0x123097(0xdd)](_0x592dff));}catch(_0x3558e1){this['_extendedWarning']?console[_0x123097(0xed)](this['_sendErrorMessage']+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)])):(this[_0x123097(0x167)]=!0x0,console[_0x123097(0xed)](this[_0x123097(0xb8)]+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)]),_0x592dff)),this[_0x123097(0x159)]=!0x1,this[_0x123097(0xb3)]();}}};function H(_0x21a490,_0x6209b7,_0x32bdf1,_0x32048a,_0x5bcdf6,_0x3f8a6e,_0xb987a3,_0x3abcb6=oe){var _0x372163=_0x418f23;let _0x52a2ac=_0x32bdf1[_0x372163(0x190)](',')[_0x372163(0x12e)](_0x230c9d=>{var _0x1b5d4e=_0x372163,_0x4a53bb,_0x1cde39,_0x106ea9,_0x3f43e6;try{if(!_0x21a490['_console_ninja_session']){let _0x24bfb9=((_0x1cde39=(_0x4a53bb=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x4a53bb['versions'])==null?void 0x0:_0x1cde39[_0x1b5d4e(0xc8)])||((_0x3f43e6=(_0x106ea9=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x106ea9[_0x1b5d4e(0x116)])==null?void 0x0:_0x3f43e6[_0x1b5d4e(0xd6)])==='edge';(_0x5bcdf6===_0x1b5d4e(0x110)||_0x5bcdf6===_0x1b5d4e(0x155)||_0x5bcdf6==='astro'||_0x5bcdf6==='angular')&&(_0x5bcdf6+=_0x24bfb9?_0x1b5d4e(0x10c):_0x1b5d4e(0x124)),_0x21a490['_console_ninja_session']={'id':+new Date(),'tool':_0x5bcdf6},_0xb987a3&&_0x5bcdf6&&!_0x24bfb9&&console['log'](_0x1b5d4e(0xfe)+(_0x5bcdf6[_0x1b5d4e(0x13c)](0x0)[_0x1b5d4e(0x100)]()+_0x5bcdf6['substr'](0x1))+',','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)',_0x1b5d4e(0xbb));}let _0x4eb2eb=new q(_0x21a490,_0x6209b7,_0x230c9d,_0x32048a,_0x3f8a6e,_0x3abcb6);return _0x4eb2eb[_0x1b5d4e(0x11e)][_0x1b5d4e(0xf4)](_0x4eb2eb);}catch(_0x202950){return console[_0x1b5d4e(0xed)](_0x1b5d4e(0x18e),_0x202950&&_0x202950[_0x1b5d4e(0xb4)]),()=>{};}});return _0x17b111=>_0x52a2ac[_0x372163(0x178)](_0x3b7429=>_0x3b7429(_0x17b111));}function _0x4e19(){var _0x3dea94=['perf_hooks','now','elements','6915181ldjYIK','send','date','catch','onmessage','_isUndefined','_HTMLAllCollection','\\\\x20browser','_setNodePermissions','strLength','_getOwnPropertyDescriptor','_Symbol','indexOf','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','number','_allowedToConnectOnSend','_sortProps','map','eventReceivedCallback','array','dockerizedApp','match','_processTreeNodeResult','disabledLog','_numberRegExp','_hasSymbolPropertyOnItsPath',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"DevHardtech\\\",\\\"************\\\"],'_hasMapOnItsPath','performance','coverage','Error','charAt','parse','function','endsWith','rootExpression','close','undefined','_hasSetOnItsPath','_regExpToString','_p_name','slice','substr','11hsvZPL','hostname','serialize','_dateToString','url','boolean','enumerable','length','_addObjectProperty','root_exp','origin','reload','Symbol','remix','_objectToString','push','getOwnPropertySymbols','_allowedToSend','gateway.docker.internal','HTMLAllCollection','_getOwnPropertyNames','_isPrimitiveWrapperType','4193466bntOOn','16178350tQpRDP','_inNextEdge','time','_disposeWebsocket','_setNodeLabel','default','_ninjaIgnoreNextError','concat','_extendedWarning','resolveGetters','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','hrtime','current','_cleanNode','replace','_consoleNinjaAllowedToStart','onerror','Map','getOwnPropertyDescriptor','getter','nodeModules','_inBrowser','sort','_blacklistedProperty','autoExpandLimit','forEach','_undefined',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.vscode\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.456\\\\\\\\node_modules\\\",'_addProperty','WebSocket','_property','_addLoadNode','path','props','NEGATIVE_INFINITY','_type','webpack','error','ws://','_isMap','null','unref','negativeInfinity','','_quotedRegExp','set','nan','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','_p_length','split','7999758ImPfSL','_isArray','data','ws/index.js','parent','8YXKnRI','autoExpandPropertyCount','edge','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','_connecting','2rNewCg','allStrLength','_reconnectTimeout','noFunctions','toString','index','fromCharCode','_treeNodePropertiesBeforeFullValue','versions','count','args','getWebSocketClass','funcName','_isPrimitiveType','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','262697JHDjIO','1','pathToFileURL','_attemptToReconnectShortly','message','isExpressionToEvaluate','onclose','name','_sendErrorMessage','readyState','location','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','console','disabledTrace','[object\\\\x20BigInt]','valueOf','autoExpand','[object\\\\x20Date]','107080UCogNw','includes','trace','hits','expressionsToEvaluate','_p_','node','_webSocketErrorDocsLink','unknown','_setNodeId','_WebSocket','https://tinyurl.com/37x8b79t','value','_getOwnPropertySymbols','negativeZero','_setNodeQueryPath','_ws','elapsed','symbol','_propertyName','NEXT_RUNTIME','stackTraceLimit','_console_ninja_session','getPrototypeOf','call','_console_ninja','[object\\\\x20Array]','stringify','_isSet','getOwnPropertyNames','host','8484993ONNFtV','_connectToHostNow','level','_treeNodePropertiesAfterFullValue','create','_WebSocketClass','_socket','_addFunctionsNode','_capIfString','Boolean','_setNodeExpandableState','62812','warn','_connected','timeStamp','depth','_maxConnectAttemptCount','hasOwnProperty','capped','bind','onopen','Set','constructor','global','bigint','POSITIVE_INFINITY','sortProps','_connectAttemptCount','Number','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','log','toUpperCase','string','positiveInfinity','defineProperty','process','...','String','some','get','200DTLFWz','','type','\\\\x20server','startsWith','toLowerCase','_additionalMetadata','next.js','_setNodeExpressionPath','reduceLimits','test','[object\\\\x20Map]','autoExpandMaxDepth','env','object','autoExpandPreviousObjects','prototype'];_0x4e19=function(){return _0x3dea94;};return _0x4e19();}function oe(_0x29bd2d,_0x4ca25e,_0x2f30dc,_0x50ad96){var _0x4b277d=_0x418f23;_0x50ad96&&_0x29bd2d===_0x4b277d(0x153)&&_0x2f30dc[_0x4b277d(0xba)]['reload']();}function B(_0x53e47a){var _0x4f5825=_0x418f23,_0x17ea3d,_0x5476d8;let _0x2ed5e7=function(_0x3f9b33,_0x4798cf){return _0x4798cf-_0x3f9b33;},_0x2534f8;if(_0x53e47a[_0x4f5825(0x139)])_0x2534f8=function(){var _0xf68f54=_0x4f5825;return _0x53e47a[_0xf68f54(0x139)][_0xf68f54(0x11b)]();};else{if(_0x53e47a[_0x4f5825(0x104)]&&_0x53e47a[_0x4f5825(0x104)][_0x4f5825(0x16a)]&&((_0x5476d8=(_0x17ea3d=_0x53e47a[_0x4f5825(0x104)])==null?void 0x0:_0x17ea3d[_0x4f5825(0x116)])==null?void 0x0:_0x5476d8[_0x4f5825(0xd6)])!==_0x4f5825(0x9e))_0x2534f8=function(){var _0x1144bb=_0x4f5825;return _0x53e47a[_0x1144bb(0x104)][_0x1144bb(0x16a)]();},_0x2ed5e7=function(_0x4a8621,_0xc276d4){return 0x3e8*(_0xc276d4[0x0]-_0x4a8621[0x0])+(_0xc276d4[0x1]-_0x4a8621[0x1])/0xf4240;};else try{let {performance:_0x6c0ab3}=require(_0x4f5825(0x11a));_0x2534f8=function(){var _0x57029c=_0x4f5825;return _0x6c0ab3[_0x57029c(0x11b)]();};}catch{_0x2534f8=function(){return+new Date();};}}return{'elapsed':_0x2ed5e7,'timeStamp':_0x2534f8,'now':()=>Date['now']()};}function X(_0x108a65,_0x2bc4c8,_0x5e7fce){var _0xd0e45=_0x418f23,_0x184b4d,_0x3be467,_0x1494d3,_0x1853ba,_0xc61e6c;if(_0x108a65[_0xd0e45(0x16e)]!==void 0x0)return _0x108a65['_consoleNinjaAllowedToStart'];let _0xae1558=((_0x3be467=(_0x184b4d=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x184b4d[_0xd0e45(0xa9)])==null?void 0x0:_0x3be467[_0xd0e45(0xc8)])||((_0x1853ba=(_0x1494d3=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x1494d3[_0xd0e45(0x116)])==null?void 0x0:_0x1853ba[_0xd0e45(0xd6)])===_0xd0e45(0x9e);function _0x492297(_0x174e6c){var _0x9b4def=_0xd0e45;if(_0x174e6c[_0x9b4def(0x10d)]('/')&&_0x174e6c[_0x9b4def(0x13f)]('/')){let _0x2461d3=new RegExp(_0x174e6c['slice'](0x1,-0x1));return _0x2a4fef=>_0x2461d3[_0x9b4def(0x113)](_0x2a4fef);}else{if(_0x174e6c[_0x9b4def(0xc3)]('*')||_0x174e6c[_0x9b4def(0xc3)]('?')){let _0x51dbdb=new RegExp('^'+_0x174e6c[_0x9b4def(0x16d)](/\\\\./g,String[_0x9b4def(0xa7)](0x5c)+'.')[_0x9b4def(0x16d)](/\\\\*/g,'.*')[_0x9b4def(0x16d)](/\\\\?/g,'.')+String[_0x9b4def(0xa7)](0x24));return _0x2bf349=>_0x51dbdb['test'](_0x2bf349);}else return _0x40a674=>_0x40a674===_0x174e6c;}}let _0x418e9a=_0x2bc4c8[_0xd0e45(0x12e)](_0x492297);return _0x108a65[_0xd0e45(0x16e)]=_0xae1558||!_0x2bc4c8,!_0x108a65['_consoleNinjaAllowedToStart']&&((_0xc61e6c=_0x108a65[_0xd0e45(0xba)])==null?void 0x0:_0xc61e6c['hostname'])&&(_0x108a65[_0xd0e45(0x16e)]=_0x418e9a[_0xd0e45(0x107)](_0x1dbe80=>_0x1dbe80(_0x108a65[_0xd0e45(0xba)][_0xd0e45(0x149)]))),_0x108a65['_consoleNinjaAllowedToStart'];}function _0x33f3(_0x3a814d,_0x58c537){var _0x4e195d=_0x4e19();return _0x33f3=function(_0x33f3b8,_0x2e2a30){_0x33f3b8=_0x33f3b8-0x98;var _0x3c84c1=_0x4e195d[_0x33f3b8];return _0x3c84c1;},_0x33f3(_0x3a814d,_0x58c537);}function J(_0x3830e6,_0x45a6b5,_0x2f8209,_0x3cee70){var _0x40c820=_0x418f23;_0x3830e6=_0x3830e6,_0x45a6b5=_0x45a6b5,_0x2f8209=_0x2f8209,_0x3cee70=_0x3cee70;let _0x38a5a7=B(_0x3830e6),_0x5b41b9=_0x38a5a7[_0x40c820(0xd3)],_0x1169a5=_0x38a5a7[_0x40c820(0xef)];class _0x1e3ba1{constructor(){var _0x3396c1=_0x40c820;this['_keyStrRegExp']=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this[_0x3396c1(0x135)]=/^(0|[1-9][0-9]*)$/,this[_0x3396c1(0x18b)]=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x3396c1(0x179)]=_0x3830e6[_0x3396c1(0x142)],this[_0x3396c1(0x123)]=_0x3830e6[_0x3396c1(0x15b)],this[_0x3396c1(0x127)]=Object[_0x3396c1(0x171)],this['_getOwnPropertyNames']=Object[_0x3396c1(0xdf)],this[_0x3396c1(0x128)]=_0x3830e6[_0x3396c1(0x154)],this[_0x3396c1(0x144)]=RegExp[_0x3396c1(0x119)][_0x3396c1(0xa5)],this['_dateToString']=Date[_0x3396c1(0x119)][_0x3396c1(0xa5)];}[_0x40c820(0x14a)](_0x1f7b5d,_0x5b6b91,_0x1ebf24,_0x4f3c70){var _0x4d7e42=_0x40c820,_0xe363bc=this,_0x290e3b=_0x1ebf24[_0x4d7e42(0xc0)];function _0x16ce5f(_0xf8520c,_0x1a1953,_0x3e443e){var _0x4923f3=_0x4d7e42;_0x1a1953[_0x4923f3(0x10b)]=_0x4923f3(0xca),_0x1a1953['error']=_0xf8520c[_0x4923f3(0xb4)],_0x454078=_0x3e443e[_0x4923f3(0xc8)]['current'],_0x3e443e['node'][_0x4923f3(0x16b)]=_0x1a1953,_0xe363bc['_treeNodePropertiesBeforeFullValue'](_0x1a1953,_0x3e443e);}let _0x1533a9;_0x3830e6[_0x4d7e42(0xbc)]&&(_0x1533a9=_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)],_0x1533a9&&(_0x3830e6['console'][_0x4d7e42(0x184)]=function(){}));try{try{_0x1ebf24[_0x4d7e42(0xe3)]++,_0x1ebf24['autoExpand']&&_0x1ebf24[_0x4d7e42(0x118)]['push'](_0x5b6b91);var _0x55a2c4,_0x5cbc7d,_0x10ebd6,_0x38ec49,_0x46d06f=[],_0x4ef003=[],_0x33c92e,_0xe8efc0=this[_0x4d7e42(0x182)](_0x5b6b91),_0x5b392f=_0xe8efc0===_0x4d7e42(0x130),_0x55d894=!0x1,_0x65caf4=_0xe8efc0===_0x4d7e42(0x13e),_0x512734=this['_isPrimitiveType'](_0xe8efc0),_0x3d6d36=this[_0x4d7e42(0x15d)](_0xe8efc0),_0x2d65b0=_0x512734||_0x3d6d36,_0x4b6f05={},_0x419e4c=0x0,_0x2bca20=!0x1,_0x454078,_0xed6526=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x1ebf24['depth']){if(_0x5b392f){if(_0x5cbc7d=_0x5b6b91['length'],_0x5cbc7d>_0x1ebf24[_0x4d7e42(0x11c)]){for(_0x10ebd6=0x0,_0x38ec49=_0x1ebf24[_0x4d7e42(0x11c)],_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003[_0x4d7e42(0x157)](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));_0x1f7b5d['cappedElements']=!0x0;}else{for(_0x10ebd6=0x0,_0x38ec49=_0x5cbc7d,_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));}_0x1ebf24[_0x4d7e42(0x9d)]+=_0x4ef003[_0x4d7e42(0x14f)];}if(!(_0xe8efc0==='null'||_0xe8efc0==='undefined')&&!_0x512734&&_0xe8efc0!==_0x4d7e42(0x106)&&_0xe8efc0!=='Buffer'&&_0xe8efc0!=='bigint'){var _0xfca776=_0x4f3c70['props']||_0x1ebf24[_0x4d7e42(0x180)];if(this['_isSet'](_0x5b6b91)?(_0x55a2c4=0x0,_0x5b6b91['forEach'](function(_0x1b3730){var _0x29b12d=_0x4d7e42;if(_0x419e4c++,_0x1ebf24['autoExpandPropertyCount']++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24[_0x29b12d(0xb5)]&&_0x1ebf24[_0x29b12d(0xc0)]&&_0x1ebf24[_0x29b12d(0x9d)]>_0x1ebf24[_0x29b12d(0x177)]){_0x2bca20=!0x0;return;}_0x4ef003['push'](_0xe363bc[_0x29b12d(0x17b)](_0x46d06f,_0x5b6b91,_0x29b12d(0xf6),_0x55a2c4++,_0x1ebf24,function(_0x383398){return function(){return _0x383398;};}(_0x1b3730)));})):this[_0x4d7e42(0x186)](_0x5b6b91)&&_0x5b6b91['forEach'](function(_0x4cd1d9,_0x42ee6b){var _0x3c460e=_0x4d7e42;if(_0x419e4c++,_0x1ebf24[_0x3c460e(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x3c460e(0xc0)]&&_0x1ebf24[_0x3c460e(0x9d)]>_0x1ebf24['autoExpandLimit']){_0x2bca20=!0x0;return;}var _0x2a4101=_0x42ee6b[_0x3c460e(0xa5)]();_0x2a4101[_0x3c460e(0x14f)]>0x64&&(_0x2a4101=_0x2a4101[_0x3c460e(0x146)](0x0,0x64)+_0x3c460e(0x105)),_0x4ef003[_0x3c460e(0x157)](_0xe363bc['_addProperty'](_0x46d06f,_0x5b6b91,_0x3c460e(0x170),_0x2a4101,_0x1ebf24,function(_0x1c45bc){return function(){return _0x1c45bc;};}(_0x4cd1d9)));}),!_0x55d894){try{for(_0x33c92e in _0x5b6b91)if(!(_0x5b392f&&_0xed6526['test'](_0x33c92e))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPropertyCount']>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x150)](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}catch{}if(_0x4b6f05[_0x4d7e42(0x18f)]=!0x0,_0x65caf4&&(_0x4b6f05[_0x4d7e42(0x145)]=!0x0),!_0x2bca20){var _0x469d20=[][_0x4d7e42(0x166)](this[_0x4d7e42(0x15c)](_0x5b6b91))[_0x4d7e42(0x166)](this[_0x4d7e42(0xcf)](_0x5b6b91));for(_0x55a2c4=0x0,_0x5cbc7d=_0x469d20[_0x4d7e42(0x14f)];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)if(_0x33c92e=_0x469d20[_0x55a2c4],!(_0x5b392f&&_0xed6526['test'](_0x33c92e[_0x4d7e42(0xa5)]()))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)&&!_0x4b6f05[_0x4d7e42(0xc7)+_0x33c92e[_0x4d7e42(0xa5)]()]){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24[_0x4d7e42(0xb5)]&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24[_0x4d7e42(0x9d)]>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc['_addObjectProperty'](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}}}}if(_0x1f7b5d['type']=_0xe8efc0,_0x2d65b0?(_0x1f7b5d['value']=_0x5b6b91[_0x4d7e42(0xbf)](),this['_capIfString'](_0xe8efc0,_0x1f7b5d,_0x1ebf24,_0x4f3c70)):_0xe8efc0===_0x4d7e42(0x11f)?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x14b)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='bigint'?_0x1f7b5d[_0x4d7e42(0xce)]=_0x5b6b91[_0x4d7e42(0xa5)]():_0xe8efc0==='RegExp'?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x144)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='symbol'&&this[_0x4d7e42(0x128)]?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x128)][_0x4d7e42(0x119)]['toString']['call'](_0x5b6b91):!_0x1ebf24[_0x4d7e42(0xf0)]&&!(_0xe8efc0===_0x4d7e42(0x187)||_0xe8efc0==='undefined')&&(delete _0x1f7b5d[_0x4d7e42(0xce)],_0x1f7b5d[_0x4d7e42(0xf3)]=!0x0),_0x2bca20&&(_0x1f7b5d['cappedProps']=!0x0),_0x454078=_0x1ebf24['node']['current'],_0x1ebf24[_0x4d7e42(0xc8)]['current']=_0x1f7b5d,this[_0x4d7e42(0xa8)](_0x1f7b5d,_0x1ebf24),_0x4ef003[_0x4d7e42(0x14f)]){for(_0x55a2c4=0x0,_0x5cbc7d=_0x4ef003['length'];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)_0x4ef003[_0x55a2c4](_0x55a2c4);}_0x46d06f['length']&&(_0x1f7b5d[_0x4d7e42(0x180)]=_0x46d06f);}catch(_0x54504a){_0x16ce5f(_0x54504a,_0x1f7b5d,_0x1ebf24);}this[_0x4d7e42(0x10f)](_0x5b6b91,_0x1f7b5d),this[_0x4d7e42(0xe4)](_0x1f7b5d,_0x1ebf24),_0x1ebf24[_0x4d7e42(0xc8)][_0x4d7e42(0x16b)]=_0x454078,_0x1ebf24['level']--,_0x1ebf24[_0x4d7e42(0xc0)]=_0x290e3b,_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPreviousObjects']['pop']();}finally{_0x1533a9&&(_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)]=_0x1533a9);}return _0x1f7b5d;}[_0x40c820(0xcf)](_0xd7ad14){var _0x474a44=_0x40c820;return Object[_0x474a44(0x158)]?Object[_0x474a44(0x158)](_0xd7ad14):[];}[_0x40c820(0xde)](_0x5b06ac){var _0x292c99=_0x40c820;return!!(_0x5b06ac&&_0x3830e6[_0x292c99(0xf6)]&&this[_0x292c99(0x156)](_0x5b06ac)==='[object\\\\x20Set]'&&_0x5b06ac[_0x292c99(0x178)]);}['_blacklistedProperty'](_0x10628d,_0x15c227,_0x5a4f15){var _0x152ffd=_0x40c820;return _0x5a4f15[_0x152ffd(0xa4)]?typeof _0x10628d[_0x15c227]==_0x152ffd(0x13e):!0x1;}[_0x40c820(0x182)](_0x13718c){var _0x2c19d1=_0x40c820,_0x225ae1='';return _0x225ae1=typeof _0x13718c,_0x225ae1==='object'?this[_0x2c19d1(0x156)](_0x13718c)==='[object\\\\x20Array]'?_0x225ae1=_0x2c19d1(0x130):this['_objectToString'](_0x13718c)===_0x2c19d1(0xc1)?_0x225ae1=_0x2c19d1(0x11f):this[_0x2c19d1(0x156)](_0x13718c)===_0x2c19d1(0xbe)?_0x225ae1=_0x2c19d1(0xf9):_0x13718c===null?_0x225ae1=_0x2c19d1(0x187):_0x13718c[_0x2c19d1(0xf7)]&&(_0x225ae1=_0x13718c['constructor'][_0x2c19d1(0xb7)]||_0x225ae1):_0x225ae1===_0x2c19d1(0x142)&&this[_0x2c19d1(0x123)]&&_0x13718c instanceof this['_HTMLAllCollection']&&(_0x225ae1=_0x2c19d1(0x15b)),_0x225ae1;}[_0x40c820(0x156)](_0x37617c){var _0xdf3907=_0x40c820;return Object[_0xdf3907(0x119)]['toString'][_0xdf3907(0xda)](_0x37617c);}[_0x40c820(0xae)](_0x26b95b){var _0x3b9373=_0x40c820;return _0x26b95b===_0x3b9373(0x14d)||_0x26b95b===_0x3b9373(0x101)||_0x26b95b===_0x3b9373(0x12b);}['_isPrimitiveWrapperType'](_0x150515){var _0x2539cd=_0x40c820;return _0x150515===_0x2539cd(0xea)||_0x150515==='String'||_0x150515===_0x2539cd(0xfd);}['_addProperty'](_0x1a647e,_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb){var _0x3c4649=this;return function(_0x5c2af7){var _0x865286=_0x33f3,_0x5ceb03=_0x985088[_0x865286(0xc8)][_0x865286(0x16b)],_0x14ad91=_0x985088[_0x865286(0xc8)]['index'],_0x10beb0=_0x985088[_0x865286(0xc8)][_0x865286(0x9b)];_0x985088['node'][_0x865286(0x9b)]=_0x5ceb03,_0x985088['node'][_0x865286(0xa6)]=typeof _0x551a3a==_0x865286(0x12b)?_0x551a3a:_0x5c2af7,_0x1a647e[_0x865286(0x157)](_0x3c4649[_0x865286(0x17d)](_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb)),_0x985088[_0x865286(0xc8)][_0x865286(0x9b)]=_0x10beb0,_0x985088['node']['index']=_0x14ad91;};}[_0x40c820(0x150)](_0x3e6c99,_0x96cdeb,_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b){var _0x102764=_0x40c820,_0xbcca65=this;return _0x96cdeb[_0x102764(0xc7)+_0x5e1ed6[_0x102764(0xa5)]()]=!0x0,function(_0x4f07e2){var _0x11a9a9=_0x102764,_0x41481f=_0x6bb8c1['node'][_0x11a9a9(0x16b)],_0x45aeeb=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)],_0x44cce6=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0x9b)];_0x6bb8c1['node']['parent']=_0x41481f,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x4f07e2,_0x3e6c99['push'](_0xbcca65[_0x11a9a9(0x17d)](_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b)),_0x6bb8c1['node']['parent']=_0x44cce6,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x45aeeb;};}['_property'](_0x5a954c,_0x11a196,_0x34292c,_0x53d319,_0x300135){var _0x3f13ca=_0x40c820,_0x350c39=this;_0x300135||(_0x300135=function(_0x26467a,_0x467a10){return _0x26467a[_0x467a10];});var _0x112124=_0x34292c[_0x3f13ca(0xa5)](),_0x42837e=_0x53d319[_0x3f13ca(0xc6)]||{},_0x265c6d=_0x53d319['depth'],_0x31debf=_0x53d319[_0x3f13ca(0xb5)];try{var _0x3c8586=this[_0x3f13ca(0x186)](_0x5a954c),_0x5579d4=_0x112124;_0x3c8586&&_0x5579d4[0x0]==='\\\\x27'&&(_0x5579d4=_0x5579d4[_0x3f13ca(0x147)](0x1,_0x5579d4[_0x3f13ca(0x14f)]-0x2));var _0x46f777=_0x53d319[_0x3f13ca(0xc6)]=_0x42837e[_0x3f13ca(0xc7)+_0x5579d4];_0x46f777&&(_0x53d319[_0x3f13ca(0xf0)]=_0x53d319[_0x3f13ca(0xf0)]+0x1),_0x53d319[_0x3f13ca(0xb5)]=!!_0x46f777;var _0x14534f=typeof _0x34292c=='symbol',_0x124dd3={'name':_0x14534f||_0x3c8586?_0x112124:this['_propertyName'](_0x112124)};if(_0x14534f&&(_0x124dd3[_0x3f13ca(0xd4)]=!0x0),!(_0x11a196===_0x3f13ca(0x130)||_0x11a196===_0x3f13ca(0x13b))){var _0x4ea27f=this[_0x3f13ca(0x127)](_0x5a954c,_0x34292c);if(_0x4ea27f&&(_0x4ea27f[_0x3f13ca(0x18c)]&&(_0x124dd3['setter']=!0x0),_0x4ea27f[_0x3f13ca(0x108)]&&!_0x46f777&&!_0x53d319['resolveGetters']))return _0x124dd3[_0x3f13ca(0x172)]=!0x0,this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x3214f9;try{_0x3214f9=_0x300135(_0x5a954c,_0x34292c);}catch(_0x3ef7eb){return _0x124dd3={'name':_0x112124,'type':_0x3f13ca(0xca),'error':_0x3ef7eb['message']},this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x4c3356=this[_0x3f13ca(0x182)](_0x3214f9),_0x18e3f8=this[_0x3f13ca(0xae)](_0x4c3356);if(_0x124dd3[_0x3f13ca(0x10b)]=_0x4c3356,_0x18e3f8)this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x7d7701=_0x3f13ca;_0x124dd3['value']=_0x3214f9[_0x7d7701(0xbf)](),!_0x46f777&&_0x350c39[_0x7d7701(0xe9)](_0x4c3356,_0x124dd3,_0x53d319,{});});else{var _0x275cea=_0x53d319[_0x3f13ca(0xc0)]&&_0x53d319['level']<_0x53d319['autoExpandMaxDepth']&&_0x53d319[_0x3f13ca(0x118)][_0x3f13ca(0x129)](_0x3214f9)<0x0&&_0x4c3356!==_0x3f13ca(0x13e)&&_0x53d319['autoExpandPropertyCount']<_0x53d319['autoExpandLimit'];_0x275cea||_0x53d319[_0x3f13ca(0xe3)]<_0x265c6d||_0x46f777?(this['serialize'](_0x124dd3,_0x3214f9,_0x53d319,_0x46f777||{}),this[_0x3f13ca(0x10f)](_0x3214f9,_0x124dd3)):this['_processTreeNodeResult'](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x2b8765=_0x3f13ca;_0x4c3356===_0x2b8765(0x187)||_0x4c3356===_0x2b8765(0x142)||(delete _0x124dd3[_0x2b8765(0xce)],_0x124dd3[_0x2b8765(0xf3)]=!0x0);});}return _0x124dd3;}finally{_0x53d319['expressionsToEvaluate']=_0x42837e,_0x53d319[_0x3f13ca(0xf0)]=_0x265c6d,_0x53d319['isExpressionToEvaluate']=_0x31debf;}}[_0x40c820(0xe9)](_0x3711dd,_0x3273d6,_0x30712a,_0x2bfd2c){var _0x22c791=_0x40c820,_0x3ed3d6=_0x2bfd2c['strLength']||_0x30712a[_0x22c791(0x126)];if((_0x3711dd==='string'||_0x3711dd==='String')&&_0x3273d6[_0x22c791(0xce)]){let _0xffdbb0=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x14f)];_0x30712a['allStrLength']+=_0xffdbb0,_0x30712a[_0x22c791(0xa2)]>_0x30712a['totalStrLength']?(_0x3273d6[_0x22c791(0xf3)]='',delete _0x3273d6[_0x22c791(0xce)]):_0xffdbb0>_0x3ed3d6&&(_0x3273d6[_0x22c791(0xf3)]=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x147)](0x0,_0x3ed3d6),delete _0x3273d6[_0x22c791(0xce)]);}}['_isMap'](_0x261c40){var _0xd2ec63=_0x40c820;return!!(_0x261c40&&_0x3830e6['Map']&&this[_0xd2ec63(0x156)](_0x261c40)===_0xd2ec63(0x114)&&_0x261c40[_0xd2ec63(0x178)]);}[_0x40c820(0xd5)](_0x24e250){var _0x49be73=_0x40c820;if(_0x24e250[_0x49be73(0x132)](/^\\\\d+$/))return _0x24e250;var _0x1d38d;try{_0x1d38d=JSON['stringify'](''+_0x24e250);}catch{_0x1d38d='\\\\x22'+this['_objectToString'](_0x24e250)+'\\\\x22';}return _0x1d38d['match'](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x1d38d=_0x1d38d[_0x49be73(0x147)](0x1,_0x1d38d[_0x49be73(0x14f)]-0x2):_0x1d38d=_0x1d38d[_0x49be73(0x16d)](/'/g,'\\\\x5c\\\\x27')[_0x49be73(0x16d)](/\\\\\\\\\\\"/g,'\\\\x22')[_0x49be73(0x16d)](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x1d38d;}[_0x40c820(0x133)](_0x1b7571,_0x8f439b,_0x2c2981,_0x188fd5){var _0xf97f10=_0x40c820;this[_0xf97f10(0xa8)](_0x1b7571,_0x8f439b),_0x188fd5&&_0x188fd5(),this[_0xf97f10(0x10f)](_0x2c2981,_0x1b7571),this['_treeNodePropertiesAfterFullValue'](_0x1b7571,_0x8f439b);}[_0x40c820(0xa8)](_0x231eb3,_0x5ea482){var _0x31f690=_0x40c820;this['_setNodeId'](_0x231eb3,_0x5ea482),this[_0x31f690(0xd1)](_0x231eb3,_0x5ea482),this[_0x31f690(0x111)](_0x231eb3,_0x5ea482),this[_0x31f690(0x125)](_0x231eb3,_0x5ea482);}[_0x40c820(0xcb)](_0x10d2e1,_0x3c8083){}[_0x40c820(0xd1)](_0x53d949,_0x188c67){}[_0x40c820(0x163)](_0x3f8259,_0x16e80a){}[_0x40c820(0x122)](_0x25a3a3){return _0x25a3a3===this['_undefined'];}[_0x40c820(0xe4)](_0x44987b,_0x4ed592){var _0x37fb3e=_0x40c820;this[_0x37fb3e(0x163)](_0x44987b,_0x4ed592),this[_0x37fb3e(0xeb)](_0x44987b),_0x4ed592[_0x37fb3e(0xfb)]&&this[_0x37fb3e(0x12d)](_0x44987b),this['_addFunctionsNode'](_0x44987b,_0x4ed592),this[_0x37fb3e(0x17e)](_0x44987b,_0x4ed592),this[_0x37fb3e(0x16c)](_0x44987b);}['_additionalMetadata'](_0x1c2784,_0x4c1dde){var _0x4f21c1=_0x40c820;try{_0x1c2784&&typeof _0x1c2784[_0x4f21c1(0x14f)]==_0x4f21c1(0x12b)&&(_0x4c1dde[_0x4f21c1(0x14f)]=_0x1c2784[_0x4f21c1(0x14f)]);}catch{}if(_0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x12b)||_0x4c1dde[_0x4f21c1(0x10b)]==='Number'){if(isNaN(_0x4c1dde[_0x4f21c1(0xce)]))_0x4c1dde[_0x4f21c1(0x18d)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];else switch(_0x4c1dde[_0x4f21c1(0xce)]){case Number[_0x4f21c1(0xfa)]:_0x4c1dde[_0x4f21c1(0x102)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case Number['NEGATIVE_INFINITY']:_0x4c1dde[_0x4f21c1(0x189)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case 0x0:this['_isNegativeZero'](_0x4c1dde[_0x4f21c1(0xce)])&&(_0x4c1dde[_0x4f21c1(0xd0)]=!0x0);break;}}else _0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x13e)&&typeof _0x1c2784['name']==_0x4f21c1(0x101)&&_0x1c2784[_0x4f21c1(0xb7)]&&_0x4c1dde['name']&&_0x1c2784[_0x4f21c1(0xb7)]!==_0x4c1dde[_0x4f21c1(0xb7)]&&(_0x4c1dde[_0x4f21c1(0xad)]=_0x1c2784[_0x4f21c1(0xb7)]);}['_isNegativeZero'](_0x289882){var _0x1b66c9=_0x40c820;return 0x1/_0x289882===Number[_0x1b66c9(0x181)];}['_sortProps'](_0x3992ee){var _0x3db550=_0x40c820;!_0x3992ee[_0x3db550(0x180)]||!_0x3992ee[_0x3db550(0x180)][_0x3db550(0x14f)]||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0x130)||_0x3992ee[_0x3db550(0x10b)]==='Map'||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0xf6)||_0x3992ee[_0x3db550(0x180)][_0x3db550(0x175)](function(_0x57a739,_0x31b40b){var _0x5dcaae=_0x3db550,_0x3d0d50=_0x57a739[_0x5dcaae(0xb7)][_0x5dcaae(0x10e)](),_0xd6d4fc=_0x31b40b[_0x5dcaae(0xb7)]['toLowerCase']();return _0x3d0d50<_0xd6d4fc?-0x1:_0x3d0d50>_0xd6d4fc?0x1:0x0;});}[_0x40c820(0xe8)](_0x12537a,_0x57f3dc){var _0x2884a4=_0x40c820;if(!(_0x57f3dc[_0x2884a4(0xa4)]||!_0x12537a['props']||!_0x12537a[_0x2884a4(0x180)][_0x2884a4(0x14f)])){for(var _0x53c006=[],_0x347d6e=[],_0x52e85a=0x0,_0x273297=_0x12537a['props']['length'];_0x52e85a<_0x273297;_0x52e85a++){var _0x1ee5b3=_0x12537a[_0x2884a4(0x180)][_0x52e85a];_0x1ee5b3[_0x2884a4(0x10b)]==='function'?_0x53c006[_0x2884a4(0x157)](_0x1ee5b3):_0x347d6e['push'](_0x1ee5b3);}if(!(!_0x347d6e[_0x2884a4(0x14f)]||_0x53c006[_0x2884a4(0x14f)]<=0x1)){_0x12537a[_0x2884a4(0x180)]=_0x347d6e;var _0x15f515={'functionsNode':!0x0,'props':_0x53c006};this[_0x2884a4(0xcb)](_0x15f515,_0x57f3dc),this[_0x2884a4(0x163)](_0x15f515,_0x57f3dc),this[_0x2884a4(0xeb)](_0x15f515),this[_0x2884a4(0x125)](_0x15f515,_0x57f3dc),_0x15f515['id']+='\\\\x20f',_0x12537a['props']['unshift'](_0x15f515);}}}['_addLoadNode'](_0x5bea6e,_0x14049e){}[_0x40c820(0xeb)](_0x199084){}[_0x40c820(0x98)](_0xf50c17){var _0x35cb98=_0x40c820;return Array['isArray'](_0xf50c17)||typeof _0xf50c17==_0x35cb98(0x117)&&this['_objectToString'](_0xf50c17)===_0x35cb98(0xdc);}[_0x40c820(0x125)](_0x3ea390,_0x54c209){}[_0x40c820(0x16c)](_0x25cdb9){var _0x1aa0a5=_0x40c820;delete _0x25cdb9[_0x1aa0a5(0x136)],delete _0x25cdb9[_0x1aa0a5(0x143)],delete _0x25cdb9[_0x1aa0a5(0x138)];}['_setNodeExpressionPath'](_0x17f351,_0x40c77e){}}let _0x459cb0=new _0x1e3ba1(),_0x218fe5={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x11fc4c={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x482c8e(_0x50675f,_0x2f7559,_0x19c481,_0x2c8a95,_0x245f16,_0x24484e){var _0x4c67af=_0x40c820;let _0x3ce9b8,_0x1af844;try{_0x1af844=_0x1169a5(),_0x3ce9b8=_0x2f8209[_0x2f7559],!_0x3ce9b8||_0x1af844-_0x3ce9b8['ts']>0x1f4&&_0x3ce9b8[_0x4c67af(0xaa)]&&_0x3ce9b8['time']/_0x3ce9b8[_0x4c67af(0xaa)]<0x64?(_0x2f8209[_0x2f7559]=_0x3ce9b8={'count':0x0,'time':0x0,'ts':_0x1af844},_0x2f8209[_0x4c67af(0xc5)]={}):_0x1af844-_0x2f8209[_0x4c67af(0xc5)]['ts']>0x32&&_0x2f8209['hits']['count']&&_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x161)]/_0x2f8209[_0x4c67af(0xc5)]['count']<0x64&&(_0x2f8209[_0x4c67af(0xc5)]={});let _0x157126=[],_0x1464d6=_0x3ce9b8[_0x4c67af(0x112)]||_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]?_0x11fc4c:_0x218fe5,_0x553948=_0x2ff7b4=>{var _0x3a1316=_0x4c67af;let _0x5d676c={};return _0x5d676c[_0x3a1316(0x180)]=_0x2ff7b4[_0x3a1316(0x180)],_0x5d676c['elements']=_0x2ff7b4['elements'],_0x5d676c[_0x3a1316(0x126)]=_0x2ff7b4[_0x3a1316(0x126)],_0x5d676c['totalStrLength']=_0x2ff7b4['totalStrLength'],_0x5d676c['autoExpandLimit']=_0x2ff7b4[_0x3a1316(0x177)],_0x5d676c[_0x3a1316(0x115)]=_0x2ff7b4['autoExpandMaxDepth'],_0x5d676c['sortProps']=!0x1,_0x5d676c['noFunctions']=!_0x45a6b5,_0x5d676c['depth']=0x1,_0x5d676c[_0x3a1316(0xe3)]=0x0,_0x5d676c['expId']='root_exp_id',_0x5d676c[_0x3a1316(0x140)]=_0x3a1316(0x151),_0x5d676c['autoExpand']=!0x0,_0x5d676c[_0x3a1316(0x118)]=[],_0x5d676c[_0x3a1316(0x9d)]=0x0,_0x5d676c[_0x3a1316(0x168)]=!0x0,_0x5d676c['allStrLength']=0x0,_0x5d676c[_0x3a1316(0xc8)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0x5d676c;};for(var _0x33de8f=0x0;_0x33de8f<_0x245f16[_0x4c67af(0x14f)];_0x33de8f++)_0x157126[_0x4c67af(0x157)](_0x459cb0[_0x4c67af(0x14a)]({'timeNode':_0x50675f===_0x4c67af(0x161)||void 0x0},_0x245f16[_0x33de8f],_0x553948(_0x1464d6),{}));if(_0x50675f==='trace'||_0x50675f===_0x4c67af(0x184)){let _0x1d9735=Error[_0x4c67af(0xd7)];try{Error[_0x4c67af(0xd7)]=0x1/0x0,_0x157126[_0x4c67af(0x157)](_0x459cb0['serialize']({'stackNode':!0x0},new Error()['stack'],_0x553948(_0x1464d6),{'strLength':0x1/0x0}));}finally{Error[_0x4c67af(0xd7)]=_0x1d9735;}}return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':_0x157126,'id':_0x2f7559,'context':_0x24484e}]};}catch(_0x2d5a77){return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':[{'type':_0x4c67af(0xca),'error':_0x2d5a77&&_0x2d5a77[_0x4c67af(0xb4)]}],'id':_0x2f7559,'context':_0x24484e}]};}finally{try{if(_0x3ce9b8&&_0x1af844){let _0xff386f=_0x1169a5();_0x3ce9b8[_0x4c67af(0xaa)]++,_0x3ce9b8['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x3ce9b8['ts']=_0xff386f,_0x2f8209[_0x4c67af(0xc5)]['count']++,_0x2f8209[_0x4c67af(0xc5)]['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x2f8209['hits']['ts']=_0xff386f,(_0x3ce9b8[_0x4c67af(0xaa)]>0x32||_0x3ce9b8[_0x4c67af(0x161)]>0x64)&&(_0x3ce9b8['reduceLimits']=!0x0),(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0xaa)]>0x3e8||_0x2f8209[_0x4c67af(0xc5)]['time']>0x12c)&&(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]=!0x0);}}catch{}}}return _0x482c8e;}((_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x296e29,_0x567fe9,_0x14adfa,_0x6b3989,_0x593945,_0x42f609)=>{var _0x543ef9=_0x418f23;if(_0x12a02f[_0x543ef9(0xdb)])return _0x12a02f[_0x543ef9(0xdb)];if(!X(_0x12a02f,_0x14adfa,_0x1164b7))return _0x12a02f[_0x543ef9(0xdb)]={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x12a02f[_0x543ef9(0xdb)];let _0x5a7d78=B(_0x12a02f),_0x236b4f=_0x5a7d78[_0x543ef9(0xd3)],_0x57b9d9=_0x5a7d78['timeStamp'],_0x163b61=_0x5a7d78[_0x543ef9(0x11b)],_0x384cd9={'hits':{},'ts':{}},_0x9c7997=J(_0x12a02f,_0x6b3989,_0x384cd9,_0x296e29),_0x3ffb36=_0xa1ec34=>{_0x384cd9['ts'][_0xa1ec34]=_0x57b9d9();},_0x4ce4d2=(_0x173258,_0x2e0c6d)=>{var _0x3b6e53=_0x543ef9;let _0x2b64c1=_0x384cd9['ts'][_0x2e0c6d];if(delete _0x384cd9['ts'][_0x2e0c6d],_0x2b64c1){let _0x557981=_0x236b4f(_0x2b64c1,_0x57b9d9());_0x593a90(_0x9c7997(_0x3b6e53(0x161),_0x173258,_0x163b61(),_0x50e896,[_0x557981],_0x2e0c6d));}},_0x46c5f3=_0x1f105e=>{var _0x1152c8=_0x543ef9,_0x4a2783;return _0x1164b7===_0x1152c8(0x110)&&_0x12a02f[_0x1152c8(0x152)]&&((_0x4a2783=_0x1f105e==null?void 0x0:_0x1f105e[_0x1152c8(0xab)])==null?void 0x0:_0x4a2783[_0x1152c8(0x14f)])&&(_0x1f105e[_0x1152c8(0xab)][0x0][_0x1152c8(0x152)]=_0x12a02f[_0x1152c8(0x152)]),_0x1f105e;};_0x12a02f[_0x543ef9(0xdb)]={'consoleLog':(_0x204f4b,_0x3e1804)=>{var _0x309615=_0x543ef9;_0x12a02f['console'][_0x309615(0xff)][_0x309615(0xb7)]!==_0x309615(0x134)&&_0x593a90(_0x9c7997(_0x309615(0xff),_0x204f4b,_0x163b61(),_0x50e896,_0x3e1804));},'consoleTrace':(_0x267a3f,_0x51c339)=>{var _0x4c4943=_0x543ef9,_0x33fd8c,_0x24e61f;_0x12a02f[_0x4c4943(0xbc)][_0x4c4943(0xff)]['name']!==_0x4c4943(0xbd)&&((_0x24e61f=(_0x33fd8c=_0x12a02f[_0x4c4943(0x104)])==null?void 0x0:_0x33fd8c['versions'])!=null&&_0x24e61f[_0x4c4943(0xc8)]&&(_0x12a02f[_0x4c4943(0x165)]=!0x0),_0x593a90(_0x46c5f3(_0x9c7997(_0x4c4943(0xc4),_0x267a3f,_0x163b61(),_0x50e896,_0x51c339))));},'consoleError':(_0xf7f1fc,_0x1384d7)=>{var _0x28d83c=_0x543ef9;_0x12a02f['_ninjaIgnoreNextError']=!0x0,_0x593a90(_0x46c5f3(_0x9c7997(_0x28d83c(0x184),_0xf7f1fc,_0x163b61(),_0x50e896,_0x1384d7)));},'consoleTime':_0x2ad865=>{_0x3ffb36(_0x2ad865);},'consoleTimeEnd':(_0x3c91cf,_0x308c8b)=>{_0x4ce4d2(_0x308c8b,_0x3c91cf);},'autoLog':(_0x4bbc9f,_0x3599a3)=>{var _0x598cfa=_0x543ef9;_0x593a90(_0x9c7997(_0x598cfa(0xff),_0x3599a3,_0x163b61(),_0x50e896,[_0x4bbc9f]));},'autoLogMany':(_0x158592,_0x29b77d)=>{var _0x425f64=_0x543ef9;_0x593a90(_0x9c7997(_0x425f64(0xff),_0x158592,_0x163b61(),_0x50e896,_0x29b77d));},'autoTrace':(_0x3f5f9d,_0xc378ab)=>{var _0x377a7d=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x377a7d(0xc4),_0xc378ab,_0x163b61(),_0x50e896,[_0x3f5f9d])));},'autoTraceMany':(_0x2c6f73,_0x35405b)=>{var _0x4f4e7f=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x4f4e7f(0xc4),_0x2c6f73,_0x163b61(),_0x50e896,_0x35405b)));},'autoTime':(_0x4915d6,_0xaaf0db,_0x4c1f1e)=>{_0x3ffb36(_0x4c1f1e);},'autoTimeEnd':(_0x397624,_0x436d57,_0x47b9b8)=>{_0x4ce4d2(_0x436d57,_0x47b9b8);},'coverage':_0x45a646=>{var _0x1e9860=_0x543ef9;_0x593a90({'method':_0x1e9860(0x13a),'version':_0x296e29,'args':[{'id':_0x45a646}]});}};let _0x593a90=H(_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x593945,_0x42f609),_0x50e896=_0x12a02f[_0x543ef9(0xd8)];return _0x12a02f[_0x543ef9(0xdb)];})(globalThis,'127.0.0.1',_0x418f23(0xec),_0x418f23(0x17a),_0x418f23(0x183),'1.0.0','1751470503293',_0x418f23(0x137),_0x418f23(0x18a),_0x418f23(0x10a),_0x418f23(0xb1));\");}catch(e){}};/* istanbul ignore next */function oo_oo(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleLog(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_tr(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleTrace(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_tx(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleError(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_ts(/**@type{any}**/v){try{oo_cm().consoleTime(v);}catch(e){} return v;};/* istanbul ignore next */function oo_te(/**@type{any}**/v, /**@type{any}**/i){try{oo_cm().consoleTimeEnd(v, i);}catch(e){} return v;};/*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/"]}, "metadata": {}, "sourceType": "module"}