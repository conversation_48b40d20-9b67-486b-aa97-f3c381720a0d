<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Operation
 * 
 * @property int $operation_id
 * @property string $operation_code
 * @property string $operation_name
 * @property bool $status
 * @property string $sunat_code
 * @property string $type
 * @property string $direction
 * @property string $scopes
 * @property int|null $accounting_file_id
 * @property bool $group_entries
 * @property string $type_dynamic
 * @property bool $is_reversal
 * @property int $entry_count
 * @property int|null $dictionary_id
 * @property bool|null $require_project
 * @property bool|null $not_scheduled_to_pay
 * @property bool|null $not_scheduled_to_redeem
 * 
 * @property AccountingFile|null $accounting_file
 * @property Dictionary|null $dictionary
 * @property Collection|InventoryReason[] $inventory_reasons
 * @property Collection|Movement[] $movements
 * @property Collection|OperationDynamic[] $operation_dynamics
 *
 * @package App\Models
 */
class Operation extends Model
{
	protected $table = 'operation';
	protected $primaryKey = 'operation_code';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'operation_id' => 'int',
		'status' => 'bool',
		'accounting_file_id' => 'int',
		'group_entries' => 'bool',
		'is_reversal' => 'bool',
		'entry_count' => 'int',
		'dictionary_id' => 'int',
		'require_project' => 'bool',
		'not_scheduled_to_pay' => 'bool',
		'not_scheduled_to_redeem' => 'bool'
	];

	protected $fillable = [
		'operation_id',
		'operation_name',
		'status',
		'sunat_code',
		'type',
		'direction',
		'scopes',
		'accounting_file_id',
		'group_entries',
		'type_dynamic',
		'is_reversal',
		'entry_count',
		'dictionary_id',
		'require_project',
		'not_scheduled_to_pay',
		'not_scheduled_to_redeem'
	];

	public function accounting_file()
	{
		return $this->belongsTo(AccountingFile::class);
	}

	public function dictionary()
	{
		return $this->belongsTo(Dictionary::class);
	}

	public function inventory_reasons()
	{
		return $this->hasMany(InventoryReason::class, 'operation_id', 'operation_id');
	}

	public function movements()
	{
		return $this->hasMany(Movement::class, 'operation_code');
	}

	public function operation_dynamics()
	{
		return $this->hasMany(OperationDynamic::class, 'operation_code');
	}
}
