<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class WebUser
 * 
 * @property int $web_user_id
 * @property string $email_address
 * @property string $firstname
 * @property string $lastname
 * @property bool $status
 * @property bool $verified
 * @property string $genre
 * @property Carbon|null $birthday
 * @property string|null $phone_number
 * @property int|null $person_id
 * @property int|null $seller_id
 * @property string $password
 * @property int|null $shopping_cart_id
 * @property int $billing_address_count
 * @property int $delivery_address_count
 * @property bool $is_guest
 * 
 * @property Person|null $person
 * @property Seller|null $seller
 * @property ShoppingCart|null $shopping_cart
 * @property Collection|Movement[] $movements
 *
 * @package App\Models
 */
class WebUser extends Model
{
	protected $table = 'web_user';
	protected $primaryKey = 'web_user_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'verified' => 'bool',
		'person_id' => 'int',
		'seller_id' => 'int',
		'shopping_cart_id' => 'int',
		'billing_address_count' => 'int',
		'delivery_address_count' => 'int',
		'is_guest' => 'bool'
	];

	protected $dates = [
		'birthday'
	];

	protected $hidden = [
		'password'
	];

	protected $fillable = [
		'email_address',
		'firstname',
		'lastname',
		'status',
		'verified',
		'genre',
		'birthday',
		'phone_number',
		'person_id',
		'seller_id',
		'password',
		'shopping_cart_id',
		'billing_address_count',
		'delivery_address_count',
		'is_guest'
	];

	public function person()
	{
		return $this->belongsTo(Person::class);
	}

	public function seller()
	{
		return $this->belongsTo(Seller::class);
	}

	public function shopping_cart()
	{
		return $this->belongsTo(ShoppingCart::class);
	}

	public function movements()
	{
		return $this->hasMany(Movement::class);
	}
}
