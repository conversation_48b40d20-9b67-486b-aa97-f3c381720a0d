<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Person
 * 
 * @property int $person_id
 * @property string $person_name
 * @property string|null $description
 * @property string $identification_type
 * @property string $identification_number
 * @property Carbon|null $birthday
 * @property bool $client_credit
 * @property float $client_line_pen
 * @property float $client_balance_pen
 * @property float $client_expired_debt_pen
 * @property float $client_no_expired_debt_pen
 * @property float $client_line_usd
 * @property float $client_balance_usd
 * @property float $client_expired_debt_usd
 * @property float $client_no_expired_debt_usd
 * @property int $client_credit_days
 * @property bool $client_expired
 * @property bool $provider_credit
 * @property float $provider_line_pen
 * @property float $provider_balance_pen
 * @property float $provider_expired_debt_pen
 * @property float $provider_no_expired_debt_pen
 * @property float $provider_line_usd
 * @property float $provider_balance_usd
 * @property float $provider_expired_debt_usd
 * @property float $provider_no_expired_debt_usd
 * @property int $provider_credit_days
 * @property bool $provider_expired
 * @property Carbon|null $credit_expiration
 * @property string $catchment
 * @property bool $status
 * @property bool $retention
 * @property bool $advanced
 * @property bool $is_trusted
 * @property bool $is_gov
 * @property bool $is_dealer
 * @property string $country_iso
 * @property int|null $country
 * @property bool|null $not_domiciled
 * @property string $verification
 * @property bool $is_associated
 * @property string|null $type
 * @property int|null $user_catchment_id
 * @property int|null $category_id
 * @property int $convention
 * 
 * @property Seller|null $seller
 * @property Multitable|null $multitable
 * @property Collection|ApiUser[] $api_users
 * @property Collection|ApiUserAdminVariant[] $api_user_admin_variants
 * @property Collection|Batch[] $batches
 * @property Collection|Changelog[] $changelogs
 * @property Collection|Claim[] $claims
 * @property Collection|CommercialCase[] $commercial_cases
 * @property Collection|CommercialCaseDetail[] $commercial_case_details
 * @property Collection|CommercialGoal[] $commercial_goals
 * @property Collection|Confirmation[] $confirmations
 * @property Collection|Contact[] $contacts
 * @property ECommerceProvider $e_commerce_provider
 * @property Employee $employee
 * @property Collection|EntryGroup[] $entry_groups
 * @property Collection|GlobalVar[] $global_vars
 * @property Collection|Inventory[] $inventories
 * @property Collection|InventoryDetailCounting[] $inventory_detail_countings
 * @property Collection|Locker[] $lockers
 * @property Collection|LockerRequest[] $locker_requests
 * @property Collection|MobilityPerson[] $mobility_people
 * @property Collection|Movement[] $movements
 * @property Collection|MovementLog[] $movement_logs
 * @property Collection|MultipleMovement[] $multiple_movements
 * @property Collection|NetworkingProject[] $networking_projects
 * @property Collection|Organization[] $organizations
 * @property PensionSystem $pension_system
 * @property Collection|Project[] $projects
 * @property Collection|Technical[] $technicals
 * @property Collection|User[] $users
 * @property Collection|WarehouseMovement[] $warehouse_movements
 * @property Collection|WebUser[] $web_users
 *
 * @package App\Models
 */
class Person extends Model
{
	protected $table = 'person';
	protected $primaryKey = 'person_id';
	public $timestamps = false;

	const TYPE_NATURAL = 'Natural';
	const TYPE_JURIDICAL = 'Juridica';
	const TYPE_ANY = 'Cualquiera';
	//CODIGOS
	const DNI = '1';
	const CARNET = '4';
	const RUC = '6';
	const PASAPORTE = '7';
	const CEDULA = 'A';
	const OTRO = '0';
	//NOMBRES
	const DNI_NOMBRE = 'DNI';
	const CARNET_NOMBRE = 'Carnet de extranjería';
	const RUC_NOMBRE = 'RUC';
	const PASAPORTE_NOMBRE = 'Pasaporte';
	const CEDULA_NOMBRE = 'Cédula diplomática de indentidad';
	const OTRO_NOMBRE = 'Otro';

	protected $casts = [
		'client_credit' => 'bool',
		'client_line_pen' => 'float',
		'client_balance_pen' => 'float',
		'client_expired_debt_pen' => 'float',
		'client_no_expired_debt_pen' => 'float',
		'client_line_usd' => 'float',
		'client_balance_usd' => 'float',
		'client_expired_debt_usd' => 'float',
		'client_no_expired_debt_usd' => 'float',
		'client_credit_days' => 'int',
		'client_expired' => 'bool',
		'provider_credit' => 'bool',
		'provider_line_pen' => 'float',
		'provider_balance_pen' => 'float',
		'provider_expired_debt_pen' => 'float',
		'provider_no_expired_debt_pen' => 'float',
		'provider_line_usd' => 'float',
		'provider_balance_usd' => 'float',
		'provider_expired_debt_usd' => 'float',
		'provider_no_expired_debt_usd' => 'float',
		'provider_credit_days' => 'int',
		'provider_expired' => 'bool',
		'status' => 'bool',
		'retention' => 'bool',
		'advanced' => 'bool',
		'is_trusted' => 'bool',
		'is_gov' => 'bool',
		'is_dealer' => 'bool',
		'country' => 'int',
		'not_domiciled' => 'bool',
		'is_associated' => 'bool',
		'user_catchment_id' => 'int',
		'category_id' => 'int',
		'convention' => 'int'
	];

	protected $dates = [
		'birthday',
		'credit_expiration'
	];

	protected $fillable = [
		'person_name',
		'description',
		'identification_type',
		'identification_number',
		'birthday',
		'client_credit',
		'client_line_pen',
		'client_balance_pen',
		'client_expired_debt_pen',
		'client_no_expired_debt_pen',
		'client_line_usd',
		'client_balance_usd',
		'client_expired_debt_usd',
		'client_no_expired_debt_usd',
		'client_credit_days',
		'client_expired',
		'provider_credit',
		'provider_line_pen',
		'provider_balance_pen',
		'provider_expired_debt_pen',
		'provider_no_expired_debt_pen',
		'provider_line_usd',
		'provider_balance_usd',
		'provider_expired_debt_usd',
		'provider_no_expired_debt_usd',
		'provider_credit_days',
		'provider_expired',
		'credit_expiration',
		'catchment',
		'status',
		'retention',
		'advanced',
		'is_trusted',
		'is_gov',
		'is_dealer',
		'country_iso',
		'country',
		'not_domiciled',
		'verification',
		'is_associated',
		'type',
		'user_catchment_id',
		'category_id',
		'convention'
	];

	public function seller()
	{
		return $this->belongsTo(Seller::class, 'user_catchment_id');
	}

	public function multitable()
	{
		return $this->belongsTo(Multitable::class, 'country');
	}

	public function api_users()
	{
		return $this->hasMany(ApiUser::class);
	}

	public function api_user_admin_variants()
	{
		return $this->hasMany(ApiUserAdminVariant::class);
	}

	public function batches()
	{
		return $this->hasMany(Batch::class, 'user_register');
	}

	public function changelogs()
	{
		return $this->hasMany(Changelog::class);
	}

	public function claims()
	{
		return $this->hasMany(Claim::class, 'user_response');
	}

	public function commercial_cases()
	{
		return $this->hasMany(CommercialCase::class, 'aux_person_id');
	}

	public function commercial_case_details()
	{
		return $this->hasMany(CommercialCaseDetail::class, 'user_register');
	}

	public function commercial_goals()
	{
		return $this->hasMany(CommercialGoal::class);
	}

	public function confirmations()
	{
		return $this->hasMany(Confirmation::class, 'user_request');
	}

	public function contacts()
	{
		return $this->hasMany(Contact::class);
	}

	public function e_commerce_provider()
	{
		return $this->hasOne(ECommerceProvider::class);
	}

	public function employee()
	{
		return $this->hasOne(Employee::class);
	}

	public function entry_groups()
	{
		return $this->hasMany(EntryGroup::class);
	}

	public function global_vars()
	{
		return $this->hasMany(GlobalVar::class, 'sunat_id');
	}

	public function inventories()
	{
		return $this->hasMany(Inventory::class, 'user_reject');
	}

	public function inventory_detail_countings()
	{
		return $this->hasMany(InventoryDetailCounting::class);
	}

	public function lockers()
	{
		return $this->hasMany(Locker::class);
	}

	public function locker_requests()
	{
		return $this->hasMany(LockerRequest::class, 'person_deliverer_id');
	}

	public function mobility_people()
	{
		return $this->hasMany(MobilityPerson::class);
	}

	public function movements()
	{
		return $this->hasMany(Movement::class, 'person_in_charge_id');
	}

	public function movement_logs()
	{
		return $this->hasMany(MovementLog::class);
	}

	public function multiple_movements()
	{
		return $this->hasMany(MultipleMovement::class, 'aval_id');
	}

	public function networking_projects()
	{
		return $this->hasMany(NetworkingProject::class, 'responsible_id');
	}

	public function organizations()
	{
		return $this->hasMany(Organization::class);
	}

	public function pension_system()
	{
		return $this->hasOne(PensionSystem::class);
	}

	public function projects()
	{
		return $this->hasMany(Project::class);
	}

	public function technicals()
	{
		return $this->hasMany(Technical::class);
	}

	public function users()
	{
		return $this->hasMany(User::class);
	}

	public function warehouse_movements()
	{
		return $this->hasMany(WarehouseMovement::class, 'transport_company_id');
	}

	public function web_users()
	{
		return $this->hasMany(WebUser::class);
	}

	public static function getItems()
	{
		return array(
			self::DNI => self::DNI_NOMBRE,
			self::CARNET => self::CARNET_NOMBRE,
			self::RUC => self::RUC_NOMBRE,
			self::PASAPORTE => self::PASAPORTE_NOMBRE,
			self::CEDULA => self::CEDULA_NOMBRE,
			self::OTRO => self::OTRO_NOMBRE
		);
	}
}
