{"ast": null, "code": "var _jsxFileName = \"D:\\\\www\\\\SWF\\\\sian-web-frontend\\\\src\\\\views\\\\commercial\\\\salesDashboard\\\\components\\\\AIChat.jsx\",\n    _s = $RefreshSig$();\n\n/* eslint-disable */\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Box, ThemeProvider } from '@mui/material';\nimport { purpleTheme } from './chat/theme';\nimport ChatButton from './chat/chatButton';\nimport ChatDialog from './chat/chatDialog';\nimport * as XLSX from 'xlsx'; // Format currency function\n\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst formatCurrency = value => {\n  return new Intl.NumberFormat('es-PE', {\n    style: 'currency',\n    currency: 'PEN',\n    minimumFractionDigits: 2\n  }).format(value);\n};\n\nconst AIChat = _ref => {\n  _s();\n\n  let {\n    apiKey = process.env.REACT_APP_OPENAI_API_KEY,\n    model = 'gpt-4o-mini',\n    temperature = 0.7,\n    apiEndpoint = process.env.REACT_APP_OPENAI_URL_BASE,\n    onError = error =>\n    /* eslint-disable */\n    console.error(...oo_tx(`798630434_23_25_23_55_11`, 'Error:', error)),\n    salesAdvanceData,\n    salesHoursData,\n    salesDivisionData,\n    salesStoreData,\n    salesLinesData\n  } = _ref;\n  const [openDialog, setOpenDialog] = useState(false);\n  const [userInput, setUserInput] = useState('');\n  const [chatHistory, setChatHistory] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [selectedChart, setSelectedChart] = useState(null);\n  const [systemPrompt, setSystemPrompt] = useState('');\n  const chatEndRef = useRef(null);\n  const analysisTriggeredRef = useRef(false); // Lista de gráficos disponibles\n\n  const chartsList = [{\n    id: 'sales-advance',\n    name: 'Avances de Ventas Por Tienda'\n  }, {\n    id: 'sales-hours',\n    name: 'Participación de Ventas por Hora'\n  }, {\n    id: 'sales-division',\n    name: 'Participación de Ventas por División'\n  }, {\n    id: 'sales-store',\n    name: 'Participación de Ventas por Tienda'\n  }, {\n    id: 'sales-lines',\n    name: 'Detalle de Ventas por División y Líneas'\n  }]; // Mensajes para otros gráficos (los que se muestran al usuario)\n\n  const systemMessages = {\n    'sales-advance': 'Estás analizando el avance de ventas por tienda. Usa la información del análisis inicial para responder preguntas sobre rendimiento, metas y recomendaciones específicas.',\n    'sales-hours': 'Estás analizando la participación de ventas por hora. Usa la información del análisis inicial para responder preguntas sobre patrones de tráfico, horas pico y recomendaciones específicas.',\n    'sales-division': 'Has seleccionado \"Participación de Ventas por División\". Pregunta lo que necesites sobre este gráfico.',\n    'sales-store': 'Has seleccionado \"Participación de Ventas por Tienda\". Pregunta lo que necesites sobre este gráfico.',\n    'sales-lines': 'Has seleccionado \"Detalle de Ventas por División y Líneas\". Pregunta lo que necesites sobre esta tabla.'\n  };\n\n  const exportToExcel = () => {\n    // Seleccionamos los datos según el gráfico seleccionado\n    let dataToExport = [];\n    let fileName = 'reporte.xlsx';\n    let sheetName = 'Datos';\n\n    switch (selectedChart) {\n      case 'sales-advance':\n        if (salesAdvanceData) {\n          dataToExport = salesAdvanceData.map(item => ({\n            Tienda: item.name,\n            'Meta Mensual': item.monthlyGoal,\n            Ventas: item.total,\n            'Avance (%)': item.monthlyProgress\n          }));\n          fileName = 'avance_ventas_tienda.xlsx';\n          sheetName = 'Avance de Ventas';\n        }\n\n        break;\n\n      case 'sales-hours':\n        if (salesHoursData) {\n          dataToExport = salesHoursData.map(item => ({\n            Hora: item.hour,\n            Ventas: item.value,\n            'Valor Formateado': item.formattedValue\n          }));\n          fileName = 'ventas_por_hora.xlsx';\n          sheetName = 'Ventas por Hora';\n        }\n\n        break;\n\n      case 'sales-division':\n        if (salesDivisionData) {\n          dataToExport = salesDivisionData.map(item => ({\n            División: item.name,\n            Valor: item.value,\n            'Porcentaje (%)': item.percentage.toFixed(2)\n          }));\n          fileName = 'ventas_por_division.xlsx';\n          sheetName = 'Ventas por División';\n        }\n\n        break;\n\n      case 'sales-store':\n        if (salesStoreData) {\n          dataToExport = salesStoreData.map(item => ({\n            Tienda: item.name,\n            Valor: item.value,\n            'Porcentaje (%)': item.percentage.toFixed(2)\n          }));\n          fileName = 'ventas_por_tienda.xlsx';\n          sheetName = 'Ventas por Tienda';\n        }\n\n        break;\n\n      case 'sales-lines':\n        if (salesLinesData) {\n          dataToExport = salesLinesData.map(item => ({\n            División: item.division,\n            Línea: item.line,\n            Valor: item.value,\n            'Porcentaje (%)': item.percentage.toFixed(2)\n          }));\n          fileName = 'ventas_por_division_linea.xlsx';\n          sheetName = 'Ventas por División y Línea';\n        }\n\n        break;\n\n      default:\n        break;\n    }\n\n    if (dataToExport.length > 0) {\n      // Crear una nueva hoja de trabajo\n      const worksheet = XLSX.utils.json_to_sheet(dataToExport); // Crear un nuevo libro de trabajo\n\n      const workbook = XLSX.utils.book_new(); // Añadir la hoja de trabajo al libro\n\n      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName); // Generar el archivo Excel\n\n      XLSX.writeFile(workbook, fileName);\n    } else {\n      /* eslint-disable */\n      console.error(...oo_tx(`798630434_138_12_138_55_11`, 'No hay datos para exportar'));\n    }\n  }; // Función para llamar a la API utilizando un mensaje interno (no visible al usuario)\n\n\n  const analyzeData = async (internalMessage, chartType) => {\n    setIsLoading(true); // Guardamos este mensaje como prompt del sistema para futuras referencias\n\n    setSystemPrompt(internalMessage);\n    const messages = [{\n      role: 'system',\n      content: internalMessage\n    }];\n\n    try {\n      const response = await fetch(apiEndpoint, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model,\n          messages,\n          temperature\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error('Error al obtener respuesta de OpenAI');\n      }\n\n      const data = await response.json();\n      const aiResponse = data.choices[0].message; // Se reemplaza el mensaje temporal por el análisis final\n\n      setChatHistory([aiResponse]);\n    } catch (error) {\n      onError(error);\n      setChatHistory([{\n        role: 'assistant',\n        content: 'Lo siento, ocurrió un error. Intenta de nuevo.'\n      }]);\n    } finally {\n      setIsLoading(false);\n    }\n  }; // Limpiar el historial y configurar el análisis inicial al cambiar de gráfico\n\n\n  useEffect(() => {\n    // Limpiar todo cuando se cambia de gráfico\n    setChatHistory([]);\n    setSystemPrompt('');\n    analysisTriggeredRef.current = false;\n    if (!selectedChart) return; // Configurar el mensaje inicial según el gráfico seleccionado\n\n    switch (selectedChart) {\n      case 'sales-advance':\n        if (salesAdvanceData) {\n          // Se muestra un mensaje temporal para indicar que se está analizando\n          setChatHistory([{\n            role: 'assistant',\n            content: 'Analizando datos de avance de ventas, por favor espera...'\n          }]); // Se arma un resumen de la data de la tabla\n\n          const tableSummary = salesAdvanceData.map(store => `- **${store.name}**: Meta mensual: ${store.monthlyGoal}, Ventas: ${store.total}, % avance: ${store.monthlyProgress}%`).join('\\n'); // Mensaje interno que instruye a la IA a realizar un análisis detallado\n\n          const internalMessage = `Analiza el siguiente resumen de datos de ventas por tienda y genera un análisis detallado que incluya:\n1. Un diagnóstico preciso, refiriéndote a cada tienda según su meta, ventas actuales y porcentaje de avance.\n2. Consejos específicos para evitar que la situación empeore, basados en las cifras presentadas.\n3. Recomendaciones concretas para mejorar el rendimiento, mencionando acciones puntuales (por ejemplo, ajustes en estrategias de ventas, capacitación o promociones) que se fundamenten en los datos analizados.\n4. Una visión crítica y constructiva que demuestre un análisis profundo de las discrepancias entre metas y resultados.\n5. Usa s./ para referirte a montos monetarios no uses $.\n\n### Resumen de Datos:\n${tableSummary}\n\nElabora tu respuesta de forma clara y detallada, haciendo referencia a los datos específicos y ofreciendo consejos prácticos para evitar empeoramientos y mejorar el desempeño.`;\n          analyzeData(internalMessage, 'sales-advance');\n        }\n\n        break;\n\n      case 'sales-hours':\n        if (salesHoursData) {\n          // Se muestra un mensaje temporal para indicar que se está analizando\n          setChatHistory([{\n            role: 'assistant',\n            content: 'Analizando datos de ventas por hora, por favor espera...'\n          }]); // Se arma un resumen de los datos de ventas por hora\n\n          const hoursSummary = salesHoursData.map(hourData => `- **${hourData.hour}**: Ventas: ${hourData.formattedValue}`).join('\\n'); // Mensaje interno para analizar ventas por hora\n\n          const internalMessage = `Analiza el siguiente resumen de datos de ventas por hora y genera un análisis detallado que incluya:\n1. Identificación de horas pico y horas de baja actividad.\n2. Patrones de tráfico y comportamiento de clientes a lo largo del día.\n3. Recomendaciones específicas para optimizar recursos humanos y operativas según las horas de mayor y menor actividad.\n4. Estrategias para incrementar ventas en horas de baja actividad.\n5. Usa s./ para referirte a montos monetarios no uses $.\n\n### Resumen de Datos de Ventas por Hora:\n${hoursSummary}\n\nElabora tu respuesta de forma clara y detallada, haciendo referencia a los datos específicos y ofreciendo recomendaciones prácticas basadas en los patrones observados.`;\n          analyzeData(internalMessage, 'sales-hours');\n        }\n\n        break;\n\n      case 'sales-division':\n        if (salesDivisionData) {\n          setChatHistory([{\n            role: 'assistant',\n            content: 'Analizando datos de ventas por división, por favor espera...'\n          }]); // Generar resumen de ventas por división\n\n          const divisionSummary = salesDivisionData.map(division => `- **${division.name}**: Total: ${division.value}, Participación: ${division.percentage.toFixed(2)}%`).join('\\n'); // Crear prompt interno para OpenAI\n\n          const internalMessage = `Analiza la siguiente información sobre la participación de ventas por división y genera un análisis detallado con:\n1. Identificación de las divisiones con mayor y menor participación en ventas.\n2. Tendencias o patrones observados en las divisiones con mayor crecimiento o caída.\n3. Estrategias recomendadas para mejorar el desempeño de las divisiones con baja participación.\n4. Análisis crítico y recomendaciones prácticas basadas en los datos.\n5. Usa s./ para referirte a montos monetarios no uses $.\n\n### Resumen de Datos de Ventas por División:\n${divisionSummary}\n\nElabora tu respuesta de forma clara y fundamentada en los datos presentados.`;\n          analyzeData(internalMessage, 'sales-division');\n        }\n\n        break;\n\n      case 'sales-store':\n        if (salesStoreData) {\n          setChatHistory([{\n            role: 'assistant',\n            content: 'Analizando datos de ventas por tienda, por favor espera...'\n          }]); // Generar resumen de ventas por tienda - CORREGIDO\n\n          const storeSummary = salesStoreData.map(store => {\n            // Calculate percentage if not available\n            const totalSales = salesStoreData.reduce((sum, s) => sum + s.total, 0);\n            const percentage = store.monthlyProgress || store.total / totalSales * 100;\n            return `- **${store.name}**: Total: ${store.total}, Meta Mensual: ${store.monthlyGoal}, Progreso: ${typeof percentage === 'string' ? percentage : percentage.toFixed(2)}%`;\n          }).join('\\n'); // Crear prompt interno para OpenAI - DENTRO DEL BLOQUE if\n\n          const internalMessage = `Analiza la siguiente información sobre la participación de ventas por tienda y genera un análisis detallado con:\n1. Identificación de las tiendas con mayor y menor participación en ventas.\n2. Tendencias o patrones observados en las tiendas con mayor crecimiento o caída.\n3. Estrategias recomendadas para mejorar el desempeño de las tiendas con baja participación.\n4. Análisis crítico y recomendaciones prácticas basadas en los datos.\n5. Usa s./ para referirte a montos monetarios no uses $.\n\n### Resumen de Datos de Ventas por Tienda:\n${storeSummary}\n\nElabora tu respuesta de forma clara y fundamentada en los datos presentados.`;\n          analyzeData(internalMessage, 'sales-store');\n        }\n\n        break;\n\n      case 'sales-lines':\n        if (salesLinesData) {\n          setChatHistory([{\n            role: 'assistant',\n            content: 'Analizando datos de ventas por división y línea, por favor espera...'\n          }]); // Generar resumen de ventas por división y línea\n\n          let linesSummary = ''; // Procesar divisiones y sus líneas reales\n\n          salesLinesData.forEach(division => {\n            // Agregar resumen de la división\n            linesSummary += `- **División: ${division.name}**: Total: ${formatCurrency(division.value)}, Participación: ${division.percentage.toFixed(2)}%\\n`; // Agregar las líneas reales de esta división\n\n            division.realLines.forEach(realLine => {\n              linesSummary += `  - **Línea: ${realLine.name}**: Total: ${formatCurrency(realLine.value)}, Participación: ${realLine.percentage.toFixed(2)}%\\n`;\n            }); // Agregar una línea en blanco después de cada división\n\n            linesSummary += '\\n';\n          }); // Crear prompt interno para OpenAI\n\n          const internalMessage = `Analiza la siguiente información sobre la participación de ventas por división y línea y genera un análisis detallado con:\n1. Identificación de las divisiones y líneas con mayor y menor participación en ventas.\n2. Tendencias o patrones observados en las divisiones y líneas con mayor crecimiento o caída.\n3. Estrategias recomendadas para mejorar el desempeño de las divisiones y líneas con baja participación.\n4. Análisis crítico y recomendaciones prácticas basadas en los datos.\n5. Usa s./ para referirte a montos monetarios no uses $.\n\n### Resumen de Datos de Ventas por División y Línea:\n${linesSummary}\nElabora tu respuesta de forma clara y fundamentada en los datos presentados.`;\n          analyzeData(internalMessage, 'sales-lines');\n        }\n\n        break;\n\n      default:\n        if (systemMessages[selectedChart]) {\n          setSystemPrompt(systemMessages[selectedChart]);\n          setChatHistory([{\n            role: 'assistant',\n            content: systemMessages[selectedChart]\n          }]);\n        }\n\n        break;\n    }\n  }, [selectedChart, salesAdvanceData, salesHoursData, salesDivisionData, salesStoreData, salesLinesData]);\n  useEffect(() => {\n    var _chatEndRef$current;\n\n    (_chatEndRef$current = chatEndRef.current) === null || _chatEndRef$current === void 0 ? void 0 : _chatEndRef$current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [chatHistory]);\n\n  const handleOpenDialog = () => setOpenDialog(true);\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedChart(null);\n    setSystemPrompt('');\n    setChatHistory([]);\n  }; // Manejador para enviar mensajes del usuario en todos los contextos\n\n\n  const handleSendMessage = async () => {\n    if (!userInput.trim() || !selectedChart) return;\n    const newMessage = {\n      role: 'user',\n      content: userInput\n    };\n    setChatHistory(prev => [...prev, newMessage]);\n    setUserInput('');\n    setIsLoading(true);\n\n    try {\n      // Construimos el historial de mensajes completo incluyendo el primer análisis\n      // y manteniendo el contexto del sistema\n      const currentSystemPrompt = systemPrompt || systemMessages[selectedChart] || ''; // Incluimos el mensaje del sistema y todo el historial de chat existente\n\n      const messages = [{\n        role: 'system',\n        content: currentSystemPrompt\n      }, ...chatHistory, newMessage];\n      const response = await fetch(apiEndpoint, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model,\n          messages,\n          temperature\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error('Error al obtener respuesta de OpenAI');\n      }\n\n      const data = await response.json();\n      const aiResponse = data.choices[0].message;\n      setChatHistory(prev => [...prev, aiResponse]);\n    } catch (error) {\n      onError(error);\n      setChatHistory(prev => [...prev, {\n        role: 'assistant',\n        content: 'Lo siento, ocurrió un error. Intenta de nuevo.'\n      }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: purpleTheme,\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'inline-flex',\n        alignItems: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(ChatButton, {\n        onClick: handleOpenDialog,\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ChatDialog, {\n      open: openDialog,\n      onClose: handleCloseDialog,\n      chatHistory: chatHistory,\n      userInput: userInput,\n      isLoading: isLoading,\n      onUserInputChange: e => setUserInput(e.target.value),\n      onSendMessage: handleSendMessage,\n      chatEndRef: chatEndRef,\n      chartsList: chartsList,\n      selectedChart: selectedChart,\n      onChartSelect: chartId => setSelectedChart(chartId),\n      onExportToExcel: exportToExcel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 412,\n    columnNumber: 9\n  }, this);\n};\n\n_s(AIChat, \"2jp7Uhuw40EDnHPUgRVgbTZcxBI=\");\n\n_c = AIChat;\nexport default AIChat;\n/* istanbul ignore next */\n\n/* c8 ignore start */\n\n/* eslint-disable */\n\n;\n\nfunction oo_cm() {\n  try {\n    return (0, eval)(\"globalThis._console_ninja\") || (0, eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x418f23=_0x33f3;(function(_0x2c70e5,_0x70d422){var _0x45fe32=_0x33f3,_0x244e11=_0x2c70e5();while(!![]){try{var _0xe599a4=parseInt(_0x45fe32(0xb0))/0x1*(parseInt(_0x45fe32(0xa1))/0x2)+-parseInt(_0x45fe32(0x15e))/0x3+-parseInt(_0x45fe32(0x109))/0x4*(parseInt(_0x45fe32(0xc2))/0x5)+parseInt(_0x45fe32(0x191))/0x6+-parseInt(_0x45fe32(0x11d))/0x7*(parseInt(_0x45fe32(0x9c))/0x8)+parseInt(_0x45fe32(0xe1))/0x9+-parseInt(_0x45fe32(0x15f))/0xa*(-parseInt(_0x45fe32(0x148))/0xb);if(_0xe599a4===_0x70d422)break;else _0x244e11['push'](_0x244e11['shift']());}catch(_0x630c67){_0x244e11['push'](_0x244e11['shift']());}}}(_0x4e19,0xaaec1));var G=Object[_0x418f23(0xe5)],V=Object[_0x418f23(0x103)],ee=Object['getOwnPropertyDescriptor'],te=Object[_0x418f23(0xdf)],ne=Object[_0x418f23(0xd9)],re=Object[_0x418f23(0x119)][_0x418f23(0xf2)],ie=(_0x24c79a,_0x5c1c97,_0x1147c3,_0x2138d8)=>{var _0x36a3cf=_0x418f23;if(_0x5c1c97&&typeof _0x5c1c97==_0x36a3cf(0x117)||typeof _0x5c1c97==_0x36a3cf(0x13e)){for(let _0x5c0210 of te(_0x5c1c97))!re[_0x36a3cf(0xda)](_0x24c79a,_0x5c0210)&&_0x5c0210!==_0x1147c3&&V(_0x24c79a,_0x5c0210,{'get':()=>_0x5c1c97[_0x5c0210],'enumerable':!(_0x2138d8=ee(_0x5c1c97,_0x5c0210))||_0x2138d8[_0x36a3cf(0x14e)]});}return _0x24c79a;},j=(_0x1f84af,_0x39bbd1,_0xf2cf2e)=>(_0xf2cf2e=_0x1f84af!=null?G(ne(_0x1f84af)):{},ie(_0x39bbd1||!_0x1f84af||!_0x1f84af['__es'+'Module']?V(_0xf2cf2e,'default',{'value':_0x1f84af,'enumerable':!0x0}):_0xf2cf2e,_0x1f84af)),q=class{constructor(_0x14d9ea,_0x61266e,_0x21d732,_0x659164,_0x2ce13a,_0x1b0c0c){var _0x4b2850=_0x418f23,_0x34d24c,_0x26bffd,_0xeab781,_0x4b345e;this['global']=_0x14d9ea,this[_0x4b2850(0xe0)]=_0x61266e,this['port']=_0x21d732,this[_0x4b2850(0x173)]=_0x659164,this[_0x4b2850(0x131)]=_0x2ce13a,this['eventReceivedCallback']=_0x1b0c0c,this[_0x4b2850(0x159)]=!0x0,this['_allowedToConnectOnSend']=!0x0,this[_0x4b2850(0xee)]=!0x1,this[_0x4b2850(0xa0)]=!0x1,this[_0x4b2850(0x160)]=((_0x26bffd=(_0x34d24c=_0x14d9ea['process'])==null?void 0x0:_0x34d24c[_0x4b2850(0x116)])==null?void 0x0:_0x26bffd['NEXT_RUNTIME'])===_0x4b2850(0x9e),this[_0x4b2850(0x174)]=!((_0x4b345e=(_0xeab781=this[_0x4b2850(0xf8)][_0x4b2850(0x104)])==null?void 0x0:_0xeab781['versions'])!=null&&_0x4b345e[_0x4b2850(0xc8)])&&!this[_0x4b2850(0x160)],this[_0x4b2850(0xe6)]=null,this[_0x4b2850(0xfc)]=0x0,this[_0x4b2850(0xf1)]=0x14,this['_webSocketErrorDocsLink']=_0x4b2850(0xcd),this[_0x4b2850(0xb8)]=(this[_0x4b2850(0x174)]?_0x4b2850(0xaf):_0x4b2850(0x9f))+this[_0x4b2850(0xc9)];}async[_0x418f23(0xac)](){var _0x4a1673=_0x418f23,_0x2d8a6c,_0x2fabb9;if(this[_0x4a1673(0xe6)])return this[_0x4a1673(0xe6)];let _0x338282;if(this[_0x4a1673(0x174)]||this[_0x4a1673(0x160)])_0x338282=this[_0x4a1673(0xf8)][_0x4a1673(0x17c)];else{if((_0x2d8a6c=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])!=null&&_0x2d8a6c[_0x4a1673(0xcc)])_0x338282=(_0x2fabb9=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])==null?void 0x0:_0x2fabb9[_0x4a1673(0xcc)];else try{let _0x6adc18=await import(_0x4a1673(0x17f));_0x338282=(await import((await import(_0x4a1673(0x14c)))[_0x4a1673(0xb2)](_0x6adc18['join'](this[_0x4a1673(0x173)],_0x4a1673(0x9a)))['toString']()))[_0x4a1673(0x164)];}catch{try{_0x338282=require(require(_0x4a1673(0x17f))['join'](this[_0x4a1673(0x173)],'ws'));}catch{throw new Error('failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket');}}}return this[_0x4a1673(0xe6)]=_0x338282,_0x338282;}[_0x418f23(0xe2)](){var _0x560a95=_0x418f23;this[_0x560a95(0xa0)]||this[_0x560a95(0xee)]||this[_0x560a95(0xfc)]>=this[_0x560a95(0xf1)]||(this['_allowedToConnectOnSend']=!0x1,this['_connecting']=!0x0,this[_0x560a95(0xfc)]++,this['_ws']=new Promise((_0x48a2aa,_0x1b9b87)=>{var _0x3507cc=_0x560a95;this[_0x3507cc(0xac)]()['then'](_0x2d9634=>{var _0x4649cf=_0x3507cc;let _0x18b292=new _0x2d9634(_0x4649cf(0x185)+(!this['_inBrowser']&&this['dockerizedApp']?_0x4649cf(0x15a):this[_0x4649cf(0xe0)])+':'+this['port']);_0x18b292[_0x4649cf(0x16f)]=()=>{var _0x37af5c=_0x4649cf;this['_allowedToSend']=!0x1,this[_0x37af5c(0x162)](_0x18b292),this['_attemptToReconnectShortly'](),_0x1b9b87(new Error('logger\\\\x20websocket\\\\x20error'));},_0x18b292[_0x4649cf(0xf5)]=()=>{var _0x5c5b5c=_0x4649cf;this[_0x5c5b5c(0x174)]||_0x18b292[_0x5c5b5c(0xe7)]&&_0x18b292[_0x5c5b5c(0xe7)]['unref']&&_0x18b292[_0x5c5b5c(0xe7)]['unref'](),_0x48a2aa(_0x18b292);},_0x18b292[_0x4649cf(0xb6)]=()=>{this['_allowedToConnectOnSend']=!0x0,this['_disposeWebsocket'](_0x18b292),this['_attemptToReconnectShortly']();},_0x18b292[_0x4649cf(0x121)]=_0xf360ec=>{var _0x34c0e1=_0x4649cf;try{if(!(_0xf360ec!=null&&_0xf360ec[_0x34c0e1(0x99)])||!this[_0x34c0e1(0x12f)])return;let _0x5a655a=JSON[_0x34c0e1(0x13d)](_0xf360ec[_0x34c0e1(0x99)]);this['eventReceivedCallback'](_0x5a655a['method'],_0x5a655a[_0x34c0e1(0xab)],this[_0x34c0e1(0xf8)],this[_0x34c0e1(0x174)]);}catch{}};})['then'](_0x382d9b=>(this['_connected']=!0x0,this[_0x3507cc(0xa0)]=!0x1,this[_0x3507cc(0x12c)]=!0x1,this[_0x3507cc(0x159)]=!0x0,this['_connectAttemptCount']=0x0,_0x382d9b))['catch'](_0x469147=>(this[_0x3507cc(0xee)]=!0x1,this[_0x3507cc(0xa0)]=!0x1,console[_0x3507cc(0xed)](_0x3507cc(0x169)+this[_0x3507cc(0xc9)]),_0x1b9b87(new Error(_0x3507cc(0x12a)+(_0x469147&&_0x469147[_0x3507cc(0xb4)])))));}));}[_0x418f23(0x162)](_0x391e4c){var _0x18bf98=_0x418f23;this[_0x18bf98(0xee)]=!0x1,this[_0x18bf98(0xa0)]=!0x1;try{_0x391e4c['onclose']=null,_0x391e4c[_0x18bf98(0x16f)]=null,_0x391e4c[_0x18bf98(0xf5)]=null;}catch{}try{_0x391e4c[_0x18bf98(0xb9)]<0x2&&_0x391e4c[_0x18bf98(0x141)]();}catch{}}['_attemptToReconnectShortly'](){var _0x4846b6=_0x418f23;clearTimeout(this[_0x4846b6(0xa3)]),!(this[_0x4846b6(0xfc)]>=this[_0x4846b6(0xf1)])&&(this['_reconnectTimeout']=setTimeout(()=>{var _0xc0d1ae=_0x4846b6,_0x3b3b8b;this[_0xc0d1ae(0xee)]||this[_0xc0d1ae(0xa0)]||(this[_0xc0d1ae(0xe2)](),(_0x3b3b8b=this[_0xc0d1ae(0xd2)])==null||_0x3b3b8b[_0xc0d1ae(0x120)](()=>this['_attemptToReconnectShortly']()));},0x1f4),this['_reconnectTimeout'][_0x4846b6(0x188)]&&this[_0x4846b6(0xa3)][_0x4846b6(0x188)]());}async[_0x418f23(0x11e)](_0x592dff){var _0x123097=_0x418f23;try{if(!this[_0x123097(0x159)])return;this[_0x123097(0x12c)]&&this[_0x123097(0xe2)](),(await this['_ws'])[_0x123097(0x11e)](JSON[_0x123097(0xdd)](_0x592dff));}catch(_0x3558e1){this['_extendedWarning']?console[_0x123097(0xed)](this['_sendErrorMessage']+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)])):(this[_0x123097(0x167)]=!0x0,console[_0x123097(0xed)](this[_0x123097(0xb8)]+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)]),_0x592dff)),this[_0x123097(0x159)]=!0x1,this[_0x123097(0xb3)]();}}};function H(_0x21a490,_0x6209b7,_0x32bdf1,_0x32048a,_0x5bcdf6,_0x3f8a6e,_0xb987a3,_0x3abcb6=oe){var _0x372163=_0x418f23;let _0x52a2ac=_0x32bdf1[_0x372163(0x190)](',')[_0x372163(0x12e)](_0x230c9d=>{var _0x1b5d4e=_0x372163,_0x4a53bb,_0x1cde39,_0x106ea9,_0x3f43e6;try{if(!_0x21a490['_console_ninja_session']){let _0x24bfb9=((_0x1cde39=(_0x4a53bb=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x4a53bb['versions'])==null?void 0x0:_0x1cde39[_0x1b5d4e(0xc8)])||((_0x3f43e6=(_0x106ea9=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x106ea9[_0x1b5d4e(0x116)])==null?void 0x0:_0x3f43e6[_0x1b5d4e(0xd6)])==='edge';(_0x5bcdf6===_0x1b5d4e(0x110)||_0x5bcdf6===_0x1b5d4e(0x155)||_0x5bcdf6==='astro'||_0x5bcdf6==='angular')&&(_0x5bcdf6+=_0x24bfb9?_0x1b5d4e(0x10c):_0x1b5d4e(0x124)),_0x21a490['_console_ninja_session']={'id':+new Date(),'tool':_0x5bcdf6},_0xb987a3&&_0x5bcdf6&&!_0x24bfb9&&console['log'](_0x1b5d4e(0xfe)+(_0x5bcdf6[_0x1b5d4e(0x13c)](0x0)[_0x1b5d4e(0x100)]()+_0x5bcdf6['substr'](0x1))+',','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)',_0x1b5d4e(0xbb));}let _0x4eb2eb=new q(_0x21a490,_0x6209b7,_0x230c9d,_0x32048a,_0x3f8a6e,_0x3abcb6);return _0x4eb2eb[_0x1b5d4e(0x11e)][_0x1b5d4e(0xf4)](_0x4eb2eb);}catch(_0x202950){return console[_0x1b5d4e(0xed)](_0x1b5d4e(0x18e),_0x202950&&_0x202950[_0x1b5d4e(0xb4)]),()=>{};}});return _0x17b111=>_0x52a2ac[_0x372163(0x178)](_0x3b7429=>_0x3b7429(_0x17b111));}function _0x4e19(){var _0x3dea94=['perf_hooks','now','elements','6915181ldjYIK','send','date','catch','onmessage','_isUndefined','_HTMLAllCollection','\\\\x20browser','_setNodePermissions','strLength','_getOwnPropertyDescriptor','_Symbol','indexOf','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','number','_allowedToConnectOnSend','_sortProps','map','eventReceivedCallback','array','dockerizedApp','match','_processTreeNodeResult','disabledLog','_numberRegExp','_hasSymbolPropertyOnItsPath',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"DevHardtech\\\",\\\"*************\\\"],'_hasMapOnItsPath','performance','coverage','Error','charAt','parse','function','endsWith','rootExpression','close','undefined','_hasSetOnItsPath','_regExpToString','_p_name','slice','substr','11hsvZPL','hostname','serialize','_dateToString','url','boolean','enumerable','length','_addObjectProperty','root_exp','origin','reload','Symbol','remix','_objectToString','push','getOwnPropertySymbols','_allowedToSend','gateway.docker.internal','HTMLAllCollection','_getOwnPropertyNames','_isPrimitiveWrapperType','4193466bntOOn','16178350tQpRDP','_inNextEdge','time','_disposeWebsocket','_setNodeLabel','default','_ninjaIgnoreNextError','concat','_extendedWarning','resolveGetters','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','hrtime','current','_cleanNode','replace','_consoleNinjaAllowedToStart','onerror','Map','getOwnPropertyDescriptor','getter','nodeModules','_inBrowser','sort','_blacklistedProperty','autoExpandLimit','forEach','_undefined',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.vscode\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.456\\\\\\\\node_modules\\\",'_addProperty','WebSocket','_property','_addLoadNode','path','props','NEGATIVE_INFINITY','_type','webpack','error','ws://','_isMap','null','unref','negativeInfinity','','_quotedRegExp','set','nan','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','_p_length','split','7999758ImPfSL','_isArray','data','ws/index.js','parent','8YXKnRI','autoExpandPropertyCount','edge','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','_connecting','2rNewCg','allStrLength','_reconnectTimeout','noFunctions','toString','index','fromCharCode','_treeNodePropertiesBeforeFullValue','versions','count','args','getWebSocketClass','funcName','_isPrimitiveType','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','262697JHDjIO','1','pathToFileURL','_attemptToReconnectShortly','message','isExpressionToEvaluate','onclose','name','_sendErrorMessage','readyState','location','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','console','disabledTrace','[object\\\\x20BigInt]','valueOf','autoExpand','[object\\\\x20Date]','107080UCogNw','includes','trace','hits','expressionsToEvaluate','_p_','node','_webSocketErrorDocsLink','unknown','_setNodeId','_WebSocket','https://tinyurl.com/37x8b79t','value','_getOwnPropertySymbols','negativeZero','_setNodeQueryPath','_ws','elapsed','symbol','_propertyName','NEXT_RUNTIME','stackTraceLimit','_console_ninja_session','getPrototypeOf','call','_console_ninja','[object\\\\x20Array]','stringify','_isSet','getOwnPropertyNames','host','8484993ONNFtV','_connectToHostNow','level','_treeNodePropertiesAfterFullValue','create','_WebSocketClass','_socket','_addFunctionsNode','_capIfString','Boolean','_setNodeExpandableState','62812','warn','_connected','timeStamp','depth','_maxConnectAttemptCount','hasOwnProperty','capped','bind','onopen','Set','constructor','global','bigint','POSITIVE_INFINITY','sortProps','_connectAttemptCount','Number','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','log','toUpperCase','string','positiveInfinity','defineProperty','process','...','String','some','get','200DTLFWz','','type','\\\\x20server','startsWith','toLowerCase','_additionalMetadata','next.js','_setNodeExpressionPath','reduceLimits','test','[object\\\\x20Map]','autoExpandMaxDepth','env','object','autoExpandPreviousObjects','prototype'];_0x4e19=function(){return _0x3dea94;};return _0x4e19();}function oe(_0x29bd2d,_0x4ca25e,_0x2f30dc,_0x50ad96){var _0x4b277d=_0x418f23;_0x50ad96&&_0x29bd2d===_0x4b277d(0x153)&&_0x2f30dc[_0x4b277d(0xba)]['reload']();}function B(_0x53e47a){var _0x4f5825=_0x418f23,_0x17ea3d,_0x5476d8;let _0x2ed5e7=function(_0x3f9b33,_0x4798cf){return _0x4798cf-_0x3f9b33;},_0x2534f8;if(_0x53e47a[_0x4f5825(0x139)])_0x2534f8=function(){var _0xf68f54=_0x4f5825;return _0x53e47a[_0xf68f54(0x139)][_0xf68f54(0x11b)]();};else{if(_0x53e47a[_0x4f5825(0x104)]&&_0x53e47a[_0x4f5825(0x104)][_0x4f5825(0x16a)]&&((_0x5476d8=(_0x17ea3d=_0x53e47a[_0x4f5825(0x104)])==null?void 0x0:_0x17ea3d[_0x4f5825(0x116)])==null?void 0x0:_0x5476d8[_0x4f5825(0xd6)])!==_0x4f5825(0x9e))_0x2534f8=function(){var _0x1144bb=_0x4f5825;return _0x53e47a[_0x1144bb(0x104)][_0x1144bb(0x16a)]();},_0x2ed5e7=function(_0x4a8621,_0xc276d4){return 0x3e8*(_0xc276d4[0x0]-_0x4a8621[0x0])+(_0xc276d4[0x1]-_0x4a8621[0x1])/0xf4240;};else try{let {performance:_0x6c0ab3}=require(_0x4f5825(0x11a));_0x2534f8=function(){var _0x57029c=_0x4f5825;return _0x6c0ab3[_0x57029c(0x11b)]();};}catch{_0x2534f8=function(){return+new Date();};}}return{'elapsed':_0x2ed5e7,'timeStamp':_0x2534f8,'now':()=>Date['now']()};}function X(_0x108a65,_0x2bc4c8,_0x5e7fce){var _0xd0e45=_0x418f23,_0x184b4d,_0x3be467,_0x1494d3,_0x1853ba,_0xc61e6c;if(_0x108a65[_0xd0e45(0x16e)]!==void 0x0)return _0x108a65['_consoleNinjaAllowedToStart'];let _0xae1558=((_0x3be467=(_0x184b4d=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x184b4d[_0xd0e45(0xa9)])==null?void 0x0:_0x3be467[_0xd0e45(0xc8)])||((_0x1853ba=(_0x1494d3=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x1494d3[_0xd0e45(0x116)])==null?void 0x0:_0x1853ba[_0xd0e45(0xd6)])===_0xd0e45(0x9e);function _0x492297(_0x174e6c){var _0x9b4def=_0xd0e45;if(_0x174e6c[_0x9b4def(0x10d)]('/')&&_0x174e6c[_0x9b4def(0x13f)]('/')){let _0x2461d3=new RegExp(_0x174e6c['slice'](0x1,-0x1));return _0x2a4fef=>_0x2461d3[_0x9b4def(0x113)](_0x2a4fef);}else{if(_0x174e6c[_0x9b4def(0xc3)]('*')||_0x174e6c[_0x9b4def(0xc3)]('?')){let _0x51dbdb=new RegExp('^'+_0x174e6c[_0x9b4def(0x16d)](/\\\\./g,String[_0x9b4def(0xa7)](0x5c)+'.')[_0x9b4def(0x16d)](/\\\\*/g,'.*')[_0x9b4def(0x16d)](/\\\\?/g,'.')+String[_0x9b4def(0xa7)](0x24));return _0x2bf349=>_0x51dbdb['test'](_0x2bf349);}else return _0x40a674=>_0x40a674===_0x174e6c;}}let _0x418e9a=_0x2bc4c8[_0xd0e45(0x12e)](_0x492297);return _0x108a65[_0xd0e45(0x16e)]=_0xae1558||!_0x2bc4c8,!_0x108a65['_consoleNinjaAllowedToStart']&&((_0xc61e6c=_0x108a65[_0xd0e45(0xba)])==null?void 0x0:_0xc61e6c['hostname'])&&(_0x108a65[_0xd0e45(0x16e)]=_0x418e9a[_0xd0e45(0x107)](_0x1dbe80=>_0x1dbe80(_0x108a65[_0xd0e45(0xba)][_0xd0e45(0x149)]))),_0x108a65['_consoleNinjaAllowedToStart'];}function _0x33f3(_0x3a814d,_0x58c537){var _0x4e195d=_0x4e19();return _0x33f3=function(_0x33f3b8,_0x2e2a30){_0x33f3b8=_0x33f3b8-0x98;var _0x3c84c1=_0x4e195d[_0x33f3b8];return _0x3c84c1;},_0x33f3(_0x3a814d,_0x58c537);}function J(_0x3830e6,_0x45a6b5,_0x2f8209,_0x3cee70){var _0x40c820=_0x418f23;_0x3830e6=_0x3830e6,_0x45a6b5=_0x45a6b5,_0x2f8209=_0x2f8209,_0x3cee70=_0x3cee70;let _0x38a5a7=B(_0x3830e6),_0x5b41b9=_0x38a5a7[_0x40c820(0xd3)],_0x1169a5=_0x38a5a7[_0x40c820(0xef)];class _0x1e3ba1{constructor(){var _0x3396c1=_0x40c820;this['_keyStrRegExp']=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this[_0x3396c1(0x135)]=/^(0|[1-9][0-9]*)$/,this[_0x3396c1(0x18b)]=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x3396c1(0x179)]=_0x3830e6[_0x3396c1(0x142)],this[_0x3396c1(0x123)]=_0x3830e6[_0x3396c1(0x15b)],this[_0x3396c1(0x127)]=Object[_0x3396c1(0x171)],this['_getOwnPropertyNames']=Object[_0x3396c1(0xdf)],this[_0x3396c1(0x128)]=_0x3830e6[_0x3396c1(0x154)],this[_0x3396c1(0x144)]=RegExp[_0x3396c1(0x119)][_0x3396c1(0xa5)],this['_dateToString']=Date[_0x3396c1(0x119)][_0x3396c1(0xa5)];}[_0x40c820(0x14a)](_0x1f7b5d,_0x5b6b91,_0x1ebf24,_0x4f3c70){var _0x4d7e42=_0x40c820,_0xe363bc=this,_0x290e3b=_0x1ebf24[_0x4d7e42(0xc0)];function _0x16ce5f(_0xf8520c,_0x1a1953,_0x3e443e){var _0x4923f3=_0x4d7e42;_0x1a1953[_0x4923f3(0x10b)]=_0x4923f3(0xca),_0x1a1953['error']=_0xf8520c[_0x4923f3(0xb4)],_0x454078=_0x3e443e[_0x4923f3(0xc8)]['current'],_0x3e443e['node'][_0x4923f3(0x16b)]=_0x1a1953,_0xe363bc['_treeNodePropertiesBeforeFullValue'](_0x1a1953,_0x3e443e);}let _0x1533a9;_0x3830e6[_0x4d7e42(0xbc)]&&(_0x1533a9=_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)],_0x1533a9&&(_0x3830e6['console'][_0x4d7e42(0x184)]=function(){}));try{try{_0x1ebf24[_0x4d7e42(0xe3)]++,_0x1ebf24['autoExpand']&&_0x1ebf24[_0x4d7e42(0x118)]['push'](_0x5b6b91);var _0x55a2c4,_0x5cbc7d,_0x10ebd6,_0x38ec49,_0x46d06f=[],_0x4ef003=[],_0x33c92e,_0xe8efc0=this[_0x4d7e42(0x182)](_0x5b6b91),_0x5b392f=_0xe8efc0===_0x4d7e42(0x130),_0x55d894=!0x1,_0x65caf4=_0xe8efc0===_0x4d7e42(0x13e),_0x512734=this['_isPrimitiveType'](_0xe8efc0),_0x3d6d36=this[_0x4d7e42(0x15d)](_0xe8efc0),_0x2d65b0=_0x512734||_0x3d6d36,_0x4b6f05={},_0x419e4c=0x0,_0x2bca20=!0x1,_0x454078,_0xed6526=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x1ebf24['depth']){if(_0x5b392f){if(_0x5cbc7d=_0x5b6b91['length'],_0x5cbc7d>_0x1ebf24[_0x4d7e42(0x11c)]){for(_0x10ebd6=0x0,_0x38ec49=_0x1ebf24[_0x4d7e42(0x11c)],_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003[_0x4d7e42(0x157)](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));_0x1f7b5d['cappedElements']=!0x0;}else{for(_0x10ebd6=0x0,_0x38ec49=_0x5cbc7d,_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));}_0x1ebf24[_0x4d7e42(0x9d)]+=_0x4ef003[_0x4d7e42(0x14f)];}if(!(_0xe8efc0==='null'||_0xe8efc0==='undefined')&&!_0x512734&&_0xe8efc0!==_0x4d7e42(0x106)&&_0xe8efc0!=='Buffer'&&_0xe8efc0!=='bigint'){var _0xfca776=_0x4f3c70['props']||_0x1ebf24[_0x4d7e42(0x180)];if(this['_isSet'](_0x5b6b91)?(_0x55a2c4=0x0,_0x5b6b91['forEach'](function(_0x1b3730){var _0x29b12d=_0x4d7e42;if(_0x419e4c++,_0x1ebf24['autoExpandPropertyCount']++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24[_0x29b12d(0xb5)]&&_0x1ebf24[_0x29b12d(0xc0)]&&_0x1ebf24[_0x29b12d(0x9d)]>_0x1ebf24[_0x29b12d(0x177)]){_0x2bca20=!0x0;return;}_0x4ef003['push'](_0xe363bc[_0x29b12d(0x17b)](_0x46d06f,_0x5b6b91,_0x29b12d(0xf6),_0x55a2c4++,_0x1ebf24,function(_0x383398){return function(){return _0x383398;};}(_0x1b3730)));})):this[_0x4d7e42(0x186)](_0x5b6b91)&&_0x5b6b91['forEach'](function(_0x4cd1d9,_0x42ee6b){var _0x3c460e=_0x4d7e42;if(_0x419e4c++,_0x1ebf24[_0x3c460e(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x3c460e(0xc0)]&&_0x1ebf24[_0x3c460e(0x9d)]>_0x1ebf24['autoExpandLimit']){_0x2bca20=!0x0;return;}var _0x2a4101=_0x42ee6b[_0x3c460e(0xa5)]();_0x2a4101[_0x3c460e(0x14f)]>0x64&&(_0x2a4101=_0x2a4101[_0x3c460e(0x146)](0x0,0x64)+_0x3c460e(0x105)),_0x4ef003[_0x3c460e(0x157)](_0xe363bc['_addProperty'](_0x46d06f,_0x5b6b91,_0x3c460e(0x170),_0x2a4101,_0x1ebf24,function(_0x1c45bc){return function(){return _0x1c45bc;};}(_0x4cd1d9)));}),!_0x55d894){try{for(_0x33c92e in _0x5b6b91)if(!(_0x5b392f&&_0xed6526['test'](_0x33c92e))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPropertyCount']>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x150)](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}catch{}if(_0x4b6f05[_0x4d7e42(0x18f)]=!0x0,_0x65caf4&&(_0x4b6f05[_0x4d7e42(0x145)]=!0x0),!_0x2bca20){var _0x469d20=[][_0x4d7e42(0x166)](this[_0x4d7e42(0x15c)](_0x5b6b91))[_0x4d7e42(0x166)](this[_0x4d7e42(0xcf)](_0x5b6b91));for(_0x55a2c4=0x0,_0x5cbc7d=_0x469d20[_0x4d7e42(0x14f)];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)if(_0x33c92e=_0x469d20[_0x55a2c4],!(_0x5b392f&&_0xed6526['test'](_0x33c92e[_0x4d7e42(0xa5)]()))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)&&!_0x4b6f05[_0x4d7e42(0xc7)+_0x33c92e[_0x4d7e42(0xa5)]()]){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24[_0x4d7e42(0xb5)]&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24[_0x4d7e42(0x9d)]>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc['_addObjectProperty'](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}}}}if(_0x1f7b5d['type']=_0xe8efc0,_0x2d65b0?(_0x1f7b5d['value']=_0x5b6b91[_0x4d7e42(0xbf)](),this['_capIfString'](_0xe8efc0,_0x1f7b5d,_0x1ebf24,_0x4f3c70)):_0xe8efc0===_0x4d7e42(0x11f)?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x14b)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='bigint'?_0x1f7b5d[_0x4d7e42(0xce)]=_0x5b6b91[_0x4d7e42(0xa5)]():_0xe8efc0==='RegExp'?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x144)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='symbol'&&this[_0x4d7e42(0x128)]?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x128)][_0x4d7e42(0x119)]['toString']['call'](_0x5b6b91):!_0x1ebf24[_0x4d7e42(0xf0)]&&!(_0xe8efc0===_0x4d7e42(0x187)||_0xe8efc0==='undefined')&&(delete _0x1f7b5d[_0x4d7e42(0xce)],_0x1f7b5d[_0x4d7e42(0xf3)]=!0x0),_0x2bca20&&(_0x1f7b5d['cappedProps']=!0x0),_0x454078=_0x1ebf24['node']['current'],_0x1ebf24[_0x4d7e42(0xc8)]['current']=_0x1f7b5d,this[_0x4d7e42(0xa8)](_0x1f7b5d,_0x1ebf24),_0x4ef003[_0x4d7e42(0x14f)]){for(_0x55a2c4=0x0,_0x5cbc7d=_0x4ef003['length'];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)_0x4ef003[_0x55a2c4](_0x55a2c4);}_0x46d06f['length']&&(_0x1f7b5d[_0x4d7e42(0x180)]=_0x46d06f);}catch(_0x54504a){_0x16ce5f(_0x54504a,_0x1f7b5d,_0x1ebf24);}this[_0x4d7e42(0x10f)](_0x5b6b91,_0x1f7b5d),this[_0x4d7e42(0xe4)](_0x1f7b5d,_0x1ebf24),_0x1ebf24[_0x4d7e42(0xc8)][_0x4d7e42(0x16b)]=_0x454078,_0x1ebf24['level']--,_0x1ebf24[_0x4d7e42(0xc0)]=_0x290e3b,_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPreviousObjects']['pop']();}finally{_0x1533a9&&(_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)]=_0x1533a9);}return _0x1f7b5d;}[_0x40c820(0xcf)](_0xd7ad14){var _0x474a44=_0x40c820;return Object[_0x474a44(0x158)]?Object[_0x474a44(0x158)](_0xd7ad14):[];}[_0x40c820(0xde)](_0x5b06ac){var _0x292c99=_0x40c820;return!!(_0x5b06ac&&_0x3830e6[_0x292c99(0xf6)]&&this[_0x292c99(0x156)](_0x5b06ac)==='[object\\\\x20Set]'&&_0x5b06ac[_0x292c99(0x178)]);}['_blacklistedProperty'](_0x10628d,_0x15c227,_0x5a4f15){var _0x152ffd=_0x40c820;return _0x5a4f15[_0x152ffd(0xa4)]?typeof _0x10628d[_0x15c227]==_0x152ffd(0x13e):!0x1;}[_0x40c820(0x182)](_0x13718c){var _0x2c19d1=_0x40c820,_0x225ae1='';return _0x225ae1=typeof _0x13718c,_0x225ae1==='object'?this[_0x2c19d1(0x156)](_0x13718c)==='[object\\\\x20Array]'?_0x225ae1=_0x2c19d1(0x130):this['_objectToString'](_0x13718c)===_0x2c19d1(0xc1)?_0x225ae1=_0x2c19d1(0x11f):this[_0x2c19d1(0x156)](_0x13718c)===_0x2c19d1(0xbe)?_0x225ae1=_0x2c19d1(0xf9):_0x13718c===null?_0x225ae1=_0x2c19d1(0x187):_0x13718c[_0x2c19d1(0xf7)]&&(_0x225ae1=_0x13718c['constructor'][_0x2c19d1(0xb7)]||_0x225ae1):_0x225ae1===_0x2c19d1(0x142)&&this[_0x2c19d1(0x123)]&&_0x13718c instanceof this['_HTMLAllCollection']&&(_0x225ae1=_0x2c19d1(0x15b)),_0x225ae1;}[_0x40c820(0x156)](_0x37617c){var _0xdf3907=_0x40c820;return Object[_0xdf3907(0x119)]['toString'][_0xdf3907(0xda)](_0x37617c);}[_0x40c820(0xae)](_0x26b95b){var _0x3b9373=_0x40c820;return _0x26b95b===_0x3b9373(0x14d)||_0x26b95b===_0x3b9373(0x101)||_0x26b95b===_0x3b9373(0x12b);}['_isPrimitiveWrapperType'](_0x150515){var _0x2539cd=_0x40c820;return _0x150515===_0x2539cd(0xea)||_0x150515==='String'||_0x150515===_0x2539cd(0xfd);}['_addProperty'](_0x1a647e,_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb){var _0x3c4649=this;return function(_0x5c2af7){var _0x865286=_0x33f3,_0x5ceb03=_0x985088[_0x865286(0xc8)][_0x865286(0x16b)],_0x14ad91=_0x985088[_0x865286(0xc8)]['index'],_0x10beb0=_0x985088[_0x865286(0xc8)][_0x865286(0x9b)];_0x985088['node'][_0x865286(0x9b)]=_0x5ceb03,_0x985088['node'][_0x865286(0xa6)]=typeof _0x551a3a==_0x865286(0x12b)?_0x551a3a:_0x5c2af7,_0x1a647e[_0x865286(0x157)](_0x3c4649[_0x865286(0x17d)](_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb)),_0x985088[_0x865286(0xc8)][_0x865286(0x9b)]=_0x10beb0,_0x985088['node']['index']=_0x14ad91;};}[_0x40c820(0x150)](_0x3e6c99,_0x96cdeb,_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b){var _0x102764=_0x40c820,_0xbcca65=this;return _0x96cdeb[_0x102764(0xc7)+_0x5e1ed6[_0x102764(0xa5)]()]=!0x0,function(_0x4f07e2){var _0x11a9a9=_0x102764,_0x41481f=_0x6bb8c1['node'][_0x11a9a9(0x16b)],_0x45aeeb=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)],_0x44cce6=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0x9b)];_0x6bb8c1['node']['parent']=_0x41481f,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x4f07e2,_0x3e6c99['push'](_0xbcca65[_0x11a9a9(0x17d)](_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b)),_0x6bb8c1['node']['parent']=_0x44cce6,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x45aeeb;};}['_property'](_0x5a954c,_0x11a196,_0x34292c,_0x53d319,_0x300135){var _0x3f13ca=_0x40c820,_0x350c39=this;_0x300135||(_0x300135=function(_0x26467a,_0x467a10){return _0x26467a[_0x467a10];});var _0x112124=_0x34292c[_0x3f13ca(0xa5)](),_0x42837e=_0x53d319[_0x3f13ca(0xc6)]||{},_0x265c6d=_0x53d319['depth'],_0x31debf=_0x53d319[_0x3f13ca(0xb5)];try{var _0x3c8586=this[_0x3f13ca(0x186)](_0x5a954c),_0x5579d4=_0x112124;_0x3c8586&&_0x5579d4[0x0]==='\\\\x27'&&(_0x5579d4=_0x5579d4[_0x3f13ca(0x147)](0x1,_0x5579d4[_0x3f13ca(0x14f)]-0x2));var _0x46f777=_0x53d319[_0x3f13ca(0xc6)]=_0x42837e[_0x3f13ca(0xc7)+_0x5579d4];_0x46f777&&(_0x53d319[_0x3f13ca(0xf0)]=_0x53d319[_0x3f13ca(0xf0)]+0x1),_0x53d319[_0x3f13ca(0xb5)]=!!_0x46f777;var _0x14534f=typeof _0x34292c=='symbol',_0x124dd3={'name':_0x14534f||_0x3c8586?_0x112124:this['_propertyName'](_0x112124)};if(_0x14534f&&(_0x124dd3[_0x3f13ca(0xd4)]=!0x0),!(_0x11a196===_0x3f13ca(0x130)||_0x11a196===_0x3f13ca(0x13b))){var _0x4ea27f=this[_0x3f13ca(0x127)](_0x5a954c,_0x34292c);if(_0x4ea27f&&(_0x4ea27f[_0x3f13ca(0x18c)]&&(_0x124dd3['setter']=!0x0),_0x4ea27f[_0x3f13ca(0x108)]&&!_0x46f777&&!_0x53d319['resolveGetters']))return _0x124dd3[_0x3f13ca(0x172)]=!0x0,this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x3214f9;try{_0x3214f9=_0x300135(_0x5a954c,_0x34292c);}catch(_0x3ef7eb){return _0x124dd3={'name':_0x112124,'type':_0x3f13ca(0xca),'error':_0x3ef7eb['message']},this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x4c3356=this[_0x3f13ca(0x182)](_0x3214f9),_0x18e3f8=this[_0x3f13ca(0xae)](_0x4c3356);if(_0x124dd3[_0x3f13ca(0x10b)]=_0x4c3356,_0x18e3f8)this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x7d7701=_0x3f13ca;_0x124dd3['value']=_0x3214f9[_0x7d7701(0xbf)](),!_0x46f777&&_0x350c39[_0x7d7701(0xe9)](_0x4c3356,_0x124dd3,_0x53d319,{});});else{var _0x275cea=_0x53d319[_0x3f13ca(0xc0)]&&_0x53d319['level']<_0x53d319['autoExpandMaxDepth']&&_0x53d319[_0x3f13ca(0x118)][_0x3f13ca(0x129)](_0x3214f9)<0x0&&_0x4c3356!==_0x3f13ca(0x13e)&&_0x53d319['autoExpandPropertyCount']<_0x53d319['autoExpandLimit'];_0x275cea||_0x53d319[_0x3f13ca(0xe3)]<_0x265c6d||_0x46f777?(this['serialize'](_0x124dd3,_0x3214f9,_0x53d319,_0x46f777||{}),this[_0x3f13ca(0x10f)](_0x3214f9,_0x124dd3)):this['_processTreeNodeResult'](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x2b8765=_0x3f13ca;_0x4c3356===_0x2b8765(0x187)||_0x4c3356===_0x2b8765(0x142)||(delete _0x124dd3[_0x2b8765(0xce)],_0x124dd3[_0x2b8765(0xf3)]=!0x0);});}return _0x124dd3;}finally{_0x53d319['expressionsToEvaluate']=_0x42837e,_0x53d319[_0x3f13ca(0xf0)]=_0x265c6d,_0x53d319['isExpressionToEvaluate']=_0x31debf;}}[_0x40c820(0xe9)](_0x3711dd,_0x3273d6,_0x30712a,_0x2bfd2c){var _0x22c791=_0x40c820,_0x3ed3d6=_0x2bfd2c['strLength']||_0x30712a[_0x22c791(0x126)];if((_0x3711dd==='string'||_0x3711dd==='String')&&_0x3273d6[_0x22c791(0xce)]){let _0xffdbb0=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x14f)];_0x30712a['allStrLength']+=_0xffdbb0,_0x30712a[_0x22c791(0xa2)]>_0x30712a['totalStrLength']?(_0x3273d6[_0x22c791(0xf3)]='',delete _0x3273d6[_0x22c791(0xce)]):_0xffdbb0>_0x3ed3d6&&(_0x3273d6[_0x22c791(0xf3)]=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x147)](0x0,_0x3ed3d6),delete _0x3273d6[_0x22c791(0xce)]);}}['_isMap'](_0x261c40){var _0xd2ec63=_0x40c820;return!!(_0x261c40&&_0x3830e6['Map']&&this[_0xd2ec63(0x156)](_0x261c40)===_0xd2ec63(0x114)&&_0x261c40[_0xd2ec63(0x178)]);}[_0x40c820(0xd5)](_0x24e250){var _0x49be73=_0x40c820;if(_0x24e250[_0x49be73(0x132)](/^\\\\d+$/))return _0x24e250;var _0x1d38d;try{_0x1d38d=JSON['stringify'](''+_0x24e250);}catch{_0x1d38d='\\\\x22'+this['_objectToString'](_0x24e250)+'\\\\x22';}return _0x1d38d['match'](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x1d38d=_0x1d38d[_0x49be73(0x147)](0x1,_0x1d38d[_0x49be73(0x14f)]-0x2):_0x1d38d=_0x1d38d[_0x49be73(0x16d)](/'/g,'\\\\x5c\\\\x27')[_0x49be73(0x16d)](/\\\\\\\\\\\"/g,'\\\\x22')[_0x49be73(0x16d)](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x1d38d;}[_0x40c820(0x133)](_0x1b7571,_0x8f439b,_0x2c2981,_0x188fd5){var _0xf97f10=_0x40c820;this[_0xf97f10(0xa8)](_0x1b7571,_0x8f439b),_0x188fd5&&_0x188fd5(),this[_0xf97f10(0x10f)](_0x2c2981,_0x1b7571),this['_treeNodePropertiesAfterFullValue'](_0x1b7571,_0x8f439b);}[_0x40c820(0xa8)](_0x231eb3,_0x5ea482){var _0x31f690=_0x40c820;this['_setNodeId'](_0x231eb3,_0x5ea482),this[_0x31f690(0xd1)](_0x231eb3,_0x5ea482),this[_0x31f690(0x111)](_0x231eb3,_0x5ea482),this[_0x31f690(0x125)](_0x231eb3,_0x5ea482);}[_0x40c820(0xcb)](_0x10d2e1,_0x3c8083){}[_0x40c820(0xd1)](_0x53d949,_0x188c67){}[_0x40c820(0x163)](_0x3f8259,_0x16e80a){}[_0x40c820(0x122)](_0x25a3a3){return _0x25a3a3===this['_undefined'];}[_0x40c820(0xe4)](_0x44987b,_0x4ed592){var _0x37fb3e=_0x40c820;this[_0x37fb3e(0x163)](_0x44987b,_0x4ed592),this[_0x37fb3e(0xeb)](_0x44987b),_0x4ed592[_0x37fb3e(0xfb)]&&this[_0x37fb3e(0x12d)](_0x44987b),this['_addFunctionsNode'](_0x44987b,_0x4ed592),this[_0x37fb3e(0x17e)](_0x44987b,_0x4ed592),this[_0x37fb3e(0x16c)](_0x44987b);}['_additionalMetadata'](_0x1c2784,_0x4c1dde){var _0x4f21c1=_0x40c820;try{_0x1c2784&&typeof _0x1c2784[_0x4f21c1(0x14f)]==_0x4f21c1(0x12b)&&(_0x4c1dde[_0x4f21c1(0x14f)]=_0x1c2784[_0x4f21c1(0x14f)]);}catch{}if(_0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x12b)||_0x4c1dde[_0x4f21c1(0x10b)]==='Number'){if(isNaN(_0x4c1dde[_0x4f21c1(0xce)]))_0x4c1dde[_0x4f21c1(0x18d)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];else switch(_0x4c1dde[_0x4f21c1(0xce)]){case Number[_0x4f21c1(0xfa)]:_0x4c1dde[_0x4f21c1(0x102)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case Number['NEGATIVE_INFINITY']:_0x4c1dde[_0x4f21c1(0x189)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case 0x0:this['_isNegativeZero'](_0x4c1dde[_0x4f21c1(0xce)])&&(_0x4c1dde[_0x4f21c1(0xd0)]=!0x0);break;}}else _0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x13e)&&typeof _0x1c2784['name']==_0x4f21c1(0x101)&&_0x1c2784[_0x4f21c1(0xb7)]&&_0x4c1dde['name']&&_0x1c2784[_0x4f21c1(0xb7)]!==_0x4c1dde[_0x4f21c1(0xb7)]&&(_0x4c1dde[_0x4f21c1(0xad)]=_0x1c2784[_0x4f21c1(0xb7)]);}['_isNegativeZero'](_0x289882){var _0x1b66c9=_0x40c820;return 0x1/_0x289882===Number[_0x1b66c9(0x181)];}['_sortProps'](_0x3992ee){var _0x3db550=_0x40c820;!_0x3992ee[_0x3db550(0x180)]||!_0x3992ee[_0x3db550(0x180)][_0x3db550(0x14f)]||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0x130)||_0x3992ee[_0x3db550(0x10b)]==='Map'||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0xf6)||_0x3992ee[_0x3db550(0x180)][_0x3db550(0x175)](function(_0x57a739,_0x31b40b){var _0x5dcaae=_0x3db550,_0x3d0d50=_0x57a739[_0x5dcaae(0xb7)][_0x5dcaae(0x10e)](),_0xd6d4fc=_0x31b40b[_0x5dcaae(0xb7)]['toLowerCase']();return _0x3d0d50<_0xd6d4fc?-0x1:_0x3d0d50>_0xd6d4fc?0x1:0x0;});}[_0x40c820(0xe8)](_0x12537a,_0x57f3dc){var _0x2884a4=_0x40c820;if(!(_0x57f3dc[_0x2884a4(0xa4)]||!_0x12537a['props']||!_0x12537a[_0x2884a4(0x180)][_0x2884a4(0x14f)])){for(var _0x53c006=[],_0x347d6e=[],_0x52e85a=0x0,_0x273297=_0x12537a['props']['length'];_0x52e85a<_0x273297;_0x52e85a++){var _0x1ee5b3=_0x12537a[_0x2884a4(0x180)][_0x52e85a];_0x1ee5b3[_0x2884a4(0x10b)]==='function'?_0x53c006[_0x2884a4(0x157)](_0x1ee5b3):_0x347d6e['push'](_0x1ee5b3);}if(!(!_0x347d6e[_0x2884a4(0x14f)]||_0x53c006[_0x2884a4(0x14f)]<=0x1)){_0x12537a[_0x2884a4(0x180)]=_0x347d6e;var _0x15f515={'functionsNode':!0x0,'props':_0x53c006};this[_0x2884a4(0xcb)](_0x15f515,_0x57f3dc),this[_0x2884a4(0x163)](_0x15f515,_0x57f3dc),this[_0x2884a4(0xeb)](_0x15f515),this[_0x2884a4(0x125)](_0x15f515,_0x57f3dc),_0x15f515['id']+='\\\\x20f',_0x12537a['props']['unshift'](_0x15f515);}}}['_addLoadNode'](_0x5bea6e,_0x14049e){}[_0x40c820(0xeb)](_0x199084){}[_0x40c820(0x98)](_0xf50c17){var _0x35cb98=_0x40c820;return Array['isArray'](_0xf50c17)||typeof _0xf50c17==_0x35cb98(0x117)&&this['_objectToString'](_0xf50c17)===_0x35cb98(0xdc);}[_0x40c820(0x125)](_0x3ea390,_0x54c209){}[_0x40c820(0x16c)](_0x25cdb9){var _0x1aa0a5=_0x40c820;delete _0x25cdb9[_0x1aa0a5(0x136)],delete _0x25cdb9[_0x1aa0a5(0x143)],delete _0x25cdb9[_0x1aa0a5(0x138)];}['_setNodeExpressionPath'](_0x17f351,_0x40c77e){}}let _0x459cb0=new _0x1e3ba1(),_0x218fe5={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x11fc4c={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x482c8e(_0x50675f,_0x2f7559,_0x19c481,_0x2c8a95,_0x245f16,_0x24484e){var _0x4c67af=_0x40c820;let _0x3ce9b8,_0x1af844;try{_0x1af844=_0x1169a5(),_0x3ce9b8=_0x2f8209[_0x2f7559],!_0x3ce9b8||_0x1af844-_0x3ce9b8['ts']>0x1f4&&_0x3ce9b8[_0x4c67af(0xaa)]&&_0x3ce9b8['time']/_0x3ce9b8[_0x4c67af(0xaa)]<0x64?(_0x2f8209[_0x2f7559]=_0x3ce9b8={'count':0x0,'time':0x0,'ts':_0x1af844},_0x2f8209[_0x4c67af(0xc5)]={}):_0x1af844-_0x2f8209[_0x4c67af(0xc5)]['ts']>0x32&&_0x2f8209['hits']['count']&&_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x161)]/_0x2f8209[_0x4c67af(0xc5)]['count']<0x64&&(_0x2f8209[_0x4c67af(0xc5)]={});let _0x157126=[],_0x1464d6=_0x3ce9b8[_0x4c67af(0x112)]||_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]?_0x11fc4c:_0x218fe5,_0x553948=_0x2ff7b4=>{var _0x3a1316=_0x4c67af;let _0x5d676c={};return _0x5d676c[_0x3a1316(0x180)]=_0x2ff7b4[_0x3a1316(0x180)],_0x5d676c['elements']=_0x2ff7b4['elements'],_0x5d676c[_0x3a1316(0x126)]=_0x2ff7b4[_0x3a1316(0x126)],_0x5d676c['totalStrLength']=_0x2ff7b4['totalStrLength'],_0x5d676c['autoExpandLimit']=_0x2ff7b4[_0x3a1316(0x177)],_0x5d676c[_0x3a1316(0x115)]=_0x2ff7b4['autoExpandMaxDepth'],_0x5d676c['sortProps']=!0x1,_0x5d676c['noFunctions']=!_0x45a6b5,_0x5d676c['depth']=0x1,_0x5d676c[_0x3a1316(0xe3)]=0x0,_0x5d676c['expId']='root_exp_id',_0x5d676c[_0x3a1316(0x140)]=_0x3a1316(0x151),_0x5d676c['autoExpand']=!0x0,_0x5d676c[_0x3a1316(0x118)]=[],_0x5d676c[_0x3a1316(0x9d)]=0x0,_0x5d676c[_0x3a1316(0x168)]=!0x0,_0x5d676c['allStrLength']=0x0,_0x5d676c[_0x3a1316(0xc8)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0x5d676c;};for(var _0x33de8f=0x0;_0x33de8f<_0x245f16[_0x4c67af(0x14f)];_0x33de8f++)_0x157126[_0x4c67af(0x157)](_0x459cb0[_0x4c67af(0x14a)]({'timeNode':_0x50675f===_0x4c67af(0x161)||void 0x0},_0x245f16[_0x33de8f],_0x553948(_0x1464d6),{}));if(_0x50675f==='trace'||_0x50675f===_0x4c67af(0x184)){let _0x1d9735=Error[_0x4c67af(0xd7)];try{Error[_0x4c67af(0xd7)]=0x1/0x0,_0x157126[_0x4c67af(0x157)](_0x459cb0['serialize']({'stackNode':!0x0},new Error()['stack'],_0x553948(_0x1464d6),{'strLength':0x1/0x0}));}finally{Error[_0x4c67af(0xd7)]=_0x1d9735;}}return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':_0x157126,'id':_0x2f7559,'context':_0x24484e}]};}catch(_0x2d5a77){return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':[{'type':_0x4c67af(0xca),'error':_0x2d5a77&&_0x2d5a77[_0x4c67af(0xb4)]}],'id':_0x2f7559,'context':_0x24484e}]};}finally{try{if(_0x3ce9b8&&_0x1af844){let _0xff386f=_0x1169a5();_0x3ce9b8[_0x4c67af(0xaa)]++,_0x3ce9b8['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x3ce9b8['ts']=_0xff386f,_0x2f8209[_0x4c67af(0xc5)]['count']++,_0x2f8209[_0x4c67af(0xc5)]['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x2f8209['hits']['ts']=_0xff386f,(_0x3ce9b8[_0x4c67af(0xaa)]>0x32||_0x3ce9b8[_0x4c67af(0x161)]>0x64)&&(_0x3ce9b8['reduceLimits']=!0x0),(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0xaa)]>0x3e8||_0x2f8209[_0x4c67af(0xc5)]['time']>0x12c)&&(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]=!0x0);}}catch{}}}return _0x482c8e;}((_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x296e29,_0x567fe9,_0x14adfa,_0x6b3989,_0x593945,_0x42f609)=>{var _0x543ef9=_0x418f23;if(_0x12a02f[_0x543ef9(0xdb)])return _0x12a02f[_0x543ef9(0xdb)];if(!X(_0x12a02f,_0x14adfa,_0x1164b7))return _0x12a02f[_0x543ef9(0xdb)]={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x12a02f[_0x543ef9(0xdb)];let _0x5a7d78=B(_0x12a02f),_0x236b4f=_0x5a7d78[_0x543ef9(0xd3)],_0x57b9d9=_0x5a7d78['timeStamp'],_0x163b61=_0x5a7d78[_0x543ef9(0x11b)],_0x384cd9={'hits':{},'ts':{}},_0x9c7997=J(_0x12a02f,_0x6b3989,_0x384cd9,_0x296e29),_0x3ffb36=_0xa1ec34=>{_0x384cd9['ts'][_0xa1ec34]=_0x57b9d9();},_0x4ce4d2=(_0x173258,_0x2e0c6d)=>{var _0x3b6e53=_0x543ef9;let _0x2b64c1=_0x384cd9['ts'][_0x2e0c6d];if(delete _0x384cd9['ts'][_0x2e0c6d],_0x2b64c1){let _0x557981=_0x236b4f(_0x2b64c1,_0x57b9d9());_0x593a90(_0x9c7997(_0x3b6e53(0x161),_0x173258,_0x163b61(),_0x50e896,[_0x557981],_0x2e0c6d));}},_0x46c5f3=_0x1f105e=>{var _0x1152c8=_0x543ef9,_0x4a2783;return _0x1164b7===_0x1152c8(0x110)&&_0x12a02f[_0x1152c8(0x152)]&&((_0x4a2783=_0x1f105e==null?void 0x0:_0x1f105e[_0x1152c8(0xab)])==null?void 0x0:_0x4a2783[_0x1152c8(0x14f)])&&(_0x1f105e[_0x1152c8(0xab)][0x0][_0x1152c8(0x152)]=_0x12a02f[_0x1152c8(0x152)]),_0x1f105e;};_0x12a02f[_0x543ef9(0xdb)]={'consoleLog':(_0x204f4b,_0x3e1804)=>{var _0x309615=_0x543ef9;_0x12a02f['console'][_0x309615(0xff)][_0x309615(0xb7)]!==_0x309615(0x134)&&_0x593a90(_0x9c7997(_0x309615(0xff),_0x204f4b,_0x163b61(),_0x50e896,_0x3e1804));},'consoleTrace':(_0x267a3f,_0x51c339)=>{var _0x4c4943=_0x543ef9,_0x33fd8c,_0x24e61f;_0x12a02f[_0x4c4943(0xbc)][_0x4c4943(0xff)]['name']!==_0x4c4943(0xbd)&&((_0x24e61f=(_0x33fd8c=_0x12a02f[_0x4c4943(0x104)])==null?void 0x0:_0x33fd8c['versions'])!=null&&_0x24e61f[_0x4c4943(0xc8)]&&(_0x12a02f[_0x4c4943(0x165)]=!0x0),_0x593a90(_0x46c5f3(_0x9c7997(_0x4c4943(0xc4),_0x267a3f,_0x163b61(),_0x50e896,_0x51c339))));},'consoleError':(_0xf7f1fc,_0x1384d7)=>{var _0x28d83c=_0x543ef9;_0x12a02f['_ninjaIgnoreNextError']=!0x0,_0x593a90(_0x46c5f3(_0x9c7997(_0x28d83c(0x184),_0xf7f1fc,_0x163b61(),_0x50e896,_0x1384d7)));},'consoleTime':_0x2ad865=>{_0x3ffb36(_0x2ad865);},'consoleTimeEnd':(_0x3c91cf,_0x308c8b)=>{_0x4ce4d2(_0x308c8b,_0x3c91cf);},'autoLog':(_0x4bbc9f,_0x3599a3)=>{var _0x598cfa=_0x543ef9;_0x593a90(_0x9c7997(_0x598cfa(0xff),_0x3599a3,_0x163b61(),_0x50e896,[_0x4bbc9f]));},'autoLogMany':(_0x158592,_0x29b77d)=>{var _0x425f64=_0x543ef9;_0x593a90(_0x9c7997(_0x425f64(0xff),_0x158592,_0x163b61(),_0x50e896,_0x29b77d));},'autoTrace':(_0x3f5f9d,_0xc378ab)=>{var _0x377a7d=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x377a7d(0xc4),_0xc378ab,_0x163b61(),_0x50e896,[_0x3f5f9d])));},'autoTraceMany':(_0x2c6f73,_0x35405b)=>{var _0x4f4e7f=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x4f4e7f(0xc4),_0x2c6f73,_0x163b61(),_0x50e896,_0x35405b)));},'autoTime':(_0x4915d6,_0xaaf0db,_0x4c1f1e)=>{_0x3ffb36(_0x4c1f1e);},'autoTimeEnd':(_0x397624,_0x436d57,_0x47b9b8)=>{_0x4ce4d2(_0x436d57,_0x47b9b8);},'coverage':_0x45a646=>{var _0x1e9860=_0x543ef9;_0x593a90({'method':_0x1e9860(0x13a),'version':_0x296e29,'args':[{'id':_0x45a646}]});}};let _0x593a90=H(_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x593945,_0x42f609),_0x50e896=_0x12a02f[_0x543ef9(0xd8)];return _0x12a02f[_0x543ef9(0xdb)];})(globalThis,'127.0.0.1',_0x418f23(0xec),_0x418f23(0x17a),_0x418f23(0x183),'1.0.0','1751578325016',_0x418f23(0x137),_0x418f23(0x18a),_0x418f23(0x10a),_0x418f23(0xb1));\");\n  } catch (e) {}\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_oo(\n/**@type{any}**/\ni) {\n  for (var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    v[_key - 1] = arguments[_key];\n  }\n\n  try {\n    oo_cm().consoleLog(i, v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_tr(\n/**@type{any}**/\ni) {\n  for (var _len2 = arguments.length, v = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    v[_key2 - 1] = arguments[_key2];\n  }\n\n  try {\n    oo_cm().consoleTrace(i, v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_tx(\n/**@type{any}**/\ni) {\n  for (var _len3 = arguments.length, v = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n    v[_key3 - 1] = arguments[_key3];\n  }\n\n  try {\n    oo_cm().consoleError(i, v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_ts(\n/**@type{any}**/\nv) {\n  try {\n    oo_cm().consoleTime(v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_te(\n/**@type{any}**/\nv,\n/**@type{any}**/\ni) {\n  try {\n    oo_cm().consoleTimeEnd(v, i);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/\n\nvar _c;\n\n$RefreshReg$(_c, \"AIChat\");", "map": {"version": 3, "sources": ["D:/www/SWF/sian-web-frontend/src/views/commercial/salesDashboard/components/AIChat.jsx"], "names": ["React", "useState", "useEffect", "useRef", "Box", "ThemeProvider", "purpleTheme", "ChatButton", "ChatDialog", "XLSX", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "AIChat", "<PERSON><PERSON><PERSON><PERSON>", "process", "env", "REACT_APP_OPENAI_API_KEY", "model", "temperature", "apiEndpoint", "REACT_APP_OPENAI_URL_BASE", "onError", "error", "console", "oo_tx", "salesAdvanceData", "salesHoursData", "salesDivisionData", "salesStoreData", "salesLinesData", "openDialog", "setOpenDialog", "userInput", "setUserInput", "chatHistory", "setChatHistory", "isLoading", "setIsLoading", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>hart", "systemPrompt", "setSystemPrompt", "chatEndRef", "analysisTriggeredRef", "chartsList", "id", "name", "systemMessages", "exportToExcel", "dataToExport", "fileName", "sheetName", "map", "item", "Tienda", "monthlyGoal", "Ventas", "total", "monthlyProgress", "<PERSON><PERSON>", "hour", "formattedValue", "División", "Valor", "percentage", "toFixed", "division", "Lín<PERSON>", "line", "length", "worksheet", "utils", "json_to_sheet", "workbook", "book_new", "book_append_sheet", "writeFile", "analyzeData", "internalMessage", "chartType", "messages", "role", "content", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "data", "json", "aiResponse", "choices", "message", "current", "tableSummary", "store", "join", "hoursSummary", "hourData", "divisionSummary", "storeSummary", "totalSales", "reduce", "sum", "s", "linesSummary", "for<PERSON>ach", "realLines", "realLine", "scrollIntoView", "behavior", "handleOpenDialog", "handleCloseDialog", "handleSendMessage", "trim", "newMessage", "prev", "currentSystemPrompt", "display", "alignItems", "e", "target", "chartId", "oo_cm", "eval", "oo_oo", "i", "v", "consoleLog", "oo_tr", "consoleTrace", "consoleError", "oo_ts", "consoleTime", "oo_te", "consoleTimeEnd"], "mappings": ";;;AAAA;AACA,OAAOA,KAAP,IAAgBC,QAAhB,EAA0BC,SAA1B,EAAqCC,MAArC,QAAmD,OAAnD;AACA,SAASC,GAAT,EAAcC,aAAd,QAAmC,eAAnC;AACA,SAASC,WAAT,QAA4B,cAA5B;AACA,OAAOC,UAAP,MAAuB,mBAAvB;AACA,OAAOC,UAAP,MAAuB,mBAAvB;AACA,OAAO,KAAKC,IAAZ,MAAsB,MAAtB,C,CAEA;;;;AACA,MAAMC,cAAc,GAAIC,KAAD,IAAW;AAC9B,SAAO,IAAIC,IAAI,CAACC,YAAT,CAAsB,OAAtB,EAA+B;AAClCC,IAAAA,KAAK,EAAE,UAD2B;AAElCC,IAAAA,QAAQ,EAAE,KAFwB;AAGlCC,IAAAA,qBAAqB,EAAE;AAHW,GAA/B,EAIJC,MAJI,CAIGN,KAJH,CAAP;AAKH,CAND;;AAQA,MAAMO,MAAM,GAAG,QAWT;AAAA;;AAAA,MAXU;AACZC,IAAAA,MAAM,GAAGC,OAAO,CAACC,GAAR,CAAYC,wBADT;AAEZC,IAAAA,KAAK,GAAG,aAFI;AAGZC,IAAAA,WAAW,GAAG,GAHF;AAIZC,IAAAA,WAAW,GAAGL,OAAO,CAACC,GAAR,CAAYK,yBAJd;AAKZC,IAAAA,OAAO,GAAIC,KAAD;AAAW;AAAoBC,IAAAA,OAAO,CAACD,KAAR,CAAc,GAAGE,KAAK,CAAE,0BAAF,EAA4B,QAA5B,EAAsCF,KAAtC,CAAtB,CAL7B;AAMZG,IAAAA,gBANY;AAOZC,IAAAA,cAPY;AAQZC,IAAAA,iBARY;AASZC,IAAAA,cATY;AAUZC,IAAAA;AAVY,GAWV;AACF,QAAM,CAACC,UAAD,EAAaC,aAAb,IAA8BpC,QAAQ,CAAC,KAAD,CAA5C;AACA,QAAM,CAACqC,SAAD,EAAYC,YAAZ,IAA4BtC,QAAQ,CAAC,EAAD,CAA1C;AACA,QAAM,CAACuC,WAAD,EAAcC,cAAd,IAAgCxC,QAAQ,CAAC,EAAD,CAA9C;AACA,QAAM,CAACyC,SAAD,EAAYC,YAAZ,IAA4B1C,QAAQ,CAAC,KAAD,CAA1C;AACA,QAAM,CAAC2C,aAAD,EAAgBC,gBAAhB,IAAoC5C,QAAQ,CAAC,IAAD,CAAlD;AACA,QAAM,CAAC6C,YAAD,EAAeC,eAAf,IAAkC9C,QAAQ,CAAC,EAAD,CAAhD;AACA,QAAM+C,UAAU,GAAG7C,MAAM,CAAC,IAAD,CAAzB;AACA,QAAM8C,oBAAoB,GAAG9C,MAAM,CAAC,KAAD,CAAnC,CARE,CAUF;;AACA,QAAM+C,UAAU,GAAG,CACf;AAAEC,IAAAA,EAAE,EAAE,eAAN;AAAuBC,IAAAA,IAAI,EAAE;AAA7B,GADe,EAEf;AAAED,IAAAA,EAAE,EAAE,aAAN;AAAqBC,IAAAA,IAAI,EAAE;AAA3B,GAFe,EAGf;AAAED,IAAAA,EAAE,EAAE,gBAAN;AAAwBC,IAAAA,IAAI,EAAE;AAA9B,GAHe,EAIf;AAAED,IAAAA,EAAE,EAAE,aAAN;AAAqBC,IAAAA,IAAI,EAAE;AAA3B,GAJe,EAKf;AAAED,IAAAA,EAAE,EAAE,aAAN;AAAqBC,IAAAA,IAAI,EAAE;AAA3B,GALe,CAAnB,CAXE,CAmBF;;AACA,QAAMC,cAAc,GAAG;AACnB,qBAAiB,2KADE;AAEnB,mBAAe,6LAFI;AAGnB,sBAAkB,wGAHC;AAInB,mBAAe,sGAJI;AAKnB,mBAAe;AALI,GAAvB;;AAQA,QAAMC,aAAa,GAAG,MAAM;AACxB;AACA,QAAIC,YAAY,GAAG,EAAnB;AACA,QAAIC,QAAQ,GAAG,cAAf;AACA,QAAIC,SAAS,GAAG,OAAhB;;AAEA,YAAQb,aAAR;AACI,WAAK,eAAL;AACI,YAAIb,gBAAJ,EAAsB;AAClBwB,UAAAA,YAAY,GAAGxB,gBAAgB,CAAC2B,GAAjB,CAAqBC,IAAI,KAAK;AACzCC,YAAAA,MAAM,EAAED,IAAI,CAACP,IAD4B;AAEzC,4BAAgBO,IAAI,CAACE,WAFoB;AAGzCC,YAAAA,MAAM,EAAEH,IAAI,CAACI,KAH4B;AAIzC,0BAAcJ,IAAI,CAACK;AAJsB,WAAL,CAAzB,CAAf;AAMAR,UAAAA,QAAQ,GAAG,2BAAX;AACAC,UAAAA,SAAS,GAAG,kBAAZ;AACH;;AACD;;AACJ,WAAK,aAAL;AACI,YAAIzB,cAAJ,EAAoB;AAChBuB,UAAAA,YAAY,GAAGvB,cAAc,CAAC0B,GAAf,CAAmBC,IAAI,KAAK;AACvCM,YAAAA,IAAI,EAAEN,IAAI,CAACO,IAD4B;AAEvCJ,YAAAA,MAAM,EAAEH,IAAI,CAAChD,KAF0B;AAGvC,gCAAoBgD,IAAI,CAACQ;AAHc,WAAL,CAAvB,CAAf;AAKAX,UAAAA,QAAQ,GAAG,sBAAX;AACAC,UAAAA,SAAS,GAAG,iBAAZ;AACH;;AACD;;AACJ,WAAK,gBAAL;AACI,YAAIxB,iBAAJ,EAAuB;AACnBsB,UAAAA,YAAY,GAAGtB,iBAAiB,CAACyB,GAAlB,CAAsBC,IAAI,KAAK;AAC1CS,YAAAA,QAAQ,EAAET,IAAI,CAACP,IAD2B;AAE1CiB,YAAAA,KAAK,EAAEV,IAAI,CAAChD,KAF8B;AAG1C,8BAAkBgD,IAAI,CAACW,UAAL,CAAgBC,OAAhB,CAAwB,CAAxB;AAHwB,WAAL,CAA1B,CAAf;AAKAf,UAAAA,QAAQ,GAAG,0BAAX;AACAC,UAAAA,SAAS,GAAG,qBAAZ;AACH;;AACD;;AACJ,WAAK,aAAL;AACI,YAAIvB,cAAJ,EAAoB;AAChBqB,UAAAA,YAAY,GAAGrB,cAAc,CAACwB,GAAf,CAAmBC,IAAI,KAAK;AACvCC,YAAAA,MAAM,EAAED,IAAI,CAACP,IAD0B;AAEvCiB,YAAAA,KAAK,EAAEV,IAAI,CAAChD,KAF2B;AAGvC,8BAAkBgD,IAAI,CAACW,UAAL,CAAgBC,OAAhB,CAAwB,CAAxB;AAHqB,WAAL,CAAvB,CAAf;AAKAf,UAAAA,QAAQ,GAAG,wBAAX;AACAC,UAAAA,SAAS,GAAG,mBAAZ;AACH;;AACD;;AACJ,WAAK,aAAL;AACI,YAAItB,cAAJ,EAAoB;AAChBoB,UAAAA,YAAY,GAAGpB,cAAc,CAACuB,GAAf,CAAmBC,IAAI,KAAK;AACvCS,YAAAA,QAAQ,EAAET,IAAI,CAACa,QADwB;AAEvCC,YAAAA,KAAK,EAAEd,IAAI,CAACe,IAF2B;AAGvCL,YAAAA,KAAK,EAAEV,IAAI,CAAChD,KAH2B;AAIvC,8BAAkBgD,IAAI,CAACW,UAAL,CAAgBC,OAAhB,CAAwB,CAAxB;AAJqB,WAAL,CAAvB,CAAf;AAMAf,UAAAA,QAAQ,GAAG,gCAAX;AACAC,UAAAA,SAAS,GAAG,6BAAZ;AACH;;AACD;;AACJ;AACI;AA3DR;;AA8DA,QAAIF,YAAY,CAACoB,MAAb,GAAsB,CAA1B,EAA6B;AACzB;AACA,YAAMC,SAAS,GAAGnE,IAAI,CAACoE,KAAL,CAAWC,aAAX,CAAyBvB,YAAzB,CAAlB,CAFyB,CAIzB;;AACA,YAAMwB,QAAQ,GAAGtE,IAAI,CAACoE,KAAL,CAAWG,QAAX,EAAjB,CALyB,CAOzB;;AACAvE,MAAAA,IAAI,CAACoE,KAAL,CAAWI,iBAAX,CAA6BF,QAA7B,EAAuCH,SAAvC,EAAkDnB,SAAlD,EARyB,CAUzB;;AACAhD,MAAAA,IAAI,CAACyE,SAAL,CAAeH,QAAf,EAAyBvB,QAAzB;AACH,KAZD,MAYO;AACH;AAAoB3B,MAAAA,OAAO,CAACD,KAAR,CAAc,GAAGE,KAAK,CAAE,4BAAF,EAA8B,4BAA9B,CAAtB;AACvB;AACJ,GAnFD,CA5BE,CAiHF;;;AACA,QAAMqD,WAAW,GAAG,OAAOC,eAAP,EAAwBC,SAAxB,KAAsC;AACtD1C,IAAAA,YAAY,CAAC,IAAD,CAAZ,CADsD,CAEtD;;AACAI,IAAAA,eAAe,CAACqC,eAAD,CAAf;AAEA,UAAME,QAAQ,GAAG,CAAC;AAAEC,MAAAA,IAAI,EAAE,QAAR;AAAkBC,MAAAA,OAAO,EAAEJ;AAA3B,KAAD,CAAjB;;AACA,QAAI;AACA,YAAMK,QAAQ,GAAG,MAAMC,KAAK,CAACjE,WAAD,EAAc;AACtCkE,QAAAA,MAAM,EAAE,MAD8B;AAEtCC,QAAAA,OAAO,EAAE;AACL,0BAAgB,kBADX;AAEL,2BAAkB,UAASzE,MAAO;AAF7B,SAF6B;AAMtC0E,QAAAA,IAAI,EAAEC,IAAI,CAACC,SAAL,CAAe;AACjBxE,UAAAA,KADiB;AAEjB+D,UAAAA,QAFiB;AAGjB9D,UAAAA;AAHiB,SAAf;AANgC,OAAd,CAA5B;;AAYA,UAAI,CAACiE,QAAQ,CAACO,EAAd,EAAkB;AACd,cAAM,IAAIC,KAAJ,CAAU,sCAAV,CAAN;AACH;;AACD,YAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAT,EAAnB;AACA,YAAMC,UAAU,GAAGF,IAAI,CAACG,OAAL,CAAa,CAAb,EAAgBC,OAAnC,CAjBA,CAkBA;;AACA7D,MAAAA,cAAc,CAAC,CAAC2D,UAAD,CAAD,CAAd;AACH,KApBD,CAoBE,OAAOxE,KAAP,EAAc;AACZD,MAAAA,OAAO,CAACC,KAAD,CAAP;AACAa,MAAAA,cAAc,CAAC,CACX;AAAE8C,QAAAA,IAAI,EAAE,WAAR;AAAqBC,QAAAA,OAAO,EAAE;AAA9B,OADW,CAAD,CAAd;AAGH,KAzBD,SAyBU;AACN7C,MAAAA,YAAY,CAAC,KAAD,CAAZ;AACH;AACJ,GAlCD,CAlHE,CAsJF;;;AACAzC,EAAAA,SAAS,CAAC,MAAM;AACZ;AACAuC,IAAAA,cAAc,CAAC,EAAD,CAAd;AACAM,IAAAA,eAAe,CAAC,EAAD,CAAf;AACAE,IAAAA,oBAAoB,CAACsD,OAArB,GAA+B,KAA/B;AAEA,QAAI,CAAC3D,aAAL,EAAoB,OANR,CAQZ;;AACA,YAAQA,aAAR;AACI,WAAK,eAAL;AACI,YAAIb,gBAAJ,EAAsB;AAClB;AACAU,UAAAA,cAAc,CAAC,CAAC;AAAE8C,YAAAA,IAAI,EAAE,WAAR;AAAqBC,YAAAA,OAAO,EAAE;AAA9B,WAAD,CAAD,CAAd,CAFkB,CAIlB;;AACA,gBAAMgB,YAAY,GAAGzE,gBAAgB,CAChC2B,GADgB,CACZ+C,KAAK,IACL,OAAMA,KAAK,CAACrD,IAAK,qBAAoBqD,KAAK,CAAC5C,WAAY,aAAY4C,KAAK,CAAC1C,KAAM,eAAc0C,KAAK,CAACzC,eAAgB,GAFvG,EAGf0C,IAHe,CAGV,IAHU,CAArB,CALkB,CAUlB;;AACA,gBAAMtB,eAAe,GAAI;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAEoB,YAAa;AACf;AACA,gLAVoB;AAYArB,UAAAA,WAAW,CAACC,eAAD,EAAkB,eAAlB,CAAX;AACH;;AACD;;AAEJ,WAAK,aAAL;AACI,YAAIpD,cAAJ,EAAoB;AAChB;AACAS,UAAAA,cAAc,CAAC,CAAC;AAAE8C,YAAAA,IAAI,EAAE,WAAR;AAAqBC,YAAAA,OAAO,EAAE;AAA9B,WAAD,CAAD,CAAd,CAFgB,CAIhB;;AACA,gBAAMmB,YAAY,GAAG3E,cAAc,CAC9B0B,GADgB,CACZkD,QAAQ,IACR,OAAMA,QAAQ,CAAC1C,IAAK,eAAc0C,QAAQ,CAACzC,cAAe,EAF9C,EAGfuC,IAHe,CAGV,IAHU,CAArB,CALgB,CAUhB;;AACA,gBAAMtB,eAAe,GAAI;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAEuB,YAAa;AACf;AACA,wKAVoB;AAYAxB,UAAAA,WAAW,CAACC,eAAD,EAAkB,aAAlB,CAAX;AACH;;AACD;;AAEJ,WAAK,gBAAL;AACI,YAAInD,iBAAJ,EAAuB;AACnBQ,UAAAA,cAAc,CAAC,CAAC;AAAE8C,YAAAA,IAAI,EAAE,WAAR;AAAqBC,YAAAA,OAAO,EAAE;AAA9B,WAAD,CAAD,CAAd,CADmB,CAGnB;;AACA,gBAAMqB,eAAe,GAAG5E,iBAAiB,CACpCyB,GADmB,CACfc,QAAQ,IACR,OAAMA,QAAQ,CAACpB,IAAK,cAAaoB,QAAQ,CAAC7D,KAAM,oBAAmB6D,QAAQ,CAACF,UAAT,CAAoBC,OAApB,CAA4B,CAA5B,CAA+B,GAFnF,EAGlBmC,IAHkB,CAGb,IAHa,CAAxB,CAJmB,CASnB;;AACA,gBAAMtB,eAAe,GAAI;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAEyB,eAAgB;AAClB;AACA,6EAVoB;AAYA1B,UAAAA,WAAW,CAACC,eAAD,EAAkB,gBAAlB,CAAX;AACH;;AACD;;AAEJ,WAAK,aAAL;AACI,YAAIlD,cAAJ,EAAoB;AAChBO,UAAAA,cAAc,CAAC,CAAC;AAAE8C,YAAAA,IAAI,EAAE,WAAR;AAAqBC,YAAAA,OAAO,EAAE;AAA9B,WAAD,CAAD,CAAd,CADgB,CAGhB;;AACA,gBAAMsB,YAAY,GAAG5E,cAAc,CAC9BwB,GADgB,CACZ+C,KAAK,IAAI;AACV;AACA,kBAAMM,UAAU,GAAG7E,cAAc,CAAC8E,MAAf,CAAsB,CAACC,GAAD,EAAMC,CAAN,KAAYD,GAAG,GAAGC,CAAC,CAACnD,KAA1C,EAAiD,CAAjD,CAAnB;AACA,kBAAMO,UAAU,GAAGmC,KAAK,CAACzC,eAAN,IAA2ByC,KAAK,CAAC1C,KAAN,GAAcgD,UAAf,GAA6B,GAA1E;AAEA,mBAAQ,OAAMN,KAAK,CAACrD,IAAK,cAAaqD,KAAK,CAAC1C,KAAM,mBAAkB0C,KAAK,CAAC5C,WAAY,eAAc,OAAOS,UAAP,KAAsB,QAAtB,GAAiCA,UAAjC,GAA8CA,UAAU,CAACC,OAAX,CAAmB,CAAnB,CAAsB,GAAxK;AACH,WAPgB,EAOdmC,IAPc,CAOT,IAPS,CAArB,CAJgB,CAahB;;AACA,gBAAMtB,eAAe,GAAI;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE0B,YAAa;AACf;AACA,6EAVoB;AAYA3B,UAAAA,WAAW,CAACC,eAAD,EAAkB,aAAlB,CAAX;AACH;;AACD;;AAEJ,WAAK,aAAL;AACI,YAAIjD,cAAJ,EAAoB;AAChBM,UAAAA,cAAc,CAAC,CAAC;AAAE8C,YAAAA,IAAI,EAAE,WAAR;AAAqBC,YAAAA,OAAO,EAAE;AAA9B,WAAD,CAAD,CAAd,CADgB,CAGhB;;AACA,cAAI2B,YAAY,GAAG,EAAnB,CAJgB,CAMhB;;AACAhF,UAAAA,cAAc,CAACiF,OAAf,CAAuB5C,QAAQ,IAAI;AAC/B;AACA2C,YAAAA,YAAY,IAAK,iBAAgB3C,QAAQ,CAACpB,IAAK,cAAa1C,cAAc,CAAC8D,QAAQ,CAAC7D,KAAV,CAAiB,oBAAmB6D,QAAQ,CAACF,UAAT,CAAoBC,OAApB,CAA4B,CAA5B,CAA+B,KAA7I,CAF+B,CAI/B;;AACAC,YAAAA,QAAQ,CAAC6C,SAAT,CAAmBD,OAAnB,CAA2BE,QAAQ,IAAI;AACnCH,cAAAA,YAAY,IAAK,gBAAeG,QAAQ,CAAClE,IAAK,cAAa1C,cAAc,CAAC4G,QAAQ,CAAC3G,KAAV,CAAiB,oBAAmB2G,QAAQ,CAAChD,UAAT,CAAoBC,OAApB,CAA4B,CAA5B,CAA+B,KAA5I;AACH,aAFD,EAL+B,CAS/B;;AACA4C,YAAAA,YAAY,IAAI,IAAhB;AACH,WAXD,EAPgB,CAoBhB;;AACA,gBAAM/B,eAAe,GAAI;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE+B,YAAa;AACf,6EAToB;AAWAhC,UAAAA,WAAW,CAACC,eAAD,EAAkB,aAAlB,CAAX;AACH;;AACD;;AAEJ;AACI,YAAI/B,cAAc,CAACT,aAAD,CAAlB,EAAmC;AAC/BG,UAAAA,eAAe,CAACM,cAAc,CAACT,aAAD,CAAf,CAAf;AACAH,UAAAA,cAAc,CAAC,CAAC;AAAE8C,YAAAA,IAAI,EAAE,WAAR;AAAqBC,YAAAA,OAAO,EAAEnC,cAAc,CAACT,aAAD;AAA5C,WAAD,CAAD,CAAd;AACH;;AACD;AA7JR;AA+JH,GAxKQ,EAwKN,CAACA,aAAD,EAAgBb,gBAAhB,EAAkCC,cAAlC,EAAkDC,iBAAlD,EAAqEC,cAArE,EAAqFC,cAArF,CAxKM,CAAT;AA0KAjC,EAAAA,SAAS,CAAC,MAAM;AAAA;;AACZ,2BAAA8C,UAAU,CAACuD,OAAX,4EAAoBgB,cAApB,CAAmC;AAAEC,MAAAA,QAAQ,EAAE;AAAZ,KAAnC;AACH,GAFQ,EAEN,CAAChF,WAAD,CAFM,CAAT;;AAIA,QAAMiF,gBAAgB,GAAG,MAAMpF,aAAa,CAAC,IAAD,CAA5C;;AACA,QAAMqF,iBAAiB,GAAG,MAAM;AAC5BrF,IAAAA,aAAa,CAAC,KAAD,CAAb;AACAQ,IAAAA,gBAAgB,CAAC,IAAD,CAAhB;AACAE,IAAAA,eAAe,CAAC,EAAD,CAAf;AACAN,IAAAA,cAAc,CAAC,EAAD,CAAd;AACH,GALD,CAtUE,CA6UF;;;AACA,QAAMkF,iBAAiB,GAAG,YAAY;AAClC,QAAI,CAACrF,SAAS,CAACsF,IAAV,EAAD,IAAqB,CAAChF,aAA1B,EAAyC;AACzC,UAAMiF,UAAU,GAAG;AAAEtC,MAAAA,IAAI,EAAE,MAAR;AAAgBC,MAAAA,OAAO,EAAElD;AAAzB,KAAnB;AACAG,IAAAA,cAAc,CAACqF,IAAI,IAAI,CAAC,GAAGA,IAAJ,EAAUD,UAAV,CAAT,CAAd;AACAtF,IAAAA,YAAY,CAAC,EAAD,CAAZ;AACAI,IAAAA,YAAY,CAAC,IAAD,CAAZ;;AAEA,QAAI;AACA;AACA;AACA,YAAMoF,mBAAmB,GAAGjF,YAAY,IAAIO,cAAc,CAACT,aAAD,CAA9B,IAAiD,EAA7E,CAHA,CAKA;;AACA,YAAM0C,QAAQ,GAAG,CACb;AAAEC,QAAAA,IAAI,EAAE,QAAR;AAAkBC,QAAAA,OAAO,EAAEuC;AAA3B,OADa,EAEb,GAAGvF,WAFU,EAGbqF,UAHa,CAAjB;AAMA,YAAMpC,QAAQ,GAAG,MAAMC,KAAK,CAACjE,WAAD,EAAc;AACtCkE,QAAAA,MAAM,EAAE,MAD8B;AAEtCC,QAAAA,OAAO,EAAE;AACL,0BAAgB,kBADX;AAEL,2BAAkB,UAASzE,MAAO;AAF7B,SAF6B;AAMtC0E,QAAAA,IAAI,EAAEC,IAAI,CAACC,SAAL,CAAe;AACjBxE,UAAAA,KADiB;AAEjB+D,UAAAA,QAFiB;AAGjB9D,UAAAA;AAHiB,SAAf;AANgC,OAAd,CAA5B;;AAYA,UAAI,CAACiE,QAAQ,CAACO,EAAd,EAAkB;AACd,cAAM,IAAIC,KAAJ,CAAU,sCAAV,CAAN;AACH;;AACD,YAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAT,EAAnB;AACA,YAAMC,UAAU,GAAGF,IAAI,CAACG,OAAL,CAAa,CAAb,EAAgBC,OAAnC;AACA7D,MAAAA,cAAc,CAACqF,IAAI,IAAI,CAAC,GAAGA,IAAJ,EAAU1B,UAAV,CAAT,CAAd;AACH,KA9BD,CA8BE,OAAOxE,KAAP,EAAc;AACZD,MAAAA,OAAO,CAACC,KAAD,CAAP;AACAa,MAAAA,cAAc,CAACqF,IAAI,IAAI,CACnB,GAAGA,IADgB,EAEnB;AAAEvC,QAAAA,IAAI,EAAE,WAAR;AAAqBC,QAAAA,OAAO,EAAE;AAA9B,OAFmB,CAAT,CAAd;AAIH,KApCD,SAoCU;AACN7C,MAAAA,YAAY,CAAC,KAAD,CAAZ;AACH;AACJ,GA9CD;;AAgDA,sBACI,QAAC,aAAD;AAAe,IAAA,KAAK,EAAErC,WAAtB;AAAA,4BACI,QAAC,GAAD;AAAK,MAAA,EAAE,EAAE;AAAE0H,QAAAA,OAAO,EAAE,aAAX;AAA0BC,QAAAA,UAAU,EAAE;AAAtC,OAAT;AAAA,6BACI,QAAC,UAAD;AAAY,QAAA,OAAO,EAAER,gBAArB;AAAuC,QAAA,IAAI,EAAC;AAA5C;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,YADJ,eAII,QAAC,UAAD;AACI,MAAA,IAAI,EAAErF,UADV;AAEI,MAAA,OAAO,EAAEsF,iBAFb;AAGI,MAAA,WAAW,EAAElF,WAHjB;AAII,MAAA,SAAS,EAAEF,SAJf;AAKI,MAAA,SAAS,EAAEI,SALf;AAMI,MAAA,iBAAiB,EAAGwF,CAAD,IAAO3F,YAAY,CAAC2F,CAAC,CAACC,MAAF,CAASxH,KAAV,CAN1C;AAOI,MAAA,aAAa,EAAEgH,iBAPnB;AAQI,MAAA,UAAU,EAAE3E,UARhB;AASI,MAAA,UAAU,EAAEE,UAThB;AAUI,MAAA,aAAa,EAAEN,aAVnB;AAWI,MAAA,aAAa,EAAGwF,OAAD,IAAavF,gBAAgB,CAACuF,OAAD,CAXhD;AAYI,MAAA,eAAe,EAAE9E;AAZrB;AAAA;AAAA;AAAA;AAAA,YAJJ;AAAA;AAAA;AAAA;AAAA;AAAA,UADJ;AAqBH,CA9ZD;;GAAMpC,M;;KAAAA,M;AAgaN,eAAeA,MAAf;AACA;;AAA0B;;AAAqB;;AAAoB;;AAAC,SAASmH,KAAT,GAAgB;AAAC,MAAG;AAAC,WAAO,CAAC,GAAEC,IAAH,EAAS,2BAAT,KAAyC,CAAC,GAAEA,IAAH,EAAS,qnuCAAT,CAAhD;AAAiruC,GAArruC,CAAqruC,OAAMJ,CAAN,EAAQ,CAAE;AAAC;;AAAA;AAAC;;AAA0B,SAASK,KAAT;AAAe;AAAgBC,CAA/B,EAAsD;AAAA,oCAAFC,CAAE;AAAFA,IAAAA,CAAE;AAAA;;AAAC,MAAG;AAACJ,IAAAA,KAAK,GAAGK,UAAR,CAAmBF,CAAnB,EAAsBC,CAAtB;AAA0B,GAA9B,CAA8B,OAAMP,CAAN,EAAQ,CAAE;;AAAC,SAAOO,CAAP;AAAS;;AAAA;AAAC;;AAA0B,SAASE,KAAT;AAAe;AAAgBH,CAA/B,EAAsD;AAAA,qCAAFC,CAAE;AAAFA,IAAAA,CAAE;AAAA;;AAAC,MAAG;AAACJ,IAAAA,KAAK,GAAGO,YAAR,CAAqBJ,CAArB,EAAwBC,CAAxB;AAA4B,GAAhC,CAAgC,OAAMP,CAAN,EAAQ,CAAE;;AAAC,SAAOO,CAAP;AAAS;;AAAA;AAAC;;AAA0B,SAAS3G,KAAT;AAAe;AAAgB0G,CAA/B,EAAsD;AAAA,qCAAFC,CAAE;AAAFA,IAAAA,CAAE;AAAA;;AAAC,MAAG;AAACJ,IAAAA,KAAK,GAAGQ,YAAR,CAAqBL,CAArB,EAAwBC,CAAxB;AAA4B,GAAhC,CAAgC,OAAMP,CAAN,EAAQ,CAAE;;AAAC,SAAOO,CAAP;AAAS;;AAAA;AAAC;;AAA0B,SAASK,KAAT;AAAe;AAAgBL,CAA/B,EAAiC;AAAC,MAAG;AAACJ,IAAAA,KAAK,GAAGU,WAAR,CAAoBN,CAApB;AAAwB,GAA5B,CAA4B,OAAMP,CAAN,EAAQ,CAAE;;AAAC,SAAOO,CAAP;AAAU;;AAAA;AAAC;;AAA0B,SAASO,KAAT;AAAe;AAAgBP,CAA/B;AAAkC;AAAgBD,CAAlD,EAAoD;AAAC,MAAG;AAACH,IAAAA,KAAK,GAAGY,cAAR,CAAuBR,CAAvB,EAA0BD,CAA1B;AAA8B,GAAlC,CAAkC,OAAMN,CAAN,EAAQ,CAAE;;AAAC,SAAOO,CAAP;AAAU;;AAAA;AAAC", "sourcesContent": ["/* eslint-disable */\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport { Box, ThemeProvider } from '@mui/material';\r\nimport { purpleTheme } from './chat/theme';\r\nimport ChatButton from './chat/chatButton';\r\nimport ChatDialog from './chat/chatDialog';\r\nimport * as XLSX from 'xlsx';\r\n\r\n// Format currency function\r\nconst formatCurrency = (value) => {\r\n    return new Intl.NumberFormat('es-PE', {\r\n        style: 'currency',\r\n        currency: 'PEN',\r\n        minimumFractionDigits: 2\r\n    }).format(value);\r\n};\r\n\r\nconst AIChat = ({\r\n    apiKey = process.env.REACT_APP_OPENAI_API_KEY,\r\n    model = 'gpt-4o-mini',\r\n    temperature = 0.7,\r\n    apiEndpoint = process.env.REACT_APP_OPENAI_URL_BASE,\r\n    onError = (error) => /* eslint-disable */console.error(...oo_tx(`798630434_23_25_23_55_11`,'Error:', error)),\r\n    salesAdvanceData,\r\n    salesHoursData,\r\n    salesDivisionData,\r\n    salesStoreData,\r\n    salesLinesData\r\n}) => {\r\n    const [openDialog, setOpenDialog] = useState(false);\r\n    const [userInput, setUserInput] = useState('');\r\n    const [chatHistory, setChatHistory] = useState([]);\r\n    const [isLoading, setIsLoading] = useState(false);\r\n    const [selectedChart, setSelectedChart] = useState(null);\r\n    const [systemPrompt, setSystemPrompt] = useState('');\r\n    const chatEndRef = useRef(null);\r\n    const analysisTriggeredRef = useRef(false);\r\n\r\n    // Lista de gráficos disponibles\r\n    const chartsList = [\r\n        { id: 'sales-advance', name: 'Avances de Ventas Por Tienda' },\r\n        { id: 'sales-hours', name: 'Participación de Ventas por Hora' },\r\n        { id: 'sales-division', name: 'Participación de Ventas por División' },\r\n        { id: 'sales-store', name: 'Participación de Ventas por Tienda' },\r\n        { id: 'sales-lines', name: 'Detalle de Ventas por División y Líneas' }\r\n    ];\r\n\r\n    // Mensajes para otros gráficos (los que se muestran al usuario)\r\n    const systemMessages = {\r\n        'sales-advance': 'Estás analizando el avance de ventas por tienda. Usa la información del análisis inicial para responder preguntas sobre rendimiento, metas y recomendaciones específicas.',\r\n        'sales-hours': 'Estás analizando la participación de ventas por hora. Usa la información del análisis inicial para responder preguntas sobre patrones de tráfico, horas pico y recomendaciones específicas.',\r\n        'sales-division': 'Has seleccionado \"Participación de Ventas por División\". Pregunta lo que necesites sobre este gráfico.',\r\n        'sales-store': 'Has seleccionado \"Participación de Ventas por Tienda\". Pregunta lo que necesites sobre este gráfico.',\r\n        'sales-lines': 'Has seleccionado \"Detalle de Ventas por División y Líneas\". Pregunta lo que necesites sobre esta tabla.'\r\n    };\r\n\r\n    const exportToExcel = () => {\r\n        // Seleccionamos los datos según el gráfico seleccionado\r\n        let dataToExport = [];\r\n        let fileName = 'reporte.xlsx';\r\n        let sheetName = 'Datos';\r\n\r\n        switch (selectedChart) {\r\n            case 'sales-advance':\r\n                if (salesAdvanceData) {\r\n                    dataToExport = salesAdvanceData.map(item => ({\r\n                        Tienda: item.name,\r\n                        'Meta Mensual': item.monthlyGoal,\r\n                        Ventas: item.total,\r\n                        'Avance (%)': item.monthlyProgress\r\n                    }));\r\n                    fileName = 'avance_ventas_tienda.xlsx';\r\n                    sheetName = 'Avance de Ventas';\r\n                }\r\n                break;\r\n            case 'sales-hours':\r\n                if (salesHoursData) {\r\n                    dataToExport = salesHoursData.map(item => ({\r\n                        Hora: item.hour,\r\n                        Ventas: item.value,\r\n                        'Valor Formateado': item.formattedValue\r\n                    }));\r\n                    fileName = 'ventas_por_hora.xlsx';\r\n                    sheetName = 'Ventas por Hora';\r\n                }\r\n                break;\r\n            case 'sales-division':\r\n                if (salesDivisionData) {\r\n                    dataToExport = salesDivisionData.map(item => ({\r\n                        División: item.name,\r\n                        Valor: item.value,\r\n                        'Porcentaje (%)': item.percentage.toFixed(2)\r\n                    }));\r\n                    fileName = 'ventas_por_division.xlsx';\r\n                    sheetName = 'Ventas por División';\r\n                }\r\n                break;\r\n            case 'sales-store':\r\n                if (salesStoreData) {\r\n                    dataToExport = salesStoreData.map(item => ({\r\n                        Tienda: item.name,\r\n                        Valor: item.value,\r\n                        'Porcentaje (%)': item.percentage.toFixed(2)\r\n                    }));\r\n                    fileName = 'ventas_por_tienda.xlsx';\r\n                    sheetName = 'Ventas por Tienda';\r\n                }\r\n                break;\r\n            case 'sales-lines':\r\n                if (salesLinesData) {\r\n                    dataToExport = salesLinesData.map(item => ({\r\n                        División: item.division,\r\n                        Línea: item.line,\r\n                        Valor: item.value,\r\n                        'Porcentaje (%)': item.percentage.toFixed(2)\r\n                    }));\r\n                    fileName = 'ventas_por_division_linea.xlsx';\r\n                    sheetName = 'Ventas por División y Línea';\r\n                }\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n\r\n        if (dataToExport.length > 0) {\r\n            // Crear una nueva hoja de trabajo\r\n            const worksheet = XLSX.utils.json_to_sheet(dataToExport);\r\n\r\n            // Crear un nuevo libro de trabajo\r\n            const workbook = XLSX.utils.book_new();\r\n\r\n            // Añadir la hoja de trabajo al libro\r\n            XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);\r\n\r\n            // Generar el archivo Excel\r\n            XLSX.writeFile(workbook, fileName);\r\n        } else {\r\n            /* eslint-disable */console.error(...oo_tx(`798630434_138_12_138_55_11`,'No hay datos para exportar'));\r\n        }\r\n    };\r\n\r\n    // Función para llamar a la API utilizando un mensaje interno (no visible al usuario)\r\n    const analyzeData = async (internalMessage, chartType) => {\r\n        setIsLoading(true);\r\n        // Guardamos este mensaje como prompt del sistema para futuras referencias\r\n        setSystemPrompt(internalMessage);\r\n\r\n        const messages = [{ role: 'system', content: internalMessage }];\r\n        try {\r\n            const response = await fetch(apiEndpoint, {\r\n                method: 'POST',\r\n                headers: {\r\n                    'Content-Type': 'application/json',\r\n                    'Authorization': `Bearer ${apiKey}`\r\n                },\r\n                body: JSON.stringify({\r\n                    model,\r\n                    messages,\r\n                    temperature\r\n                })\r\n            });\r\n            if (!response.ok) {\r\n                throw new Error('Error al obtener respuesta de OpenAI');\r\n            }\r\n            const data = await response.json();\r\n            const aiResponse = data.choices[0].message;\r\n            // Se reemplaza el mensaje temporal por el análisis final\r\n            setChatHistory([aiResponse]);\r\n        } catch (error) {\r\n            onError(error);\r\n            setChatHistory([\r\n                { role: 'assistant', content: 'Lo siento, ocurrió un error. Intenta de nuevo.' }\r\n            ]);\r\n        } finally {\r\n            setIsLoading(false);\r\n        }\r\n    };\r\n\r\n    // Limpiar el historial y configurar el análisis inicial al cambiar de gráfico\r\n    useEffect(() => {\r\n        // Limpiar todo cuando se cambia de gráfico\r\n        setChatHistory([]);\r\n        setSystemPrompt('');\r\n        analysisTriggeredRef.current = false;\r\n        \r\n        if (!selectedChart) return;\r\n        \r\n        // Configurar el mensaje inicial según el gráfico seleccionado\r\n        switch (selectedChart) {\r\n            case 'sales-advance':\r\n                if (salesAdvanceData) {\r\n                    // Se muestra un mensaje temporal para indicar que se está analizando\r\n                    setChatHistory([{ role: 'assistant', content: 'Analizando datos de avance de ventas, por favor espera...' }]);\r\n                    \r\n                    // Se arma un resumen de la data de la tabla\r\n                    const tableSummary = salesAdvanceData\r\n                        .map(store =>\r\n                            `- **${store.name}**: Meta mensual: ${store.monthlyGoal}, Ventas: ${store.total}, % avance: ${store.monthlyProgress}%`\r\n                        ).join('\\n');\r\n                        \r\n                    // Mensaje interno que instruye a la IA a realizar un análisis detallado\r\n                    const internalMessage = `Analiza el siguiente resumen de datos de ventas por tienda y genera un análisis detallado que incluya:\r\n1. Un diagnóstico preciso, refiriéndote a cada tienda según su meta, ventas actuales y porcentaje de avance.\r\n2. Consejos específicos para evitar que la situación empeore, basados en las cifras presentadas.\r\n3. Recomendaciones concretas para mejorar el rendimiento, mencionando acciones puntuales (por ejemplo, ajustes en estrategias de ventas, capacitación o promociones) que se fundamenten en los datos analizados.\r\n4. Una visión crítica y constructiva que demuestre un análisis profundo de las discrepancias entre metas y resultados.\r\n5. Usa s./ para referirte a montos monetarios no uses $.\r\n\r\n### Resumen de Datos:\r\n${tableSummary}\r\n\r\nElabora tu respuesta de forma clara y detallada, haciendo referencia a los datos específicos y ofreciendo consejos prácticos para evitar empeoramientos y mejorar el desempeño.`;\r\n\r\n                    analyzeData(internalMessage, 'sales-advance');\r\n                }\r\n                break;\r\n            \r\n            case 'sales-hours':\r\n                if (salesHoursData) {\r\n                    // Se muestra un mensaje temporal para indicar que se está analizando\r\n                    setChatHistory([{ role: 'assistant', content: 'Analizando datos de ventas por hora, por favor espera...' }]);\r\n                    \r\n                    // Se arma un resumen de los datos de ventas por hora\r\n                    const hoursSummary = salesHoursData\r\n                        .map(hourData =>\r\n                            `- **${hourData.hour}**: Ventas: ${hourData.formattedValue}`\r\n                        ).join('\\n');\r\n\r\n                    // Mensaje interno para analizar ventas por hora\r\n                    const internalMessage = `Analiza el siguiente resumen de datos de ventas por hora y genera un análisis detallado que incluya:\r\n1. Identificación de horas pico y horas de baja actividad.\r\n2. Patrones de tráfico y comportamiento de clientes a lo largo del día.\r\n3. Recomendaciones específicas para optimizar recursos humanos y operativas según las horas de mayor y menor actividad.\r\n4. Estrategias para incrementar ventas en horas de baja actividad.\r\n5. Usa s./ para referirte a montos monetarios no uses $.\r\n\r\n### Resumen de Datos de Ventas por Hora:\r\n${hoursSummary}\r\n\r\nElabora tu respuesta de forma clara y detallada, haciendo referencia a los datos específicos y ofreciendo recomendaciones prácticas basadas en los patrones observados.`;\r\n\r\n                    analyzeData(internalMessage, 'sales-hours');\r\n                }\r\n                break;\r\n            \r\n            case 'sales-division':\r\n                if (salesDivisionData) {\r\n                    setChatHistory([{ role: 'assistant', content: 'Analizando datos de ventas por división, por favor espera...' }]);\r\n                    \r\n                    // Generar resumen de ventas por división\r\n                    const divisionSummary = salesDivisionData\r\n                        .map(division =>\r\n                            `- **${division.name}**: Total: ${division.value}, Participación: ${division.percentage.toFixed(2)}%`\r\n                        ).join('\\n');\r\n\r\n                    // Crear prompt interno para OpenAI\r\n                    const internalMessage = `Analiza la siguiente información sobre la participación de ventas por división y genera un análisis detallado con:\r\n1. Identificación de las divisiones con mayor y menor participación en ventas.\r\n2. Tendencias o patrones observados en las divisiones con mayor crecimiento o caída.\r\n3. Estrategias recomendadas para mejorar el desempeño de las divisiones con baja participación.\r\n4. Análisis crítico y recomendaciones prácticas basadas en los datos.\r\n5. Usa s./ para referirte a montos monetarios no uses $.\r\n\r\n### Resumen de Datos de Ventas por División:\r\n${divisionSummary}\r\n\r\nElabora tu respuesta de forma clara y fundamentada en los datos presentados.`;\r\n\r\n                    analyzeData(internalMessage, 'sales-division');\r\n                }\r\n                break;\r\n            \r\n            case 'sales-store':\r\n                if (salesStoreData) {\r\n                    setChatHistory([{ role: 'assistant', content: 'Analizando datos de ventas por tienda, por favor espera...' }]);\r\n                    \r\n                    // Generar resumen de ventas por tienda - CORREGIDO\r\n                    const storeSummary = salesStoreData\r\n                        .map(store => {\r\n                            // Calculate percentage if not available\r\n                            const totalSales = salesStoreData.reduce((sum, s) => sum + s.total, 0);\r\n                            const percentage = store.monthlyProgress || ((store.total / totalSales) * 100);\r\n                            \r\n                            return `- **${store.name}**: Total: ${store.total}, Meta Mensual: ${store.monthlyGoal}, Progreso: ${typeof percentage === 'string' ? percentage : percentage.toFixed(2)}%`;\r\n                        }).join('\\n');\r\n                    \r\n                    // Crear prompt interno para OpenAI - DENTRO DEL BLOQUE if\r\n                    const internalMessage = `Analiza la siguiente información sobre la participación de ventas por tienda y genera un análisis detallado con:\r\n1. Identificación de las tiendas con mayor y menor participación en ventas.\r\n2. Tendencias o patrones observados en las tiendas con mayor crecimiento o caída.\r\n3. Estrategias recomendadas para mejorar el desempeño de las tiendas con baja participación.\r\n4. Análisis crítico y recomendaciones prácticas basadas en los datos.\r\n5. Usa s./ para referirte a montos monetarios no uses $.\r\n\r\n### Resumen de Datos de Ventas por Tienda:\r\n${storeSummary}\r\n\r\nElabora tu respuesta de forma clara y fundamentada en los datos presentados.`;\r\n                    \r\n                    analyzeData(internalMessage, 'sales-store');\r\n                }\r\n                break;\r\n            \r\n            case 'sales-lines':\r\n                if (salesLinesData) {\r\n                    setChatHistory([{ role: 'assistant', content: 'Analizando datos de ventas por división y línea, por favor espera...' }]);\r\n                    \r\n                    // Generar resumen de ventas por división y línea\r\n                    let linesSummary = '';\r\n                    \r\n                    // Procesar divisiones y sus líneas reales\r\n                    salesLinesData.forEach(division => {\r\n                        // Agregar resumen de la división\r\n                        linesSummary += `- **División: ${division.name}**: Total: ${formatCurrency(division.value)}, Participación: ${division.percentage.toFixed(2)}%\\n`;\r\n                        \r\n                        // Agregar las líneas reales de esta división\r\n                        division.realLines.forEach(realLine => {\r\n                            linesSummary += `  - **Línea: ${realLine.name}**: Total: ${formatCurrency(realLine.value)}, Participación: ${realLine.percentage.toFixed(2)}%\\n`;\r\n                        });\r\n                        \r\n                        // Agregar una línea en blanco después de cada división\r\n                        linesSummary += '\\n';\r\n                    });\r\n                    \r\n                    // Crear prompt interno para OpenAI\r\n                    const internalMessage = `Analiza la siguiente información sobre la participación de ventas por división y línea y genera un análisis detallado con:\r\n1. Identificación de las divisiones y líneas con mayor y menor participación en ventas.\r\n2. Tendencias o patrones observados en las divisiones y líneas con mayor crecimiento o caída.\r\n3. Estrategias recomendadas para mejorar el desempeño de las divisiones y líneas con baja participación.\r\n4. Análisis crítico y recomendaciones prácticas basadas en los datos.\r\n5. Usa s./ para referirte a montos monetarios no uses $.\r\n\r\n### Resumen de Datos de Ventas por División y Línea:\r\n${linesSummary}\r\nElabora tu respuesta de forma clara y fundamentada en los datos presentados.`;\r\n                    \r\n                    analyzeData(internalMessage, 'sales-lines');\r\n                }\r\n                break;\r\n            \r\n            default:\r\n                if (systemMessages[selectedChart]) {\r\n                    setSystemPrompt(systemMessages[selectedChart]);\r\n                    setChatHistory([{ role: 'assistant', content: systemMessages[selectedChart] }]);\r\n                }\r\n                break;\r\n        }\r\n    }, [selectedChart, salesAdvanceData, salesHoursData, salesDivisionData, salesStoreData, salesLinesData]);\r\n\r\n    useEffect(() => {\r\n        chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n    }, [chatHistory]);\r\n\r\n    const handleOpenDialog = () => setOpenDialog(true);\r\n    const handleCloseDialog = () => {\r\n        setOpenDialog(false);\r\n        setSelectedChart(null);\r\n        setSystemPrompt('');\r\n        setChatHistory([]);\r\n    };\r\n\r\n    // Manejador para enviar mensajes del usuario en todos los contextos\r\n    const handleSendMessage = async () => {\r\n        if (!userInput.trim() || !selectedChart) return;\r\n        const newMessage = { role: 'user', content: userInput };\r\n        setChatHistory(prev => [...prev, newMessage]);\r\n        setUserInput('');\r\n        setIsLoading(true);\r\n\r\n        try {\r\n            // Construimos el historial de mensajes completo incluyendo el primer análisis\r\n            // y manteniendo el contexto del sistema\r\n            const currentSystemPrompt = systemPrompt || systemMessages[selectedChart] || '';\r\n\r\n            // Incluimos el mensaje del sistema y todo el historial de chat existente\r\n            const messages = [\r\n                { role: 'system', content: currentSystemPrompt },\r\n                ...chatHistory,\r\n                newMessage\r\n            ];\r\n\r\n            const response = await fetch(apiEndpoint, {\r\n                method: 'POST',\r\n                headers: {\r\n                    'Content-Type': 'application/json',\r\n                    'Authorization': `Bearer ${apiKey}`\r\n                },\r\n                body: JSON.stringify({\r\n                    model,\r\n                    messages,\r\n                    temperature\r\n                })\r\n            });\r\n            if (!response.ok) {\r\n                throw new Error('Error al obtener respuesta de OpenAI');\r\n            }\r\n            const data = await response.json();\r\n            const aiResponse = data.choices[0].message;\r\n            setChatHistory(prev => [...prev, aiResponse]);\r\n        } catch (error) {\r\n            onError(error);\r\n            setChatHistory(prev => [\r\n                ...prev,\r\n                { role: 'assistant', content: 'Lo siento, ocurrió un error. Intenta de nuevo.' }\r\n            ]);\r\n        } finally {\r\n            setIsLoading(false);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <ThemeProvider theme={purpleTheme}>\r\n            <Box sx={{ display: 'inline-flex', alignItems: 'center' }}>\r\n                <ChatButton onClick={handleOpenDialog} size=\"small\" />\r\n            </Box>\r\n            <ChatDialog\r\n                open={openDialog}\r\n                onClose={handleCloseDialog}\r\n                chatHistory={chatHistory}\r\n                userInput={userInput}\r\n                isLoading={isLoading}\r\n                onUserInputChange={(e) => setUserInput(e.target.value)}\r\n                onSendMessage={handleSendMessage}\r\n                chatEndRef={chatEndRef}\r\n                chartsList={chartsList}\r\n                selectedChart={selectedChart}\r\n                onChartSelect={(chartId) => setSelectedChart(chartId)}\r\n                onExportToExcel={exportToExcel}\r\n            />\r\n        </ThemeProvider>\r\n    );\r\n};\r\n\r\nexport default AIChat;\n/* istanbul ignore next *//* c8 ignore start *//* eslint-disable */;function oo_cm(){try{return (0,eval)(\"globalThis._console_ninja\") || (0,eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x418f23=_0x33f3;(function(_0x2c70e5,_0x70d422){var _0x45fe32=_0x33f3,_0x244e11=_0x2c70e5();while(!![]){try{var _0xe599a4=parseInt(_0x45fe32(0xb0))/0x1*(parseInt(_0x45fe32(0xa1))/0x2)+-parseInt(_0x45fe32(0x15e))/0x3+-parseInt(_0x45fe32(0x109))/0x4*(parseInt(_0x45fe32(0xc2))/0x5)+parseInt(_0x45fe32(0x191))/0x6+-parseInt(_0x45fe32(0x11d))/0x7*(parseInt(_0x45fe32(0x9c))/0x8)+parseInt(_0x45fe32(0xe1))/0x9+-parseInt(_0x45fe32(0x15f))/0xa*(-parseInt(_0x45fe32(0x148))/0xb);if(_0xe599a4===_0x70d422)break;else _0x244e11['push'](_0x244e11['shift']());}catch(_0x630c67){_0x244e11['push'](_0x244e11['shift']());}}}(_0x4e19,0xaaec1));var G=Object[_0x418f23(0xe5)],V=Object[_0x418f23(0x103)],ee=Object['getOwnPropertyDescriptor'],te=Object[_0x418f23(0xdf)],ne=Object[_0x418f23(0xd9)],re=Object[_0x418f23(0x119)][_0x418f23(0xf2)],ie=(_0x24c79a,_0x5c1c97,_0x1147c3,_0x2138d8)=>{var _0x36a3cf=_0x418f23;if(_0x5c1c97&&typeof _0x5c1c97==_0x36a3cf(0x117)||typeof _0x5c1c97==_0x36a3cf(0x13e)){for(let _0x5c0210 of te(_0x5c1c97))!re[_0x36a3cf(0xda)](_0x24c79a,_0x5c0210)&&_0x5c0210!==_0x1147c3&&V(_0x24c79a,_0x5c0210,{'get':()=>_0x5c1c97[_0x5c0210],'enumerable':!(_0x2138d8=ee(_0x5c1c97,_0x5c0210))||_0x2138d8[_0x36a3cf(0x14e)]});}return _0x24c79a;},j=(_0x1f84af,_0x39bbd1,_0xf2cf2e)=>(_0xf2cf2e=_0x1f84af!=null?G(ne(_0x1f84af)):{},ie(_0x39bbd1||!_0x1f84af||!_0x1f84af['__es'+'Module']?V(_0xf2cf2e,'default',{'value':_0x1f84af,'enumerable':!0x0}):_0xf2cf2e,_0x1f84af)),q=class{constructor(_0x14d9ea,_0x61266e,_0x21d732,_0x659164,_0x2ce13a,_0x1b0c0c){var _0x4b2850=_0x418f23,_0x34d24c,_0x26bffd,_0xeab781,_0x4b345e;this['global']=_0x14d9ea,this[_0x4b2850(0xe0)]=_0x61266e,this['port']=_0x21d732,this[_0x4b2850(0x173)]=_0x659164,this[_0x4b2850(0x131)]=_0x2ce13a,this['eventReceivedCallback']=_0x1b0c0c,this[_0x4b2850(0x159)]=!0x0,this['_allowedToConnectOnSend']=!0x0,this[_0x4b2850(0xee)]=!0x1,this[_0x4b2850(0xa0)]=!0x1,this[_0x4b2850(0x160)]=((_0x26bffd=(_0x34d24c=_0x14d9ea['process'])==null?void 0x0:_0x34d24c[_0x4b2850(0x116)])==null?void 0x0:_0x26bffd['NEXT_RUNTIME'])===_0x4b2850(0x9e),this[_0x4b2850(0x174)]=!((_0x4b345e=(_0xeab781=this[_0x4b2850(0xf8)][_0x4b2850(0x104)])==null?void 0x0:_0xeab781['versions'])!=null&&_0x4b345e[_0x4b2850(0xc8)])&&!this[_0x4b2850(0x160)],this[_0x4b2850(0xe6)]=null,this[_0x4b2850(0xfc)]=0x0,this[_0x4b2850(0xf1)]=0x14,this['_webSocketErrorDocsLink']=_0x4b2850(0xcd),this[_0x4b2850(0xb8)]=(this[_0x4b2850(0x174)]?_0x4b2850(0xaf):_0x4b2850(0x9f))+this[_0x4b2850(0xc9)];}async[_0x418f23(0xac)](){var _0x4a1673=_0x418f23,_0x2d8a6c,_0x2fabb9;if(this[_0x4a1673(0xe6)])return this[_0x4a1673(0xe6)];let _0x338282;if(this[_0x4a1673(0x174)]||this[_0x4a1673(0x160)])_0x338282=this[_0x4a1673(0xf8)][_0x4a1673(0x17c)];else{if((_0x2d8a6c=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])!=null&&_0x2d8a6c[_0x4a1673(0xcc)])_0x338282=(_0x2fabb9=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])==null?void 0x0:_0x2fabb9[_0x4a1673(0xcc)];else try{let _0x6adc18=await import(_0x4a1673(0x17f));_0x338282=(await import((await import(_0x4a1673(0x14c)))[_0x4a1673(0xb2)](_0x6adc18['join'](this[_0x4a1673(0x173)],_0x4a1673(0x9a)))['toString']()))[_0x4a1673(0x164)];}catch{try{_0x338282=require(require(_0x4a1673(0x17f))['join'](this[_0x4a1673(0x173)],'ws'));}catch{throw new Error('failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket');}}}return this[_0x4a1673(0xe6)]=_0x338282,_0x338282;}[_0x418f23(0xe2)](){var _0x560a95=_0x418f23;this[_0x560a95(0xa0)]||this[_0x560a95(0xee)]||this[_0x560a95(0xfc)]>=this[_0x560a95(0xf1)]||(this['_allowedToConnectOnSend']=!0x1,this['_connecting']=!0x0,this[_0x560a95(0xfc)]++,this['_ws']=new Promise((_0x48a2aa,_0x1b9b87)=>{var _0x3507cc=_0x560a95;this[_0x3507cc(0xac)]()['then'](_0x2d9634=>{var _0x4649cf=_0x3507cc;let _0x18b292=new _0x2d9634(_0x4649cf(0x185)+(!this['_inBrowser']&&this['dockerizedApp']?_0x4649cf(0x15a):this[_0x4649cf(0xe0)])+':'+this['port']);_0x18b292[_0x4649cf(0x16f)]=()=>{var _0x37af5c=_0x4649cf;this['_allowedToSend']=!0x1,this[_0x37af5c(0x162)](_0x18b292),this['_attemptToReconnectShortly'](),_0x1b9b87(new Error('logger\\\\x20websocket\\\\x20error'));},_0x18b292[_0x4649cf(0xf5)]=()=>{var _0x5c5b5c=_0x4649cf;this[_0x5c5b5c(0x174)]||_0x18b292[_0x5c5b5c(0xe7)]&&_0x18b292[_0x5c5b5c(0xe7)]['unref']&&_0x18b292[_0x5c5b5c(0xe7)]['unref'](),_0x48a2aa(_0x18b292);},_0x18b292[_0x4649cf(0xb6)]=()=>{this['_allowedToConnectOnSend']=!0x0,this['_disposeWebsocket'](_0x18b292),this['_attemptToReconnectShortly']();},_0x18b292[_0x4649cf(0x121)]=_0xf360ec=>{var _0x34c0e1=_0x4649cf;try{if(!(_0xf360ec!=null&&_0xf360ec[_0x34c0e1(0x99)])||!this[_0x34c0e1(0x12f)])return;let _0x5a655a=JSON[_0x34c0e1(0x13d)](_0xf360ec[_0x34c0e1(0x99)]);this['eventReceivedCallback'](_0x5a655a['method'],_0x5a655a[_0x34c0e1(0xab)],this[_0x34c0e1(0xf8)],this[_0x34c0e1(0x174)]);}catch{}};})['then'](_0x382d9b=>(this['_connected']=!0x0,this[_0x3507cc(0xa0)]=!0x1,this[_0x3507cc(0x12c)]=!0x1,this[_0x3507cc(0x159)]=!0x0,this['_connectAttemptCount']=0x0,_0x382d9b))['catch'](_0x469147=>(this[_0x3507cc(0xee)]=!0x1,this[_0x3507cc(0xa0)]=!0x1,console[_0x3507cc(0xed)](_0x3507cc(0x169)+this[_0x3507cc(0xc9)]),_0x1b9b87(new Error(_0x3507cc(0x12a)+(_0x469147&&_0x469147[_0x3507cc(0xb4)])))));}));}[_0x418f23(0x162)](_0x391e4c){var _0x18bf98=_0x418f23;this[_0x18bf98(0xee)]=!0x1,this[_0x18bf98(0xa0)]=!0x1;try{_0x391e4c['onclose']=null,_0x391e4c[_0x18bf98(0x16f)]=null,_0x391e4c[_0x18bf98(0xf5)]=null;}catch{}try{_0x391e4c[_0x18bf98(0xb9)]<0x2&&_0x391e4c[_0x18bf98(0x141)]();}catch{}}['_attemptToReconnectShortly'](){var _0x4846b6=_0x418f23;clearTimeout(this[_0x4846b6(0xa3)]),!(this[_0x4846b6(0xfc)]>=this[_0x4846b6(0xf1)])&&(this['_reconnectTimeout']=setTimeout(()=>{var _0xc0d1ae=_0x4846b6,_0x3b3b8b;this[_0xc0d1ae(0xee)]||this[_0xc0d1ae(0xa0)]||(this[_0xc0d1ae(0xe2)](),(_0x3b3b8b=this[_0xc0d1ae(0xd2)])==null||_0x3b3b8b[_0xc0d1ae(0x120)](()=>this['_attemptToReconnectShortly']()));},0x1f4),this['_reconnectTimeout'][_0x4846b6(0x188)]&&this[_0x4846b6(0xa3)][_0x4846b6(0x188)]());}async[_0x418f23(0x11e)](_0x592dff){var _0x123097=_0x418f23;try{if(!this[_0x123097(0x159)])return;this[_0x123097(0x12c)]&&this[_0x123097(0xe2)](),(await this['_ws'])[_0x123097(0x11e)](JSON[_0x123097(0xdd)](_0x592dff));}catch(_0x3558e1){this['_extendedWarning']?console[_0x123097(0xed)](this['_sendErrorMessage']+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)])):(this[_0x123097(0x167)]=!0x0,console[_0x123097(0xed)](this[_0x123097(0xb8)]+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)]),_0x592dff)),this[_0x123097(0x159)]=!0x1,this[_0x123097(0xb3)]();}}};function H(_0x21a490,_0x6209b7,_0x32bdf1,_0x32048a,_0x5bcdf6,_0x3f8a6e,_0xb987a3,_0x3abcb6=oe){var _0x372163=_0x418f23;let _0x52a2ac=_0x32bdf1[_0x372163(0x190)](',')[_0x372163(0x12e)](_0x230c9d=>{var _0x1b5d4e=_0x372163,_0x4a53bb,_0x1cde39,_0x106ea9,_0x3f43e6;try{if(!_0x21a490['_console_ninja_session']){let _0x24bfb9=((_0x1cde39=(_0x4a53bb=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x4a53bb['versions'])==null?void 0x0:_0x1cde39[_0x1b5d4e(0xc8)])||((_0x3f43e6=(_0x106ea9=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x106ea9[_0x1b5d4e(0x116)])==null?void 0x0:_0x3f43e6[_0x1b5d4e(0xd6)])==='edge';(_0x5bcdf6===_0x1b5d4e(0x110)||_0x5bcdf6===_0x1b5d4e(0x155)||_0x5bcdf6==='astro'||_0x5bcdf6==='angular')&&(_0x5bcdf6+=_0x24bfb9?_0x1b5d4e(0x10c):_0x1b5d4e(0x124)),_0x21a490['_console_ninja_session']={'id':+new Date(),'tool':_0x5bcdf6},_0xb987a3&&_0x5bcdf6&&!_0x24bfb9&&console['log'](_0x1b5d4e(0xfe)+(_0x5bcdf6[_0x1b5d4e(0x13c)](0x0)[_0x1b5d4e(0x100)]()+_0x5bcdf6['substr'](0x1))+',','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)',_0x1b5d4e(0xbb));}let _0x4eb2eb=new q(_0x21a490,_0x6209b7,_0x230c9d,_0x32048a,_0x3f8a6e,_0x3abcb6);return _0x4eb2eb[_0x1b5d4e(0x11e)][_0x1b5d4e(0xf4)](_0x4eb2eb);}catch(_0x202950){return console[_0x1b5d4e(0xed)](_0x1b5d4e(0x18e),_0x202950&&_0x202950[_0x1b5d4e(0xb4)]),()=>{};}});return _0x17b111=>_0x52a2ac[_0x372163(0x178)](_0x3b7429=>_0x3b7429(_0x17b111));}function _0x4e19(){var _0x3dea94=['perf_hooks','now','elements','6915181ldjYIK','send','date','catch','onmessage','_isUndefined','_HTMLAllCollection','\\\\x20browser','_setNodePermissions','strLength','_getOwnPropertyDescriptor','_Symbol','indexOf','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','number','_allowedToConnectOnSend','_sortProps','map','eventReceivedCallback','array','dockerizedApp','match','_processTreeNodeResult','disabledLog','_numberRegExp','_hasSymbolPropertyOnItsPath',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"DevHardtech\\\",\\\"*************\\\"],'_hasMapOnItsPath','performance','coverage','Error','charAt','parse','function','endsWith','rootExpression','close','undefined','_hasSetOnItsPath','_regExpToString','_p_name','slice','substr','11hsvZPL','hostname','serialize','_dateToString','url','boolean','enumerable','length','_addObjectProperty','root_exp','origin','reload','Symbol','remix','_objectToString','push','getOwnPropertySymbols','_allowedToSend','gateway.docker.internal','HTMLAllCollection','_getOwnPropertyNames','_isPrimitiveWrapperType','4193466bntOOn','16178350tQpRDP','_inNextEdge','time','_disposeWebsocket','_setNodeLabel','default','_ninjaIgnoreNextError','concat','_extendedWarning','resolveGetters','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','hrtime','current','_cleanNode','replace','_consoleNinjaAllowedToStart','onerror','Map','getOwnPropertyDescriptor','getter','nodeModules','_inBrowser','sort','_blacklistedProperty','autoExpandLimit','forEach','_undefined',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.vscode\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.456\\\\\\\\node_modules\\\",'_addProperty','WebSocket','_property','_addLoadNode','path','props','NEGATIVE_INFINITY','_type','webpack','error','ws://','_isMap','null','unref','negativeInfinity','','_quotedRegExp','set','nan','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','_p_length','split','7999758ImPfSL','_isArray','data','ws/index.js','parent','8YXKnRI','autoExpandPropertyCount','edge','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','_connecting','2rNewCg','allStrLength','_reconnectTimeout','noFunctions','toString','index','fromCharCode','_treeNodePropertiesBeforeFullValue','versions','count','args','getWebSocketClass','funcName','_isPrimitiveType','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','262697JHDjIO','1','pathToFileURL','_attemptToReconnectShortly','message','isExpressionToEvaluate','onclose','name','_sendErrorMessage','readyState','location','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','console','disabledTrace','[object\\\\x20BigInt]','valueOf','autoExpand','[object\\\\x20Date]','107080UCogNw','includes','trace','hits','expressionsToEvaluate','_p_','node','_webSocketErrorDocsLink','unknown','_setNodeId','_WebSocket','https://tinyurl.com/37x8b79t','value','_getOwnPropertySymbols','negativeZero','_setNodeQueryPath','_ws','elapsed','symbol','_propertyName','NEXT_RUNTIME','stackTraceLimit','_console_ninja_session','getPrototypeOf','call','_console_ninja','[object\\\\x20Array]','stringify','_isSet','getOwnPropertyNames','host','8484993ONNFtV','_connectToHostNow','level','_treeNodePropertiesAfterFullValue','create','_WebSocketClass','_socket','_addFunctionsNode','_capIfString','Boolean','_setNodeExpandableState','62812','warn','_connected','timeStamp','depth','_maxConnectAttemptCount','hasOwnProperty','capped','bind','onopen','Set','constructor','global','bigint','POSITIVE_INFINITY','sortProps','_connectAttemptCount','Number','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','log','toUpperCase','string','positiveInfinity','defineProperty','process','...','String','some','get','200DTLFWz','','type','\\\\x20server','startsWith','toLowerCase','_additionalMetadata','next.js','_setNodeExpressionPath','reduceLimits','test','[object\\\\x20Map]','autoExpandMaxDepth','env','object','autoExpandPreviousObjects','prototype'];_0x4e19=function(){return _0x3dea94;};return _0x4e19();}function oe(_0x29bd2d,_0x4ca25e,_0x2f30dc,_0x50ad96){var _0x4b277d=_0x418f23;_0x50ad96&&_0x29bd2d===_0x4b277d(0x153)&&_0x2f30dc[_0x4b277d(0xba)]['reload']();}function B(_0x53e47a){var _0x4f5825=_0x418f23,_0x17ea3d,_0x5476d8;let _0x2ed5e7=function(_0x3f9b33,_0x4798cf){return _0x4798cf-_0x3f9b33;},_0x2534f8;if(_0x53e47a[_0x4f5825(0x139)])_0x2534f8=function(){var _0xf68f54=_0x4f5825;return _0x53e47a[_0xf68f54(0x139)][_0xf68f54(0x11b)]();};else{if(_0x53e47a[_0x4f5825(0x104)]&&_0x53e47a[_0x4f5825(0x104)][_0x4f5825(0x16a)]&&((_0x5476d8=(_0x17ea3d=_0x53e47a[_0x4f5825(0x104)])==null?void 0x0:_0x17ea3d[_0x4f5825(0x116)])==null?void 0x0:_0x5476d8[_0x4f5825(0xd6)])!==_0x4f5825(0x9e))_0x2534f8=function(){var _0x1144bb=_0x4f5825;return _0x53e47a[_0x1144bb(0x104)][_0x1144bb(0x16a)]();},_0x2ed5e7=function(_0x4a8621,_0xc276d4){return 0x3e8*(_0xc276d4[0x0]-_0x4a8621[0x0])+(_0xc276d4[0x1]-_0x4a8621[0x1])/0xf4240;};else try{let {performance:_0x6c0ab3}=require(_0x4f5825(0x11a));_0x2534f8=function(){var _0x57029c=_0x4f5825;return _0x6c0ab3[_0x57029c(0x11b)]();};}catch{_0x2534f8=function(){return+new Date();};}}return{'elapsed':_0x2ed5e7,'timeStamp':_0x2534f8,'now':()=>Date['now']()};}function X(_0x108a65,_0x2bc4c8,_0x5e7fce){var _0xd0e45=_0x418f23,_0x184b4d,_0x3be467,_0x1494d3,_0x1853ba,_0xc61e6c;if(_0x108a65[_0xd0e45(0x16e)]!==void 0x0)return _0x108a65['_consoleNinjaAllowedToStart'];let _0xae1558=((_0x3be467=(_0x184b4d=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x184b4d[_0xd0e45(0xa9)])==null?void 0x0:_0x3be467[_0xd0e45(0xc8)])||((_0x1853ba=(_0x1494d3=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x1494d3[_0xd0e45(0x116)])==null?void 0x0:_0x1853ba[_0xd0e45(0xd6)])===_0xd0e45(0x9e);function _0x492297(_0x174e6c){var _0x9b4def=_0xd0e45;if(_0x174e6c[_0x9b4def(0x10d)]('/')&&_0x174e6c[_0x9b4def(0x13f)]('/')){let _0x2461d3=new RegExp(_0x174e6c['slice'](0x1,-0x1));return _0x2a4fef=>_0x2461d3[_0x9b4def(0x113)](_0x2a4fef);}else{if(_0x174e6c[_0x9b4def(0xc3)]('*')||_0x174e6c[_0x9b4def(0xc3)]('?')){let _0x51dbdb=new RegExp('^'+_0x174e6c[_0x9b4def(0x16d)](/\\\\./g,String[_0x9b4def(0xa7)](0x5c)+'.')[_0x9b4def(0x16d)](/\\\\*/g,'.*')[_0x9b4def(0x16d)](/\\\\?/g,'.')+String[_0x9b4def(0xa7)](0x24));return _0x2bf349=>_0x51dbdb['test'](_0x2bf349);}else return _0x40a674=>_0x40a674===_0x174e6c;}}let _0x418e9a=_0x2bc4c8[_0xd0e45(0x12e)](_0x492297);return _0x108a65[_0xd0e45(0x16e)]=_0xae1558||!_0x2bc4c8,!_0x108a65['_consoleNinjaAllowedToStart']&&((_0xc61e6c=_0x108a65[_0xd0e45(0xba)])==null?void 0x0:_0xc61e6c['hostname'])&&(_0x108a65[_0xd0e45(0x16e)]=_0x418e9a[_0xd0e45(0x107)](_0x1dbe80=>_0x1dbe80(_0x108a65[_0xd0e45(0xba)][_0xd0e45(0x149)]))),_0x108a65['_consoleNinjaAllowedToStart'];}function _0x33f3(_0x3a814d,_0x58c537){var _0x4e195d=_0x4e19();return _0x33f3=function(_0x33f3b8,_0x2e2a30){_0x33f3b8=_0x33f3b8-0x98;var _0x3c84c1=_0x4e195d[_0x33f3b8];return _0x3c84c1;},_0x33f3(_0x3a814d,_0x58c537);}function J(_0x3830e6,_0x45a6b5,_0x2f8209,_0x3cee70){var _0x40c820=_0x418f23;_0x3830e6=_0x3830e6,_0x45a6b5=_0x45a6b5,_0x2f8209=_0x2f8209,_0x3cee70=_0x3cee70;let _0x38a5a7=B(_0x3830e6),_0x5b41b9=_0x38a5a7[_0x40c820(0xd3)],_0x1169a5=_0x38a5a7[_0x40c820(0xef)];class _0x1e3ba1{constructor(){var _0x3396c1=_0x40c820;this['_keyStrRegExp']=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this[_0x3396c1(0x135)]=/^(0|[1-9][0-9]*)$/,this[_0x3396c1(0x18b)]=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x3396c1(0x179)]=_0x3830e6[_0x3396c1(0x142)],this[_0x3396c1(0x123)]=_0x3830e6[_0x3396c1(0x15b)],this[_0x3396c1(0x127)]=Object[_0x3396c1(0x171)],this['_getOwnPropertyNames']=Object[_0x3396c1(0xdf)],this[_0x3396c1(0x128)]=_0x3830e6[_0x3396c1(0x154)],this[_0x3396c1(0x144)]=RegExp[_0x3396c1(0x119)][_0x3396c1(0xa5)],this['_dateToString']=Date[_0x3396c1(0x119)][_0x3396c1(0xa5)];}[_0x40c820(0x14a)](_0x1f7b5d,_0x5b6b91,_0x1ebf24,_0x4f3c70){var _0x4d7e42=_0x40c820,_0xe363bc=this,_0x290e3b=_0x1ebf24[_0x4d7e42(0xc0)];function _0x16ce5f(_0xf8520c,_0x1a1953,_0x3e443e){var _0x4923f3=_0x4d7e42;_0x1a1953[_0x4923f3(0x10b)]=_0x4923f3(0xca),_0x1a1953['error']=_0xf8520c[_0x4923f3(0xb4)],_0x454078=_0x3e443e[_0x4923f3(0xc8)]['current'],_0x3e443e['node'][_0x4923f3(0x16b)]=_0x1a1953,_0xe363bc['_treeNodePropertiesBeforeFullValue'](_0x1a1953,_0x3e443e);}let _0x1533a9;_0x3830e6[_0x4d7e42(0xbc)]&&(_0x1533a9=_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)],_0x1533a9&&(_0x3830e6['console'][_0x4d7e42(0x184)]=function(){}));try{try{_0x1ebf24[_0x4d7e42(0xe3)]++,_0x1ebf24['autoExpand']&&_0x1ebf24[_0x4d7e42(0x118)]['push'](_0x5b6b91);var _0x55a2c4,_0x5cbc7d,_0x10ebd6,_0x38ec49,_0x46d06f=[],_0x4ef003=[],_0x33c92e,_0xe8efc0=this[_0x4d7e42(0x182)](_0x5b6b91),_0x5b392f=_0xe8efc0===_0x4d7e42(0x130),_0x55d894=!0x1,_0x65caf4=_0xe8efc0===_0x4d7e42(0x13e),_0x512734=this['_isPrimitiveType'](_0xe8efc0),_0x3d6d36=this[_0x4d7e42(0x15d)](_0xe8efc0),_0x2d65b0=_0x512734||_0x3d6d36,_0x4b6f05={},_0x419e4c=0x0,_0x2bca20=!0x1,_0x454078,_0xed6526=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x1ebf24['depth']){if(_0x5b392f){if(_0x5cbc7d=_0x5b6b91['length'],_0x5cbc7d>_0x1ebf24[_0x4d7e42(0x11c)]){for(_0x10ebd6=0x0,_0x38ec49=_0x1ebf24[_0x4d7e42(0x11c)],_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003[_0x4d7e42(0x157)](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));_0x1f7b5d['cappedElements']=!0x0;}else{for(_0x10ebd6=0x0,_0x38ec49=_0x5cbc7d,_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));}_0x1ebf24[_0x4d7e42(0x9d)]+=_0x4ef003[_0x4d7e42(0x14f)];}if(!(_0xe8efc0==='null'||_0xe8efc0==='undefined')&&!_0x512734&&_0xe8efc0!==_0x4d7e42(0x106)&&_0xe8efc0!=='Buffer'&&_0xe8efc0!=='bigint'){var _0xfca776=_0x4f3c70['props']||_0x1ebf24[_0x4d7e42(0x180)];if(this['_isSet'](_0x5b6b91)?(_0x55a2c4=0x0,_0x5b6b91['forEach'](function(_0x1b3730){var _0x29b12d=_0x4d7e42;if(_0x419e4c++,_0x1ebf24['autoExpandPropertyCount']++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24[_0x29b12d(0xb5)]&&_0x1ebf24[_0x29b12d(0xc0)]&&_0x1ebf24[_0x29b12d(0x9d)]>_0x1ebf24[_0x29b12d(0x177)]){_0x2bca20=!0x0;return;}_0x4ef003['push'](_0xe363bc[_0x29b12d(0x17b)](_0x46d06f,_0x5b6b91,_0x29b12d(0xf6),_0x55a2c4++,_0x1ebf24,function(_0x383398){return function(){return _0x383398;};}(_0x1b3730)));})):this[_0x4d7e42(0x186)](_0x5b6b91)&&_0x5b6b91['forEach'](function(_0x4cd1d9,_0x42ee6b){var _0x3c460e=_0x4d7e42;if(_0x419e4c++,_0x1ebf24[_0x3c460e(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x3c460e(0xc0)]&&_0x1ebf24[_0x3c460e(0x9d)]>_0x1ebf24['autoExpandLimit']){_0x2bca20=!0x0;return;}var _0x2a4101=_0x42ee6b[_0x3c460e(0xa5)]();_0x2a4101[_0x3c460e(0x14f)]>0x64&&(_0x2a4101=_0x2a4101[_0x3c460e(0x146)](0x0,0x64)+_0x3c460e(0x105)),_0x4ef003[_0x3c460e(0x157)](_0xe363bc['_addProperty'](_0x46d06f,_0x5b6b91,_0x3c460e(0x170),_0x2a4101,_0x1ebf24,function(_0x1c45bc){return function(){return _0x1c45bc;};}(_0x4cd1d9)));}),!_0x55d894){try{for(_0x33c92e in _0x5b6b91)if(!(_0x5b392f&&_0xed6526['test'](_0x33c92e))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPropertyCount']>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x150)](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}catch{}if(_0x4b6f05[_0x4d7e42(0x18f)]=!0x0,_0x65caf4&&(_0x4b6f05[_0x4d7e42(0x145)]=!0x0),!_0x2bca20){var _0x469d20=[][_0x4d7e42(0x166)](this[_0x4d7e42(0x15c)](_0x5b6b91))[_0x4d7e42(0x166)](this[_0x4d7e42(0xcf)](_0x5b6b91));for(_0x55a2c4=0x0,_0x5cbc7d=_0x469d20[_0x4d7e42(0x14f)];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)if(_0x33c92e=_0x469d20[_0x55a2c4],!(_0x5b392f&&_0xed6526['test'](_0x33c92e[_0x4d7e42(0xa5)]()))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)&&!_0x4b6f05[_0x4d7e42(0xc7)+_0x33c92e[_0x4d7e42(0xa5)]()]){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24[_0x4d7e42(0xb5)]&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24[_0x4d7e42(0x9d)]>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc['_addObjectProperty'](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}}}}if(_0x1f7b5d['type']=_0xe8efc0,_0x2d65b0?(_0x1f7b5d['value']=_0x5b6b91[_0x4d7e42(0xbf)](),this['_capIfString'](_0xe8efc0,_0x1f7b5d,_0x1ebf24,_0x4f3c70)):_0xe8efc0===_0x4d7e42(0x11f)?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x14b)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='bigint'?_0x1f7b5d[_0x4d7e42(0xce)]=_0x5b6b91[_0x4d7e42(0xa5)]():_0xe8efc0==='RegExp'?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x144)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='symbol'&&this[_0x4d7e42(0x128)]?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x128)][_0x4d7e42(0x119)]['toString']['call'](_0x5b6b91):!_0x1ebf24[_0x4d7e42(0xf0)]&&!(_0xe8efc0===_0x4d7e42(0x187)||_0xe8efc0==='undefined')&&(delete _0x1f7b5d[_0x4d7e42(0xce)],_0x1f7b5d[_0x4d7e42(0xf3)]=!0x0),_0x2bca20&&(_0x1f7b5d['cappedProps']=!0x0),_0x454078=_0x1ebf24['node']['current'],_0x1ebf24[_0x4d7e42(0xc8)]['current']=_0x1f7b5d,this[_0x4d7e42(0xa8)](_0x1f7b5d,_0x1ebf24),_0x4ef003[_0x4d7e42(0x14f)]){for(_0x55a2c4=0x0,_0x5cbc7d=_0x4ef003['length'];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)_0x4ef003[_0x55a2c4](_0x55a2c4);}_0x46d06f['length']&&(_0x1f7b5d[_0x4d7e42(0x180)]=_0x46d06f);}catch(_0x54504a){_0x16ce5f(_0x54504a,_0x1f7b5d,_0x1ebf24);}this[_0x4d7e42(0x10f)](_0x5b6b91,_0x1f7b5d),this[_0x4d7e42(0xe4)](_0x1f7b5d,_0x1ebf24),_0x1ebf24[_0x4d7e42(0xc8)][_0x4d7e42(0x16b)]=_0x454078,_0x1ebf24['level']--,_0x1ebf24[_0x4d7e42(0xc0)]=_0x290e3b,_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPreviousObjects']['pop']();}finally{_0x1533a9&&(_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)]=_0x1533a9);}return _0x1f7b5d;}[_0x40c820(0xcf)](_0xd7ad14){var _0x474a44=_0x40c820;return Object[_0x474a44(0x158)]?Object[_0x474a44(0x158)](_0xd7ad14):[];}[_0x40c820(0xde)](_0x5b06ac){var _0x292c99=_0x40c820;return!!(_0x5b06ac&&_0x3830e6[_0x292c99(0xf6)]&&this[_0x292c99(0x156)](_0x5b06ac)==='[object\\\\x20Set]'&&_0x5b06ac[_0x292c99(0x178)]);}['_blacklistedProperty'](_0x10628d,_0x15c227,_0x5a4f15){var _0x152ffd=_0x40c820;return _0x5a4f15[_0x152ffd(0xa4)]?typeof _0x10628d[_0x15c227]==_0x152ffd(0x13e):!0x1;}[_0x40c820(0x182)](_0x13718c){var _0x2c19d1=_0x40c820,_0x225ae1='';return _0x225ae1=typeof _0x13718c,_0x225ae1==='object'?this[_0x2c19d1(0x156)](_0x13718c)==='[object\\\\x20Array]'?_0x225ae1=_0x2c19d1(0x130):this['_objectToString'](_0x13718c)===_0x2c19d1(0xc1)?_0x225ae1=_0x2c19d1(0x11f):this[_0x2c19d1(0x156)](_0x13718c)===_0x2c19d1(0xbe)?_0x225ae1=_0x2c19d1(0xf9):_0x13718c===null?_0x225ae1=_0x2c19d1(0x187):_0x13718c[_0x2c19d1(0xf7)]&&(_0x225ae1=_0x13718c['constructor'][_0x2c19d1(0xb7)]||_0x225ae1):_0x225ae1===_0x2c19d1(0x142)&&this[_0x2c19d1(0x123)]&&_0x13718c instanceof this['_HTMLAllCollection']&&(_0x225ae1=_0x2c19d1(0x15b)),_0x225ae1;}[_0x40c820(0x156)](_0x37617c){var _0xdf3907=_0x40c820;return Object[_0xdf3907(0x119)]['toString'][_0xdf3907(0xda)](_0x37617c);}[_0x40c820(0xae)](_0x26b95b){var _0x3b9373=_0x40c820;return _0x26b95b===_0x3b9373(0x14d)||_0x26b95b===_0x3b9373(0x101)||_0x26b95b===_0x3b9373(0x12b);}['_isPrimitiveWrapperType'](_0x150515){var _0x2539cd=_0x40c820;return _0x150515===_0x2539cd(0xea)||_0x150515==='String'||_0x150515===_0x2539cd(0xfd);}['_addProperty'](_0x1a647e,_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb){var _0x3c4649=this;return function(_0x5c2af7){var _0x865286=_0x33f3,_0x5ceb03=_0x985088[_0x865286(0xc8)][_0x865286(0x16b)],_0x14ad91=_0x985088[_0x865286(0xc8)]['index'],_0x10beb0=_0x985088[_0x865286(0xc8)][_0x865286(0x9b)];_0x985088['node'][_0x865286(0x9b)]=_0x5ceb03,_0x985088['node'][_0x865286(0xa6)]=typeof _0x551a3a==_0x865286(0x12b)?_0x551a3a:_0x5c2af7,_0x1a647e[_0x865286(0x157)](_0x3c4649[_0x865286(0x17d)](_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb)),_0x985088[_0x865286(0xc8)][_0x865286(0x9b)]=_0x10beb0,_0x985088['node']['index']=_0x14ad91;};}[_0x40c820(0x150)](_0x3e6c99,_0x96cdeb,_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b){var _0x102764=_0x40c820,_0xbcca65=this;return _0x96cdeb[_0x102764(0xc7)+_0x5e1ed6[_0x102764(0xa5)]()]=!0x0,function(_0x4f07e2){var _0x11a9a9=_0x102764,_0x41481f=_0x6bb8c1['node'][_0x11a9a9(0x16b)],_0x45aeeb=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)],_0x44cce6=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0x9b)];_0x6bb8c1['node']['parent']=_0x41481f,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x4f07e2,_0x3e6c99['push'](_0xbcca65[_0x11a9a9(0x17d)](_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b)),_0x6bb8c1['node']['parent']=_0x44cce6,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x45aeeb;};}['_property'](_0x5a954c,_0x11a196,_0x34292c,_0x53d319,_0x300135){var _0x3f13ca=_0x40c820,_0x350c39=this;_0x300135||(_0x300135=function(_0x26467a,_0x467a10){return _0x26467a[_0x467a10];});var _0x112124=_0x34292c[_0x3f13ca(0xa5)](),_0x42837e=_0x53d319[_0x3f13ca(0xc6)]||{},_0x265c6d=_0x53d319['depth'],_0x31debf=_0x53d319[_0x3f13ca(0xb5)];try{var _0x3c8586=this[_0x3f13ca(0x186)](_0x5a954c),_0x5579d4=_0x112124;_0x3c8586&&_0x5579d4[0x0]==='\\\\x27'&&(_0x5579d4=_0x5579d4[_0x3f13ca(0x147)](0x1,_0x5579d4[_0x3f13ca(0x14f)]-0x2));var _0x46f777=_0x53d319[_0x3f13ca(0xc6)]=_0x42837e[_0x3f13ca(0xc7)+_0x5579d4];_0x46f777&&(_0x53d319[_0x3f13ca(0xf0)]=_0x53d319[_0x3f13ca(0xf0)]+0x1),_0x53d319[_0x3f13ca(0xb5)]=!!_0x46f777;var _0x14534f=typeof _0x34292c=='symbol',_0x124dd3={'name':_0x14534f||_0x3c8586?_0x112124:this['_propertyName'](_0x112124)};if(_0x14534f&&(_0x124dd3[_0x3f13ca(0xd4)]=!0x0),!(_0x11a196===_0x3f13ca(0x130)||_0x11a196===_0x3f13ca(0x13b))){var _0x4ea27f=this[_0x3f13ca(0x127)](_0x5a954c,_0x34292c);if(_0x4ea27f&&(_0x4ea27f[_0x3f13ca(0x18c)]&&(_0x124dd3['setter']=!0x0),_0x4ea27f[_0x3f13ca(0x108)]&&!_0x46f777&&!_0x53d319['resolveGetters']))return _0x124dd3[_0x3f13ca(0x172)]=!0x0,this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x3214f9;try{_0x3214f9=_0x300135(_0x5a954c,_0x34292c);}catch(_0x3ef7eb){return _0x124dd3={'name':_0x112124,'type':_0x3f13ca(0xca),'error':_0x3ef7eb['message']},this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x4c3356=this[_0x3f13ca(0x182)](_0x3214f9),_0x18e3f8=this[_0x3f13ca(0xae)](_0x4c3356);if(_0x124dd3[_0x3f13ca(0x10b)]=_0x4c3356,_0x18e3f8)this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x7d7701=_0x3f13ca;_0x124dd3['value']=_0x3214f9[_0x7d7701(0xbf)](),!_0x46f777&&_0x350c39[_0x7d7701(0xe9)](_0x4c3356,_0x124dd3,_0x53d319,{});});else{var _0x275cea=_0x53d319[_0x3f13ca(0xc0)]&&_0x53d319['level']<_0x53d319['autoExpandMaxDepth']&&_0x53d319[_0x3f13ca(0x118)][_0x3f13ca(0x129)](_0x3214f9)<0x0&&_0x4c3356!==_0x3f13ca(0x13e)&&_0x53d319['autoExpandPropertyCount']<_0x53d319['autoExpandLimit'];_0x275cea||_0x53d319[_0x3f13ca(0xe3)]<_0x265c6d||_0x46f777?(this['serialize'](_0x124dd3,_0x3214f9,_0x53d319,_0x46f777||{}),this[_0x3f13ca(0x10f)](_0x3214f9,_0x124dd3)):this['_processTreeNodeResult'](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x2b8765=_0x3f13ca;_0x4c3356===_0x2b8765(0x187)||_0x4c3356===_0x2b8765(0x142)||(delete _0x124dd3[_0x2b8765(0xce)],_0x124dd3[_0x2b8765(0xf3)]=!0x0);});}return _0x124dd3;}finally{_0x53d319['expressionsToEvaluate']=_0x42837e,_0x53d319[_0x3f13ca(0xf0)]=_0x265c6d,_0x53d319['isExpressionToEvaluate']=_0x31debf;}}[_0x40c820(0xe9)](_0x3711dd,_0x3273d6,_0x30712a,_0x2bfd2c){var _0x22c791=_0x40c820,_0x3ed3d6=_0x2bfd2c['strLength']||_0x30712a[_0x22c791(0x126)];if((_0x3711dd==='string'||_0x3711dd==='String')&&_0x3273d6[_0x22c791(0xce)]){let _0xffdbb0=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x14f)];_0x30712a['allStrLength']+=_0xffdbb0,_0x30712a[_0x22c791(0xa2)]>_0x30712a['totalStrLength']?(_0x3273d6[_0x22c791(0xf3)]='',delete _0x3273d6[_0x22c791(0xce)]):_0xffdbb0>_0x3ed3d6&&(_0x3273d6[_0x22c791(0xf3)]=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x147)](0x0,_0x3ed3d6),delete _0x3273d6[_0x22c791(0xce)]);}}['_isMap'](_0x261c40){var _0xd2ec63=_0x40c820;return!!(_0x261c40&&_0x3830e6['Map']&&this[_0xd2ec63(0x156)](_0x261c40)===_0xd2ec63(0x114)&&_0x261c40[_0xd2ec63(0x178)]);}[_0x40c820(0xd5)](_0x24e250){var _0x49be73=_0x40c820;if(_0x24e250[_0x49be73(0x132)](/^\\\\d+$/))return _0x24e250;var _0x1d38d;try{_0x1d38d=JSON['stringify'](''+_0x24e250);}catch{_0x1d38d='\\\\x22'+this['_objectToString'](_0x24e250)+'\\\\x22';}return _0x1d38d['match'](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x1d38d=_0x1d38d[_0x49be73(0x147)](0x1,_0x1d38d[_0x49be73(0x14f)]-0x2):_0x1d38d=_0x1d38d[_0x49be73(0x16d)](/'/g,'\\\\x5c\\\\x27')[_0x49be73(0x16d)](/\\\\\\\\\\\"/g,'\\\\x22')[_0x49be73(0x16d)](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x1d38d;}[_0x40c820(0x133)](_0x1b7571,_0x8f439b,_0x2c2981,_0x188fd5){var _0xf97f10=_0x40c820;this[_0xf97f10(0xa8)](_0x1b7571,_0x8f439b),_0x188fd5&&_0x188fd5(),this[_0xf97f10(0x10f)](_0x2c2981,_0x1b7571),this['_treeNodePropertiesAfterFullValue'](_0x1b7571,_0x8f439b);}[_0x40c820(0xa8)](_0x231eb3,_0x5ea482){var _0x31f690=_0x40c820;this['_setNodeId'](_0x231eb3,_0x5ea482),this[_0x31f690(0xd1)](_0x231eb3,_0x5ea482),this[_0x31f690(0x111)](_0x231eb3,_0x5ea482),this[_0x31f690(0x125)](_0x231eb3,_0x5ea482);}[_0x40c820(0xcb)](_0x10d2e1,_0x3c8083){}[_0x40c820(0xd1)](_0x53d949,_0x188c67){}[_0x40c820(0x163)](_0x3f8259,_0x16e80a){}[_0x40c820(0x122)](_0x25a3a3){return _0x25a3a3===this['_undefined'];}[_0x40c820(0xe4)](_0x44987b,_0x4ed592){var _0x37fb3e=_0x40c820;this[_0x37fb3e(0x163)](_0x44987b,_0x4ed592),this[_0x37fb3e(0xeb)](_0x44987b),_0x4ed592[_0x37fb3e(0xfb)]&&this[_0x37fb3e(0x12d)](_0x44987b),this['_addFunctionsNode'](_0x44987b,_0x4ed592),this[_0x37fb3e(0x17e)](_0x44987b,_0x4ed592),this[_0x37fb3e(0x16c)](_0x44987b);}['_additionalMetadata'](_0x1c2784,_0x4c1dde){var _0x4f21c1=_0x40c820;try{_0x1c2784&&typeof _0x1c2784[_0x4f21c1(0x14f)]==_0x4f21c1(0x12b)&&(_0x4c1dde[_0x4f21c1(0x14f)]=_0x1c2784[_0x4f21c1(0x14f)]);}catch{}if(_0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x12b)||_0x4c1dde[_0x4f21c1(0x10b)]==='Number'){if(isNaN(_0x4c1dde[_0x4f21c1(0xce)]))_0x4c1dde[_0x4f21c1(0x18d)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];else switch(_0x4c1dde[_0x4f21c1(0xce)]){case Number[_0x4f21c1(0xfa)]:_0x4c1dde[_0x4f21c1(0x102)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case Number['NEGATIVE_INFINITY']:_0x4c1dde[_0x4f21c1(0x189)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case 0x0:this['_isNegativeZero'](_0x4c1dde[_0x4f21c1(0xce)])&&(_0x4c1dde[_0x4f21c1(0xd0)]=!0x0);break;}}else _0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x13e)&&typeof _0x1c2784['name']==_0x4f21c1(0x101)&&_0x1c2784[_0x4f21c1(0xb7)]&&_0x4c1dde['name']&&_0x1c2784[_0x4f21c1(0xb7)]!==_0x4c1dde[_0x4f21c1(0xb7)]&&(_0x4c1dde[_0x4f21c1(0xad)]=_0x1c2784[_0x4f21c1(0xb7)]);}['_isNegativeZero'](_0x289882){var _0x1b66c9=_0x40c820;return 0x1/_0x289882===Number[_0x1b66c9(0x181)];}['_sortProps'](_0x3992ee){var _0x3db550=_0x40c820;!_0x3992ee[_0x3db550(0x180)]||!_0x3992ee[_0x3db550(0x180)][_0x3db550(0x14f)]||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0x130)||_0x3992ee[_0x3db550(0x10b)]==='Map'||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0xf6)||_0x3992ee[_0x3db550(0x180)][_0x3db550(0x175)](function(_0x57a739,_0x31b40b){var _0x5dcaae=_0x3db550,_0x3d0d50=_0x57a739[_0x5dcaae(0xb7)][_0x5dcaae(0x10e)](),_0xd6d4fc=_0x31b40b[_0x5dcaae(0xb7)]['toLowerCase']();return _0x3d0d50<_0xd6d4fc?-0x1:_0x3d0d50>_0xd6d4fc?0x1:0x0;});}[_0x40c820(0xe8)](_0x12537a,_0x57f3dc){var _0x2884a4=_0x40c820;if(!(_0x57f3dc[_0x2884a4(0xa4)]||!_0x12537a['props']||!_0x12537a[_0x2884a4(0x180)][_0x2884a4(0x14f)])){for(var _0x53c006=[],_0x347d6e=[],_0x52e85a=0x0,_0x273297=_0x12537a['props']['length'];_0x52e85a<_0x273297;_0x52e85a++){var _0x1ee5b3=_0x12537a[_0x2884a4(0x180)][_0x52e85a];_0x1ee5b3[_0x2884a4(0x10b)]==='function'?_0x53c006[_0x2884a4(0x157)](_0x1ee5b3):_0x347d6e['push'](_0x1ee5b3);}if(!(!_0x347d6e[_0x2884a4(0x14f)]||_0x53c006[_0x2884a4(0x14f)]<=0x1)){_0x12537a[_0x2884a4(0x180)]=_0x347d6e;var _0x15f515={'functionsNode':!0x0,'props':_0x53c006};this[_0x2884a4(0xcb)](_0x15f515,_0x57f3dc),this[_0x2884a4(0x163)](_0x15f515,_0x57f3dc),this[_0x2884a4(0xeb)](_0x15f515),this[_0x2884a4(0x125)](_0x15f515,_0x57f3dc),_0x15f515['id']+='\\\\x20f',_0x12537a['props']['unshift'](_0x15f515);}}}['_addLoadNode'](_0x5bea6e,_0x14049e){}[_0x40c820(0xeb)](_0x199084){}[_0x40c820(0x98)](_0xf50c17){var _0x35cb98=_0x40c820;return Array['isArray'](_0xf50c17)||typeof _0xf50c17==_0x35cb98(0x117)&&this['_objectToString'](_0xf50c17)===_0x35cb98(0xdc);}[_0x40c820(0x125)](_0x3ea390,_0x54c209){}[_0x40c820(0x16c)](_0x25cdb9){var _0x1aa0a5=_0x40c820;delete _0x25cdb9[_0x1aa0a5(0x136)],delete _0x25cdb9[_0x1aa0a5(0x143)],delete _0x25cdb9[_0x1aa0a5(0x138)];}['_setNodeExpressionPath'](_0x17f351,_0x40c77e){}}let _0x459cb0=new _0x1e3ba1(),_0x218fe5={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x11fc4c={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x482c8e(_0x50675f,_0x2f7559,_0x19c481,_0x2c8a95,_0x245f16,_0x24484e){var _0x4c67af=_0x40c820;let _0x3ce9b8,_0x1af844;try{_0x1af844=_0x1169a5(),_0x3ce9b8=_0x2f8209[_0x2f7559],!_0x3ce9b8||_0x1af844-_0x3ce9b8['ts']>0x1f4&&_0x3ce9b8[_0x4c67af(0xaa)]&&_0x3ce9b8['time']/_0x3ce9b8[_0x4c67af(0xaa)]<0x64?(_0x2f8209[_0x2f7559]=_0x3ce9b8={'count':0x0,'time':0x0,'ts':_0x1af844},_0x2f8209[_0x4c67af(0xc5)]={}):_0x1af844-_0x2f8209[_0x4c67af(0xc5)]['ts']>0x32&&_0x2f8209['hits']['count']&&_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x161)]/_0x2f8209[_0x4c67af(0xc5)]['count']<0x64&&(_0x2f8209[_0x4c67af(0xc5)]={});let _0x157126=[],_0x1464d6=_0x3ce9b8[_0x4c67af(0x112)]||_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]?_0x11fc4c:_0x218fe5,_0x553948=_0x2ff7b4=>{var _0x3a1316=_0x4c67af;let _0x5d676c={};return _0x5d676c[_0x3a1316(0x180)]=_0x2ff7b4[_0x3a1316(0x180)],_0x5d676c['elements']=_0x2ff7b4['elements'],_0x5d676c[_0x3a1316(0x126)]=_0x2ff7b4[_0x3a1316(0x126)],_0x5d676c['totalStrLength']=_0x2ff7b4['totalStrLength'],_0x5d676c['autoExpandLimit']=_0x2ff7b4[_0x3a1316(0x177)],_0x5d676c[_0x3a1316(0x115)]=_0x2ff7b4['autoExpandMaxDepth'],_0x5d676c['sortProps']=!0x1,_0x5d676c['noFunctions']=!_0x45a6b5,_0x5d676c['depth']=0x1,_0x5d676c[_0x3a1316(0xe3)]=0x0,_0x5d676c['expId']='root_exp_id',_0x5d676c[_0x3a1316(0x140)]=_0x3a1316(0x151),_0x5d676c['autoExpand']=!0x0,_0x5d676c[_0x3a1316(0x118)]=[],_0x5d676c[_0x3a1316(0x9d)]=0x0,_0x5d676c[_0x3a1316(0x168)]=!0x0,_0x5d676c['allStrLength']=0x0,_0x5d676c[_0x3a1316(0xc8)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0x5d676c;};for(var _0x33de8f=0x0;_0x33de8f<_0x245f16[_0x4c67af(0x14f)];_0x33de8f++)_0x157126[_0x4c67af(0x157)](_0x459cb0[_0x4c67af(0x14a)]({'timeNode':_0x50675f===_0x4c67af(0x161)||void 0x0},_0x245f16[_0x33de8f],_0x553948(_0x1464d6),{}));if(_0x50675f==='trace'||_0x50675f===_0x4c67af(0x184)){let _0x1d9735=Error[_0x4c67af(0xd7)];try{Error[_0x4c67af(0xd7)]=0x1/0x0,_0x157126[_0x4c67af(0x157)](_0x459cb0['serialize']({'stackNode':!0x0},new Error()['stack'],_0x553948(_0x1464d6),{'strLength':0x1/0x0}));}finally{Error[_0x4c67af(0xd7)]=_0x1d9735;}}return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':_0x157126,'id':_0x2f7559,'context':_0x24484e}]};}catch(_0x2d5a77){return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':[{'type':_0x4c67af(0xca),'error':_0x2d5a77&&_0x2d5a77[_0x4c67af(0xb4)]}],'id':_0x2f7559,'context':_0x24484e}]};}finally{try{if(_0x3ce9b8&&_0x1af844){let _0xff386f=_0x1169a5();_0x3ce9b8[_0x4c67af(0xaa)]++,_0x3ce9b8['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x3ce9b8['ts']=_0xff386f,_0x2f8209[_0x4c67af(0xc5)]['count']++,_0x2f8209[_0x4c67af(0xc5)]['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x2f8209['hits']['ts']=_0xff386f,(_0x3ce9b8[_0x4c67af(0xaa)]>0x32||_0x3ce9b8[_0x4c67af(0x161)]>0x64)&&(_0x3ce9b8['reduceLimits']=!0x0),(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0xaa)]>0x3e8||_0x2f8209[_0x4c67af(0xc5)]['time']>0x12c)&&(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]=!0x0);}}catch{}}}return _0x482c8e;}((_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x296e29,_0x567fe9,_0x14adfa,_0x6b3989,_0x593945,_0x42f609)=>{var _0x543ef9=_0x418f23;if(_0x12a02f[_0x543ef9(0xdb)])return _0x12a02f[_0x543ef9(0xdb)];if(!X(_0x12a02f,_0x14adfa,_0x1164b7))return _0x12a02f[_0x543ef9(0xdb)]={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x12a02f[_0x543ef9(0xdb)];let _0x5a7d78=B(_0x12a02f),_0x236b4f=_0x5a7d78[_0x543ef9(0xd3)],_0x57b9d9=_0x5a7d78['timeStamp'],_0x163b61=_0x5a7d78[_0x543ef9(0x11b)],_0x384cd9={'hits':{},'ts':{}},_0x9c7997=J(_0x12a02f,_0x6b3989,_0x384cd9,_0x296e29),_0x3ffb36=_0xa1ec34=>{_0x384cd9['ts'][_0xa1ec34]=_0x57b9d9();},_0x4ce4d2=(_0x173258,_0x2e0c6d)=>{var _0x3b6e53=_0x543ef9;let _0x2b64c1=_0x384cd9['ts'][_0x2e0c6d];if(delete _0x384cd9['ts'][_0x2e0c6d],_0x2b64c1){let _0x557981=_0x236b4f(_0x2b64c1,_0x57b9d9());_0x593a90(_0x9c7997(_0x3b6e53(0x161),_0x173258,_0x163b61(),_0x50e896,[_0x557981],_0x2e0c6d));}},_0x46c5f3=_0x1f105e=>{var _0x1152c8=_0x543ef9,_0x4a2783;return _0x1164b7===_0x1152c8(0x110)&&_0x12a02f[_0x1152c8(0x152)]&&((_0x4a2783=_0x1f105e==null?void 0x0:_0x1f105e[_0x1152c8(0xab)])==null?void 0x0:_0x4a2783[_0x1152c8(0x14f)])&&(_0x1f105e[_0x1152c8(0xab)][0x0][_0x1152c8(0x152)]=_0x12a02f[_0x1152c8(0x152)]),_0x1f105e;};_0x12a02f[_0x543ef9(0xdb)]={'consoleLog':(_0x204f4b,_0x3e1804)=>{var _0x309615=_0x543ef9;_0x12a02f['console'][_0x309615(0xff)][_0x309615(0xb7)]!==_0x309615(0x134)&&_0x593a90(_0x9c7997(_0x309615(0xff),_0x204f4b,_0x163b61(),_0x50e896,_0x3e1804));},'consoleTrace':(_0x267a3f,_0x51c339)=>{var _0x4c4943=_0x543ef9,_0x33fd8c,_0x24e61f;_0x12a02f[_0x4c4943(0xbc)][_0x4c4943(0xff)]['name']!==_0x4c4943(0xbd)&&((_0x24e61f=(_0x33fd8c=_0x12a02f[_0x4c4943(0x104)])==null?void 0x0:_0x33fd8c['versions'])!=null&&_0x24e61f[_0x4c4943(0xc8)]&&(_0x12a02f[_0x4c4943(0x165)]=!0x0),_0x593a90(_0x46c5f3(_0x9c7997(_0x4c4943(0xc4),_0x267a3f,_0x163b61(),_0x50e896,_0x51c339))));},'consoleError':(_0xf7f1fc,_0x1384d7)=>{var _0x28d83c=_0x543ef9;_0x12a02f['_ninjaIgnoreNextError']=!0x0,_0x593a90(_0x46c5f3(_0x9c7997(_0x28d83c(0x184),_0xf7f1fc,_0x163b61(),_0x50e896,_0x1384d7)));},'consoleTime':_0x2ad865=>{_0x3ffb36(_0x2ad865);},'consoleTimeEnd':(_0x3c91cf,_0x308c8b)=>{_0x4ce4d2(_0x308c8b,_0x3c91cf);},'autoLog':(_0x4bbc9f,_0x3599a3)=>{var _0x598cfa=_0x543ef9;_0x593a90(_0x9c7997(_0x598cfa(0xff),_0x3599a3,_0x163b61(),_0x50e896,[_0x4bbc9f]));},'autoLogMany':(_0x158592,_0x29b77d)=>{var _0x425f64=_0x543ef9;_0x593a90(_0x9c7997(_0x425f64(0xff),_0x158592,_0x163b61(),_0x50e896,_0x29b77d));},'autoTrace':(_0x3f5f9d,_0xc378ab)=>{var _0x377a7d=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x377a7d(0xc4),_0xc378ab,_0x163b61(),_0x50e896,[_0x3f5f9d])));},'autoTraceMany':(_0x2c6f73,_0x35405b)=>{var _0x4f4e7f=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x4f4e7f(0xc4),_0x2c6f73,_0x163b61(),_0x50e896,_0x35405b)));},'autoTime':(_0x4915d6,_0xaaf0db,_0x4c1f1e)=>{_0x3ffb36(_0x4c1f1e);},'autoTimeEnd':(_0x397624,_0x436d57,_0x47b9b8)=>{_0x4ce4d2(_0x436d57,_0x47b9b8);},'coverage':_0x45a646=>{var _0x1e9860=_0x543ef9;_0x593a90({'method':_0x1e9860(0x13a),'version':_0x296e29,'args':[{'id':_0x45a646}]});}};let _0x593a90=H(_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x593945,_0x42f609),_0x50e896=_0x12a02f[_0x543ef9(0xd8)];return _0x12a02f[_0x543ef9(0xdb)];})(globalThis,'127.0.0.1',_0x418f23(0xec),_0x418f23(0x17a),_0x418f23(0x183),'1.0.0','1751578325016',_0x418f23(0x137),_0x418f23(0x18a),_0x418f23(0x10a),_0x418f23(0xb1));\");}catch(e){}};/* istanbul ignore next */function oo_oo(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleLog(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_tr(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleTrace(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_tx(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleError(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_ts(/**@type{any}**/v){try{oo_cm().consoleTime(v);}catch(e){} return v;};/* istanbul ignore next */function oo_te(/**@type{any}**/v, /**@type{any}**/i){try{oo_cm().consoleTimeEnd(v, i);}catch(e){} return v;};/*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/"]}, "metadata": {}, "sourceType": "module"}