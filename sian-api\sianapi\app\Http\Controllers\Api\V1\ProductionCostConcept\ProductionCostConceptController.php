<?php

namespace App\Http\Controllers\Api\V1\ProductionCostConcept;

use App\Models\ProductionCostConcept;
use App\Models\ProductionCostDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class ProductionCostConceptController extends Controller {
    /**
     *
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        $validate = Validator::make($request->all(), [
            'period' => 'required|integer',
            'store_id' => 'sometimes|string',
        ]);

        if ($validate->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validate->errors()
            ], 400);
        }

        $productionCost = ProductionCostConcept::get();

        return response()->json([
            'success' => false,
            'data' => $productionCost
        ]);
    }


    public function show($id) {
        try {
            $data = DB::table('production_cost_concept AS PCC')
                ->join('production_cost_detail AS PCD', 'PCC.production_cost_concept_id', '=', 'PCD.production_cost_concept_id')
                ->join('production_cost_concept AS PPCC', 'PPCC.production_cost_concept_id', '=', 'PCC.parent_concept_id')
                ->select(
                    'PCC.production_cost_concept_id',
                    'PCC.concept_name',
                    'PCD.amount',
                    DB::raw('(SELECT COUNT(*) FROM production_cost_concept AS CPCC WHERE CPCC.parent_concept_id = PCC.production_cost_concept_id) AS child_count')
                )
                ->where('PPCC.production_cost_concept_id', $id)
                ->get();
            return response()->json([
                'success' => true,
                'data' => $data,
            ]);

        } catch (\Exception $ex) {
            Log::error('Error retrieving recipe: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error retrieving recipe: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function store(Request $request) {

        $validate = Validator::make($request->all(), [
            'concept_name' => 'required|string',
            'parent_concept_id' => 'nullable|integer',
            'percentage' => 'nullable|integer|required_without:parent_concept_id',
            'production_cost_id' => 'required|integer',
            'amount' => 'nullable|numeric',
        ]);

        if ($validate->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validate->errors(),
            ], 400);
        }

        try {

            DB::beginTransaction();

            $nProductionCostDetails = 0;
            if ($request->has('parent_concept_id')) {
                $nProductionCostDetails = ProductionCostConcept::where('parent_concept_id', $request->input('parent_concept_id'))->count();
            }

            $productionCostConceptData = [
                'concept_name' => $request->concept_name,
                'parent_concept_id' => $request->parent_concept_id,
                'status' => true,
                'order' => $nProductionCostDetails + 1,
                'percentage' => $request->percentage,
            ];

            $productionCostConcept = ProductionCostConcept::create($productionCostConceptData);

            $productionCostDetail = [
                'production_cost_id' => $request->production_cost_id,
                'production_cost_concept_id' => $productionCostConcept->production_cost_concept_id,
                'amount' => $request->amount,
            ];

            $productionCostDetail = ProductionCostDetail::create($productionCostDetail);


            DB::commit();

            return response()->json([
                'success' => true,
                'data' => [
                    'concept' => $productionCostDetail,
                    'detail' => $productionCostDetail,
                    'rowData' => [
                        'production_cost_concept_id' => $productionCostConcept->production_cost_concept_id,
                        'concept_name' => $productionCostConcept->concept_name,
                        'amount' => $productionCostDetail->amount,
                        'child_count' => 0
                    ]
                ],
            ], 201);

        } catch (\Exception $ex) {
            Log::error('Error creating production cost: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error creating production cost: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function update(Request $request, $id) {
        $validate = Validator::make($request->all(), [
            'concept_name' => 'string',
            'percentage' => 'nullable|integer',
            'production_cost_id' => 'required|integer',
            'amount' => 'nullable|numeric',
        ]);

        if ($validate->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validate->errors(),
            ], 400);
        }

        try {
            DB::beginTransaction();

            $productionCostConcept = ProductionCostConcept::findOrFail($id);

            if ($request->has('concept_name') || $request->has('percentage')) {

                $conceptData = [];

                if ($request->has('conept_name')) {
                    $conceptData['concept_name'] = $request->concept_name;
                }

                if ($request->has('percentage')) {
                    $conceptData['percentage'] = $request->percentage;
                }

                $productionCostConcept->update($conceptData);

            }
            $productionCostDetail = ProductionCostDetail::where('production_cost_concept_id', $id)
                ->where('production_cost_id', $request->production_cost_id)
                ->firstOrFail();

            if ($request->has('amount')) {
                $productionCostDetail->update([
                    'amount' => $request->amount,
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => [
                    'concept' => $productionCostConcept,
                    'detail' => $productionCostDetail,
                    'rowData' => [
                        'production_cost_concept_id' => $productionCostConcept->production_cost_concept_id,
                        'concept_name' => $productionCostConcept->concept_name,
                        'amount' => $productionCostDetail->amount,
                        'child_count' => 0
                    ]
                ],
            ], 200);

        } catch (\Exception $ex) {
            DB::rollBack();
            Log::error('Error updating production cost: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error updating production cost: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function destroy($id, $production_cost_id) {
        try {
            DB::beginTransaction();

            $productionCostConcept = ProductionCostConcept::findOrFail($id);
            ProductionCostDetail::where('production_cost_id', $production_cost_id)
                ->where('production_cost_concept_id', $id)
                ->delete();

            $productionCostConcept->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Production cost concept and detail deleted successfully.',
            ], 200);

        } catch (\Exception $ex) {
            DB::rollBack();
            Log::error('Error deleting production cost: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error deleting production cost: ' . $ex->getMessage(),
            ], 500);
        }
    }
    private static function generateDateRange($period) {
        list($month, $year) = explode('-', $period);
        $month = (int) $month;
        $year = (int) $year;
        $startDate = (new \DateTime())->setDate($year, $month, 1)->format('Y-m-d');

        $currentDate = new \DateTime();

        $endDate = '';
        $daysInMonth = '';

        if ($month == $currentDate->format('n') && $year == $currentDate->format('Y')) {
            $endDate = $currentDate->format('Y-m-d');
            $daysInMonth = $currentDate->format('j');
        } else {
            $endDate = (new \DateTime())->setDate($year, $month, 1)->modify('last day of this month')->format('Y-m-d');
            $daysInMonth = (new \DateTime())->setDate($year, $month, 1)->format('t');
        }

        return [$startDate, $endDate, $daysInMonth, $month, $year];
    }

}
