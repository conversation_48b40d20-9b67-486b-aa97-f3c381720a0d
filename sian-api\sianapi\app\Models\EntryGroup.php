<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class EntryGroup
 * 
 * @property int $entry_group_id
 * @property string $pk
 * @property int $movement_id
 * @property string $column
 * @property string $account_group
 * @property string $owner
 * @property int $owner_id
 * @property bool $fee_number
 * @property string $route
 * @property string $account_code
 * @property string $detail
 * @property Carbon $emission_date
 * @property Carbon $expiration_date
 * @property string $currency
 * @property float $total
 * @property float $balance
 * @property float $ret_balance
 * @property string $single_balance
 * @property float $balance_percent
 * @property bool $scheduled
 * @property string|null $scheduled_entry_id
 * @property Carbon|null $scheduled_date
 * @property string|null $scheduled_currency
 * @property float|null $scheduled_amount
 * @property string $condition
 * @property int $person_id
 * @property int $store_id
 * @property bool $checked
 * @property bool $is_paid
 * @property bool $is_payable
 * @property bool $is_redeemable
 * @property bool $is_applicable
 * @property bool $is_schedulable
 * @property bool $scheduled_to_pay
 * @property bool $scheduled_to_redeem
 * @property bool $ready_to_pay
 * @property bool $ready_to_apply
 * @property bool $to_collect
 * @property bool $to_pay
 * @property bool $to_do
 * @property int $days_overdue
 * @property bool $expired
 * @property int $entry_link_count
 * @property bool $default
 * 
 * @property Account $account
 * @property Movement $movement
 * @property Person $person
 * @property Scenario $scenario
 * @property Store $store
 *
 * @package App\Models
 */
class EntryGroup extends Model
{
	protected $table = 'entry_group';
	protected $primaryKey = 'entry_group_id';
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'owner_id' => 'int',
		'fee_number' => 'bool',
		'total' => 'float',
		'balance' => 'float',
		'ret_balance' => 'float',
		'balance_percent' => 'float',
		'scheduled' => 'bool',
		'scheduled_amount' => 'float',
		'person_id' => 'int',
		'store_id' => 'int',
		'checked' => 'bool',
		'is_paid' => 'bool',
		'is_payable' => 'bool',
		'is_redeemable' => 'bool',
		'is_applicable' => 'bool',
		'is_schedulable' => 'bool',
		'scheduled_to_pay' => 'bool',
		'scheduled_to_redeem' => 'bool',
		'ready_to_pay' => 'bool',
		'ready_to_apply' => 'bool',
		'to_collect' => 'bool',
		'to_pay' => 'bool',
		'to_do' => 'bool',
		'days_overdue' => 'int',
		'expired' => 'bool',
		'entry_link_count' => 'int',
		'default' => 'bool'
	];

	protected $dates = [
		'emission_date',
		'expiration_date',
		'scheduled_date'
	];

	protected $fillable = [
		'pk',
		'movement_id',
		'column',
		'account_group',
		'owner',
		'owner_id',
		'fee_number',
		'route',
		'account_code',
		'detail',
		'emission_date',
		'expiration_date',
		'currency',
		'total',
		'balance',
		'ret_balance',
		'single_balance',
		'balance_percent',
		'scheduled',
		'scheduled_entry_id',
		'scheduled_date',
		'scheduled_currency',
		'scheduled_amount',
		'condition',
		'person_id',
		'store_id',
		'checked',
		'is_paid',
		'is_payable',
		'is_redeemable',
		'is_applicable',
		'is_schedulable',
		'scheduled_to_pay',
		'scheduled_to_redeem',
		'ready_to_pay',
		'ready_to_apply',
		'to_collect',
		'to_pay',
		'to_do',
		'days_overdue',
		'expired',
		'entry_link_count',
		'default'
	];

	public function account()
	{
		return $this->belongsTo(Account::class, 'account_group');
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}

	public function person()
	{
		return $this->belongsTo(Person::class);
	}

	public function scenario()
	{
		return $this->belongsTo(Scenario::class, 'route');
	}

	public function store()
	{
		return $this->belongsTo(Store::class);
	}
}
