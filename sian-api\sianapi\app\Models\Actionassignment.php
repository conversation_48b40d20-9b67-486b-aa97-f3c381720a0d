<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Actionassignment
 * 
 * @property string $module
 * @property string $controller
 * @property string $action
 * @property string $owner
 * @property int $owner_id
 * 
 *
 * @package App\Models
 */
class Actionassignment extends Model
{
	protected $table = 'actionassignment';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'owner_id' => 'int'
	];

	public function action()
	{
		return $this->belongsTo(Action::class, 'module')
					->where('action.module', '=', 'actionassignment.module')
					->where('action.controller', '=', 'actionassignment.controller')
					->where('action.action', '=', 'actionassignment.action');
	}

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}
}
