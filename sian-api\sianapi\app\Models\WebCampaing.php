<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class WebCampaing
 * 
 * @property int $web_campaing_id
 * @property string $web_campaing_name
 * @property string $hex_color
 * @property bool $status
 *
 * @package App\Models
 */
class WebCampaing extends Model
{
	protected $table = 'web_campaing';
	protected $primaryKey = 'web_campaing_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool'
	];

	protected $fillable = [
		'web_campaing_name',
		'hex_color',
		'status'
	];
}
