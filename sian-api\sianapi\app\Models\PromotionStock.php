<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class PromotionStock
 * 
 * @property int $promotion_stock_id
 * @property int $movement_id
 * @property int $promotion_id
 * @property int|null $promotion_item_id
 * @property int|null $promotion_gift_option_id
 * @property float $unit_quantity
 * 
 * @property Movement $movement
 * @property Promotion $promotion
 * @property PromotionGiftOption|null $promotion_gift_option
 * @property PromotionItem|null $promotion_item
 *
 * @package App\Models
 */
class PromotionStock extends Model
{
	protected $table = 'promotion_stock';
	protected $primaryKey = 'promotion_stock_id';
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'promotion_id' => 'int',
		'promotion_item_id' => 'int',
		'promotion_gift_option_id' => 'int',
		'unit_quantity' => 'float'
	];

	protected $fillable = [
		'movement_id',
		'promotion_id',
		'promotion_item_id',
		'promotion_gift_option_id',
		'unit_quantity'
	];

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function promotion()
	{
		return $this->belongsTo(Promotion::class);
	}

	public function promotion_gift_option()
	{
		return $this->belongsTo(PromotionGiftOption::class);
	}

	public function promotion_item()
	{
		return $this->belongsTo(PromotionItem::class);
	}
}
