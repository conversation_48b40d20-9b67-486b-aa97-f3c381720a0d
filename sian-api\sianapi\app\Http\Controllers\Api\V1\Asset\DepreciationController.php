<?php

namespace App\Http\Controllers\Api\V1\Asset;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\Asset\DepreciationCollection;
use Illuminate\Support\Facades\Validator;
use App\Models\Depreciation;
use Doctrine\DBAL\Query\QueryException;
use Illuminate\Support\Facades\DB;

class DepreciationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'year' => 'required',
            'period' => 'required',
            'page' => 'int',
            'pageSize' => 'int',
            'sortField' => 'String',
            'direction' => 'String',
            'searchTerm' => 'String',
        ], [
            'year.required' => 'El año es obligatorio.',
            'period.required' => 'El mes es obligatorio.'
        ]);

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {

            $i_page = (isset($request->page) && $request->page > 0) ? $request->page : 1;
            $i_pageSize = (isset($request->pageSize) && $request->pageSize > 0) ? $request->pageSize : 10;
            $s_sortField = isset($request->sortField) ? $request->sortField : 'business_unit_id';
            $s_direction = isset($request->direction) ? $request->direction : 'ASC';
            $i_skip = ($i_page - 1) * $i_pageSize;

            //Query
            $s_query = Depreciation::skip($i_skip);
            $s_query->where('year', $request->year);
            $s_query->where('period', $request->period);
            if (isset($request->businessUnitId)) {
                $s_query->where('business_unit_id', $request->businessUnitId);
            }
            $s_query->orderBy($s_sortField, $s_direction);

            $depreciations = $s_query->paginate($i_pageSize);

            $paginator =  new DepreciationCollection($depreciations);

            $a_response = [
                'success' => true,
                'data' => [
                    'count' => $paginator->count(),
                    'totalPages' => $paginator->lastPage(),
                    'page' => $paginator->currentPage(),
                    'pageSize' => $paginator->perPage(),
                    'totalItems' => $paginator->total(),
                    'items' => $paginator->items(),
                ]
            ];
        }
        return $a_response;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getHeader(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'year' => 'required',
            'businessUnitId' => 'int'
        ], [
            'year.required' => 'El campo Año es obligatorio.',
        ]);

        $a_response = [];

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {
            $s_groupType = isset($request->groupType) ? $request->groupType : 'period';
            $i_businessUnitId = isset($request->businessUnitId) ? $request->businessUnitId : 0;
            $a_spGetDepreciationHeader = DB::select(
                "call sp_get_depreciation_header (:year, :groupType, :businessUnitId)",
                [
                    ':year' =>  $request->year,
                    ':groupType' =>  $s_groupType,
                    ':businessUnitId' => $i_businessUnitId
                ]
            );

            $a_response = [
                'success' => true,
                'data' => [
                    'groupType' => $s_groupType,
                    'items' => $a_spGetDepreciationHeader
                ]
            ];
        }

        return response()->json($a_response);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function preCalculate(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'year' => 'required',
            'period' => 'required',
        ], [
            'year.required' => 'El campo Año es obligatorio.',
            'period.required' => 'El campo Periodo es obligatorio.'
        ]);

        $a_response = [];

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {
            $a_spPreCalcDepreciation = DB::select(
                "call sp_pre_calc_depreciation (:year, :period)",
                [
                    ':year' =>  $request->year,
                    ':period' =>  $request->period,
                ]
            );

            $a_response = [
                'success' => true,
                'data' => [
                    'items' => $a_spPreCalcDepreciation
                ]
            ];
        }

        return response()->json($a_response);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function calculate(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'year' => 'required',
            'period' => 'required',
            'ids' => 'required'
        ], [
            'year.required' => 'El campo Año es obligatorio.',
            'period.required' => 'El campo Periodo es obligatorio.',
            'ids.required' => 'El campo ids es obligatorio.'
        ]);

        $a_response = [];

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {

            DB::beginTransaction();
            try {
                DB::select(
                    "call sp_calculate_depreciation (:year, :period, :ids)",
                    [
                        ':year' =>  $request->year,
                        ':period' =>  $request->period,
                        ':ids' => $request->ids
                    ]
                );
                DB::commit();
                $a_response = [
                    'success' => true,
                    'message' => 'Se ejecutó correctamente.',
                ];
            } catch (QueryException $ex) {
                DB::rollBack();
                $a_response = [
                    'success' => false,
                    'message' => $ex->getMessage(),
                ];
            }
        }
        return response()->json($a_response);
    }
}
