<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class WarehouseSerie
 * 
 * @property int $item_id
 * @property string $serie
 * 
 * @property WarehouseMerchandise $warehouse_merchandise
 *
 * @package App\Models
 */
class WarehouseSerie extends Model
{
	protected $table = 'warehouse_serie';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'item_id' => 'int'
	];

	public function warehouse_merchandise()
	{
		return $this->belongsTo(WarehouseMerchandise::class, 'item_id');
	}
}
