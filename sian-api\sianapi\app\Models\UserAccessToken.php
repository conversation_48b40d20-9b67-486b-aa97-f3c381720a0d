<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ApiUserAccessToken
 *
 * @property int $_id
 * @property int $user_id
 * @property string $user_token
 * @property Carbon $last_used_at
 * @property Carbon $expires_at
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property User|null $user
 *
 * @package App\Models
 */
class UserAccessToken extends Model {
    protected $table = 'user_access_token';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $casts = [
        'user_id' => 'int',
    ];

    protected $hidden = [];

    protected $fillable = [
        'user_id',
        'user_token',
        'last_used_at',
        'expires_at',
        'created_at',
        'updated_at',
    ];

    public function user() {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }

    public static function deleteAccessToken($token) {
        $userAccessToken = UserAccessToken::where('user_token', $token)->first();
        if ($userAccessToken) {
            $userAccessToken->delete();
            return true;
        }
        return true;

    }
}
