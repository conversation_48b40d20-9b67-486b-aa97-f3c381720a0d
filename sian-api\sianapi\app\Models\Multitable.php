<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Multitable
 *
 * @property int $multi_id
 * @property string $value
 * @property string $description
 * @property string|null $abbreviation
 * @property int|null $multi_parent_id
 * @property int $order
 *
 * @property Multitable|null $multitable
 * @property Collection|BankAccount[] $bank_accounts
 * @property Collection|Cashbox[] $cashboxes
 * @property Collection|CommercialCaseDetail[] $commercial_case_details
 * @property Collection|Concept[] $concepts
 * @property Collection|Employee[] $employees
 * @property Collection|FixedAsset[] $fixed_assets
 * @property Collection|Inventory[] $inventories
 * @property Collection|InventoryReason[] $inventory_reasons
 * @property Collection|Item[] $items
 * @property Collection|Movement[] $movements
 * @property Collection|MovementSetting[] $movement_settings
 * @property Collection|Multitable[] $multitables
 * @property Collection|Person[] $people
 * @property Collection|Presentation[] $presentations
 * @property Collection|Product[] $products
 * @property Collection|ProductTypeSetting[] $product_type_settings
 * @property Collection|Seller[] $sellers
 *
 * @package App\Models
 */
class Multitable extends Model {
    protected $table = 'multitable';
    protected $primaryKey = 'multi_id';
    public $timestamps = false;

    const OWNER = 'Multitable';
    //CONST
    const MONTH_CODE = 'MONTH';
    const LICENSE_CATEGORY_CODE = 'CLC';
    const TYPE_FIXED_ASSET_CODE = 'TAF';
    const METHOD_DEPRECIATION_CODE = 'MDD';
    const STATE_FIXED_ASSET_CODE = 'EAF';
    const CURRENT_TAXES_CODE = 'CURRENT_TAXES';
    const UN_ECE_5153_CODE = 'UN_ECE_5153';
    const SUNAT_CODE = 'SUNAT_CODE';
    const MEASURE_UNIT = 'MEASURE_UNIT';
    const CUSTOM_MEASURE = 'CUSTOM_MEASURE';
    const CATALOG_3 = 'CATALOG_3';
    const CATALOG_9 = 'CATALOG_9';
    const CATALOG_10 = 'CATALOG_10';
    const CATALOG_25 = 'CATALOG_25';
    const CATALOG_35 = 'CATALOG_35';
    const CATALOG_54 = 'CATALOG_54';
    const ISO3166_COUNTRY = 'ISO3166_COUNTRY';
    const ID_TYPES = 'ID_TYPES';
    const GENRE = 'GENRE';
    const CONFIRMATION_TYPES = 'CONFIRMATION_TYPES';
    const PLAME = 'PLAME';
    const ABSENCE = 'Absence';
    const SKILL_MOVEMENT = 'SKILL_MOVEMENT';
    const SKILL_ENTRY_GROUP = 'SKILL_ENTRY_GROUP';
    const SKILL_DIVISION = 'SKILL_DIVISION';
    const SKILL_LINE = 'SKILL_LINE';
    const SKILL_SUBLINE = 'SKILL_SUBLINE';
    const SKILL_MARK = 'SKILL_MARK';
    const SKILL_MERCHANDISE = 'SKILL_MERCHANDISE';
    const SKILL_COMBO = 'SKILL_COMBO';
    const SKILL_OBJECT = 'SKILL_OBJECT';
    const SKILL_SUBCATEGORY = 'SKILL_SUBCATEGORY';
    const SKILL_CATEGORY = 'SKILL_CATEGORY';
    const SKILL_WAREHOUSE = 'SKILL_WAREHOUSE';
    const SKILL_STORE = 'SKILL_STORE';
    const SKILL_LOCKER_BOX_REQUEST = 'SKILL_LOCKER_BOX_REQUEST';
    const SKILL_LOCKER_REQUEST = 'SKILL_LOCKER_REQUEST';
    const ELECTRONIC_DOCUMENT_CODES = 'ELECTRONIC_DOCUMENT_CODES';
    const ITEM_TYPE = 'ITEM_TYPE';
    const ITEM_TYPE_MERCHANDISE = 'Merc';
    const ITEM_TYPE_SUPPY = 'Sumi';
    const ITEM_TYPE_SERVICE = 'Serv';
    const CATEGORY_PERSON = 'CATEGORY_PERSON';
    const CONTACT_MODE_CRM = 'CONTACT_MODE_CRM';
    const INVENTORY_TYPE = 'INVENTORY_TYPE';
    const SELLER_TYPE = 'SELLER_TYPE';
    const AGREEMENT = 'AGREEMENT';

    protected $casts = [
        'multi_parent_id' => 'int',
        'order' => 'int'
    ];

    protected $fillable = [
        'value',
        'description',
        'abbreviation',
        'multi_parent_id',
        'order'
    ];

    public function multitable() {
        return $this->belongsTo(Multitable::class, 'multi_parent_id');
    }

    public function bank_accounts() {
        return $this->hasMany(BankAccount::class, 'entity_multi_id');
    }

    public function cashboxes() {
        return $this->hasMany(Cashbox::class, 'entity_type_id');
    }

    public function commercial_case_details() {
        return $this->hasMany(CommercialCaseDetail::class, 'contact_mode');
    }

    public function concepts() {
        return $this->hasMany(Concept::class, 'plame_id');
    }

    public function employees() {
        return $this->hasMany(Employee::class, 'category_id');
    }

    public function fixed_assets() {
        return $this->hasMany(FixedAsset::class, 'type_sunat');
    }

    public function inventories() {
        return $this->hasMany(Inventory::class, 'inventory_type_id');
    }

    public function inventory_reasons() {
        return $this->hasMany(InventoryReason::class, 'inventory_type_id');
    }

    public function items() {
        return $this->hasMany(Item::class, 'item_type_id');
    }

    public function movements() {
        return $this->hasMany(Movement::class, 'agreement_id');
    }

    public function movement_settings() {
        return $this->hasMany(MovementSetting::class, 'item_type_id');
    }

    public function multitables() {
        return $this->hasMany(Multitable::class, 'multi_parent_id');
    }

    public function people() {
        return $this->hasMany(Person::class, 'country');
    }

    public function presentations() {
        return $this->hasMany(Presentation::class, 'measure_id');
    }

    public function products() {
        return $this->hasMany(Product::class, 'item_type_id');
    }

    public function product_type_settings() {
        return $this->hasMany(ProductTypeSetting::class, 'item_type_id');
    }

    public function sellers() {
        return $this->hasMany(Seller::class, 'seller_type_id');
    }

    public static function getMultitableByValue($value) {
        return self::where('value', $value)->firstOrFail();
    }
}
