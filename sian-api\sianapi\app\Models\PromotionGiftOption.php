<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PromotionGiftOption
 * 
 * @property int $promotion_gift_option_id
 * @property int $promotion_gift_id
 * @property int $product_id
 * @property float $equivalence
 * @property float $carry
 * @property float $discount_factor
 * @property float $pprice_pen
 * @property float $rprice_pen
 * @property float $rtotal_pen
 * @property float $pprice_usd
 * @property float $rprice_usd
 * @property float $rtotal_usd
 * @property float $pres_stock
 * @property float $unit_stock
 * @property float $unit_balance
 * @property int $order
 * @property bool $is_removable
 * 
 * @property Presentation $presentation
 * @property PromotionGift $promotion_gift
 * @property Collection|Item[] $items
 * @property Collection|PromotionStock[] $promotion_stocks
 *
 * @package App\Models
 */
class PromotionGiftOption extends Model
{
	protected $table = 'promotion_gift_option';
	protected $primaryKey = 'promotion_gift_option_id';
	public $timestamps = false;

	protected $casts = [
		'promotion_gift_id' => 'int',
		'product_id' => 'int',
		'equivalence' => 'float',
		'carry' => 'float',
		'discount_factor' => 'float',
		'pprice_pen' => 'float',
		'rprice_pen' => 'float',
		'rtotal_pen' => 'float',
		'pprice_usd' => 'float',
		'rprice_usd' => 'float',
		'rtotal_usd' => 'float',
		'pres_stock' => 'float',
		'unit_stock' => 'float',
		'unit_balance' => 'float',
		'order' => 'int',
		'is_removable' => 'bool'
	];

	protected $fillable = [
		'promotion_gift_id',
		'product_id',
		'equivalence',
		'carry',
		'discount_factor',
		'pprice_pen',
		'rprice_pen',
		'rtotal_pen',
		'pprice_usd',
		'rprice_usd',
		'rtotal_usd',
		'pres_stock',
		'unit_stock',
		'unit_balance',
		'order',
		'is_removable'
	];

	public function presentation()
	{
		return $this->belongsTo(Presentation::class, 'product_id')
					->where('presentation.product_id', '=', 'promotion_gift_option.product_id')
					->where('presentation.equivalence', '=', 'promotion_gift_option.equivalence');
	}

	public function promotion_gift()
	{
		return $this->belongsTo(PromotionGift::class);
	}

	public function items()
	{
		return $this->hasMany(Item::class);
	}

	public function promotion_stocks()
	{
		return $this->hasMany(PromotionStock::class);
	}
}
