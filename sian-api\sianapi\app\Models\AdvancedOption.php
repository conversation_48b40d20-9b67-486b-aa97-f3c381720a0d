<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class AdvancedOption
 * 
 * @property int $movement_id
 * @property bool $skip_check_debt
 * @property bool $skip_check_later
 * @property bool $no_income
 * @property bool $omit_deactivation
 * @property bool $allow_sale_below_cost
 * @property bool $allow_sale_below_min_price
 * @property bool $allow_dispatch_below_cost
 * 
 * @property Movement $movement
 *
 * @package App\Models
 */
class AdvancedOption extends Model
{
	protected $table = 'advanced_option';
	protected $primaryKey = 'movement_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'skip_check_debt' => 'bool',
		'skip_check_later' => 'bool',
		'no_income' => 'bool',
		'omit_deactivation' => 'bool',
		'allow_sale_below_cost' => 'bool',
		'allow_sale_below_min_price' => 'bool',
		'allow_dispatch_below_cost' => 'bool'
	];

	protected $fillable = [
		'skip_check_debt',
		'skip_check_later',
		'no_income',
		'omit_deactivation',
		'allow_sale_below_cost',
		'allow_sale_below_min_price',
		'allow_dispatch_below_cost'
	];

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}
}
