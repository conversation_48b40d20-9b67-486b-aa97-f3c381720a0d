<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Combo
 * 
 * @property int $product_id
 * @property int $link_prices
 * @property float $rprice_pen
 * @property float $rprice_usd
 * @property bool $show_without_stock
 * @property int $item_count
 * 
 * @property Product $product
 * @property Collection|ComboItem[] $combo_items
 *
 * @package App\Models
 */
class Combo extends Model
{
	protected $table = 'combo';
	protected $primaryKey = 'product_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'product_id' => 'int',
		'link_prices' => 'int',
		'rprice_pen' => 'float',
		'rprice_usd' => 'float',
		'show_without_stock' => 'bool',
		'item_count' => 'int'
	];

	protected $fillable = [
		'link_prices',
		'rprice_pen',
		'rprice_usd',
		'show_without_stock',
		'item_count'
	];

	public function product()
	{
		return $this->belongsTo(Product::class);
	}

	public function combo_items()
	{
		return $this->hasMany(ComboItem::class, 'product_id');
	}
}
