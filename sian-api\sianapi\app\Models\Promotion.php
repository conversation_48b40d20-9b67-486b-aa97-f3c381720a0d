<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Promotion
 * 
 * @property int $promotion_id
 * @property string $promotion_name
 * @property Carbon $begin_date
 * @property Carbon $end_date
 * @property bool $status
 * @property string $state
 * @property bool $price_mode
 * @property string $price_currency
 * @property int $store_count
 * @property int $item_count
 * @property int|null $begin_job_id
 * @property int|null $end_job_id
 * 
 * @property Collection|PromotionItem[] $promotion_items
 * @property Collection|PromotionStock[] $promotion_stocks
 *
 * @package App\Models
 */
class Promotion extends Model
{
	protected $table = 'promotion';
	protected $primaryKey = 'promotion_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'price_mode' => 'bool',
		'store_count' => 'int',
		'item_count' => 'int',
		'begin_job_id' => 'int',
		'end_job_id' => 'int'
	];

	protected $dates = [
		'begin_date',
		'end_date'
	];

	protected $fillable = [
		'promotion_name',
		'begin_date',
		'end_date',
		'status',
		'state',
		'price_mode',
		'price_currency',
		'store_count',
		'item_count',
		'begin_job_id',
		'end_job_id'
	];

	public function promotion_items()
	{
		return $this->hasMany(PromotionItem::class);
	}

	public function promotion_stocks()
	{
		return $this->hasMany(PromotionStock::class);
	}
}
