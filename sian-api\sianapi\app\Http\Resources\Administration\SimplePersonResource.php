<?php

namespace App\Http\Resources\Administration;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\Person;

class SimplePersonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->person_id,
            'codeType' => Person::getItems()[$this->identification_type],
            'code' => $this->identification_number,
            'name' => str_replace(',', ' ', $this->person_name)
        ];
    }
}
