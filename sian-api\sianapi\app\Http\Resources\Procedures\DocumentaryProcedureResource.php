<?php

namespace App\Http\Resources\Procedures;

use Illuminate\Http\Resources\Json\JsonResource;

class DocumentaryProcedureResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            'id' => $this->id,
            'code' => $this->codename,
            'description' => $this->description,
            'state' => $this->state,
            'is_active' => $this->is_active,
            'parent_id' => $this->parent_id,
            'user' => [
                'id' => $this->user->user_id,
                'name' => $this->user->username,
            ],
            'type_documentary_procedure' => [
                'id' => $this->type_documentary_procedure->id,
                'name' => $this->type_documentary_procedure->name,
            ],
            'created_at' => $this->published_at            
        ];
    }
}
