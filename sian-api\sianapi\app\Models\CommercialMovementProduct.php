<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class CommercialMovementProduct
 * 
 * @property int $item_id
 * @property int $movement_id
 * @property float $vprice_pen
 * @property float $sprice_pen
 * @property float $rprice_pen
 * @property float $cprice_pen
 * @property float $cdiscount_pen
 * @property float $price_pen
 * @property float $discount_pen
 * @property float $crude_pen
 * @property float $affected1_pen
 * @property float $affected2_pen
 * @property float $affected3_pen
 * @property float $affected_pen
 * @property float $inaffected_pen
 * @property float $nobill_pen
 * @property float $export_pen
 * @property float $free_pen
 * @property float $nnet_pen
 * @property float $fnet_pen
 * @property float $net_pen
 * @property float $nigv_pen
 * @property float $figv_pen
 * @property float $igv_pen
 * @property float $ntotal_pen
 * @property float $ftotal_pen
 * @property float $total_pen
 * @property float $perception_pen
 * @property float $nreal_pen
 * @property float $freal_pen
 * @property float $real_pen
 * @property float $vprice_usd
 * @property float $sprice_usd
 * @property float $rprice_usd
 * @property float $cprice_usd
 * @property float $cdiscount_usd
 * @property float $price_usd
 * @property float $discount_usd
 * @property float $crude_usd
 * @property float $affected1_usd
 * @property float $affected2_usd
 * @property float $affected3_usd
 * @property float $affected_usd
 * @property float $inaffected_usd
 * @property float $nobill_usd
 * @property float $export_usd
 * @property float $free_usd
 * @property float $nnet_usd
 * @property float $fnet_usd
 * @property float $net_usd
 * @property float $nigv_usd
 * @property float $figv_usd
 * @property float $igv_usd
 * @property float $ntotal_usd
 * @property float $ftotal_usd
 * @property float $total_usd
 * @property float $perception_usd
 * @property float $nreal_usd
 * @property float $freal_usd
 * @property float $real_usd
 * @property string|null $observation
 * @property int $expiration
 * @property int $igv_affection
 * @property bool $perception_affected
 * @property bool $include_igv
 * @property bool $as_bill
 * @property bool $force_igv
 * @property bool $is_locked
 * @property bool $free_transfer
 * @property bool $round_mode
 * @property int|null $group_id
 * @property int|null $gift_of
 * 
 * @property CommercialMovement $commercial_movement
 * @property CommercialMovementProduct|null $commercial_movement_product
 * @property Item $item
 * @property Collection|CommercialMovementProduct[] $commercial_movement_products
 * @property DuaProduct $dua_product
 *
 * @package App\Models
 */
class CommercialMovementProduct extends Model
{
	protected $table = 'commercial_movement_product';
	protected $primaryKey = 'item_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'item_id' => 'int',
		'movement_id' => 'int',
		'vprice_pen' => 'float',
		'sprice_pen' => 'float',
		'rprice_pen' => 'float',
		'cprice_pen' => 'float',
		'cdiscount_pen' => 'float',
		'price_pen' => 'float',
		'discount_pen' => 'float',
		'crude_pen' => 'float',
		'affected1_pen' => 'float',
		'affected2_pen' => 'float',
		'affected3_pen' => 'float',
		'affected_pen' => 'float',
		'inaffected_pen' => 'float',
		'nobill_pen' => 'float',
		'export_pen' => 'float',
		'free_pen' => 'float',
		'nnet_pen' => 'float',
		'fnet_pen' => 'float',
		'net_pen' => 'float',
		'nigv_pen' => 'float',
		'figv_pen' => 'float',
		'igv_pen' => 'float',
		'ntotal_pen' => 'float',
		'ftotal_pen' => 'float',
		'total_pen' => 'float',
		'perception_pen' => 'float',
		'nreal_pen' => 'float',
		'freal_pen' => 'float',
		'real_pen' => 'float',
		'vprice_usd' => 'float',
		'sprice_usd' => 'float',
		'rprice_usd' => 'float',
		'cprice_usd' => 'float',
		'cdiscount_usd' => 'float',
		'price_usd' => 'float',
		'discount_usd' => 'float',
		'crude_usd' => 'float',
		'affected1_usd' => 'float',
		'affected2_usd' => 'float',
		'affected3_usd' => 'float',
		'affected_usd' => 'float',
		'inaffected_usd' => 'float',
		'nobill_usd' => 'float',
		'export_usd' => 'float',
		'free_usd' => 'float',
		'nnet_usd' => 'float',
		'fnet_usd' => 'float',
		'net_usd' => 'float',
		'nigv_usd' => 'float',
		'figv_usd' => 'float',
		'igv_usd' => 'float',
		'ntotal_usd' => 'float',
		'ftotal_usd' => 'float',
		'total_usd' => 'float',
		'perception_usd' => 'float',
		'nreal_usd' => 'float',
		'freal_usd' => 'float',
		'real_usd' => 'float',
		'expiration' => 'int',
		'igv_affection' => 'int',
		'perception_affected' => 'bool',
		'include_igv' => 'bool',
		'as_bill' => 'bool',
		'force_igv' => 'bool',
		'is_locked' => 'bool',
		'free_transfer' => 'bool',
		'round_mode' => 'bool',
		'group_id' => 'int',
		'gift_of' => 'int'
	];

	protected $fillable = [
		'movement_id',
		'vprice_pen',
		'sprice_pen',
		'rprice_pen',
		'cprice_pen',
		'cdiscount_pen',
		'price_pen',
		'discount_pen',
		'crude_pen',
		'affected1_pen',
		'affected2_pen',
		'affected3_pen',
		'affected_pen',
		'inaffected_pen',
		'nobill_pen',
		'export_pen',
		'free_pen',
		'nnet_pen',
		'fnet_pen',
		'net_pen',
		'nigv_pen',
		'figv_pen',
		'igv_pen',
		'ntotal_pen',
		'ftotal_pen',
		'total_pen',
		'perception_pen',
		'nreal_pen',
		'freal_pen',
		'real_pen',
		'vprice_usd',
		'sprice_usd',
		'rprice_usd',
		'cprice_usd',
		'cdiscount_usd',
		'price_usd',
		'discount_usd',
		'crude_usd',
		'affected1_usd',
		'affected2_usd',
		'affected3_usd',
		'affected_usd',
		'inaffected_usd',
		'nobill_usd',
		'export_usd',
		'free_usd',
		'nnet_usd',
		'fnet_usd',
		'net_usd',
		'nigv_usd',
		'figv_usd',
		'igv_usd',
		'ntotal_usd',
		'ftotal_usd',
		'total_usd',
		'perception_usd',
		'nreal_usd',
		'freal_usd',
		'real_usd',
		'observation',
		'expiration',
		'igv_affection',
		'perception_affected',
		'include_igv',
		'as_bill',
		'force_igv',
		'is_locked',
		'free_transfer',
		'round_mode',
		'group_id',
		'gift_of'
	];

	public function commercial_movement()
	{
		return $this->belongsTo(CommercialMovement::class, 'movement_id');
	}

	public function commercial_movement_product()
	{
		return $this->belongsTo(CommercialMovementProduct::class, 'group_id');
	}

	public function item()
	{
		return $this->belongsTo(Item::class);
	}

	public function commercial_movement_products()
	{
		return $this->hasMany(CommercialMovementProduct::class, 'group_id');
	}

	public function dua_product()
	{
		return $this->hasOne(DuaProduct::class, 'item_id');
	}
}
