<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class HumanCategoryRemuneration
 * 
 * @property int $human_category_remuneration_id
 * @property int $human_category_id
 * @property Carbon|null $date_ini
 * @property Carbon|null $date_end
 * @property float|null $remuneration_diary
 * @property float|null $buc
 * 
 * @property HumanCategory $human_category
 *
 * @package App\Models
 */
class HumanCategoryRemuneration extends Model
{
	protected $table = 'human_category_remuneration';
	protected $primaryKey = 'human_category_remuneration_id';
	public $timestamps = false;

	protected $casts = [
		'human_category_id' => 'int',
		'remuneration_diary' => 'float',
		'buc' => 'float'
	];

	protected $dates = [
		'date_ini',
		'date_end'
	];

	protected $fillable = [
		'human_category_id',
		'date_ini',
		'date_end',
		'remuneration_diary',
		'buc'
	];

	public function human_category()
	{
		return $this->belongsTo(HumanCategory::class);
	}
}
