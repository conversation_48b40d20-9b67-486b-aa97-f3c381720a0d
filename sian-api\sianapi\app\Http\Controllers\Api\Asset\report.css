@page {
    size: auto;
    odd-header-name: html_myHeader1;
    even-header-name: html_myHeader2;
    odd-footer-name: html_myFooter1;
    even-footer-name: html_myFooter2;
}


@import url('https://fonts.googleapis.com/css?family=Barlow+Condensed:500&display=swap');
@import url('https://fonts.googleapis.com/css?family=Roboto+Condensed:300,300i,400,400i,700,700i&display=swap');


.container {
    margin: 0;
    padding: 0;
    width: 100%;
}

.single-row {
    width: 100%;
}

.row {
    margin-right: -15px;
    margin-left: -15px;
}


/* (START) Terminos y condiciones */
.numbering {
    width: 50px;
}

.general-title {
    line-height: 56px;
    margin: 0px;
    font-family: 'Barlow Condensed', sans-serif;
    font-size: 17px;
    text-transform: uppercase;
}

.structure_type_name {
    margin-top: 0px;
    font-family: '<PERSON> Condensed', sans-serif;
    font-size: 15px;
    text-transform: uppercase;
}

.title-level-1 {
    margin-top: 0px;
    margin-bottom: 0px;
    padding: 0px;
    background-color: #696969;
}

/* violeta: #8F00FF*/
.title-level-1 p {
    float: left;
    margin-top: 0px;
    margin-bottom: 0px;
    margin-right: 0px;
    padding-right: 0px;
    background-color: red;
}

.desc-level-1 {
    margin: 0px;
    margin-left: 50px;
    background-color: orange;
    text-transform: capitalize;
}


.title-level-2 {
    margin-top: 0px;
    margin-bottom: 0px;
    padding: 0px;
    background-color: #DCDCDC;
}

.title-level-2 p {
    float: left;
    margin: 0px;
    background-color: orange;
}

.desc-level-2 {
    margin-top: 0px;
    margin-bottom: 10px;
    background-color: yellow;
    text-align: justify;
}

.numbering-no-title {
    margin: 0px;
    background-color: 094293;
}

/* violeta: #8F00FF*/
/* Añil #094293 */
.numbering-no-title p {
    margin: 0px;
    float: left;
    background-color: yellow;
}


.bullet-for-level-2 {
    background-color: blue;
    margin-top: 0px;
    margin-bottom: 0px;
    margin-left: 30px;
}

.numbering-level-3 {
    margin-top: 0px;
    margin-bottom: 3px;
    background-color: blue;
}


/* (END) Terminos y condiciones */

.col-1 {
    width: 8.333333%;
}

.col-2 {
    width: 16.666667%;
}

.col-3 {
    width: 25%;
}

.col-4 {
    width: 33.333333%;
}

.col-5 {
    width: 41.666667%;
}

.col-6 {
    width: 50%;
}

.col-7 {
    width: 58.333333%;
}

.col-8 {
    width: 66.666667%;
}

.col-9 {
    width: 75%;
}

.col-10 {
    width: 83.333333%;
}

.col-11 {
    width: 91.666667%;
}

.col-12 {
    width: 100%;
}

/* stylos para letras y parrafos*/
.font-weight-light {
    font-weight: 300 !important;
}

.font-weight-lighter {
    font-weight: lighter !important;
}

.font-weight-normal {
    font-weight: normal;
}

.font-weight-bold {
    font-weight: bold;
}

.bg-blue {
    background-color: blue;
}

.bg-red {
    background-color: red;
}

.background-yellow {
    background-color: yellow;
}

.background-cyan {
    background-color: cyan;
}

.background-brown {
    background-color: brown;
}


.table {
    width: 100%;
    /*margin-bottom: 1rem;*/
    /*background-color: transparent;*/
}

.table-90 {
    width: 90%;
    /*background-color: transparent;*/
}

.div-centered {
    margin-left: auto;
    margin-right: auto;
}


.float-left {
    float: left;
}

.float-right {
    float: right;
}

.text-left {
    text-align: left !important;
}

.text-right {
    text-align: right !important;
}

.text-center {
    text-align: center !important;
}

table.table-td-mg-10 td {
    padding-left: 10px;
    padding-right: 10px;
}

table.table,
.table th,
.table td {
    border-collapse: collapse;
}

.table td {
    background-color: rgba(212, 221, 228, .15);
    border: 2px solid #fff;
    box-shadow: inset 0 -1px 0 0 rgba(212, 221, 228, .5);
}

.table th {
    border: 2px solid #fff;
    /*border-bottom: 2px solid #d4dde4;*/
    background: #eaeef2;
    /*background: rgba(212,221,228,.5);*/
    /*padding: 2px 8px 4px; */
    padding: 6px 20px;
}

#pdfLogo {
    width: 150px;
    margin: 0px;
    padding: 0px;
}

.img-logo {
    margin: 5px 5px 5px 5px;
    width: 150px;
}

.title-thead {
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 700;
}



.text-tbody {
    font-family: Roboto Condensed;
    font-size: 15px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 1.5em;
    color: #212529;
}

.text-tbody-2 {
    font-family: Roboto Condensed;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    letter-spacing: 0px;
    line-height: 1.5em;
    color: #212529;
    box-sizing: border-box;
}

.title-note-structure {
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 700;
}

.text-note-structure {
    font-family: Roboto Condensed;
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    letter-spacing: 0px;
    line-height: 1.5em;
    color: #25221b;
    box-sizing: border-box;
}

.disclaimer-title {
    font-family: 'Barlow Condensed', sans-serif;
    font-size: 20px;
    text-transform: uppercase;
    letter-spacing: 0px;
    text-transform: none;
    line-height: 1.2em;
    color: #25221b;
}

.disclaimer-contenido {
    font-family: 'Barlow Condensed', sans-serif;
    font-weight: 400;
    font-style: normal;
    font-size: 15px;
    margin: 0;
    margin-top: 0px;
    margin-bottom: 0px;
    padding: 0px;
    text-transform: none;
    text-align: justify;
    letter-spacing: 0px;
    line-height: 1.2em;
    white-space: pre-wrap;
    color: #6f7071;
}