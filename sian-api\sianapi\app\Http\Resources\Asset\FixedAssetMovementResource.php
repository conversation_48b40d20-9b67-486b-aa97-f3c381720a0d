<?php

namespace App\Http\Resources\Asset;

use App\Http\Resources\Administration\SimpleBusinessUnitResource;
use App\Http\Resources\Administration\SimplePersonResource;
use App\Http\Resources\Human\SimpleAreaResource;
use App\Models\FixedAssetMovement;
use Illuminate\Http\Resources\Json\JsonResource;

class FixedAssetMovementResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->fixed_asset_movement_id,
            'fixedAsset' => new SimpleFixedAssetResource($this->fixedAsset),
            'movementType' => FixedAssetMovement::getMovementTypeItems()[$this->movement_type],
            'movementDate' => $this->mov_date,
            'ubication' => $this->ubication,
            'increase' => $this->increase,
            'decrease' => $this->decrease,
            'historicaDepreciation' => $this->historical_depreciation,
            'historicalIncrease' => $this->historical_increase,
            'historicalDecrease' => $this->historical_increase,
            'actualValue' => $this->actual_value,            
            'observation' => $this->observation,
            'businessUnit' => new SimpleBusinessUnitResource($this->businessUnit),
            'area' => new SimpleAreaResource($this->area),
            'responsible' => new SimplePersonResource($this->responsible)
        ];
    }
}
