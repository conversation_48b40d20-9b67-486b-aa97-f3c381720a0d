<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class CommercialMovement
 *
 * @property int $movement_id
 * @property float $munit_quantity
 * @property float $munit_balance
 * @property float $sunit_quantity
 * @property int $merchandise_count
 * @property int $service_count
 * @property float $amortization_pen
 * @property float $interest_pen
 * @property float $desgravamen_pen
 * @property float $oae_adm_pen
 * @property float $oae_financial_pen
 * @property float $oae_sales_pen
 * @property float $porte_pen
 * @property float $itf_pen
 * @property float $other_expenses_pen
 * @property float $crude_pen
 * @property float $affected1_pen
 * @property float $affected2_pen
 * @property float $affected3_pen
 * @property float $affected_pen
 * @property float $inaffected_pen
 * @property float $nobill_pen
 * @property float $export_pen
 * @property float $free_pen
 * @property float $isc_pen
 * @property float $nnet_pen
 * @property float $fnet_pen
 * @property float $mnet_pen
 * @property float $snet_pen
 * @property float $net_pen
 * @property float $nigv_pen
 * @property float $figv_pen
 * @property float $igv_pen
 * @property float $icbp_pen
 * @property float $ntotal_pen
 * @property float $ftotal_pen
 * @property float $total_pen
 * @property float $perception_pen
 * @property float $nreal_pen
 * @property float $freal_pen
 * @property float $real_pen
 * @property float $ret_pen
 * @property float $det_pen
 * @property float $no_ret_pen
 * @property float $credit_balance_pen
 * @property float $amortization_usd
 * @property float $interest_usd
 * @property float $desgravamen_usd
 * @property float $oae_adm_usd
 * @property float $oae_financial_usd
 * @property float $oae_sales_usd
 * @property float $porte_usd
 * @property float $itf_usd
 * @property float $other_expenses_usd
 * @property float $crude_usd
 * @property float $affected1_usd
 * @property float $affected2_usd
 * @property float $affected3_usd
 * @property float $affected_usd
 * @property float $inaffected_usd
 * @property float $nobill_usd
 * @property float $export_usd
 * @property float $free_usd
 * @property float $isc_usd
 * @property float $nnet_usd
 * @property float $fnet_usd
 * @property float $mnet_usd
 * @property float $snet_usd
 * @property float $net_usd
 * @property float $nigv_usd
 * @property float $figv_usd
 * @property float $igv_usd
 * @property float $icbp_usd
 * @property float $ntotal_usd
 * @property float $ftotal_usd
 * @property float $total_usd
 * @property float $perception_usd
 * @property float $nreal_usd
 * @property float $freal_usd
 * @property float $real_usd
 * @property float $ret_usd
 * @property float|null $det_usd
 * @property float $no_ret_usd
 * @property float $credit_balance_usd
 * @property string $condition
 * @property int $credit_days
 * @property string $payment_method
 * @property bool $include_igv
 * @property bool $retention
 * @property bool $detraction
 * @property bool $as_bill
 * @property bool $force_igv
 * @property bool $allow_duplicate
 * @property bool $free_transfer
 * @property float $igv_percent
 * @property float $retention_percent
 * @property float $detraction_percent
 * @property int|null $detraction_code
 * @property int|null $seller_id
 * @property int|null $locker_id
 * @property float $kilos
 * @property float $ad_valorem_pen
 * @property float $ad_valorem_usd
 * @property bool $round_mode
 * @property int $item_count
 * @property string $currency_additional_cost
 * @property float $additional_cost_pen
 * @property float $additional_cost_usd
 * @property bool $assigned_prices
 *
 * @property Locker|null $locker
 * @property Movement $movement
 * @property Seller|null $seller
 * @property Collection|CommercialMovementProduct[] $commercial_movement_products
 * @property PurchaseOrder $purchase_order
 * @property ServiceOrder $service_order
 *
 * @package App\Models
 */
class CommercialMovement extends Model
{
	protected $table = 'commercial_movement';
	protected $primaryKey = 'movement_id';
	public $incrementing = false;
	public $timestamps = false;

    const PAYMENT_METHOD_CASH = 'Efectivo';
    const PAYMENT_METHOD_DEPOSIT = 'Deposito';
    const PAYMENT_METHOD_CHECK = 'Cheque';

	protected $casts = [
		'movement_id' => 'int',
		'munit_quantity' => 'float',
		'munit_balance' => 'float',
		'sunit_quantity' => 'float',
		'merchandise_count' => 'int',
		'service_count' => 'int',
		'amortization_pen' => 'float',
		'interest_pen' => 'float',
		'desgravamen_pen' => 'float',
		'oae_adm_pen' => 'float',
		'oae_financial_pen' => 'float',
		'oae_sales_pen' => 'float',
		'porte_pen' => 'float',
		'itf_pen' => 'float',
		'other_expenses_pen' => 'float',
		'crude_pen' => 'float',
		'affected1_pen' => 'float',
		'affected2_pen' => 'float',
		'affected3_pen' => 'float',
		'affected_pen' => 'float',
		'inaffected_pen' => 'float',
		'nobill_pen' => 'float',
		'export_pen' => 'float',
		'free_pen' => 'float',
		'isc_pen' => 'float',
		'nnet_pen' => 'float',
		'fnet_pen' => 'float',
		'mnet_pen' => 'float',
		'snet_pen' => 'float',
		'net_pen' => 'float',
		'nigv_pen' => 'float',
		'figv_pen' => 'float',
		'igv_pen' => 'float',
		'icbp_pen' => 'float',
		'ntotal_pen' => 'float',
		'ftotal_pen' => 'float',
		'total_pen' => 'float',
		'perception_pen' => 'float',
		'nreal_pen' => 'float',
		'freal_pen' => 'float',
		'real_pen' => 'float',
		'ret_pen' => 'float',
		'det_pen' => 'float',
		'no_ret_pen' => 'float',
		'credit_balance_pen' => 'float',
		'amortization_usd' => 'float',
		'interest_usd' => 'float',
		'desgravamen_usd' => 'float',
		'oae_adm_usd' => 'float',
		'oae_financial_usd' => 'float',
		'oae_sales_usd' => 'float',
		'porte_usd' => 'float',
		'itf_usd' => 'float',
		'other_expenses_usd' => 'float',
		'crude_usd' => 'float',
		'affected1_usd' => 'float',
		'affected2_usd' => 'float',
		'affected3_usd' => 'float',
		'affected_usd' => 'float',
		'inaffected_usd' => 'float',
		'nobill_usd' => 'float',
		'export_usd' => 'float',
		'free_usd' => 'float',
		'isc_usd' => 'float',
		'nnet_usd' => 'float',
		'fnet_usd' => 'float',
		'mnet_usd' => 'float',
		'snet_usd' => 'float',
		'net_usd' => 'float',
		'nigv_usd' => 'float',
		'figv_usd' => 'float',
		'igv_usd' => 'float',
		'icbp_usd' => 'float',
		'ntotal_usd' => 'float',
		'ftotal_usd' => 'float',
		'total_usd' => 'float',
		'perception_usd' => 'float',
		'nreal_usd' => 'float',
		'freal_usd' => 'float',
		'real_usd' => 'float',
		'ret_usd' => 'float',
		'det_usd' => 'float',
		'no_ret_usd' => 'float',
		'credit_balance_usd' => 'float',
		'credit_days' => 'int',
		'include_igv' => 'bool',
		'retention' => 'bool',
		'detraction' => 'bool',
		'as_bill' => 'bool',
		'force_igv' => 'bool',
		'allow_duplicate' => 'bool',
		'free_transfer' => 'bool',
		'igv_percent' => 'float',
		'retention_percent' => 'float',
		'detraction_percent' => 'float',
		'detraction_code' => 'int',
		'seller_id' => 'int',
		'locker_id' => 'int',
		'kilos' => 'float',
		'ad_valorem_pen' => 'float',
		'ad_valorem_usd' => 'float',
		'round_mode' => 'bool',
		'item_count' => 'int',
		'additional_cost_pen' => 'float',
		'additional_cost_usd' => 'float',
		'assigned_prices' => 'bool'
	];

	protected $fillable = [
		'munit_quantity',
		'munit_balance',
		'sunit_quantity',
		'merchandise_count',
		'service_count',
		'amortization_pen',
		'interest_pen',
		'desgravamen_pen',
		'oae_adm_pen',
		'oae_financial_pen',
		'oae_sales_pen',
		'porte_pen',
		'itf_pen',
		'other_expenses_pen',
		'crude_pen',
		'affected1_pen',
		'affected2_pen',
		'affected3_pen',
		'affected_pen',
		'inaffected_pen',
		'nobill_pen',
		'export_pen',
		'free_pen',
		'isc_pen',
		'nnet_pen',
		'fnet_pen',
		'mnet_pen',
		'snet_pen',
		'net_pen',
		'nigv_pen',
		'figv_pen',
		'igv_pen',
		'icbp_pen',
		'ntotal_pen',
		'ftotal_pen',
		'total_pen',
		'perception_pen',
		'nreal_pen',
		'freal_pen',
		'real_pen',
		'ret_pen',
		'det_pen',
		'no_ret_pen',
		'credit_balance_pen',
		'amortization_usd',
		'interest_usd',
		'desgravamen_usd',
		'oae_adm_usd',
		'oae_financial_usd',
		'oae_sales_usd',
		'porte_usd',
		'itf_usd',
		'other_expenses_usd',
		'crude_usd',
		'affected1_usd',
		'affected2_usd',
		'affected3_usd',
		'affected_usd',
		'inaffected_usd',
		'nobill_usd',
		'export_usd',
		'free_usd',
		'isc_usd',
		'nnet_usd',
		'fnet_usd',
		'mnet_usd',
		'snet_usd',
		'net_usd',
		'nigv_usd',
		'figv_usd',
		'igv_usd',
		'icbp_usd',
		'ntotal_usd',
		'ftotal_usd',
		'total_usd',
		'perception_usd',
		'nreal_usd',
		'freal_usd',
		'real_usd',
		'ret_usd',
		'det_usd',
		'no_ret_usd',
		'credit_balance_usd',
		'condition',
		'credit_days',
		'payment_method',
		'include_igv',
		'retention',
		'detraction',
		'as_bill',
		'force_igv',
		'allow_duplicate',
		'free_transfer',
		'igv_percent',
		'retention_percent',
		'detraction_percent',
		'detraction_code',
		'seller_id',
		'locker_id',
		'kilos',
		'ad_valorem_pen',
		'ad_valorem_usd',
		'round_mode',
		'item_count',
		'currency_additional_cost',
		'additional_cost_pen',
		'additional_cost_usd',
		'assigned_prices'
	];

	public function locker()
	{
		return $this->belongsTo(Locker::class);
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function seller()
	{
		return $this->belongsTo(Seller::class);
	}

	public function commercial_movement_products()
	{
		return $this->hasMany(CommercialMovementProduct::class, 'movement_id');
	}

	public function purchase_order()
	{
		return $this->hasOne(PurchaseOrder::class, 'movement_id');
	}

	public function service_order()
	{
		return $this->hasOne(ServiceOrder::class, 'movement_id');
	}
}
