<?php

namespace App\Http\Controllers\Api\V1\Administration;

use App\Models\DataProviders\DpBusinessPartner;
use App\Models\Owner;
use Illuminate\Http\Request;
use App\Models\Person;
use App\Http\Controllers\Controller;
use App\Http\Resources\Administration\PersonResource;
use App\Http\Resources\Administration\SimplePersonResource;

class PersonController extends Controller {
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getItems(Request $request) {
        $a_exclude_type = [Person::OTRO];

        if (isset($request->type)) {
            if ($request->type == Person::TYPE_NATURAL) {
                $a_exclude_type = [Person::RUC, Person::OTRO];
            }
        }
        $a_person = Person::where("status", "=", 1)
            ->whereNotIn('identification_type', $a_exclude_type)
            ->get();

        $a_response = [
            'success' => true,
            'data' => [
                'items' => SimplePersonResource::collection($a_person)
            ]
        ];
        return response()->json($a_response);
    }

    public function getProviderItems(Request $request) {
        try {

            $dpBusinessPartner = DpBusinessPartner::withScenario(DpBusinessPartner::SCENARIO_PROVIDER)
                ->where('status', 1);

            if ($request->has('keyword')) {
                $keyword = $request->input('keyword');
                $dpBusinessPartner->where('P.person_name', 'like', '%' . $keyword . '%');
            }

            if ($request->has('provider')) {
                $providerSelected = $request->input('provider');
                $arrayProvider = explode(",", $providerSelected);
                $dpBusinessPartner->whereNotIn("P.person_id", $arrayProvider);
            }

            if ($request->has('limit')) {
                $limit = $request->input('limit');
                $dpBusinessPartner->limit($limit);
            } else {
                $dpBusinessPartner->limit(10);
            }


            $activeProviders = $dpBusinessPartner->get();


            return response()->json([
                'success' => true,
                'data' => $activeProviders
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);

        }
    }
}
