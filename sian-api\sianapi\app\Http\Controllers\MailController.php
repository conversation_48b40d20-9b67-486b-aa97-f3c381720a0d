<?php

namespace App\Http\Controllers;

use App\Models\EmailQueue;
use App\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Resend\Laravel\Facades\Resend;

class MailController extends Controller {

    public function sendEmail(Request $request, $limit = null) {
        try {
            $domain = Tenant::getDomain($request);
            $pendingEmails = EmailQueue::getEmailQueueWithEmails($limit);
            $responses = [];
            $env = app()->environment();

            foreach ($pendingEmails as $pending_email) {
                if ($env !== 'production') {
                    $recipientEmails = [env('HELPDESK_EMAIL')];
                    $senderEmail = env('MAIL_FROM_ADDRESS');
                } else {
                    $recipientEmails = collect($pending_email->reciver_emails)->pluck('email_address')->all();
                    $senderEmail = $pending_email->email_address;
                }

                $attachments = [];

                if (count($pending_email->attachments) > 0) {
                    $a_response = $this->getResources([$pending_email->email_queue_id], $domain);
                    if (isset($a_response)) {
                        $o_files = $a_response->data;
                        $a_files = $o_files->{$pending_email->email_queue_id};
                        foreach ($a_files as $o_file) {
                            $base64Raw = preg_replace('#^data:.*;base64,#', '', $o_file->base64);
                            $attachments[] = [
                                'filename' => $o_file->name,
                                'content' => $base64Raw,
                            ];
                        }
                    }
                }

                $fixedHtml = preg_replace_callback(
                    '#(href|src)=["\'](/admin/[^"\']+)["\']#i',
                    function ($matches) use ($domain) {
                        return $matches[1] . '="' . rtrim($domain, '/') . $matches[2] . '"';
                    },
                    $pending_email->message
                );

                $response = Resend::emails()->send([
                    'from' => $senderEmail,
                    'to' => $recipientEmails,
                    'subject' => $pending_email->subject,
                    'html' => $fixedHtml,
                    'attachments' => $attachments
                ]);

                if (isset($response['id'])) {
                    EmailQueue::where('email_queue_id', $pending_email->email_queue_id)
                        ->update(['state' => EmailQueue::STATE_SUCCESS, 'sent_date' => now(), 'response' => $response['id']]);
                    $responses[$pending_email->email_queue_id] = $response;
                }
            }
            return response()->json(['message' => 'Se enviaron ' . count($responses) . 'correos exitosamente', 'resend_responses' => $responses]);
        } catch (\Throwable $e) {
            return response()->json(['error' => 'Error al enviar el correo: ' . $e->getMessage()], 500);
        }
    }

    public function getResources($idsResources, $domain) {

        $url = $domain . '/admin/apiSian/resources';

        $appResponse = SianController::authenticateAPI($domain);
        $appToken = $appResponse['appToken'];

        $response = Http::withoutVerifying()->withHeaders([
            'Content-Type' => 'application/json',
            'App-Authorization' => $appToken,
        ])->post($url, ['idsResources' => $idsResources]);

        if (!$response->successful()) {
            return null;
        }
        return json_decode($response->body());
    }
}
