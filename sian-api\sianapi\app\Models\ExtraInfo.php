<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ExtraInfo
 * 
 * @property int $movement_id
 * @property string|null $movement_name
 * @property Carbon|null $delivery_date
 * @property string|null $purchase_order
 * @property string|null $contact_name
 * @property string|null $contact_email
 * @property string|null $contact_phone
 * @property int|null $warehouse_id
 * @property int|null $cashbox_id
 * @property string|null $sunat_code
 * @property string|null $shipping_type
 * @property bool|null $manual_address
 * @property int|null $combination_id
 * @property bool|null $kardex_rlock
 * @property bool|null $kardex_clock
 * 
 * @property Cashbox|null $cashbox
 * @property Combination|null $combination
 * @property Movement $movement
 * @property Warehouse|null $warehouse
 *
 * @package App\Models
 */
class ExtraInfo extends Model
{
	protected $table = 'extra_info';
	protected $primaryKey = 'movement_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'warehouse_id' => 'int',
		'cashbox_id' => 'int',
		'manual_address' => 'bool',
		'combination_id' => 'int',
		'kardex_rlock' => 'bool',
		'kardex_clock' => 'bool'
	];

	protected $dates = [
		'delivery_date'
	];

	protected $fillable = [
		'movement_name',
		'delivery_date',
		'purchase_order',
		'contact_name',
		'contact_email',
		'contact_phone',
		'warehouse_id',
		'cashbox_id',
		'sunat_code',
		'shipping_type',
		'manual_address',
		'combination_id',
		'kardex_rlock',
		'kardex_clock'
	];

	public function cashbox()
	{
		return $this->belongsTo(Cashbox::class);
	}

	public function combination()
	{
		return $this->belongsTo(Combination::class);
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function warehouse()
	{
		return $this->belongsTo(Warehouse::class);
	}
}
