<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class EntryDetail
 * 
 * @property int|null $movement_id
 * @property int|null $aux_person_id
 * @property string|null $account_code
 * @property float|null $amount
 *
 * @package App\Models
 */
class EntryDetail extends Model
{
	protected $table = 'entry_detail';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'aux_person_id' => 'int',
		'amount' => 'float'
	];

	protected $fillable = [
		'movement_id',
		'aux_person_id',
		'account_code',
		'amount'
	];
}
