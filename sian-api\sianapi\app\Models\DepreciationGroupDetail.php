<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class DepreciationGroupDetail
 * 
 * @property int $depreciation_group_detail_id
 * @property int $depreciation_group_id
 * @property int $year
 * @property int $annual_rate
 * @property float $period01
 * @property float $period02
 * @property float $period03
 * @property float $period04
 * @property float $period05
 * @property float $period06
 * @property float $period07
 * @property float $period08
 * @property float $period09
 * @property float $period10
 * @property float $period11
 * @property float $period12
 * 
 * @property DepreciationGroup $depreciation_group
 *
 * @package App\Models
 */
class DepreciationGroupDetail extends Model
{
	protected $table = 'depreciation_group_detail';
	protected $primaryKey = 'depreciation_group_detail_id';
	public $timestamps = false;

	protected $casts = [
		'depreciation_group_id' => 'int',
		'year' => 'int',
		'annual_rate' => 'float',
		'period01' => 'float',
		'period02' => 'float',
		'period03' => 'float',
		'period04' => 'float',
		'period05' => 'float',
		'period06' => 'float',
		'period07' => 'float',
		'period08' => 'float',
		'period09' => 'float',
		'period10' => 'float',
		'period11' => 'float',
		'period12' => 'float'
	];

	protected $fillable = [
		'depreciation_group_id',
		'year',
		'annual_rate',
		'period01',
		'period02',
		'period03',
		'period04',
		'period05',
		'period06',
		'period07',
		'period08',
		'period09',
		'period10',
		'period11',
		'period12'
	];

	public function depreciation_group()
	{
		return $this->belongsTo(DepreciationGroup::class);
	}
}
