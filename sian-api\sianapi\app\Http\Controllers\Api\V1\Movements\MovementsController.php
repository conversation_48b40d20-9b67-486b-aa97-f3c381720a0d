<?php

namespace App\Http\Controllers\Api\V1\Movements;

use App\Models\Procedures\SpGetMovementReferences;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;


class MovementsController extends Controller {
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getReferences($movementID) {
        try {

            $data = SpGetMovementReferences::execute($movementID);

            return response()->json([
                'success' => true,
                'data' => $data,

            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }
}
