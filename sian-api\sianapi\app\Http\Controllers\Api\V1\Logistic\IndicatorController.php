<?php

namespace App\Http\Controllers\Api\V1\Logistic;

use App\Models\Presentation;
use App\Models\Product;
use App\Models\ProductLink;
use App\Models\Recipe;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\Fake\Currency;
use App\Models\Procedures\SpGetProductPresentations;
use App\Http\Controllers\Controller;
use PhpOffice\PhpSpreadsheet\IOFactory;


class IndicatorController extends Controller
{

    private $recipeCache = [];
    private $presentationCache = [];
    private $productLinkCache = [];
    private $productCache = [];
    private $derivedProductsIds = [];

    public const SUPPLY_RECIPE = 'supplyRecipe';
    public const DERIVED_PRODUCT = 'derivedProduct';
    public const RAW_MATERIAL = 'rawMaterial';
    public const SUPPLY = 'supply';


    public function getReposition(Request $request)
    {
        // Aumentar tiempo de ejecución para stored procedures que demoran
        set_time_limit(300); // 5 minutos
        ini_set('max_execution_time', 300);

        try {
            $validate = Validator::make($request->all(), [
                "page" => "required|integer",
                "pageSize" => "required|integer",
                'store' => 'required|integer',
                'daysOfReposition' => 'required|integer',
                'daysMinStock' => 'required|integer',
                'daysToOrder' => 'required|integer'
            ]);

            if ($validate->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validate->errors()
                ], 400);
            }

            $s_providerIds = "";
            $s_divisionIds = "";
            $s_lineIds = "";
            $s_sublineIds = "";
            $s_productIds = "";
            $s_sort = "";
            $s_order = "";
            $s_break_scale = "";
            $s_rotation_scale = "";
            $s_product_type = $request->input('mode', 'Merc');

            if ($request->has('provider')) {
                $s_providerIds = $request->input('provider');
            }
            if ($request->has('division')) {
                $s_divisionIds = $request->input('division');
            }
            if ($request->has('line')) {
                $s_lineIds = $request->input('line');
            }
            if ($request->has('subline')) {
                $s_sublineIds = $request->input('subline');
            }
            if ($request->has(key: 'product')) {
                $s_productIds = $request->input('product');
            }

            if ($request->has('sort')) {
                $s_sort = $request->input('sort');
            }
            if ($request->has('order')) {
                $s_order = $request->input('order');
            }

            if ($request->has('breakScale')) {
                $break_scale_array = explode(',', $request->input('breakScale'));
                $s_break_scale = "'" . implode("','", $break_scale_array) . "'";
            }

            if ($request->has('rotationScale')) {
                $rotation_scale_array = explode(',', $request->input('rotationScale'));
                $s_rotation_scale = "'" . implode("','", $rotation_scale_array) . "'";
            }

            if ($s_product_type !== 'Food') {

                $a_params = [
                    ':xprovider_ids' => $s_providerIds,
                    ':xdivision_ids' => $s_divisionIds,
                    ':xline_ids' => $s_lineIds,
                    ':xsubline_ids' => $s_sublineIds,
                    ':xproduct_ids' => $s_productIds,
                    ':xsort' => $s_sort,
                    ':xorder' => $s_order,
                    ':xlimit' => 0,
                    ':xoffset' => 0,
                    ':xrotation_scale' => $s_rotation_scale,
                    ':xbreak_scale' => $s_break_scale,
                    ':xproduct_type' => $s_product_type
                ];


                $a_total_result = DB::select(
                    "call sp_get_reposition_products (:xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xrotation_scale, :xbreak_scale, :xsort, :xorder, :xlimit, :xoffset, :xproduct_type)",
                    $a_params
                );

                $i_pageSize = intval($request->input('pageSize'));
                $i_page = intval($request->input('page'));
                $i_offset = ($i_page - 1) * $i_pageSize;
                $i_totalItems = count($a_total_result);

                $a_params[':xlimit'] = $i_pageSize;
                $a_params[':xoffset'] = $i_offset;

                $a_result = DB::select(
                    "call sp_get_reposition_products(:xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xrotation_scale, :xbreak_scale, :xsort, :xorder, :xlimit, :xoffset, :xproduct_type)",
                    $a_params
                );

                $ids = [];
                foreach ($a_result as $o_row) {
                    $ids[] = $o_row->product_id;
                }

                $a_productPresentations = SpGetProductPresentations::getAssociative(
                    SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES,
                    $ids,
                    Currency::PEN,
                    0
                );



                if ($s_product_type === 'Merc2') {
                    $a_analisys_params = [
                        ':xstore_id' => $request->input('store'),
                        ':xdays_min_stock' => $request->input('daysMinStock'),
                        ':xdays_reposition' => $request->input('daysOfReposition'),
                        ':xprovider_ids' => "",
                        ':xdivision_ids' => "",
                        ':xline_ids' => "",
                        ':xsubline_ids' => "",
                        ':xproduct_ids' => "",
                        ':xscale_rotation' => "",
                        ':xexpiration_alert' => "",
                        ':xobsolete_alert' => "",
                        ':xestimated_order' => "",
                        ':xdays_to_order' => intval($request->input('daysToOrder', 1)),
                        ':xsort' => "",
                        ':xorder' => "",
                        ':xlimit' => 0,
                        ':xoffset' => 0,
                        ':xproduct_type' => "Merc2"
                    ];

                    $a_analisys = DB::select(
                        "call sp_get_reposition_analysis (:xstore_id, :xdays_min_stock, :xdays_reposition, :xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xscale_rotation, :xexpiration_alert, :xobsolete_alert, :xestimated_order, :xdays_to_order, :xsort, :xorder, :xlimit, :xoffset, :xproduct_type)",
                        $a_analisys_params
                    );

                    $grouped_analisys = [];

                    foreach ($a_analisys as $item) {
                        $productId = $item->product_id ?? null;
                        if (!$productId)
                            continue;

                        $grouped_analisys[$productId][] = $item;
                    }
                }

                foreach ($a_result as &$o_row) {
                    $o_row->product_id = intval($o_row->product_id);
                    $o_row->presentations = $a_productPresentations[$o_row->product_id] ?? [];

                    if ($s_product_type === 'Merc2') {
                        if (!isset($o_row->unit_quantity_order)) {
                            $o_row->unit_quantity_order = 0;
                        }

                        $analisysRows = $grouped_analisys[$o_row->product_id] ?? [];

                        foreach ($analisysRows as $analysis) {
                            $o_row->unit_quantity_order += floatval($analysis->unit_quantity_order ?? 0);
                        }

                        $o_row->analisys = $analisysRows;
                    }
                }

                $a_response = $a_response = [
                    'success' => false,
                    'data' => []
                ];

                if ($i_totalItems > 0) {

                    $a_response = [
                        'success' => true,
                        'pagination' => [
                            'page' => $i_page,
                            'pageSize' => $i_pageSize,
                            'totalRecords' => $i_totalItems,
                            'totalPages' => ceil($i_totalItems / $i_pageSize)
                        ],
                        'data' => $a_result
                    ];
                }
                return response()->json($a_response);
            } else {
                $a_supplys = $this->getSupplyData($request);

                return response()->json([
                    'success' => true,
                    'data' => $a_supplys,
                ]);
            }
        } catch (\Exception $ex) {
            Log::error('Error reposition: ' . $ex->getTraceAsString());
            return response()->json([
                'success' => false,
                'error' => 'Error reposition: ' . $ex->getTraceAsString(),
            ], 500);
        }
    }

    public function getRepositionByProduct(Request $request)
    {
        set_time_limit(300);
        ini_set('max_execution_time', 300);

        try {
            $validate = Validator::make($request->all(), [
                "page" => "required|integer",
                "pageSize" => "required|integer",
                'store' => 'required|integer',
                'daysOfReposition' => 'required|integer',
                'daysMinStock' => 'required|integer',
                'daysToOrder' => 'required|integer'
            ]);

            if ($validate->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validate->errors()
                ], 400);
            }

            $s_providerIds = "";
            $s_divisionIds = "";
            $s_lineIds = "";
            $s_sublineIds = "";
            $s_scaleRotation = "";
            $s_expirationAlert = "2";
            $s_obsoleteAlert = "2";
            $s_estimatedOrder = "";
            $i_daysToOrder = 1;
            $s_sort = "";
            $s_order = "";
            $s_productIds = "";

            if ($request->has('provider')) {
                $s_providerIds = $request->input('provider');
            }
            if ($request->has('division')) {
                $s_divisionIds = $request->input('division');
            }
            if ($request->has('line')) {
                $s_lineIds = $request->input('line');
            }
            if ($request->has('subline')) {
                $s_sublineIds = $request->input('subline');
            }
            if ($request->has(key: 'product')) {
                $s_productIds = $request->input('product');
            }
            if ($request->has('scaleRotation')) {
                $s_scaleRotation = $request->input('scaleRotation');
            }
            if ($request->has('expirationAlert')) {
                $s_expirationAlert = $request->input('expirationAlert');
            }
            if ($request->has('obsoleteAlert')) {
                $s_obsoleteAlert = $request->input('obsoleteAlert');
            }
            if ($request->has('estimatedOrder')) {
                $s_estimatedOrder = $request->input('estimatedOrder');
            }
            if ($request->has('daysToOrder')) {
                $i_daysToOrder = intval($request->input('daysToOrder'));
                if ($i_daysToOrder <= 0) {
                    $i_daysToOrder = 1;
                }
            }
            if ($request->has('sort')) {
                $s_sort = $request->input('sort');
            }
            if ($request->has('order')) {
                $s_order = $request->input('order');
            }


            $a_params = [
                ':xstore_id' => $request->input('store'),
                ':xdays_min_stock' => $request->input('daysMinStock'),
                ':xdays_reposition' => $request->input('daysOfReposition'),
                ':xprovider_ids' => $s_providerIds,
                ':xdivision_ids' => $s_divisionIds,
                ':xline_ids' => $s_lineIds,
                ':xsubline_ids' => $s_sublineIds,
                ':xproduct_ids' => $s_productIds,
                ':xscale_rotation' => $s_scaleRotation,
                ':xexpiration_alert' => $s_expirationAlert,
                ':xobsolete_alert' => $s_obsoleteAlert,
                ':xestimated_order' => $s_estimatedOrder,
                ':xdays_to_order' => $i_daysToOrder,
                ':xsort' => $s_sort,
                ':xorder' => $s_order,
                ':xlimit' => 0,
                ':xoffset' => 0,
                ':xproduct_type' => $request->input('mode', 'Merc')
            ];


            $a_total_result = DB::select(
                "call sp_get_reposition_analysis (:xstore_id, :xdays_min_stock, :xdays_reposition, :xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xscale_rotation, :xexpiration_alert, :xobsolete_alert, :xestimated_order, :xdays_to_order, :xsort, :xorder, :xlimit, :xoffset, :xproduct_type)",
                $a_params
            );

            $i_pageSize = intval($request->input('pageSize'));
            $i_page = intval($request->input('page'));
            $i_offset = ($i_page - 1) * $i_pageSize;
            $i_totalItems = count($a_total_result);

            $a_params[':xlimit'] = $i_pageSize;
            $a_params[':xoffset'] = $i_offset;

            $a_result = DB::select(
                "call sp_get_reposition_analysis (:xstore_id, :xdays_min_stock, :xdays_reposition, :xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xscale_rotation, :xexpiration_alert, :xobsolete_alert, :xestimated_order, :xdays_to_order, :xsort, :xorder, :xlimit, :xoffset, :xproduct_type)",
                $a_params
            );

            $a_response = $a_response = [
                'success' => true,
                'data' => $a_result,
            ];

            return response()->json($a_response);
        } catch (\Exception $ex) {
            Log::error('Error reposition: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error reposition: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function getRotation(Request $request)
    {
        try {
            $validate = Validator::make($request->all(), [
                'store' => 'required|integer',
                'product' => 'required|integer'
            ]);

            if ($validate->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validate->errors()
                ], 400);
            }

            $a_params = [
                ':xstore_id' => intval($request->input('store')),
                ':xproduct_id' => intval($request->input('product')),
                //':xlimit' => 0,
                //':xoffset' => 0,
            ];

            $a_result = DB::select(
                "call sp_get_current_rotation (:xstore_id, :xproduct_id)",
                $a_params
            );

            $a_response = $a_response = [
                'success' => false,
                'data' => []
            ];

            if (count($a_result) > 0) {
                $a_response = [
                    'success' => true,
                    'data' => [
                        'values' => $a_result
                    ]
                ];
            }
            return response()->json($a_response);
        } catch (\Exception $ex) {
            Log::error('Error reposition: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error reposition: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function exportOcExcel(Request $request)
    {
        try {
            $data = $request->input('data');

            $templatePath = storage_path('app/templates/RepositionOC.xlsx');
            $spreadsheet = IOFactory::load($templatePath);
            $sheet = $spreadsheet->getActiveSheet();

            $rowStart = 2;
            foreach ($data as $rowIndex => $rowData) {
                $columnStart = 'A';
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['product_id']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['product_name']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['provider']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['equivalence']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['measure_name']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['6']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['8']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['9']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['10'] ?? 0);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['total_quantity']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['unit_price']);
                $columnStart++;

                $rowStart++;
            }

            // Crear el escritor de Excel y definir el nombre del archivo
            $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
            $dateTime = now()->format('Y-m-d_H-i-s');
            $filename = "Reposicion_{$dateTime}.xlsx";

            // Retornar el archivo para su descarga
            return response()->stream(
                function () use ($writer) {
                    $writer->save('php://output');
                },
                200,
                [
                    'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                    'Content-FileName' => $filename,
                ]
            );
        } catch (\Exception $ex) {
            Log::error('Error exporting to Excel: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error exporting to Excel: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function exportOtaExcel(Request $request)
    {
        try {
            $data = $request->input('data');

            $templatePath = storage_path('app/templates/RepositionOTA.xlsx');
            $spreadsheet = IOFactory::load($templatePath);
            $sheet = $spreadsheet->getActiveSheet();

            $rowStart = 2;
            foreach ($data as $rowIndex => $rowData) {
                $columnStart = 'A';
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['product_id']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['product_name']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['equivalence']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['measure_name']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['quantity']);
                $columnStart++;
                $rowStart++;
            }

            // Crear el escritor de Excel y definir el nombre del archivo
            $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
            $dateTime = now()->format('Y-m-d_H-i-s');
            $filename = "Reposicion_{$dateTime}.xlsx";

            // Retornar el archivo para su descarga
            return response()->stream(
                function () use ($writer) {
                    $writer->save('php://output');
                },
                200,
                [
                    'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                    'Content-FileName' => $filename,
                ]
            );
        } catch (\Exception $ex) {
            // Manejar el error
            Log::error('Error exporting to Excel: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error exporting to Excel: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function getSupplys(Request $request)
    {
        set_time_limit(300);
        ini_set('max_execution_time', 300);

        $data = $request->json()->all();

        if (!$data) {
            return response()->json(['error' => 'No se recibió JSON válido'], 400);
        }

        try {
            $items = array_values($data);
            $grouped = [];

            foreach ($items as $item) {
                $productId = $item['product_id'] ?? null;
                if (!$productId)
                    continue;
                $grouped[$productId][] = $item;
            }

            $allSupplys = [];

            $this->derivedProductsIds = $this->getAllDerivedProductIds();

            foreach ($grouped as $product_id => $array_by_stores) {
                foreach ($array_by_stores as $store) {
                    $store_id = $store['store_id'];
                    $quantity_oc = floatval($store['quantity_oc']);

                    $derivedProductsCapture = [];
                    $supplys = $this->getPlainSupplys($product_id, $quantity_oc, $store_id, $this->getRecipe($product_id), $quantity_oc, [self::DERIVED_PRODUCT], false, false, $derivedProductsCapture);

                    foreach ($supplys as $id => $supply) {
                        if (!isset($allSupplys[$id])) {
                            $allSupplys[$id] = $supply;
                        } else {
                            $allSupplys[$id]['quantity'] += $supply['quantity'];
                            $allSupplys[$id]['recipes'] = array_merge($allSupplys[$id]['recipes'], $supply['recipes']);
                            foreach ($supply['quantities_by_store'] as $sid => $qty) {
                                $allSupplys[$id]['quantities_by_store'][$sid] = ($allSupplys[$id]['quantities_by_store'][$sid] ?? 0) + $qty;
                            }
                        }
                    }

                    // Agregar productos derivados capturados a allSupplys para su posterior procesamiento
                    foreach ($derivedProductsCapture as $derivedId => $derivedQty) {
                        if (!isset($allSupplys[$derivedId])) {
                            $allSupplys[$derivedId] = [
                                'quantity' => $derivedQty,
                                'unit_quantity_proyected' => $derivedQty,
                                'recipes' => [],
                                'quantities_by_store' => [$store->store_id => $derivedQty]
                            ];
                        } else {
                            $allSupplys[$derivedId]['quantity'] += $derivedQty;
                            $allSupplys[$derivedId]['unit_quantity_proyected'] += $derivedQty;
                            $allSupplys[$derivedId]['quantities_by_store'][$store->store_id] = ($allSupplys[$derivedId]['quantities_by_store'][$store->store_id] ?? 0) + $derivedQty;
                        }
                    }
                }
            }

            $productIds = implode(',', array_keys($allSupplys));

            $a_params = [
                ':xproduct_ids' => $productIds,
            ];

            $a_result = DB::select(
                "call sp_get_reposition_supply(:xproduct_ids)",
                $a_params
            );

            $a_productPresentations = SpGetProductPresentations::getAssociative(
                SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES,
                $productIds,
                Currency::PEN,
                0
            );

            foreach ($a_result as &$o_row) {
                $o_row->product_id = intval($o_row->product_id);
                $o_row->presentations = $a_productPresentations[$o_row->product_id] ?? [];
                $o_row = array_merge((array) $o_row, $allSupplys[$o_row->product_id]);
            }

            return response()->json([
                'success' => true,
                'data' => $a_result,
            ]);
        } catch (\Exception $ex) {
            Log::error('Error reposition: ' . $ex->getTraceAsString());
            return response()->json([
                'success' => false,
                'error' => 'Error reposition: ' . $ex->getTraceAsString(),
            ], 500);
        }
    }

    public function getPlainSupplys(
        $product_id,
        $quantity,
        $store_id,
        $main_recipe,
        $main_recipe_quantity,
        array $excludedTypes = [],
        $onlyFromDerived = false,
        $inheritedFromDerived = false,
        &$derivedProductsCapture = []
    ) {
        $recipe = $this->getRecipe($product_id);
        $supplys = $this->getProductLinks($product_id);

        switch (true) {
            case ($recipe && $supplys->isNotEmpty()):
                $type = self::SUPPLY_RECIPE;
                break;
            case (!$recipe && $supplys->isNotEmpty()):
                $type = self::DERIVED_PRODUCT;
                break;
            case (!$recipe && $supplys->isEmpty()):
                $type = self::SUPPLY;
                break;
            default:
                $type = 'unknown';
        }

        if (in_array($type, $excludedTypes)) {
            return [];
        }

        $results = [];
        $factor = 1;

        if ($recipe) {
            $output_quantity = (float) $recipe->pres_quantity * (float) $recipe->equivalence;
            $factor = $output_quantity > 0 ? ($quantity / $output_quantity) : 1;
        } else {
            $presentation = $this->getDefaultPresentation($product_id);
            if ($presentation && intval($presentation->equivalence) > 0) {
                $factor = $presentation->equivalence / $quantity;
            }
        }

        if ($supplys->isNotEmpty()) {

            foreach ($supplys as $supply) {
                $requiredQty = $supply->quantity * $factor;

                $subResults = $this->getPlainSupplys(
                    $supply->product_id,
                    $requiredQty,
                    $store_id,
                    $main_recipe,
                    $main_recipe_quantity,
                    $excludedTypes,
                    $onlyFromDerived,
                    $onlyFromDerived && $,
                    $derivedProductsCapture
                );

                foreach ($subResults as $id => $sub) {

                    $isDerived = (!$recipe && $supplys->isNotEmpty());

                    // Capturar producto derivado si es el caso
                    if ($isDerived) {
                        $derivedProductsCapture[$product_id] = ($derivedProductsCapture[$product_id] ?? 0) + $quantity;
                        Log::info("Producto derivado capturado: ID={$product_id}, Cantidad={$quantity}, Store={$store_id}");
                    }



                    if (!isset($results[$id])) {
                        $results[$id] = $sub;
                    } else {
                        $results[$id]['unit_quantity_proyected'] += $sub['unit_quantity_proyected'];

                        foreach ($sub['recipes'] as $rid => $recipeData) {
                            if (!isset($results[$id]['recipes'][$rid])) {
                                $results[$id]['recipes'][$rid] = $recipeData;
                            } else {
                                $results[$id]['recipes'][$rid]['quantity'] += $recipeData['quantity'];
                                $results[$id]['recipes'][$rid]['recipe_quantity'] += $recipeData['recipe_quantity'];
                            }
                        }

                        foreach ($sub['quantities_by_store'] as $sid => $qty) {
                            $results[$id]['quantities_by_store'][$sid] = ($results[$id]['quantities_by_store'][$sid] ?? 0) + $qty;
                        }
                    }
                }
            }
        } else {
            if ($onlyFromDerived && !$inheritedFromDerived) {
                return [];
            }

            $recipes = [];

            if (!isset($recipes[$main_recipe->recipe_id])) {
                $recipes[$main_recipe->recipe_id] = [
                    'parent_product' => $this->getProduct($main_recipe->product_id)->product_name,
                    'recipe_quantity' => $main_recipe_quantity,
                    'quantity' => $quantity,
                    'recipe_id' => $main_recipe->recipe_id,
                    'product_id' => $main_recipe->product_id
                ];
            } else {
                $recipes[$main_recipe->recipe_id]['recipe_quantity'] += $main_recipe_quantity;
                $recipes[$main_recipe->recipe_id]['quantity'] += $quantity;
            }

            $results[$product_id] = [
                'product_id' => $product_id,
                'unit_quantity_proyected' => $quantity,
                'type' => $type,
                'recipes' => $recipes,
                'quantities_by_store' => [
                    $store_id => $quantity
                ]
            ];
        }

        return $results;
    }

    protected function getRecipe($product_id)
    {
        if (!array_key_exists($product_id, $this->recipeCache)) {
            $this->recipeCache[$product_id] = Recipe::where('product_id', $product_id)->first();
        }
        return $this->recipeCache[$product_id];
    }

    protected function getProduct($product_id)
    {
        if (!array_key_exists($product_id, $this->productCache)) {
            $this->productCache[$product_id] = Product::where('product_id', $product_id)->first();
        }
        return $this->productCache[$product_id];
    }

    protected function getProductLinks($product_id)
    {
        if (!array_key_exists($product_id, $this->productLinkCache)) {
            $this->productLinkCache[$product_id] = ProductLink::where('product_parent_id', $product_id)->get();
        }
        return $this->productLinkCache[$product_id];
    }

    protected function getDefaultPresentation($product_id)
    {
        if (!array_key_exists($product_id, $this->presentationCache)) {
            $this->presentationCache[$product_id] = Presentation::where('product_id', $product_id)
                ->where('default', 1)
                ->first();
        }
        return $this->presentationCache[$product_id];
    }

    public function getRepositionBySupply(Request $request)
    {
        try {
            $validate = Validator::make($request->all(), [
                'store' => 'required|integer',
                'daysMinStock' => 'required|integer',
                'daysOfReposition' => 'required|integer'
            ]);

            if ($validate->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validate->errors()
                ], 400);
            }

            $s_productIds = "";

            if ($request->has(key: 'product')) {
                $s_productIds = $request->input('product');
            }

            $a_params = [
                ':xstore_id' => $request->input('store'),
                ':xdays_min_stock' => $request->input('daysMinStock'),
                ':xdays_reposition' => $request->input('daysOfReposition'),
                ':xproduct_ids' => $s_productIds,
            ];

            $a_result = DB::select(
                "call sp_get_supply_reposition_analysis(:xstore_id, :xdays_min_stock, :xdays_reposition, :xproduct_ids)",
                $a_params
            );

            $a_response = $a_response = [
                'success' => true,
                'data' => $a_result,
            ];

            return response()->json($a_response);
        } catch (\Exception $ex) {
            Log::error('Error reposition: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error reposition: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function getSupplyData(Request $request)
    {
        set_time_limit(300);
        ini_set('max_execution_time', 300);

        $s_providerIds = "";
        $s_divisionIds = "";
        $s_lineIds = "";
        $s_sublineIds = "";
        $s_productIds = "";
        $s_sort = "";
        $s_order = "";
        $s_break_scale = "";
        $s_rotation_scale = "";
        $s_product_type = 'Food';

        if ($request->has('provider')) {
            $s_providerIds = $request->input('provider');
        }
        if ($request->has('division')) {
            $s_divisionIds = $request->input('division');
        }
        if ($request->has('line')) {
            $s_lineIds = $request->input('line');
        }
        if ($request->has('subline')) {
            $s_sublineIds = $request->input('subline');
        }
        if ($request->has(key: 'product')) {
            $s_productIds = $request->input('product');
        }

        if ($request->has('sort')) {
            $s_sort = $request->input('sort');
        }
        if ($request->has('order')) {
            $s_order = $request->input('order');
        }

        if ($request->has('breakScale')) {
            $break_scale_array = explode(',', $request->input('breakScale'));
            $s_break_scale = "'" . implode("','", $break_scale_array) . "'";
        }

        if ($request->has('rotationScale')) {
            $rotation_scale_array = explode(',', $request->input('rotationScale'));
            $s_rotation_scale = "'" . implode("','", $rotation_scale_array) . "'";
        }

        $a_params = [
            ':xprovider_ids' => "",
            ':xdivision_ids' => "",
            ':xline_ids' => "",
            ':xsubline_ids' => "",
            ':xproduct_ids' => "",
            ':xrotation_scale' => "",
            ':xbreak_scale' => "",
            ':xproduct_type' => "Merc2"
        ];

        $food_merchandise_ids = collect(DB::select(
            "call sp_get_reposition_products_simplified(:xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xrotation_scale, :xbreak_scale, :xproduct_type)",
            $a_params
        ))->pluck('product_id')->all();

        $s_food_merchandise_ids = implode(',', $food_merchandise_ids);

        $food_merchandise_params = [
            ':xstore_id' => intval($request->input('store', 0)),
            ':xdays_min_stock' => intval($request->input('daysMinStock', 7)),
            ':xdays_reposition' => intval($request->input('daysOfReposition', 3)),
            ':xprovider_ids' => "",
            ':xdivision_ids' => "",
            ':xline_ids' => "",
            ':xsubline_ids' => "",
            ':xproduct_ids' => $s_food_merchandise_ids,
            ':xscale_rotation' => "",
            ':xexpiration_alert' => "",
            ':xobsolete_alert' => "",
            ':xestimated_order' => "",
            ':xdays_to_order' => intval($request->input('daysToOrder', 1)),
            ':xsort' => "",
            ':xorder' => "",
            ':xlimit' => 0,
            ':xoffset' => 0,
            ':xproduct_type' => 'Merc2'
        ];

        $food_merchandise_analisys = DB::select(
            "call sp_get_reposition_analysis(:xstore_id, :xdays_min_stock, :xdays_reposition, :xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xscale_rotation, :xexpiration_alert, :xobsolete_alert, :xestimated_order, :xdays_to_order, :xsort, :xorder, :xlimit, :xoffset, :xproduct_type)",
            $food_merchandise_params
        );

        $grouped_food_merchandise = [];

        foreach ($food_merchandise_analisys as $item) {
            $productId = $item->product_id ?? null;
            if (!$productId)
                continue;

            if (!isset($grouped_food_merchandise[$productId])) {
                $grouped_food_merchandise[$productId] = [
                    'quantity_oc' => 0,
                    'stores' => [],
                ];
            }

            $grouped_food_merchandise[$productId]['quantity_oc'] += $item->unit_quantity_order;
            $grouped_food_merchandise[$productId]['stores'][] = $item;
        }

        $allSupplys = [];

        $only_derived = $request->input('foodMode', self::SUPPLY) == self::RAW_MATERIAL;
        $excluded_supplys = $only_derived ? [] : [self::DERIVED_PRODUCT];

        foreach ($grouped_food_merchandise as $product_id => $product_data) {
            foreach ($product_data['stores'] as $store) {
                $store_quantity_oc = floatval($store->unit_quantity_order);
                if ($store_quantity_oc > 0) {

                    $derivedProductsCapture = [];
                    $supplys = $this->getPlainSupplys(
                        $product_id,
                        $store_quantity_oc,
                        $store->store_id,
                        $this->getRecipe($product_id),
                        $store_quantity_oc,
                        $excluded_supplys,
                        $only_derived,
                        false,
                        $derivedProductsCapture
                    );

                    foreach ($supplys as $id => $supply) {
                        if (!isset($allSupplys[$id])) {
                            $allSupplys[$id] = $supply;
                        } else {
                            $allSupplys[$id]['unit_quantity_proyected'] += $supply['unit_quantity_proyected'];
                            foreach ($supply['recipes'] as $rid => $recipeData) {
                                if (!isset($allSupplys[$id]['recipes'][$rid])) {
                                    $allSupplys[$id]['recipes'][$rid] = $recipeData;
                                } else {
                                    $allSupplys[$id]['recipes'][$rid]['quantity'] += $recipeData['quantity'];
                                    $allSupplys[$id]['recipes'][$rid]['recipe_quantity'] += $recipeData['recipe_quantity'];
                                }
                            }

                            foreach ($supply['quantities_by_store'] as $sid => $qty) {
                                $allSupplys[$id]['quantities_by_store'][$sid] = ($allSupplys[$id]['quantities_by_store'][$sid] ?? 0) + $qty;
                            }
                        }
                    }
                }
            }
        }

        $s_supplys_ids = implode(',', array_keys($allSupplys));

        $a_supply_params = [
            ':xprovider_ids' => $s_providerIds,
            ':xdivision_ids' => $s_divisionIds,
            ':xline_ids' => $s_lineIds,
            ':xsubline_ids' => $s_sublineIds,
            ':xproduct_ids' => $s_supplys_ids,
        ];

        $a_supplys = DB::select(
            "call sp_get_reposition_supply(:xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids)",
            $a_supply_params
        );

        $a_supply_analysis_params = [
            ':xstore_id' => 0,
            ':xdays_min_stock' => $request->input('daysMinStock'),
            ':xdays_reposition' => $request->input('daysOfReposition'),
            ':xproduct_ids' => $s_supplys_ids
        ];

        $a_supplys_analisys = DB::select(
            "call sp_get_supply_reposition_analysis(:xstore_id, :xdays_min_stock, :xdays_reposition, :xproduct_ids)",
            $a_supply_analysis_params
        );

        $grouped_supply_analisys = [];

        foreach ($a_supplys_analisys as $item) {
            $productId = $item->product_id ?? null;
            if (!$productId)
                continue;

            $grouped_supply_analisys[$productId][] = $item;
        }

        $a_productPresentations = SpGetProductPresentations::getAssociative(
            SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES,
            $s_supplys_ids,
            Currency::PEN,
            0
        );

        $finalSupplys = [];

        foreach ($a_supplys as $o_row) {
            $productId = intval($o_row->product_id);

            if (!isset($allSupplys[$productId]))
                continue;

            $o_row->presentations = $a_productPresentations[$productId] ?? [];
            $o_row->presentation = $a_productPresentations[$productId][Presentation::UNIT_EQUIVALENCE];

            $analisysRows = $grouped_supply_analisys[$productId] ?? [];

            foreach ($analisysRows as &$analysis) {
                $storeId = $analysis->store_id;
                $stockQty = $allSupplys[$productId]['quantities_by_store'][$storeId] ?? 0;

                $analysis->unit_quantity_proyected = $stockQty;
                $analysis->unit_quantity_order = $stockQty - $analysis->purchase_stock;
            }

            $mergedRow = array_merge(
                (array) $o_row,
                $allSupplys[$productId],
                ['analisys' => $analisysRows],
                ['unit_quantity_order' => $allSupplys[$productId]['unit_quantity_proyected'] - $o_row->purchase_stock]
            );

            $finalSupplys[] = $mergedRow;
        }

        if ($only_derived) {
            // Recopilar todas las capturas de productos derivados de todos los stores
            $allDerivedCaptures = [];
            foreach ($grouped_food_merchandise as $product_id => $product_data) {
                foreach ($product_data['stores'] as $store) {
                    $store_quantity_oc = floatval($store->unit_quantity_order);
                    if ($store_quantity_oc > 0) {
                        $derivedProductsCapture = [];
                        $this->getPlainSupplys(
                            $product_id,
                            $store_quantity_oc,
                            $store->store_id,
                            $this->getRecipe($product_id),
                            $store_quantity_oc,
                            $excluded_supplys,
                            $only_derived,
                            false,
                            $derivedProductsCapture
                        );

                        // Agregar capturas por tienda
                        foreach ($derivedProductsCapture as $derivedId => $derivedQty) {
                            if (!isset($allDerivedCaptures[$derivedId])) {
                                $allDerivedCaptures[$derivedId] = [];
                            }
                            $allDerivedCaptures[$derivedId][$store->store_id] = ($allDerivedCaptures[$derivedId][$store->store_id] ?? 0) + $derivedQty;
                        }
                    }
                }
            }

            $finalSupplys = $this->addDerivedProductsToSupplies($finalSupplys, $request, $allDerivedCaptures);
        }

        return $finalSupplys;
    }


    private function addDerivedProductsToSupplies($supplies, $request, $derivedProductsCaptures = [])
    {
        $allProductIds = [];
        foreach ($supplies as $supply) {
            $derivedProducts = $this->getDerivedProductsRecursive($supply['product_id'], $request);
            if (!empty($derivedProducts)) {
                $allProductIds[] = $supply['product_id'];
                $this->collectAllDerivedProductIds($derivedProducts, $allProductIds);
            }
        }

        // Obtener porcentajes de merma para todos los productos de una vez
        $wastePercentages = [];
        if (!empty($allProductIds)) {
            $uniqueProductIds = array_unique($allProductIds);
            Log::info("Obteniendo porcentajes de merma para productos: " . implode(',', $uniqueProductIds));
            $wastePercentages = $this->getWastePercentages($uniqueProductIds);
            Log::info("Porcentajes de merma obtenidos: " . count($wastePercentages) . " registros");
        }

        foreach ($supplies as &$supply) {
            $derivedProducts = $this->getDerivedProductsRecursive($supply['product_id'], $request);
            if (!empty($derivedProducts)) {
                $this->addWasteInfoToProducts($derivedProducts, $wastePercentages);
                $this->addProjectedQuantitiesToDerivedProducts($derivedProducts, $derivedProductsCaptures);
                $supply['derivedProducts'] = $derivedProducts;

                if (isset($wastePercentages[$supply['product_id']])) {
                    $supply['waste_info'] = $wastePercentages[$supply['product_id']];
                }
            }
        }
        return $supplies;
    }

    private function collectAllDerivedProductIds($derivedProducts, &$allProductIds)
    {
        foreach ($derivedProducts as $derived) {
            $allProductIds[] = $derived['product_id'];

            // Si este producto derivado tiene sus propios productos derivados, recopilarlos también
            if (!empty($derived['derivedProducts'])) {
                $this->collectAllDerivedProductIds($derived['derivedProducts'], $allProductIds);
            }
        }
    }

    private function addWasteInfoToProducts(&$products, $wastePercentages)
    {
        foreach ($products as &$product) {
            // Agregar información de merma si existe para este producto
            if (isset($wastePercentages[$product['product_id']])) {
                $product['waste_info'] = $wastePercentages[$product['product_id']];
            }

            // Si este producto tiene productos derivados, agregar merma recursivamente
            if (!empty($product['derivedProducts'])) {
                $this->addWasteInfoToProducts($product['derivedProducts'], $wastePercentages);
            }
        }
    }

    private function addProjectedQuantitiesToDerivedProducts(&$products, $derivedProductsCaptures)
    {
        foreach ($products as &$product) {
            $productId = $product['product_id'];

            // Si tenemos captura para este producto derivado, agregar las proyecciones por tienda
            if (isset($derivedProductsCaptures[$productId])) {
                $capturedQuantities = $derivedProductsCaptures[$productId];

                // Crear análisis por tienda basado en las cantidades capturadas
                if (!isset($product['analisys'])) {
                    $product['analisys'] = [];
                }

                foreach ($capturedQuantities as $storeId => $quantity) {
                    // Buscar si ya existe análisis para esta tienda
                    $existingAnalysis = null;
                    if (isset($product['analisys']) && is_array($product['analisys'])) {
                        foreach ($product['analisys'] as &$analysis) {
                            if ($analysis->store_id == $storeId) {
                                $existingAnalysis = &$analysis;
                                break;
                            }
                        }
                    }

                    if ($existingAnalysis) {
                        // Actualizar cantidad proyectada existente
                        $existingAnalysis->unit_quantity_proyected = $quantity;
                    } else {
                        // Crear nuevo análisis para esta tienda
                        $newAnalysis = (object) [
                            'store_id' => $storeId,
                            'product_id' => $productId,
                            'unit_quantity_proyected' => $quantity,
                            'purchase_stock' => 0,
                            'unit_quantity_order' => $quantity
                        ];
                        $product['analisys'][] = $newAnalysis;
                    }
                }

                Log::info("Asignadas proyecciones a producto derivado {$productId}: " . json_encode($capturedQuantities));
            }

            // Si este producto tiene productos derivados, procesar recursivamente
            if (!empty($product['derivedProducts'])) {
                $this->addProjectedQuantitiesToDerivedProducts($product['derivedProducts'], $derivedProductsCaptures);
            }
        }
    }

    private function getWastePercentages($productIds)
    {
        if (empty($productIds)) {
            return [];
        }

        $productIdsString = implode(',', $productIds);
        $query = "CALL sp_get_waste_percentage(?)";

        Log::info("Ejecutando consulta de merma: " . $query . " con parámetros: " . $productIdsString);

        $results = DB::select($query, [$productIdsString]);
        Log::info("Resultados de merma obtenidos: " . count($results) . " registros");

        $wasteData = [];
        foreach ($results as $result) {
            $wasteData[$result->product_id] = [
                'product_id' => $result->product_id,
                'product_name' => $result->product_name,
                'normal_quantity' => floatval($result->normal_quantity),
                'waste_quantity' => floatval($result->waste_quantity),
                'total_quantity' => floatval($result->total_quantity),
                'waste_percentage_total' => floatval($result->waste_percentage_total),
                'multiplier_to_total' => floatval($result->multiplier_to_total)
            ];
        }

        return $wasteData;
    }



    private function getDerivedProductsRecursive($productId, $request, $depth = 0, $maxDepth = 10)
    {
        if ($depth > $maxDepth) {
            return [];
        }

        $directDerived = $this->getDirectDerivedProducts($productId);

        if (empty($directDerived)) {
            return [];
        }

        $derivedProducts = [];

        $derivedProductIds = array_map(function ($derived) {
            return $derived->product_id;
        }, $directDerived);

        $allProductPresentations = [];
        if (!empty($derivedProductIds)) {
            $allProductPresentations = SpGetProductPresentations::getAssociative(
                SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES,
                $derivedProductIds,
                Currency::PEN,
                0
            );
        }

        foreach ($directDerived as $derived) {
            $derivedData = $this->getProductSupplyData($derived->product_id);
            if (!empty($derivedData)) {
                $productData = (array) $derivedData[0];

                if (!empty($allProductPresentations[$derived->product_id])) {
                    $productData['presentations'] = $allProductPresentations[$derived->product_id];
                    $productData['presentation'] = $allProductPresentations[$derived->product_id][Presentation::UNIT_EQUIVALENCE] ?? null;
                }

                // Agregar análisis al producto derivado usando consulta directa
                Log::info("Intentando obtener análisis para producto derivado: " . $derived->product_id);
                $analysisData = $this->getCustomAnalysisForDerivedProduct($derived->product_id);
                Log::info("Análisis obtenido para producto " . $derived->product_id . ": " . count($analysisData) . " registros");

                if (!empty($analysisData)) {
                    $productData['analisys'] = $analysisData;
                    Log::info("Análisis agregado exitosamente para producto derivado: " . $derived->product_id);
                } else {
                    Log::warning("No se obtuvo análisis para producto derivado: " . $derived->product_id);
                }

                $nestedDerived = $this->getDerivedProductsRecursive($derived->product_id, $request, $depth + 1, $maxDepth);

                if (!empty($nestedDerived)) {
                    $productData['derivedProducts'] = $nestedDerived;
                }

                $derivedProducts[] = $productData;
            }
        }

        return $derivedProducts;
    }

    private function getDirectDerivedProducts($productId)
    {
        $query = "
            SELECT DP.product_id, DP.product_name
            FROM product DP
            JOIN product_link PL ON PL.product_parent_id = DP.product_id
            JOIN presentation DPUPRES ON DPUPRES.product_id = DP.product_id AND DPUPRES.equivalence = 1
            JOIN product RAWP ON RAWP.product_id = PL.product_id
            JOIN presentation RAWUPRES ON RAWUPRES.product_id = RAWP.product_id AND RAWUPRES.equivalence = 1
            LEFT JOIN recipe RE ON DP.product_id = RE.product_id
            WHERE RAWP.product_id = ? AND RE.recipe_id IS NULL
        ";
        $result = DB::select($query, [$productId]);
        return $result;
    }

    /**
     * Obtiene todos los IDs de productos derivados en la base de datos
     * Un producto derivado es aquel que:
     * - NO tiene receta (no está en la tabla recipe)
     * - SÍ tiene product_links (está como product_parent_id en product_link)
     *
     * @return array Array de IDs de productos derivados
     */
    public function getAllDerivedProductIds()
    {
        $query = "
            SELECT DISTINCT P.product_id
            FROM product P
            JOIN product_link PL ON PL.product_parent_id = P.product_id
            LEFT JOIN recipe R ON R.product_id = P.product_id
            WHERE R.recipe_id IS NULL
            AND P.status = 1
            ORDER BY P.product_id
        ";

        $result = DB::select($query);
        return array_column($result, 'product_id');
    }

    /**
     * Obtiene información completa de todos los productos derivados en la base de datos
     * Un producto derivado es aquel que:
     * - NO tiene receta (no está en la tabla recipe)
     * - SÍ tiene product_links (está como product_parent_id en product_link)
     *
     * @return array Array de objetos con información de productos derivados
     */
    public function getAllDerivedProductsInfo()
    {
        $query = "
            SELECT DISTINCT
                P.product_id,
                P.product_name,
                P.status,
                COUNT(PL.product_link_id) as raw_materials_count,
                GROUP_CONCAT(DISTINCT PL.product_id ORDER BY PL.product_id) as raw_material_ids
            FROM product P
            JOIN product_link PL ON PL.product_parent_id = P.product_id
            LEFT JOIN recipe R ON R.product_id = P.product_id
            WHERE R.recipe_id IS NULL
            AND P.status = 1
            GROUP BY P.product_id, P.product_name, P.status
            ORDER BY P.product_id
        ";

        $result = DB::select($query);
        return $result;
    }

    private function getProductSupplyData($productId)
    {
        $query = "CALL sp_get_reposition_supply(?, ?, ?, ?, ?)";
        $result = DB::select($query, [
            '',
            '',
            '',
            '',
            $productId
        ]);
        return $result;
    }

    private function getProductAnalysisData($productId, $request)
    {
        $store = $request->input('store', 0);
        $daysMinStock = $request->input('daysMinStock', 3);
        $daysOfReposition = $request->input('daysOfReposition', 7);
        $daysToOrder = $request->input('daysToOrder', 0);
        $provider = $request->input('provider', '');
        $division = $request->input('division', '');
        $line = $request->input('line', '');
        $subline = $request->input('subline', '');
        $scaleRotation = $request->input('scaleRotation', '');
        $expirationAlert = $request->input('expirationAlert', '');
        $obsoleteAlert = $request->input('obsoleteAlert', '');
        $estimatedOrder = $request->input('estimatedOrder', '');
        $sort = $request->input('sort', '');
        $order = $request->input('order', '');

        $query = "CALL sp_get_reposition_analysis(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        Log::info("Llamando SP analysis para producto derivado: " . $productId);
        Log::info("Parámetros analysis completos: store=$store, daysMinStock=$daysMinStock, daysOfReposition=$daysOfReposition, productId=$productId");

        $result = DB::select($query, [
            $store,           // xstore_id
            $daysMinStock,    // xdays_min_stock
            $daysOfReposition, // xdays_reposition
            $provider,        // xprovider_ids
            $division,        // xdivision_ids
            $line,            // xline_ids
            $subline,         // xsubline_ids
            $productId,       // xproduct_ids
            $scaleRotation,   // xscale_rotation
            $expirationAlert, // xexpiration_alert
            $obsoleteAlert,   // xobsolete_alert
            $estimatedOrder,  // xestimated_order
            $daysToOrder,     // xdays_to_order
            $sort,            // xsort
            $order,           // xorder
            0,                // xlimit
            0,                // xoffset
            ''                // xproduct_type
        ]);

        Log::info("SP analysis retornó " . count($result) . " registros para producto " . $productId);

        return $result;
    }

    private function getCustomAnalysisForDerivedProduct($productId)
    {
        $query = "
            SELECT
                W.warehouse_id,
                W.warehouse_name,
                ST.store_id,
                ST.store_name,
                P.product_id,
                P.product_name,
                PR.measure_name,
                PD.equivalence AS equivalence_default,
                PD.measure_name AS measure_default,
                PP.person_id AS provider_id,
                XP.identification_number AS provider_number,
                REPLACE(XP.person_name, ',', ' ') AS provider,
                ROUND(ME.lpcost_pen * (1 + GV.igv), 4) AS unit_price,
                COALESCE(K.unit_stock, 0) AS stock,
                0 AS to_enter,
                0 AS to_dispatch,
                COALESCE(K.unit_stock, 0) AS purchase_stock,
                0 AS unit_quantity_proyected,
                0 AS unit_quantity_order
            FROM product P
                JOIN presentation PR ON PR.product_id = P.product_id AND PR.equivalence = 1
                JOIN presentation PD ON PD.product_id = P.product_id AND PD.default = 1
                JOIN merchandise ME ON ME.product_id = P.product_id
                LEFT JOIN product_provider PP ON PP.product_id = P.product_id
                LEFT JOIN person XP ON XP.person_id = PP.person_id
                JOIN global_var GV ON GV.organization_id = 1
                LEFT JOIN merchandise_master MM ON MM.product_id = P.product_id
                LEFT JOIN kardex K ON K.kardex_id = MM.kardex_id
                LEFT JOIN warehouse W ON W.warehouse_id = MM.warehouse_id
                LEFT JOIN store ST ON ST.store_id = W.store_id
            WHERE P.product_id = ?
                AND W.warehouse_type = 'Suministros'
                AND W.status = 1
                AND W.warehouse_id != 50
                AND ST.store_id IN (6,8,9,10)
                AND W.warehouse_name LIKE '%Food%'
                AND W.warehouse_name NOT LIKE '%Principal%'
            ORDER BY W.warehouse_id
        ";

        Log::info("Ejecutando consulta personalizada de análisis para producto derivado: " . $productId);

        $result = DB::select($query, [$productId]);

        Log::info("Consulta personalizada retornó " . count($result) . " registros para producto " . $productId);

        return $result;
    }
}
