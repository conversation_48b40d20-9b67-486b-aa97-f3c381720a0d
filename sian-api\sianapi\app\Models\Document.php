<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Document
 * 
 * @property int $document_id
 * @property string $document_code
 * @property string $document_name
 * @property bool $status
 * @property bool $serialized
 * @property bool $third_party
 * @property string $sunat_code
 * @property string $person_type
 * @property string $type
 * @property string $direction
 * @property string $scopes
 * @property int|null $accounting_file_id
 * @property int|null $is_electronic
 * @property string|null $format
 * 
 * @property AccountingFile|null $accounting_file
 * @property Collection|DocumentSerie[] $document_series
 * @property Collection|InventoryReason[] $inventory_reasons
 * @property Collection|Movement[] $movements
 *
 * @package App\Models
 */
class Document extends Model
{
	protected $table = 'document';
	protected $primaryKey = 'document_code';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'document_id' => 'int',
		'status' => 'bool',
		'serialized' => 'bool',
		'third_party' => 'bool',
		'accounting_file_id' => 'int',
		'is_electronic' => 'int'
	];

	protected $fillable = [
		'document_id',
		'document_name',
		'status',
		'serialized',
		'third_party',
		'sunat_code',
		'person_type',
		'type',
		'direction',
		'scopes',
		'accounting_file_id',
		'is_electronic',
		'format'
	];

	public function accounting_file()
	{
		return $this->belongsTo(AccountingFile::class);
	}

	public function document_series()
	{
		return $this->hasMany(DocumentSerie::class, 'document_code');
	}

	public function inventory_reasons()
	{
		return $this->hasMany(InventoryReason::class, 'document_id', 'document_id');
	}

	public function movements()
	{
		return $this->hasMany(Movement::class, 'document_code');
	}
}
