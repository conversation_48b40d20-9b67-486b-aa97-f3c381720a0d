<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class LockerAccess
 * 
 * @property int $locker_access_id
 * @property string|null $firstname
 * @property string|null $lastname
 * @property string $dni
 * @property string $pin
 * @property string $type
 * @property bool $status
 * @property string $email_address
 * @property bool|null $rating
 * @property int $locker_request_id
 * @property int $order
 * 
 * @property LockerRequest $locker_request
 * @property Collection|LockerLog[] $locker_logs
 *
 * @package App\Models
 */
class LockerAccess extends Model
{
	protected $table = 'locker_access';
	protected $primaryKey = 'locker_access_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'rating' => 'bool',
		'locker_request_id' => 'int',
		'order' => 'int'
	];

	protected $fillable = [
		'firstname',
		'lastname',
		'dni',
		'pin',
		'type',
		'status',
		'email_address',
		'rating',
		'locker_request_id',
		'order'
	];

	public function locker_request()
	{
		return $this->belongsTo(LockerRequest::class);
	}

	public function locker_logs()
	{
		return $this->hasMany(LockerLog::class);
	}
}
