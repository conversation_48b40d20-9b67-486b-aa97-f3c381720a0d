<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class WarehouseMovement
 * 
 * @property int $movement_id
 * @property float $munit_quantity
 * @property float $munit_balance
 * @property float $bnet_pen
 * @property float $inet_pen
 * @property float $enet_pen
 * @property float $dnet_pen
 * @property float $net_pen
 * @property float $bnet_usd
 * @property float $inet_usd
 * @property float $enet_usd
 * @property float $dnet_usd
 * @property float $net_usd
 * @property int $warehouse_id
 * @property bool $allow_duplicate
 * @property bool $transport_data
 * @property int|null $transport_company_id
 * @property int|null $transportist_id
 * @property int|null $fixed_asset_id
 * @property int|null $locker_request_id
 * @property int|null $inventory_id
 * 
 * @property FixedAsset|null $fixed_asset
 * @property Person|null $person
 * @property Employee|null $employee
 * @property Movement $movement
 * @property Warehouse $warehouse
 * @property Collection|WarehouseMerchandise[] $warehouse_merchandises
 *
 * @package App\Models
 */
class WarehouseMovement extends Model
{
	protected $table = 'warehouse_movement';
	protected $primaryKey = 'movement_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'munit_quantity' => 'float',
		'munit_balance' => 'float',
		'bnet_pen' => 'float',
		'inet_pen' => 'float',
		'enet_pen' => 'float',
		'dnet_pen' => 'float',
		'net_pen' => 'float',
		'bnet_usd' => 'float',
		'inet_usd' => 'float',
		'enet_usd' => 'float',
		'dnet_usd' => 'float',
		'net_usd' => 'float',
		'warehouse_id' => 'int',
		'allow_duplicate' => 'bool',
		'transport_data' => 'bool',
		'transport_company_id' => 'int',
		'transportist_id' => 'int',
		'fixed_asset_id' => 'int',
		'locker_request_id' => 'int',
		'inventory_id' => 'int'
	];

	protected $fillable = [
		'munit_quantity',
		'munit_balance',
		'bnet_pen',
		'inet_pen',
		'enet_pen',
		'dnet_pen',
		'net_pen',
		'bnet_usd',
		'inet_usd',
		'enet_usd',
		'dnet_usd',
		'net_usd',
		'warehouse_id',
		'allow_duplicate',
		'transport_data',
		'transport_company_id',
		'transportist_id',
		'fixed_asset_id',
		'locker_request_id',
		'inventory_id'
	];

	public function fixed_asset()
	{
		return $this->belongsTo(FixedAsset::class);
	}

	public function person()
	{
		return $this->belongsTo(Person::class, 'transport_company_id');
	}

	public function employee()
	{
		return $this->belongsTo(Employee::class, 'transportist_id');
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function warehouse()
	{
		return $this->belongsTo(Warehouse::class);
	}

	public function warehouse_merchandises()
	{
		return $this->hasMany(WarehouseMerchandise::class, 'movement_id');
	}
}
