<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class WarehouseMerchandise
 * 
 * @property int $item_id
 * @property int $movement_id
 * @property float $bcost_pen
 * @property float $icost_pen
 * @property float $ecost_pen
 * @property float $dcost_pen
 * @property float $cost_pen
 * @property float $bcost_usd
 * @property float $icost_usd
 * @property float $ecost_usd
 * @property float $dcost_usd
 * @property float $cost_usd
 * @property float $bnet_pen
 * @property float $inet_pen
 * @property float $enet_pen
 * @property float $dnet_pen
 * @property float $net_pen
 * @property float $bnet_usd
 * @property float $inet_usd
 * @property float $enet_usd
 * @property float $dnet_usd
 * @property float $net_usd
 * @property bool $serialized
 * 
 * @property Item $item
 * @property WarehouseMovement $warehouse_movement
 * @property Collection|WarehouseSerie[] $warehouse_series
 *
 * @package App\Models
 */
class WarehouseMerchandise extends Model
{
	protected $table = 'warehouse_merchandise';
	protected $primaryKey = 'item_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'item_id' => 'int',
		'movement_id' => 'int',
		'bcost_pen' => 'float',
		'icost_pen' => 'float',
		'ecost_pen' => 'float',
		'dcost_pen' => 'float',
		'cost_pen' => 'float',
		'bcost_usd' => 'float',
		'icost_usd' => 'float',
		'ecost_usd' => 'float',
		'dcost_usd' => 'float',
		'cost_usd' => 'float',
		'bnet_pen' => 'float',
		'inet_pen' => 'float',
		'enet_pen' => 'float',
		'dnet_pen' => 'float',
		'net_pen' => 'float',
		'bnet_usd' => 'float',
		'inet_usd' => 'float',
		'enet_usd' => 'float',
		'dnet_usd' => 'float',
		'net_usd' => 'float',
		'serialized' => 'bool'
	];

	protected $fillable = [
		'movement_id',
		'bcost_pen',
		'icost_pen',
		'ecost_pen',
		'dcost_pen',
		'cost_pen',
		'bcost_usd',
		'icost_usd',
		'ecost_usd',
		'dcost_usd',
		'cost_usd',
		'bnet_pen',
		'inet_pen',
		'enet_pen',
		'dnet_pen',
		'net_pen',
		'bnet_usd',
		'inet_usd',
		'enet_usd',
		'dnet_usd',
		'net_usd',
		'serialized'
	];

	public function item()
	{
		return $this->belongsTo(Item::class);
	}

	public function warehouse_movement()
	{
		return $this->belongsTo(WarehouseMovement::class, 'movement_id');
	}

	public function warehouse_series()
	{
		return $this->hasMany(WarehouseSerie::class, 'item_id');
	}
}
