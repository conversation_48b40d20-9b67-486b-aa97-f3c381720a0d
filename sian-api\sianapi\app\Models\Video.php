<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Video
 *
 * @property int $video_id
 * @property string $url
 * @property string $image
 * @property int $object_id
 *
 * @property ObjectModel $object
 *
 * @package App\Models
 */
class Video extends Model
{
	protected $table = 'video';
	protected $primaryKey = 'video_id';
	public $timestamps = false;

	protected $casts = [
		'object_id' => 'int'
	];

	protected $fillable = [
		'url',
		'image',
		'object_id'
	];

	public function object()
	{
		return $this->belongsTo(ObjectModel::class);
	}
}
