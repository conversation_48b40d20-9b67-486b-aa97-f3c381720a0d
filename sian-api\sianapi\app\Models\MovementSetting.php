<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class MovementSetting
 * 
 * @property int $movement_setting_id
 * @property int $movement_id
 * @property int $item_type_id
 * @property int|null $product_id
 * @property string $purchase_account
 * @property string $purchase_discount_account
 * @property string $sale_account
 * @property string $sale_discount_account
 * @property string|null $sale_cost_account
 * @property string|null $variation_account
 * @property string|null $warehouse_account
 * @property string|null $transit_account
 * @property string|null $cost_transport_account
 * @property string|null $cost_other_account
 * @property string|null $adjust_account
 * @property float $proportion
 * 
 * @property Account|null $account
 * @property AccountingMovement $accounting_movement
 * @property Product|null $product
 * @property Multitable $multitable
 *
 * @package App\Models
 */
class MovementSetting extends Model
{
	protected $table = 'movement_setting';
	protected $primaryKey = 'movement_setting_id';
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'item_type_id' => 'int',
		'product_id' => 'int',
		'proportion' => 'float'
	];

	protected $fillable = [
		'movement_id',
		'item_type_id',
		'product_id',
		'purchase_account',
		'purchase_discount_account',
		'sale_account',
		'sale_discount_account',
		'sale_cost_account',
		'variation_account',
		'warehouse_account',
		'transit_account',
		'cost_transport_account',
		'cost_other_account',
		'adjust_account',
		'proportion'
	];

	public function account()
	{
		return $this->belongsTo(Account::class, 'variation_account');
	}

	public function accounting_movement()
	{
		return $this->belongsTo(AccountingMovement::class, 'movement_id');
	}

	public function product()
	{
		return $this->belongsTo(Product::class);
	}

	public function multitable()
	{
		return $this->belongsTo(Multitable::class, 'item_type_id');
	}
}
