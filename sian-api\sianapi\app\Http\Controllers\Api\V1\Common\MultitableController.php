<?php

namespace App\Http\Controllers\Api\V1\Common;

use Illuminate\Http\Request;
use App\Models\FixedAsset;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Resources\Common\MultitableResource;
use App\Http\Resources\Common\SimpleMultitableResource;
use App\Models\Multitable;
use PhpParser\Parser\Multiple;

class MultitableController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getItems(Request $request)
    {
        $validatedData = $request->validate([
            'value' => 'required|string|max:30'
        ]);

        $multitables = DB::table('multitable as MP')
            ->join('multitable AS MH', 'MH.multi_parent_id', 'MP.multi_id')
            ->where("MP.value", "=", $validatedData['value'])
            ->select('MH.*')
            ->get();

        if(count($multitables)){
            $a_response = [
                'success' => true,
                'data' => [
                    'items' => SimpleMultitableResource::collection($multitables)
                ]
            ];
        }      

        return response()->json($a_response);
    }
}
