<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Employee
 * 
 * @property int $employee_id
 * @property int $person_id
 * @property int|null $regime_id
 * @property Carbon|null $date_in
 * @property int|null $work_days
 * @property int $children
 * @property float|null $amount_house_hold_allow
 * @property float|null $salary
 * @property bool $has_pension_system
 * @property int|null $pension_system_id
 * @property string|null $type_comission
 * @property string|null $afiliate_code
 * @property bool|null $status
 * @property int|null $station_id
 * @property int|null $human_category_id
 * @property int|null $project_id
 * @property bool $is_shipper
 * @property string|null $license_number
 * @property int|null $category_id
 * @property Carbon|null $expedition_date
 * @property Carbon|null $revalidation_date
 * 
 * @property HumanCategory|null $human_category
 * @property Regime|null $regime
 * @property Multitable|null $multitable
 * @property PensionSystem|null $pension_system
 * @property Person $person
 * @property Station|null $station
 * @property Collection|AssistanceSummary[] $assistance_summaries
 * @property Collection|PayrollMovementHeader[] $payroll_movement_headers
 * @property Collection|WarehouseMovement[] $warehouse_movements
 *
 * @package App\Models
 */
class Employee extends Model
{
	protected $table = 'employee';
	protected $primaryKey = 'person_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'employee_id' => 'int',
		'person_id' => 'int',
		'regime_id' => 'int',
		'work_days' => 'int',
		'children' => 'int',
		'amount_house_hold_allow' => 'float',
		'salary' => 'float',
		'has_pension_system' => 'bool',
		'pension_system_id' => 'int',
		'status' => 'bool',
		'station_id' => 'int',
		'human_category_id' => 'int',
		'project_id' => 'int',
		'is_shipper' => 'bool',
		'category_id' => 'int'
	];

	protected $dates = [
		'date_in',
		'expedition_date',
		'revalidation_date'
	];

	protected $fillable = [
		'employee_id',
		'regime_id',
		'date_in',
		'work_days',
		'children',
		'amount_house_hold_allow',
		'salary',
		'has_pension_system',
		'pension_system_id',
		'type_comission',
		'afiliate_code',
		'status',
		'station_id',
		'human_category_id',
		'project_id',
		'is_shipper',
		'license_number',
		'category_id',
		'expedition_date',
		'revalidation_date'
	];

	public function human_category()
	{
		return $this->belongsTo(HumanCategory::class);
	}

	public function regime()
	{
		return $this->belongsTo(Regime::class);
	}

	public function multitable()
	{
		return $this->belongsTo(Multitable::class, 'category_id');
	}

	public function pension_system()
	{
		return $this->belongsTo(PensionSystem::class);
	}

	public function person()
	{
		return $this->belongsTo(Person::class);
	}

	public function station()
	{
		return $this->belongsTo(Station::class);
	}

	public function assistance_summaries()
	{
		return $this->belongsToMany(AssistanceSummary::class, 'assistance_summary_employee', 'person_id')
					->withPivot('assistance_summary_employee_id', 'assistance_summary_employee_code', 'work_days_calc', 'work_days_employee', 'absences', 'work_hours', 'extrahours1', 'extrahours2', 'night_hours', 'holiday_hours', 'lateness');
	}

	public function payroll_movement_headers()
	{
		return $this->hasMany(PayrollMovementHeader::class, 'person_id');
	}

	public function warehouse_movements()
	{
		return $this->hasMany(WarehouseMovement::class, 'transportist_id');
	}
}
