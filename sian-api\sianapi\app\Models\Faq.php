<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Faq
 *
 * @property int $faq_id
 * @property string $answer
 * @property int $object_id
 *
 * @property ObjectModel $object
 *
 * @package App\Models
 */
class Faq extends Model
{
	protected $table = 'faq';
	protected $primaryKey = 'faq_id';
	public $timestamps = false;

	protected $casts = [
		'object_id' => 'int'
	];

	protected $fillable = [
		'answer',
		'object_id'
	];

	public function object()
	{
		return $this->belongsTo(ObjectModel::class);
	}
}
