<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Module
 * 
 * @property string $module
 * @property string $title
 * 
 * @property Collection|Controller[] $controllers
 * @property Collection|Scenario[] $scenarios
 *
 * @package App\Models
 */
class Module extends Model
{
	protected $table = 'module';
	protected $primaryKey = 'module';
	public $incrementing = false;
	public $timestamps = false;

	protected $fillable = [
		'title'
	];

	public function controllers()
	{
		return $this->hasMany(Controller::class, 'module');
	}

	public function scenarios()
	{
		return $this->hasMany(Scenario::class, 'module');
	}
}
