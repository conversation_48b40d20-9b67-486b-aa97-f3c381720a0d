<?php

namespace App\Models\DataProviders;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class DpBusinessPartner extends Model {
    protected $table = 'person';

    protected $primaryKey = 'person_id';

    protected $fillable = [
        'person_id', 'identification_type', 'identification_number', 'person_name',
        'phone_number', 'email_address', 'official_address', 'dept_code',
        'prov_code', 'dist_code', 'lat', 'lng', 'type', 'not_domiciled',
        'account_number', 'status'
    ];

    const SCENARIO_CLIENT = 'client';
    const SCENARIO_PROVIDER = 'provider';
    const SCENARIO_PEOPLE = 'people';
    const SCENARIO_ASSOCIATED = 'associated';
    const SCENARIO_PERSON_NOT_EMPLOYEE = 'personNotEmployee';
    const SCENARIO_FOR_SELLER = 'forSeller';
    const SCENARIO_API_WEB = 'apiWeb';
    const SCENARIO_API_POS = 'apiPos';
    const SCENARIO_API_USER_FORM = 'apiPos';
    const SCENARIO_FOR_COMERCIAL_CASE = 'comercialCase';
    const SCENARIO_TRANSPORT_COMPANY = 'transportCompany';
    const SCENARIO_PARTNERS_BY_GROUP = 'partnersByGroup';
    const SCENARIO_PARTNERS_WITH_FIXED_CASHBOX = 'partnersWithFixedCashbox';

    protected $scenario = null;

    public function setScenario($scenario) {
        $this->scenario = $scenario;
    }

    public function scopeWithScenario(Builder $query, $scenario) {
        $this->setScenario($scenario);


        $query->from('person as P');

        switch ($scenario) {
            case self::SCENARIO_CLIENT:
                $query->select([
                    'P.person_id',
                    'P.identification_type',
                    'P.identification_number',
                    DB::raw("REPLACE(P.person_name, ',', ' ') as person_name"),
                    'P.client_credit as credit',
                    'P.client_expired as expired',
                    'P.retention'
                ]);
                break;

            case self::SCENARIO_PROVIDER:
                $query->select([
                    'P.person_id',
                    'P.identification_type',
                    'P.identification_number',
                    DB::raw("REPLACE(P.person_name, ',', ' ') as person_name"),
                    'P.provider_credit as credit',
                    'P.provider_credit_days as credit_days',
                    'P.provider_expired as expired',
                    DB::raw('0 as retention')
                ]);

                $query->where('P.status', 1)
                    ->where('P.identification_number', '!=', '0');
                break;

            case self::SCENARIO_ASSOCIATED:
                $query->select([
                    'P.person_id',
                    'P.identification_type',
                    'P.identification_number',
                    DB::raw("REPLACE(P.person_name, ',', ' ') as person_name"),
                    'P.phone_number',
                    'P.email_address',
                    'P.official_address',
                    'P.dept_code',
                    'P.prov_code',
                    'P.dist_code',
                    'P.lat',
                    'P.lng',
                    'P.type',
                    'P.not_domiciled',
                    'P.account_number',
                    'P.status',
                    'EM.employee_id',
                    'EM.employee_type',
                    'EM.join_date',
                    'EM.leave_date',
                    'S.station_id',
                    'S.station_name',
                    'ST.store_name',
                    'AR.area_name',
                    'CC.combination_name',
                    'CB.cashbox_id'
                ]);

                $query->leftJoin('employee as EM', 'EM.person_id', '=', 'P.person_id')
                    ->leftJoin('station as S', 'S.station_id', '=', 'EM.station_id')
                    ->leftJoin('store as ST', 'ST.store_id', '=', 'S.store_id')
                    ->leftJoin('area as AR', 'AR.area_id', '=', 'S.area_id')
                    ->leftJoin('combination as CC', 'CC.combination_id', '=', 'AR.combination_id')
                    ->leftJoin('cashbox as CB', 'CB.responsible_id', '=', 'P.person_id');

                $query->where('EM.employee_type', '!=', 'terminated');

                break;

            case self::SCENARIO_API_WEB:
                $query->select([
                    'P.person_id',
                    'P.identification_type',
                    'P.identification_number',
                    DB::raw("REPLACE(P.person_name, ',', ' ') as person_name"),
                    'P.status'
                ]);

                $query->where('P.status', 1);
                break;

            case self::SCENARIO_API_POS:
                $query->select([
                    'P.person_id',
                    'P.identification_type',
                    'P.identification_number',
                    DB::raw("REPLACE(P.person_name, ',', ' ') as person_name"),
                    'P.type'
                ]);
                break;

            case self::SCENARIO_FOR_SELLER:
                $query->select([
                    'P.person_id',
                    'P.identification_type',
                    'P.identification_number',
                    DB::raw("REPLACE(P.person_name, ',', ' ') as person_name"),
                    'U.user_id',
                    'U.username',
                    'U.email'
                ]);
                $query->leftJoin('user as U', 'U.person_id', '=', 'P.person_id');
                break;

            case self::SCENARIO_FOR_COMERCIAL_CASE:
                $query->select([
                    'P.person_id',
                    'P.identification_type',
                    'P.identification_number',
                    DB::raw("REPLACE(P.person_name, ',', ' ') as person_name"),
                    'P.type',
                    'P.status'
                ]);
                break;

            case self::SCENARIO_TRANSPORT_COMPANY:
                $query->select([
                    'P.person_id',
                    'P.identification_type',
                    'P.identification_number',
                    DB::raw("REPLACE(P.person_name, ',', ' ') as person_name"),
                    'P.phone_number',
                    'P.email_address',
                    'P.official_address',
                    'P.type',
                    'P.not_domiciled',
                    'P.status'
                ]);
                break;

            case self::SCENARIO_PARTNERS_BY_GROUP:
                $query->select([
                    'P.person_id',
                    DB::raw("REPLACE(P.person_name, ',', ' ') as person_name"),
                    'G.group_id',
                    'G.group_name'
                ])
                    ->leftJoin('group as G', 'G.group_id', '=', 'P.group_id');
                break;

            case self::SCENARIO_PARTNERS_WITH_FIXED_CASHBOX:
                $query->select([
                    'P.person_id',
                    'P.identification_type',
                    'P.identification_number',
                    DB::raw("REPLACE(P.person_name, ',', ' ') as person_name"),
                    'CB.cashbox_id',
                    'CB.cashbox_name'
                ])
                    ->leftJoin('cashbox as CB', 'CB.responsible_id', '=', 'P.person_id');
                break;

            default:

                break;
        }

        return $query;
    }

}
