<?php

namespace App\Http\Controllers\Api\Procedures;

use App\Http\Controllers\Controller;
use App\Models\Procedures\TypeDocumentaryProcedure;
use Illuminate\Http\Request;

use App\Http\Resources\Procedures\TypeDocumentaryProcedureResource;
use App\Http\Resources\Procedures\TypeDocumentaryProcedureCollection;

class TypeDocumentaryProcedureController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return new TypeDocumentaryProcedureCollection(TypeDocumentaryProcedure::latest()->paginate());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\TypeDocumentaryProcedure  $typeDocumentaryProcedure
     * @return \Illuminate\Http\Response
     */
    public function show(TypeDocumentaryProcedure $typeDocumentaryProcedure)
    {
        return new TypeDocumentaryProcedureResource($typeDocumentaryProcedure);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\TypeDocumentaryProcedure  $typeDocumentaryProcedure
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, TypeDocumentaryProcedure $typeDocumentaryProcedure)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\TypeDocumentaryProcedure  $typeDocumentaryProcedure
     * @return \Illuminate\Http\Response
     */
    public function destroy(TypeDocumentaryProcedure $typeDocumentaryProcedure)
    {
        //
    }
}
