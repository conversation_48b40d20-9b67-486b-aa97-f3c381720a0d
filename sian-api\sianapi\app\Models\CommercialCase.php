<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class CommercialCase
 * 
 * @property int $commercial_case_id
 * @property int $aux_person_id
 * @property int $contact_id
 * @property string $state
 * @property string|null $observation
 * @property int|null $seller_id
 * @property string $offer_description
 * @property string $currency
 * @property float $amount
 * @property int|null $movement_id
 * 
 * @property Movement|null $movement
 * @property Person $person
 * @property Contact $contact
 * @property Seller|null $seller
 * @property Collection|CommercialCaseDetail[] $commercial_case_details
 *
 * @package App\Models
 */
class CommercialCase extends Model
{
	protected $table = 'commercial_case';
	protected $primaryKey = 'commercial_case_id';
	public $timestamps = false;

	protected $casts = [
		'aux_person_id' => 'int',
		'contact_id' => 'int',
		'seller_id' => 'int',
		'amount' => 'float',
		'movement_id' => 'int'
	];

	protected $fillable = [
		'aux_person_id',
		'contact_id',
		'state',
		'observation',
		'seller_id',
		'offer_description',
		'currency',
		'amount',
		'movement_id'
	];

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function person()
	{
		return $this->belongsTo(Person::class, 'aux_person_id');
	}

	public function contact()
	{
		return $this->belongsTo(Contact::class);
	}

	public function seller()
	{
		return $this->belongsTo(Seller::class);
	}

	public function commercial_case_details()
	{
		return $this->hasMany(CommercialCaseDetail::class);
	}
}
