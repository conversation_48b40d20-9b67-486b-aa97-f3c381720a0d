<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Stock
 * 
 * @property int $stock_id
 * @property int $product_id
 * @property int $movement_id
 * @property float $unit_incoming
 * @property float $unit_outgoing
 * @property float $unit_stock
 * @property float $unit_rstock
 * @property float $unit_cstock
 * @property float $unit_pstock
 * @property float $unit_sstock
 * @property float $default_stock
 * @property float $default_rstock
 * @property float $default_cstock
 * @property float $default_pstock
 * @property float $default_sstock
 * @property string $mixed_stock
 * @property string $mixed_rstock
 * @property string $mixed_cstock
 * @property string $mixed_pstock
 * @property string $mixed_sstock
 * @property float $licost
 * @property bool $last_of_day
 * @property int $order
 * 
 * @property Merchandise $merchandise
 * @property Movement $movement
 * @property Collection|Merchandise[] $merchandises
 * @property Collection|StockRange[] $stock_ranges
 *
 * @package App\Models
 */
class Stock extends Model
{
	protected $table = 'stock';
	protected $primaryKey = 'stock_id';
	public $timestamps = false;

	protected $casts = [
		'product_id' => 'int',
		'movement_id' => 'int',
		'unit_incoming' => 'float',
		'unit_outgoing' => 'float',
		'unit_stock' => 'float',
		'unit_rstock' => 'float',
		'unit_cstock' => 'float',
		'unit_pstock' => 'float',
		'unit_sstock' => 'float',
		'default_stock' => 'float',
		'default_rstock' => 'float',
		'default_cstock' => 'float',
		'default_pstock' => 'float',
		'default_sstock' => 'float',
		'licost' => 'float',
		'last_of_day' => 'bool',
		'order' => 'int'
	];

	protected $fillable = [
		'product_id',
		'movement_id',
		'unit_incoming',
		'unit_outgoing',
		'unit_stock',
		'unit_rstock',
		'unit_cstock',
		'unit_pstock',
		'unit_sstock',
		'default_stock',
		'default_rstock',
		'default_cstock',
		'default_pstock',
		'default_sstock',
		'mixed_stock',
		'mixed_rstock',
		'mixed_cstock',
		'mixed_pstock',
		'mixed_sstock',
		'licost',
		'last_of_day',
		'order'
	];

	public function merchandise()
	{
		return $this->belongsTo(Merchandise::class, 'product_id');
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function merchandises()
	{
		return $this->hasMany(Merchandise::class);
	}

	public function stock_ranges()
	{
		return $this->hasMany(StockRange::class);
	}
}
