<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Gallery
 *
 * @property int $gallery_id
 * @property bool $slide
 * @property bool $pause_hover
 * @property bool $prev_next
 * @property int $interval
 * @property int $show_title
 * @property int $object_id
 *
 * @property ObjectModel $object
 *
 * @package App\Models
 */
class Gallery extends Model
{
	protected $table = 'gallery';
	protected $primaryKey = 'gallery_id';
	public $timestamps = false;

	protected $casts = [
		'slide' => 'bool',
		'pause_hover' => 'bool',
		'prev_next' => 'bool',
		'interval' => 'int',
		'show_title' => 'int',
		'object_id' => 'int'
	];

	protected $fillable = [
		'slide',
		'pause_hover',
		'prev_next',
		'interval',
		'show_title',
		'object_id'
	];

	public function object()
	{
		return $this->belongsTo(ObjectModel::class);
	}
}
