<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Line
 *
 * @property int $line_id
 * @property string $line_name
 * @property string $alias
 * @property string $description
 * @property bool $status
 * @property bool $frontend
 * @property bool $outstanding
 * @property bool $web_enabled
 * @property int $web_order
 * @property string|null $transaction_code
 * @property int $division_id
 *
 * @property Division $division
 * @property Collection|Subline[] $sublines
 *
 * @package App\Models
 */
class Line extends Model
{
    const LINE_NAME_SERVICES = 'SERVICIOS';
	protected $table = 'line';
	protected $primaryKey = 'line_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'frontend' => 'bool',
		'outstanding' => 'bool',
		'web_enabled' => 'bool',
		'web_order' => 'int',
		'division_id' => 'int'
	];

	protected $fillable = [
		'line_name',
		'alias',
		'description',
		'status',
		'frontend',
		'outstanding',
		'web_enabled',
		'web_order',
		'transaction_code',
		'division_id'
	];

	public function division()
	{
		return $this->belongsTo(Division::class);
	}

	public function sublines()
	{
		return $this->hasMany(Subline::class);
	}
}
