<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ECommerceTariff
 * 
 * @property int $person_id
 * @property string $destiny_type
 * @property float $begin_weight
 * @property float $final_weight
 * @property float $cost
 * 
 * @property ECommerceDestinyType $e_commerce_destiny_type
 *
 * @package App\Models
 */
class ECommerceTariff extends Model
{
	protected $table = 'e_commerce_tariff';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'person_id' => 'int',
		'begin_weight' => 'float',
		'final_weight' => 'float',
		'cost' => 'float'
	];

	protected $fillable = [
		'cost'
	];

	public function e_commerce_destiny_type()
	{
		return $this->belongsTo(ECommerceDestinyType::class, 'person_id')
					->where('e_commerce_destiny_type.person_id', '=', 'e_commerce_tariff.person_id')
					->where('e_commerce_destiny_type.destiny_type', '=', 'e_commerce_tariff.destiny_type');
	}
}
