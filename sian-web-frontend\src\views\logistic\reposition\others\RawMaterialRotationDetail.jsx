import { Box, Typography } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'store';
import { getRepositionDataByProduct, openRotationModal, setSelectedProduct } from 'store/slices/reposition/reposition';
import useLoading from 'hooks/useLoading';
import { BlockLoader } from 'ui-component/loaders/loaders';
import RightAlignedNumber from 'ui-component/grid/RightAlignedNumber';
import Grid from 'ui-component/grid/Grid';
import NestedGrid from 'ui-component/grid/NestedGrid';
import MainCard from 'ui-component/cards/MainCard';

const lastDerivedProductColumns = [
    {
        name: 'product_id',
        label: 'ID',
        options: {
            filter: true,
            sort: true,
            display: false
        }
    },
    {
        name: 'product_name',
        label: 'PRODUCTO',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { minWidth: '300px', width: 'auto' } }),
            setCellProps: () => ({ style: { minWidth: '300px', width: 'auto' } }),
            customBodyRender: (value) => (
                <Typography sx={{ whiteSpace: 'nowrap' }}>
                    <strong>{value}</strong>
                </Typography>
            )
        }
    },

    {
        name: 'unit_quantity_proyected',
        label: 'C.PROYECTADA',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { minWidth: '140px', width: 'auto' } }),
            setCellProps: () => ({ style: { minWidth: '140px', width: 'auto' } }),
            customBodyRender: (value) => <RightAlignedNumber value={Math.random() * 100} />
        }
    },
    {
        name: 'purchase_stock',
        label: 'STOCK EN TIENDAS',
        options: {
            filter: true,
            sort: true,
            customBodyRender: (value) => <RightAlignedNumber value={value} />
        }
    },
    {
        name: 'supplying_stock',
        label: 'STOCK A.PRINCIPAL',
        options: {
            filter: true,
            sort: true,
            display: false,
            customBodyRender: (value) => <RightAlignedNumber value={value} />
        }
    },
    {
        name: 'to_transform',
        label: 'TRANSFORMAR',
        options: {
            filter: true,
            sort: true,
            customBodyRender: (value) => <RightAlignedNumber value={20} />
        }
    },
    {
        name: 'measure_default',
        label: 'PRES',
        options: {
            filter: true,
            sort: true,
            customBodyRender: (value) => <Typography>{value}</Typography>
        }
    },
    {
        name: 'rep_pres_min',
        label: 'NETO',
        options: {
            filter: true,
            sort: false,
            customBodyRender: (value, tableMeta) => <RightAlignedNumber value={Math.random() * 100} />
        }
    },
    {
        name: 'measure_name',
        label: 'PRES MIN',
        options: {
            filter: true,
            sort: true,
            customBodyRender: (value) => <Typography>{value}</Typography>
        }
    }
];

const findDerivedProductRecursively = (derivedProducts, targetProductId) => {
    if (!derivedProducts || !Array.isArray(derivedProducts)) return null;

    for (const derived of derivedProducts) {
        if (derived.product_id === targetProductId) {
            return derived;
        }

        if (derived.derivedProducts && derived.derivedProducts.length > 0) {
            const found = findDerivedProductRecursively(derived.derivedProducts, targetProductId);
            if (found) return found;
        }
    }

    return null;
};

const findMainProductWithDerivedProduct = (data, targetProductId) => {
    if (!data || !Array.isArray(data)) return null;

    for (const item of data) {
        const found = findDerivedProductRecursively(item.derivedProducts, targetProductId);
        if (found) return item;
    }

    return null;
};

const processAnalisys = (productData, storeData) => {
    if (!productData || !productData.analisys || !Array.isArray(productData.analisys)) {
        return [];
    }

    const listData = productData.analisys.map((item) => {
        const store = storeData.find((s) => s.store_id === item.store_id);
        return {
            ...item,
            pk: `${item.store_id}_${productData.product_id}`,
            store_name: store ? store.store_name : 'Tienda no encontrada'
        };
    });

    listData.sort((a, b) => a.warehouse_id - b.warehouse_id);

    return listData;
};

const getDerivedProducts = (productData) => {
    if (!productData || !productData.derivedProducts) return [];

    return productData.derivedProducts.map((item, index) => ({
        ...item,
        pk: item.product_id,
        globalIndex: index
    }));
};

const NestedCard = ({ children, width = '50%' }) => (
    <Box sx={{ p: 1, backgroundColor: 'white', borderRadius: '1rem', border: '1px solid #e0e0e0', width }}>{children}</Box>
);

const SimpleAnalysisNestedContent = ({ row, data, analisysDerivedProducts }) => {
    const productId = row[0];
    const mainProductData = findMainProductWithDerivedProduct(data, productId);
    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);



    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'row',
                gap: 2,
                pb: 1,
                px: 2,
                justifyContent: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: '1rem',
                my: 1
            }}
        >
            <Box sx={{ width: '40%' }}>
                <MainCard>
                    <DerivedProductAnalysis row={row} columns={analisysDerivedProducts} />
                </MainCard>
            </Box>
        </Box>
    );
};

const DerivedProductAnalysis = ({ row, columns }) => {
    const productId = row[0];
    const { data } = useSelector((state) => state.reposition);
    const { data: storeData } = useSelector((state) => state.store);
    const mainProductData = findMainProductWithDerivedProduct(data, productId);
    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);
    const derivedAnalysis = derivedProduct?.analisys ? processAnalisys(derivedProduct, storeData) : null;

    if (!derivedAnalysis || derivedAnalysis.length === 0) {
        return (
            <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                <Typography variant="body2">No hay análisis disponible para este producto derivado</Typography>
            </Box>
        );
    }

    return (
        <Grid
            columns={columns}
            data={derivedAnalysis}
            options={{
                search: false,
                download: false,
                print: false,
                sort: false,
                viewColumns: false,
                filter: false,
                pagination: false,
                selectableRows: 'none',
                toolbar: false,
                elevation: 0
            }}
        />
    );
};

const DerivedProductNestedContent = ({ row, data, derivedAnalysisColumns, simpleAnalysisColumns, completeAnalysisColumns }) => {
    const productId = row[0];
    const mainProductData = findMainProductWithDerivedProduct(data, productId);
    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);
    const hasSubDerived = derivedProduct?.derivedProducts && derivedProduct.derivedProducts.length > 0;



    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'row',
                gap: 2,
                pb: 1,
                px: 2,
                justifyContent: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: '1rem',
                my: 1
            }}
        >
            <Box sx={{ width: hasSubDerived ? '25%' : '40%' }}>
                <MainCard>
                    <DerivedProductAnalysis row={row} columns={derivedAnalysisColumns} />
                </MainCard>
            </Box>
            {hasSubDerived && (
                <Box sx={{ width: '75%' }}>
                    <MainCard>
                        <SubDerivedProducts
                            row={row}
                            columns={lastDerivedProductColumns}
                            analysisColumns={simpleAnalysisColumns}
                            completeAnalysisColumns={derivedAnalysisColumns}
                            lastDerivedProductColumns={lastDerivedProductColumns}
                        />
                    </MainCard>
                </Box>
            )}
        </Box>
    );
};

const SubDerivedProducts = ({ row, columns, analysisColumns, completeAnalysisColumns }) => {
    const productId = row[0];
    const { data } = useSelector((state) => state.reposition);
    const mainProductData = findMainProductWithDerivedProduct(data, productId);
    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);
    const rawSubDerivedProducts = derivedProduct?.derivedProducts || [];

    const subDerivedProducts = rawSubDerivedProducts.map((item, index) => ({
        ...item,
        pk: item.product_id,
        globalIndex: index
    }));

    return (
        <Box sx={{ width: '100%', height: 'fit-content' }}>
            <Box
                sx={{
                    '& .MuiTable-root': {
                        width: '100% !important',
                        tableLayout: 'auto'
                    },
                    '& .MuiTableCell-root': {
                        padding: '4px 8px',
                        fontSize: '0.75rem'
                    }
                }}
            >
                <NestedGrid
                    columns={columns}
                    data={subDerivedProducts}
                    title={<Typography sx={{ fontSize: '1rem', fontWeight: 'bold' }}>Sub-Derivados</Typography>}
                    RenderNestedContent={(props) => {
                        // Evaluar si este sub-derivado específico tiene sub-sub-derivados
                        const subProductId = props.row[0];
                        const subDerivedProduct = rawSubDerivedProducts.find(p => p.product_id === subProductId);
                        const hasSubSubDerived = subDerivedProduct?.derivedProducts && subDerivedProduct.derivedProducts.length > 0;

                        // Usar columnas simples si tiene sub-sub-derivados, completas si no tiene
                        const columnsToUse = hasSubSubDerived ? analysisColumns : completeAnalysisColumns;

                        console.log(`Sub-derivado ${subProductId}: hasSubSubDerived=${hasSubSubDerived}, columns=${columnsToUse?.map(c => c.label).join(', ')}`);

                        return (
                            <DerivedProductNestedContent
                                {...props}
                                data={data}
                                derivedAnalysisColumns={columnsToUse}
                                simpleAnalysisColumns={analysisColumns}
                                simplifiedDerivedProductColumns={columns}
                                lastDerivedProductColumns={lastDerivedProductColumns}
                            />
                        );
                    }}
                    options={{
                        search: false,
                        download: false,
                        print: false,
                        sort: false,
                        viewColumns: false,
                        filter: false,
                        pagination: false,
                        selectableRows: 'none',
                        toolbar: false,
                        elevation: 0,
                        responsive: 'vertical'
                    }}
                />
            </Box>
        </Box>
    );
};

const RawMaterialRotationDetail = ({ row, filters, supplyAnalisys, isFromProyection, merchandiseFoodData }) => {
    const dispatch = useDispatch();
    const { data } = useSelector((state) => state.reposition);
    const { data: storeData } = useSelector((state) => state.store);

    const productData = isFromProyection
        ? merchandiseFoodData.find((item) => item.product_id === row[0])
        : data.find((item) => item.product_id === row[0]);

    const derivedProducts = getDerivedProducts(productData);

    // Separar productos derivados en dos grupos
    const derivedWithSubProducts = derivedProducts.filter((product) => product.derivedProducts && product.derivedProducts.length > 0);

    const derivedWithoutSubProducts = derivedProducts.filter((product) => !product.derivedProducts || product.derivedProducts.length === 0);

    const displayProducts =
        derivedProducts.length > 0
            ? derivedProducts
            : productData
            ? [
                  {
                      ...productData,
                      pk: productData.product_id || productData.pk,
                      globalIndex: 0
                  }
              ]
            : [];

    const [repositionProduct, setRepositionProduct] = useState(supplyAnalisys || []);
    const [isAsync] = useState(!supplyAnalisys);
    const [loading, startLoading, endLoading] = useLoading(isAsync);

    const openModal = () => dispatch(openRotationModal());
    const setSelected = (data) => dispatch(setSelectedProduct(data));

    const reload = () => {
        if (isAsync) {
            startLoading();
            dispatch(getRepositionDataByProduct(row[0], { ...filters, mode: isFromProyection ? 'Merc2' : filters.mode }, storeData)).then(
                (data) => {
                    setRepositionProduct(data);
                    endLoading();
                }
            );
        }
    };

    useEffect(() => {
        reload();
    }, []);

    const getRowDataSafely = (pk) => {
        return repositionProduct.find((item) => item.pk === pk) || {};
    };

    const isRowDataAvailable = (rowData) => {
        return rowData && !rowData.notAvailable;
    };

    // Supply columns for raw material analysis
    const supplyColumns = [
        {
            name: 'pk',
            label: 'PK',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'store_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'store_name',
            label: 'TIENDA',
            options: {
                filter: true,
                sort: true
            }
        },
        {
            name: 'unit_quantity_proyected',
            label: 'C.PROYECTADA',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'purchase_stock',
            label: 'STOCK',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = getRowDataSafely(pk);
                    if (!isRowDataAvailable(rowData)) {
                        return <Typography color="gray">-</Typography>;
                    }
                    return (
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end', gap: 1 }}>
                            <RightAlignedNumber value={value} />
                        </Box>
                    );
                }
            }
        },
        {
            name: 'waste_info',
            label: 'MERMA %',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),
                setCellProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),
                customBodyRender: (value) => {
                    const wasteInfo = value;

                    if (!wasteInfo || !wasteInfo.waste_percentage_total) {
                        return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>-</Typography>;
                    }

                    const percentage = parseFloat(wasteInfo.waste_percentage_total);

                    return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>{percentage.toFixed(2)}%</Typography>;
                }
            }
        }
    ];

    // Simple analysis columns for products WITH sub-derived products (basic columns only)
    const analisysDerivedProducts = [
        {
            name: 'pk',
            label: 'PK',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'store_name',
            label: 'TIENDA',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <Typography sx={{ fontSize: '1.2rem', fontWeight: 'bold' }}>{value}</Typography>
            }
        },
        {
            name: 'purchase_stock',
            label: 'STOCK',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = getRowDataSafely(pk);
                    if (!isRowDataAvailable(rowData)) {
                        return <Typography color="gray">-</Typography>;
                    }
                    return (
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end', gap: 1 }}>
                            <RightAlignedNumber value={value} />
                        </Box>
                    );
                }
            }
        }
    ];

    // Simplified columns for derived products (with waste info)
    const simplifiedDerivedProductColumns = [
        {
            name: 'product_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { width: '80px', maxWidth: '80px' } }),
                setCellProps: () => ({ style: { width: '80px', maxWidth: '80px' } })
            }
        },
        {
            name: 'product_name',
            label: 'PRODUCTO',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { minWidth: '450px', whiteSpace: 'nowrap' } }),
                setCellProps: () => ({ style: { minWidth: '450px', whiteSpace: 'nowrap' } }),
                customBodyRender: (value) => (
                    <Typography sx={{ whiteSpace: 'nowrap' }}>
                        <strong>{value}</strong>
                    </Typography>
                )
            }
        },
        {
            name: 'waste_info',
            label: 'MERMA %',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),
                setCellProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),
                customBodyRender: (value) => {
                    const wasteInfo = value;

                    if (!wasteInfo || !wasteInfo.waste_percentage_total) {
                        return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>-</Typography>;
                    }

                    const percentage = parseFloat(wasteInfo.waste_percentage_total);

                    return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>{percentage.toFixed(2)}%</Typography>;
                }
            }
        },
        {
            name: 'unit_quantity_proyected',
            label: 'C.PROYECTADA',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <RightAlignedNumber value={Math.random() * 100} />
            }
        },
        {
            name: 'purchase_stock',
            label: 'STOCK TIENDAS',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'supplying_stock',
            label: 'STOCK A.PRINCIPAL',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'measure_default',
            label: 'PRES',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <Typography>{value}</Typography>
            }
        },
        {
            name: 'rep_pres_min',
            label: '(NETO)',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (value, tableMeta) => <RightAlignedNumber value={Math.random() * 100} />
            }
        },
        {
            name: 'measure_name',
            label: 'PRES MIN',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <Typography>{value}</Typography>
            }
        }
    ];

    // Simplified columns for non-derived products (without waste info)
    const simplifiedNonDerivedProductColumns = [
        {
            name: 'product_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { width: '80px', maxWidth: '80px' } }),
                setCellProps: () => ({ style: { width: '80px', maxWidth: '80px' } })
            }
        },
        {
            name: 'product_name',
            label: 'PRODUCTO',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { minWidth: '450px', whiteSpace: 'nowrap' } }),
                setCellProps: () => ({ style: { minWidth: '450px', whiteSpace: 'nowrap' } }),
                customBodyRender: (value) => (
                    <Typography sx={{ whiteSpace: 'nowrap' }}>
                        <strong>{value}</strong>
                    </Typography>
                )
            }
        },
        {
            name: 'unit_quantity_proyected',
            label: 'C.PROYECTADA',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <RightAlignedNumber value={Math.random() * 100} />
            }
        },
        {
            name: 'purchase_stock',
            label: 'STOCK EN TIENDAS',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'supplying_stock',
            label: 'S A.PRINCIPAL',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'measure_default',
            label: 'PRES',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <Typography>{value}</Typography>
            }
        },
        {
            name: 'rep_pres_min',
            label: '(NETO)',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (value, tableMeta) => <RightAlignedNumber value={Math.random() * 100} />
            }
        },
        {
            name: 'measure_name',
            label: 'PRES MIN',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <Typography>{value}</Typography>
            }
        }
    ];

    // Complete analysis columns for products WITHOUT sub-derived products (all columns)
    const derivedAnalysisColumns = [
        {
            name: 'pk',
            label: 'PK',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'store_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'store_name',
            label: 'TIENDA',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <Typography sx={{ fontSize: '1.2rem', fontWeight: 'bold' }}>{value}</Typography>
            }
        },
        {
            name: 'purchase_stock',
            label: 'STOCK',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = getRowDataSafely(pk);
                    if (!isRowDataAvailable(rowData)) {
                        return <Typography color="gray">-</Typography>;
                    }
                    return (
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end', gap: 1 }}>
                            <RightAlignedNumber value={value} />
                        </Box>
                    );
                }
            }
        },
        {
            name: 'transformar',
            label: 'TRANSFORMAR',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <RightAlignedNumber value={Math.random() * 100} />
            }
        },
        {
            name: 'presentation',
            label: 'PRES',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (value, tableMeta) => <RightAlignedNumber value={Math.random() * 100} />
            }
        },
        {
            name: 'neto',
            label: 'NETO',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <RightAlignedNumber value={Math.random() * 100} />
            }
        },
        {
            name: 'pres_min',
            label: 'PRES MIN',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <RightAlignedNumber value={Math.random() * 100} />
            }
        }
    ];

    return (
        <Box sx={{ display: 'flex', gap: 1, flexDirection: 'row', width: '100%', justifyContent: 'center' }}>
            <NestedCard
                width="90%"
                sx={{
                    '& .MuiTable-root': {
                        width: '100% !important',
                        tableLayout: 'fixed'
                    },
                    '& .MuiTableCell-root': {
                        padding: '8px 16px'
                    }
                }}
            >
                {/* Grid para productos derivados CON sub-derivados */}
                {derivedWithSubProducts.length > 0 && (
                    <NestedGrid
                        columns={simplifiedDerivedProductColumns}
                        data={derivedWithSubProducts.map((item, index) => ({
                            ...item,
                            pk: item.product_id,
                            globalIndex: index
                        }))}
                        title={
                            <Typography sx={{ fontSize: '1.2rem', fontWeight: 'bold' }}>Productos Derivados (Con Sub-derivados)</Typography>
                        }
                        RenderNestedContent={(props) => (
                            <DerivedProductNestedContent
                                {...props}
                                data={data}
                                derivedAnalysisColumns={analisysDerivedProducts}
                                simpleAnalysisColumns={analisysDerivedProducts}
                                completeAnalysisColumns={derivedAnalysisColumns}
                                simplifiedDerivedProductColumns={simplifiedDerivedProductColumns}
                            />
                        )}
                        options={{
                            search: false,
                            download: false,
                            print: false,
                            sort: false,
                            viewColumns: true,
                            filter: false,
                            filterType: 'multiselect',
                            responsive: 'vertical',
                            fixedHeader: true,
                            fixedSelectColumn: true,
                            jumpToPage: false,
                            resizableColumns: false,
                            draggableColumns: {
                                enabled: true
                            },
                            serverSide: true,
                            selectableRows: 'none',
                            pagination: false,
                            toolbar: false
                        }}
                    />
                )}

                {/* Grid para productos derivados SIN sub-derivados */}
                {derivedWithoutSubProducts.length > 0 && (
                    <NestedGrid
                        columns={simplifiedNonDerivedProductColumns}
                        data={derivedWithoutSubProducts.map((item, index) => ({
                            ...item,
                            pk: item.product_id,
                            globalIndex: index + derivedWithSubProducts.length
                        }))}
                        title={
                            <Typography sx={{ fontSize: '1.2rem', fontWeight: 'bold' }}>Productos Derivados (Sin Sub-derivados)</Typography>
                        }
                        RenderNestedContent={(props) => (
                            <SimpleAnalysisNestedContent {...props} data={data} analisysDerivedProducts={derivedAnalysisColumns} />
                        )}
                        options={{
                            search: false,
                            download: false,
                            print: false,
                            sort: false,
                            viewColumns: true,
                            filter: false,
                            filterType: 'multiselect',
                            responsive: 'vertical',
                            fixedHeader: true,
                            fixedSelectColumn: true,
                            jumpToPage: false,
                            resizableColumns: false,
                            draggableColumns: {
                                enabled: true
                            },
                            serverSide: true,
                            selectableRows: 'none',
                            pagination: false,
                            toolbar: false
                        }}
                    />
                )}

                {/* Mostrar producto principal si no hay derivados */}
                {derivedProducts.length === 0 && (
                    <NestedGrid
                        columns={simplifiedNonDerivedProductColumns}
                        data={displayProducts}
                        title={<Typography sx={{ fontSize: '1.2rem', fontWeight: 'bold' }}>Producto Principal</Typography>}
                        RenderNestedContent={(props) => (
                            <SimpleAnalysisNestedContent {...props} data={data} analisysDerivedProducts={derivedAnalysisColumns} />
                        )}
                        options={{
                            search: false,
                            download: false,
                            print: false,
                            sort: false,
                            viewColumns: true,
                            filter: false,
                            filterType: 'multiselect',
                            responsive: 'vertical',
                            fixedHeader: true,
                            fixedSelectColumn: true,
                            jumpToPage: false,
                            resizableColumns: false,
                            draggableColumns: {
                                enabled: true
                            },
                            serverSide: true,
                            selectableRows: 'none',
                            pagination: false,
                            toolbar: false
                        }}
                    />
                )}
            </NestedCard>

            {loading && <BlockLoader />}
        </Box>
    );
};

export default RawMaterialRotationDetail;
