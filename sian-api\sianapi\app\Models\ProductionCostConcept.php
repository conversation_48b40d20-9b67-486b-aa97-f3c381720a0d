<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ProductionCostConcept
 *
 * @property int $production_cost_concept_id
 * @property string $concept_name
 * @property int|null $parent_concept_id
 * @property boolean $status
 * @property int $order
 * @property float $percentage
 *
 * @property-read ProductionCostConcept $parentProductionCostConcept
 *
 * @package App\Models
 */
class ProductionCostConcept extends Model {
    protected $table = 'production_cost_concept';
    protected $primaryKey = 'production_cost_concept_id';
    public $timestamps = false;

    protected $casts = [
        'production_cost_concept_id' => 'int',
        'concept_name' => 'string',
        'parent_concept_id' => 'int',
        'status' => 'boolean',
        'order' => 'int',
        'percentage' => 'float',
    ];

    protected $fillable = [
        'concept_name',
        'parent_concept_id',
        'status',
        'order',
        'percentage',
    ];

    public function parentProductionCostConcept() {
        return $this->belongsTo(self::class, 'parent_concept_id');
    }


}
