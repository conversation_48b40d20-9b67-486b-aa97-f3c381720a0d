<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Technical
 * 
 * @property int $technical_id
 * @property string $technical_code
 * @property int $person_id
 * @property bool $status
 * @property bool $is_own
 * 
 * @property Person $person
 * @property Collection|NetworkingActivity[] $networking_activities
 *
 * @package App\Models
 */
class Technical extends Model
{
	protected $table = 'technical';
	protected $primaryKey = 'technical_id';
	public $timestamps = false;

	protected $casts = [
		'person_id' => 'int',
		'status' => 'bool',
		'is_own' => 'bool'
	];

	protected $fillable = [
		'technical_code',
		'person_id',
		'status',
		'is_own'
	];

	public function person()
	{
		return $this->belongsTo(Person::class);
	}

	public function networking_activities()
	{
		return $this->hasMany(NetworkingActivity::class);
	}
}
