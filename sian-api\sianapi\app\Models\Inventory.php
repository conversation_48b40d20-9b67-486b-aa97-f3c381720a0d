<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Inventory
 * 
 * @property int $inventory_id
 * @property string $inventory_name
 * @property string|null $description
 * @property int $warehouse_id
 * @property int $business_unit_id
 * @property int $inventory_type_id
 * @property string|null $scenarios
 * @property string $state
 * @property int $person_id
 * @property Carbon $begin_date
 * @property int|null $begin_date_job_id
 * @property float|null $percent
 * @property int|null $product_count
 * @property int|null $product_total
 * @property string|null $owner
 * @property int|null $owner_count
 * @property string|null $reason_annul
 * @property bool $has_snapshot
 * @property bool|null $reason_confirm
 * @property bool|null $reject_reason_count
 * @property string|null $reject_reason_observation
 * @property int|null $user_confirm
 * @property Carbon|null $confirm_date
 * @property int|null $user_reject
 * @property bool|null $reject_count
 * @property Carbon|null $reject_date
 * @property bool|null $with_stock
 * 
 * @property BusinessUnit $business_unit
 * @property Warehouse $warehouse
 * @property Multitable $multitable
 * @property Person|null $person
 * @property Collection|InventoryDetail[] $inventory_details
 *
 * @package App\Models
 */
class Inventory extends Model
{
	protected $table = 'inventory';
	protected $primaryKey = 'inventory_id';
	public $timestamps = false;

	protected $casts = [
		'warehouse_id' => 'int',
		'business_unit_id' => 'int',
		'inventory_type_id' => 'int',
		'person_id' => 'int',
		'begin_date_job_id' => 'int',
		'percent' => 'float',
		'product_count' => 'int',
		'product_total' => 'int',
		'owner_count' => 'int',
		'has_snapshot' => 'bool',
		'reason_confirm' => 'bool',
		'reject_reason_count' => 'bool',
		'user_confirm' => 'int',
		'user_reject' => 'int',
		'reject_count' => 'bool',
		'with_stock' => 'bool'
	];

	protected $dates = [
		'begin_date',
		'confirm_date',
		'reject_date'
	];

	protected $fillable = [
		'inventory_name',
		'description',
		'warehouse_id',
		'business_unit_id',
		'inventory_type_id',
		'scenarios',
		'state',
		'person_id',
		'begin_date',
		'begin_date_job_id',
		'percent',
		'product_count',
		'product_total',
		'owner',
		'owner_count',
		'reason_annul',
		'has_snapshot',
		'reason_confirm',
		'reject_reason_count',
		'reject_reason_observation',
		'user_confirm',
		'confirm_date',
		'user_reject',
		'reject_count',
		'reject_date',
		'with_stock'
	];

	public function business_unit()
	{
		return $this->belongsTo(BusinessUnit::class);
	}

	public function warehouse()
	{
		return $this->belongsTo(Warehouse::class);
	}

	public function multitable()
	{
		return $this->belongsTo(Multitable::class, 'inventory_type_id');
	}

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}

	public function person()
	{
		return $this->belongsTo(Person::class, 'user_reject');
	}

	public function inventory_details()
	{
		return $this->hasMany(InventoryDetail::class);
	}
}
