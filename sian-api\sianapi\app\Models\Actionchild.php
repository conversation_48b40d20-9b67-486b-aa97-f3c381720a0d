<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Actionchild
 * 
 * @property string $module_parent
 * @property string $controller_parent
 * @property string $action_parent
 * @property string $owner_parent
 * @property string $module_child
 * @property string $controller_child
 * @property string $action_child
 * @property string $owner_child
 * 
 * @property Action $action
 *
 * @package App\Models
 */
class Actionchild extends Model
{
	protected $table = 'actionchild';
	public $incrementing = false;
	public $timestamps = false;

	public function action()
	{
		return $this->belongsTo(Action::class, 'module_parent')
					->where('action.module', '=', 'actionchild.module_parent')
					->where('action.controller', '=', 'actionchild.controller_parent')
					->where('action.action', '=', 'actionchild.action_parent')
					->where('action.owner', '=', 'actionchild.owner_parent');
	}
}
