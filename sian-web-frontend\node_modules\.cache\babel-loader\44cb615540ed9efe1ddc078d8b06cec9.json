{"ast": null, "code": "var _jsxFileName = \"D:\\\\www\\\\SWF\\\\sian-web-frontend\\\\src\\\\views\\\\commercial\\\\salesDashboard\\\\components\\\\SummaryTab.jsx\",\n    _s = $RefreshSig$();\n\n/* eslint-disable */\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, styled, CircularProgress, Backdrop, Button, TextField, MenuItem, IconButton, Menu, Divider, Switch, FormControlLabel, Stack } from '@mui/material';\nimport { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\nimport { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';\nimport { es } from 'date-fns/locale';\nimport { startOfMonth, endOfMonth, startOfWeek, endOfWeek, subDays, subMonths, isSameDay, format, getDaysInMonth, addDays } from 'date-fns';\nimport { getSalesDashboardGoalsPromise } from 'services/salesDashboard';\nimport PredictionModal from './PredictionModal';\nimport { CalendarToday as CalendarTodayIcon, Schedule as ScheduleIcon, Clear as ClearIcon, CompareArrows as CompareIcon, ExpandMore as ExpandMoreIcon } from '@mui/icons-material';\nimport { updateGlobalPercentages, trafficLightColors, LegendGroup, getStoreColor, getStatusLabel, MetricPercentage, StoreGrid, StoreName, MetricCard, MetricHeader, MetricTitle, MetricDate, MetricContent, MetricInfo, ChartContainer, MetricValues, MetricValue, ChartLegend, LegendItem, LegendDot, TotalCard, TotalHeader, TotalTitle, TotalTitleIcon, TotalStats, StatItem, StatLabel, StatValue, ProgressBarContainer, ProgressLabel, StyledLinearProgress, PredictButton, ButtonContainer, UpdatedAt, TotalSection, FooterLegend } from './summary/components';\nimport { RadialBarChart, RadialBar, ResponsiveContainer, PolarAngleAxis, BarChart, Bar, XAxis, YAxis, Tooltip } from 'recharts';\nimport DateRangeInput from './DateRangeInput';\nimport { formatFriendlyDate, getFirstDayOfCurrentMonth, getFirstDayOfGivenMonth, getTodayDate, getTodayDateTime, isFullMonth } from 'utils/dates';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalLegendPercentages = {\n  green: 80,\n  yellow: 51,\n  orange: 50,\n  red: 25\n};\n\nconst SummaryTab = () => {\n  _s();\n\n  const [data, setData] = useState([]);\n  const [compareData, setCompareData] = useState([]); // Datos del periodo para comparar\n\n  const [loading, setLoading] = useState(true);\n  const [compareLoading, setCompareLoading] = useState(false);\n  const [apiMode, setApiMode] = useState(0); // 0 para la api oficial, 1 para api url completa\n\n  const [selectedTab, setSelectedTab] = useState('day'); // 'day', 'month', 'date'\n\n  const [dateRange, setDateRange] = useState([getTodayDate(), getTodayDate()]); // [startDate, endDate]\n\n  const [dateRangePopperOpen, setDateRangePopperOpen] = useState(false);\n  const [predictionModalOpen, setPredictionModalOpen] = useState(false);\n  const [selectedStore, setSelectedStore] = useState(null);\n  const [selectedStoreData, setSelectedStoreData] = useState(null);\n  const [showComparison, setShowComparison] = useState(false); // Controla si se muestra la comparación\n\n  const [comparePeriod, setComparePeriod] = useState(1); // Número de periodos hacia atrás para comparar\n\n  const [customCompareDate, setCustomCompareDate] = useState(null);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [compareMenuAnchor, setCompareMenuAnchor] = useState(null);\n  const [originalTab, setOriginalTab] = useState('day'); // Guardar la pestaña original antes de la comparación\n\n  const [compareTicketCount, setCompareTicketCount] = useState(0); // Estado para los menús personalizados\n\n  const [showDateRangeMenu, setShowDateRangeMenu] = useState(false);\n  const [showCalendarMenu, setShowCalendarMenu] = useState(false); // Función para comprobar si dos fechas son el mismo día\n\n  const isSameDay = (date1, date2) => {\n    if (!date1 || !date2) return false;\n    return date1.getDate() === date2.getDate() && date1.getMonth() === date2.getMonth() && date1.getFullYear() === date2.getFullYear();\n  }; // Función para determinar si el periodo actual permite comparación\n\n\n  const isComparisonAllowed = () => {\n    const today = getTodayDate(); // \"Hoy\" no permite comparación\n\n    if (selectedTab === 'day') return false; // \"Avance del mes\" no permite comparación\n\n    if (selectedTab === 'period') return false; // \"Mes\" solo permite comparación si NO es el mes actual\n\n    if (selectedTab === 'month' || selectedTab === 'thisMonth') {\n      return true;\n      const isCurrentMonth = today.getMonth() === dateRange[0].getMonth() && today.getFullYear() === dateRange[0].getFullYear();\n      return !isCurrentMonth;\n    } // Fecha personalizada solo permite comparación si NO es hoy\n\n\n    if (selectedTab === 'date') {\n      return !isSameDay(dateRange[0], today);\n    } // El resto de periodos (ayer, mes pasado, etc.) sí permiten comparación\n\n\n    return true;\n  }; // Función para formatear fechas correctamente para Perú (UTC-5)\n\n\n  const formatPeruDate = date => {\n    if (!date) return ''; // Creamos una nueva fecha para no modificar la original\n\n    const d = new Date(date); // Obtenemos año, mes y día en formato peruano\n\n    const year = d.getFullYear();\n    const month = String(d.getMonth() + 1).padStart(2, '0');\n    const day = String(d.getDate()).padStart(2, '0'); // Retornamos en formato YYYY-MM-DD\n\n    return `${year}-${month}-${day}`;\n  }; // Función para obtener la fecha actual considerando horario de Perú\n\n\n  const getPeruDate = () => {\n    // Crear una nueva fecha que represente el momento actual\n    const now = getTodayDateTime(); // Ajustar a la zona horaria de Perú (UTC-5)\n    // Esto asegura que la fecha sea correcta para Perú\n\n    const peruDate = new Date(now.toLocaleString('en-US', {\n      timeZone: 'America/Lima'\n    }));\n    return peruDate;\n  }; // Función para calcular el porcentaje de avance de ventas\n\n\n  const calculatePercentage = function (salesOrStore) {\n    let goal = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n\n    // Si se pasa un objeto store completo\n    if (typeof salesOrStore === 'object' && salesOrStore !== null) {\n      const storeData = salesOrStore;\n      if (!storeData) return 0;\n      let salesValue, goalValue;\n\n      if (selectedTab === 'day' || selectedTab === 'yesterday') {\n        salesValue = parseFloat(storeData.today_sales || 0);\n        goalValue = parseFloat(storeData.today_goal || 0);\n      } else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth') {\n        salesValue = parseFloat(storeData.progress_sales || 0);\n        goalValue = parseFloat(storeData.period_goal || 0);\n      } else {\n        // Para semana actual o semana pasada\n        salesValue = parseFloat(storeData.progress_sales || 0);\n        goalValue = parseFloat(storeData.today_goal || 0) * totalDays;\n      }\n\n      if (goalValue === 0) return 0;\n      return salesValue / goalValue * 100;\n    } // Si se pasan valores numéricos directamente\n    else {\n      const sales = parseFloat(salesOrStore || 0);\n      const targetGoal = parseFloat(goal || 0);\n      if (!targetGoal || targetGoal === 0) return 0;\n      return sales / targetGoal * 100;\n    }\n  }; // Nueva función para cerrar el menú de opciones de rango de fechas\n\n\n  const handleDateRangeMenuClose = () => {\n    setDateRangePopperOpen(false);\n  }; // Maneja la apertura del menú de comparación\n\n\n  const handleCompareMenuOpen = event => {\n    setCompareMenuAnchor(event.currentTarget);\n  }; // Maneja el cierre del menú de comparación\n\n\n  const handleCompareMenuClose = () => {\n    setCompareMenuAnchor(null);\n  }; // Maneja la selección de una opción de comparación rápida\n\n\n  const handleQuickCompareSelect = (periodType, value) => {\n    setComparePeriod(value); // Calcular la fecha correspondiente basada en la selección\n\n    const today = getPeruDate();\n    let newDate = new Date(today);\n\n    if (periodType === 'days') {\n      newDate.setDate(today.getDate() - value);\n    } else if (periodType === 'months') {\n      newDate.setMonth(today.getMonth() - value);\n    } else if (periodType === 'years') {\n      newDate.setFullYear(today.getFullYear() - value);\n    } // Ajustar la fecha según el tipo de vista seleccionada\n\n\n    if (selectedTab === 'month') {\n      // Para vista mensual, asegurarse de que sea el primer día del mes\n      newDate = new Date(newDate.getFullYear(), newDate.getMonth(), 1);\n    }\n\n    setCustomCompareDate(newDate);\n    handleCompareMenuClose();\n\n    if (showComparison) {\n      fetchComparisonData(value, newDate);\n    }\n  }; // Maneja la selección de fecha personalizada\n\n\n  const handleDatePickerChange = newDate => {\n    if (newDate) {\n      setCustomCompareDate(newDate); // Calculamos el offset aproximado en días para mantener la compatibilidad\n\n      const today = getPeruDate();\n      const diffTime = Math.abs(today - newDate);\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n      setComparePeriod(diffDays);\n\n      if (showComparison) {\n        fetchComparisonData(diffDays, newDate);\n      }\n    }\n\n    setAnchorEl(null); // cerrar el selector de fecha\n  }; // Actualizar fetchComparisonData para manejar rangos de fechas\n\n\n  const fetchComparisonData = async function (days, specificDate) {\n    let endDate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    setCompareLoading(true);\n\n    try {\n      const params = {};\n      const today = getPeruDate();\n\n      if (specificDate) {\n        // Si se proporciona una fecha específica, la usamos directamente\n        if (selectedTab === 'day') {\n          params.date = formatPeruDate(specificDate);\n        } else if (selectedTab === 'month') {\n          //params.month = (specificDate.getMonth() + 1).toString();\n          //params.year = specificDate.getFullYear().toString();\n          // Usar rango de fechas en lugar de month/year para mayor precisión en la comparación\n          if (specificDate && endDate) {\n            // Si se proporcionan fechas específicas de inicio y fin para la comparación\n            params.date_from = formatPeruDate(specificDate);\n            params.date_to = formatPeruDate(endDate);\n          } else if (specificDate) {\n            // Si solo se proporciona una fecha, calcular el rango del mes anterior\n            const startOfMonth = new Date(specificDate.getFullYear(), specificDate.getMonth(), 1);\n            const daysInMonth = new Date(specificDate.getFullYear(), specificDate.getMonth() + 1, 0).getDate();\n            const endOfMonth = new Date(specificDate.getFullYear(), specificDate.getMonth(), Math.min(daysInMonth, dateRange[1].getDate()));\n            params.date_from = formatPeruDate(startOfMonth);\n            params.date_to = formatPeruDate(endOfMonth);\n          } else {\n            // Si no se proporciona fecha, usar los mismos rangos que generados en generateSmartComparisonPeriod\n            const [compStart, compEnd] = generateSmartComparisonPeriod();\n            params.date_from = formatPeruDate(compStart);\n            params.date_to = formatPeruDate(compEnd);\n          }\n        } else {\n          // Para rango de fechas, usar fecha_desde y fecha_hasta\n          const compareEndDate = endDate || specificDate;\n          params.date_from = formatPeruDate(specificDate);\n          params.date_to = formatPeruDate(compareEndDate);\n        }\n      } else {\n        // Cálculo basado en días hacia atrás\n        const compareDate = subDays(today, days);\n\n        if (selectedTab === 'day') {\n          params.date = formatPeruDate(compareDate);\n        } else if (selectedTab === 'month') {\n          params.month = (compareDate.getMonth() + 1).toString();\n          params.year = compareDate.getFullYear().toString();\n        } else {\n          // Para rango de fechas, calcular un rango equivalente en el pasado\n          const diffTime = Math.abs(dateRange[1] - dateRange[0]);\n          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n          const compareStartDate = subDays(dateRange[0], days);\n          const compareEndDate = subDays(dateRange[1], days);\n          params.date_from = formatPeruDate(compareStartDate);\n          params.date_to = formatPeruDate(compareEndDate);\n        }\n      }\n\n      const response = await getSalesDashboardGoalsPromise(params);\n      setCompareData(response.data); // Calcular la cantidad de tickets para la comparación\n\n      const compareTickets = response.data.reduce((acc, store) => acc + parseInt(store.today_tickets || 0), 0);\n      setCompareTicketCount(compareTickets); // Calcular totales para el periodo de comparación\n\n      const totalResults = calculateTotals(response.data); // setCompareTotals(totalResults);\n      // Calcular porcentaje total de cumplimiento para comparación\n\n      let comparePercentageTotal;\n\n      if (selectedTab === 'day' || selectedTab === 'yesterday') {\n        comparePercentageTotal = calculatePercentage(totalResults.today_sales, totalResults.today_goal);\n      } else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth') {\n        comparePercentageTotal = calculatePercentage(totalResults.sale_progress, totalResults.period_goal);\n      } else {\n        comparePercentageTotal = calculatePercentage(totalResults.sale_progress, totalResults.today_goal * totalDays);\n      } // setCompareTotalPercentage(comparePercentageTotal);\n\n    } catch (error) {\n      /* eslint-disable */\n      console.error(...oo_tx(`1047047346_344_12_344_67_11`, 'Error fetching comparison data:', error));\n    } finally {\n      setCompareLoading(false);\n    }\n  }; // Función para alternar la vista de comparación\n\n\n  const toggleComparison = () => {\n    const newValue = !showComparison;\n    setShowComparison(newValue);\n\n    if (newValue) {\n      // Guardar la pestaña original\n      setOriginalTab(selectedTab); // Generar periodo de comparación inteligente\n\n      const [comparisonStartDate, comparisonEndDate, diffDays] = generateSmartComparisonPeriod(); // Usar la fecha de inicio como fecha de comparación principal\n\n      setCustomCompareDate(comparisonStartDate);\n      setComparePeriod(diffDays); // Obtener datos de comparación basados en el periodo generado\n\n      fetchComparisonData(diffDays, comparisonStartDate, comparisonEndDate);\n    } else {\n      // Limpiar comparación\n      setCustomCompareDate(null);\n      setCompareData([]);\n    }\n  };\n\n  const desactivateComparison = () => {\n    setShowComparison(false);\n    setCustomCompareDate(null);\n    setComparePeriod(0);\n    setCompareData([]);\n  };\n\n  useEffect(() => {\n    const fetchData = async () => {\n      setLoading(true); // Activar loading al inicio\n\n      try {\n        let response;\n\n        if (apiMode === 0) {\n          // Crear un objeto con los parámetros según la pestaña seleccionada\n          const params = {}; // Obtener la fecha actual para usar en las consultas (usando zona horaria de Perú)\n\n          const today = getPeruDate();\n          const formattedDate = formatPeruDate(today); // YYYY-MM-DD con zona horaria de Perú\n\n          if (selectedTab === 'day') {\n            // Para compatibilidad con modo 'día', usar solo una fecha\n            // Si es un rango del mismo día, usar ese día, sino usar la fecha actual\n            if (dateRange[0] && dateRange[1] && isSameDay(dateRange[0], dateRange[1])) {\n              params.date = formatPeruDate(dateRange[0]);\n            } else {\n              params.date = formattedDate;\n            }\n          } else if (selectedTab === 'yesterday') {\n            // Para el caso específico de \"Ayer\"\n            const yesterday = subDays(today, 1);\n            params.date = formatPeruDate(yesterday);\n          } else if (selectedTab === 'week' || selectedTab === 'thisWeek') {\n            // Para el caso de \"Esta semana\" (últimos 7 días)\n            const weekStart = new Date(today);\n            weekStart.setDate(today.getDate() - 6); // 6 días atrás + hoy = 7 días\n\n            params.date_from = formatPeruDate(weekStart);\n            params.date_to = formatPeruDate(today);\n          } else if (selectedTab === 'lastWeek') {\n            // Para semana pasada (7 días anteriores a la semana actual)\n            const lastWeekStart = new Date(today);\n            lastWeekStart.setDate(today.getDate() - 13); // 7 días antes del inicio de los últimos 7 días\n\n            const lastWeekEnd = new Date(today);\n            lastWeekEnd.setDate(today.getDate() - 7); // 7 días antes de hoy\n\n            params.date_from = formatPeruDate(lastWeekStart);\n            params.date_to = formatPeruDate(lastWeekEnd);\n          } else if (selectedTab === 'period' || selectedTab === 'thisPeriod') {\n            // Para este periodo, desde inicio del mes hasta hoy\n            const startOfCurrentMonth = startOfMonth(today);\n            params.date_from = formatPeruDate(startOfCurrentMonth);\n            params.date_to = formatPeruDate(today);\n          } else if (selectedTab === 'month') {\n            // Usar rango de fechas en lugar de mes/año para mayor precisión\n            if (dateRange[0] && dateRange[1]) {\n              params.date_from = formatPeruDate(dateRange[0]);\n              params.date_to = formatPeruDate(dateRange[1]);\n            } else {\n              // Fallback al mes actual si no hay rango definido\n              params.month = (today.getMonth() + 1).toString();\n              params.year = today.getFullYear().toString();\n            }\n          } else if (selectedTab === 'date') {\n            // Configurar parámetros para consulta por rango de fechas\n            if (dateRange[0] && dateRange[1]) {\n              // Validación adicional para asegurarse de que las fechas estén en el mismo mes/año\n              // si estamos en modo 'date' pero el API requiere fechas del mismo mes\n              if (dateRange[0].getMonth() === dateRange[1].getMonth() && dateRange[0].getFullYear() === dateRange[1].getFullYear()) {\n                params.date_from = formatPeruDate(dateRange[0]);\n                params.date_to = formatPeruDate(dateRange[1]);\n              } else {\n                // Si se requieren fechas del mismo mes pero no lo son, ajustar el rango\n                // al último día del mes de la fecha inicial\n                const lastDayOfStartMonth = endOfMonth(dateRange[0]);\n                params.date_from = formatPeruDate(dateRange[0]);\n                params.date_to = formatPeruDate(lastDayOfStartMonth);\n                console.warn('Las fechas deben ser del mismo mes para la API. Ajustando fecha final al último día del mes inicial.');\n              }\n            } else {\n              // Comportamiento por defecto (desde inicio de mes hasta hoy)\n              const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\n              params.date_from = formatPeruDate(firstDayOfMonth);\n              params.date_to = formattedDate;\n            }\n          } // params.business_unit_id = ...;\n          // params.store_id = ...;\n\n\n          response = await getSalesDashboardGoalsPromise(params);\n          setData(response.data);\n\n          if (response.legend_percentages) {\n            updateGlobalPercentages(response.legend_percentages);\n          }\n        }\n      } catch (error) {\n        /* eslint-disable */\n        console.error(...oo_tx(`1047047346_475_16_475_60_11`, 'Error fetching data:', error)); // Si falla la primera API, intentar con la segunda\n\n        if (apiMode === 0) {\n          setApiMode(1);\n        }\n      } finally {\n        // Agregar delay para evitar parpadeos\n        setTimeout(() => {\n          setLoading(false);\n        }, 500);\n      }\n    };\n\n    fetchData();\n  }, [apiMode, selectedTab, dateRange]); // Agregar dateRange como dependencia\n\n  const handlePredictClick = storeName => {\n    // Encontrar los datos de la tienda seleccionada\n    let storeData;\n\n    if (storeName === 'Total MIA') {\n      // Para el caso de Total MIA, creamos un objeto con los totales\n      if (selectedTab === 'day') {\n        var _data$;\n\n        storeData = {\n          business_unit_name: 'Total MIA',\n          today_sales: totals.today_sales,\n          today_goal: totals.today_goal,\n          progress_sales: totals.sale_progress,\n          // Mantener para compatibilidad\n          period_goal: totals.period_goal,\n          // Mantener para compatibilidad\n          query_date: (_data$ = data[0]) === null || _data$ === void 0 ? void 0 : _data$.query_date,\n          selected_tab: 'day'\n        };\n      } else if (selectedTab === 'month') {\n        var _data$2;\n\n        storeData = {\n          business_unit_name: 'Total MIA',\n          today_sales: totals.today_sales,\n          // Mantener para compatibilidad\n          today_goal: totals.today_goal,\n          // Mantener para compatibilidad\n          progress_sales: totals.sale_progress,\n          period_goal: totals.period_goal,\n          query_date: (_data$2 = data[0]) === null || _data$2 === void 0 ? void 0 : _data$2.query_date,\n          selected_tab: 'month'\n        };\n      } else {\n        var _data$3;\n\n        // Para el tab 'date', calculamos la meta ajustada por días transcurridos\n        storeData = {\n          business_unit_name: 'Total MIA',\n          today_sales: totals.today_sales,\n          // Mantener para compatibilidad\n          today_goal: totals.today_goal,\n          // Meta diaria original\n          progress_sales: totals.sale_progress,\n          // Ventas acumuladas\n          period_goal: totals.period_goal,\n          // Mantener para compatibilidad\n          date_goal: dateTotals.goal,\n          // Meta ajustada por días\n          elapsed_days: elapsedDays,\n          // Días transcurridos\n          query_date: (_data$3 = data[0]) === null || _data$3 === void 0 ? void 0 : _data$3.query_date,\n          selected_tab: 'date'\n        };\n      }\n    } else {\n      // Para tiendas individuales, buscamos en el array de datos\n      const store = data.find(store => store.business_unit_name === storeName);\n\n      if (store) {\n        if (selectedTab === 'day') {\n          storeData = { ...store,\n            selected_tab: 'day'\n          };\n        } else if (selectedTab === 'month') {\n          storeData = { ...store,\n            selected_tab: 'month'\n          };\n        } else {\n          // Para el tab 'date', calculamos la meta ajustada por días transcurridos\n          const dateGoal = parseFloat(store.today_goal) * elapsedDays;\n          storeData = { ...store,\n            date_goal: dateGoal,\n            // Meta ajustada por días\n            elapsed_days: elapsedDays,\n            // Días transcurridos\n            selected_tab: 'date'\n          };\n        }\n      }\n    }\n\n    if (storeData) {\n      setSelectedStore(storeName);\n      setSelectedStoreData(storeData);\n      setPredictionModalOpen(true);\n    }\n  };\n\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Backdrop, {\n      sx: {\n        color: '#fff',\n        zIndex: theme => theme.zIndex.drawer + 1,\n        backgroundColor: 'rgba(255, 255, 255, 0.8)'\n      },\n      open: loading,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 50,\n          thickness: 4,\n          sx: {\n            color: '#b3256e'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            color: '#b3256e',\n            fontWeight: 'bold'\n          },\n          children: \"Cargando datos...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 575,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 13\n    }, this);\n  } // Formato para montos con símbolo de moneda\n\n\n  const formatNumber = value => {\n    if (value === undefined || value === null) return 'S/ 0.00';\n    return `S/ ${parseFloat(value).toLocaleString('es-PE', {\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    })}`;\n  };\n\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const day = date.getDate().toString().padStart(2, '0');\n    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n    const year = date.getFullYear();\n    return `${day}/${month}/${year}`;\n  }; // Cálculo de días transcurridos y días totales para el rango seleccionado\n\n\n  const calculateDateRangeInfo = () => {\n    // Si no hay rango de fechas, devolver valores por defecto\n    if (!dateRange[0] || !dateRange[1]) return {\n      elapsedDays: 1,\n      totalDays: 1\n    }; // Función para normalizar una fecha (ponerla a medianoche)\n\n    const normalizeDate = date => {\n      const d = new Date(date);\n      d.setHours(0, 0, 0, 0);\n      return d;\n    }; // Calcular la diferencia en días entre dos fechas normalizadas\n\n\n    const getDayDifference = (start, end) => {\n      const diffTime = Math.abs(end - start);\n      return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 para incluir ambos días\n    };\n\n    const today = getPeruDate();\n    const normalizedToday = normalizeDate(today); // Determinar el rango según la pestaña seleccionada\n\n    let startDate, endDate, currentDate;\n\n    if (selectedTab === 'day' || selectedTab === 'yesterday') {\n      // Para día, el rango es solo un día\n      return {\n        elapsedDays: 1,\n        totalDays: 1\n      };\n    } else if (selectedTab === 'week' || selectedTab === 'thisWeek') {\n      // Para semana, mostrar los últimos 7 días incluido hoy\n      startDate = normalizeDate(new Date(today));\n      startDate.setDate(startDate.getDate() - 6); // 6 días atrás + hoy = 7 días\n\n      endDate = normalizeDate(today);\n      currentDate = normalizedToday;\n    } else if (selectedTab === 'lastWeek') {\n      // Para semana pasada, los 7 días anteriores a la semana actual\n      startDate = normalizeDate(new Date(today));\n      startDate.setDate(startDate.getDate() - 13);\n      endDate = normalizeDate(new Date(today));\n      endDate.setDate(endDate.getDate() - 7);\n      currentDate = endDate; // Ya pasaron todos los días\n    } else if (selectedTab === 'period' || selectedTab === 'thisPeriod') {\n      // Para este periodo, desde inicio del mes hasta hoy\n      startDate = normalizeDate(startOfMonth(today));\n      endDate = normalizeDate(today);\n      currentDate = normalizedToday;\n    } else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth') {\n      // Para este mes, desde inicio del mes hasta hoy\n      startDate = normalizeDate(startOfMonth(today));\n      endDate = normalizeDate(today);\n      currentDate = normalizedToday;\n    } else if (selectedTab === 'lastMonth') {\n      // Para mes pasado, todo el mes anterior\n      const lastMonth = subMonths(today, 1);\n      startDate = normalizeDate(startOfMonth(lastMonth));\n      endDate = normalizeDate(endOfMonth(lastMonth));\n      currentDate = endDate; // Ya pasaron todos los días\n    } else {\n      // Para otros casos o rango personalizado\n      startDate = normalizeDate(dateRange[0]);\n      endDate = normalizeDate(dateRange[1]);\n      currentDate = normalizedToday;\n    } // Cálculo de días\n\n\n    const totalDays = getDayDifference(startDate, endDate);\n    let elapsedDays;\n\n    if (currentDate > endDate) {\n      // Si la fecha actual es posterior al final del rango, todos los días han transcurrido\n      elapsedDays = totalDays;\n    } else if (currentDate < startDate) {\n      // Si la fecha actual es anterior al inicio del rango, aún no ha transcurrido ningún día\n      elapsedDays = 0;\n    } else {\n      // Si la fecha actual está dentro del rango, calcular los días transcurridos\n      elapsedDays = getDayDifference(startDate, currentDate);\n    }\n\n    return {\n      elapsedDays,\n      totalDays\n    };\n  };\n\n  const {\n    elapsedDays,\n    totalDays\n  } = calculateDateRangeInfo(); // Calcular totales para tarjetas y gráficos\n\n  const calculateTotals = () => {\n    if (!data || data.length === 0) return {\n      today_sales: 0,\n      today_goal: 0,\n      period_goal: 0,\n      sale_progress: 0,\n      today_average_ticket: 0,\n      today_tickets: 0\n    };\n    return data.reduce((acc, store) => {\n      return {\n        today_sales: acc.today_sales + parseFloat(store.today_sales || 0),\n        today_goal: acc.today_goal + parseFloat(store.today_goal || 0),\n        period_goal: acc.period_goal + parseFloat(store.period_goal || 0),\n        sale_progress: acc.sale_progress + parseFloat(store.progress_sales || 0),\n        today_average_ticket: parseFloat(store.today_average_ticket || 0),\n        today_tickets: acc.today_tickets + parseInt(store.today_tickets || 0)\n      };\n    }, {\n      today_sales: 0,\n      today_goal: 0,\n      period_goal: 0,\n      sale_progress: 0,\n      today_average_ticket: 0,\n      today_tickets: 0\n    });\n  }; // Calcular totales\n\n\n  const totals = calculateTotals(); // Calcular los totales para el tab de fecha\n\n  const dateTotals = {\n    goal: totals.today_goal * totalDays,\n    remaining: totals.today_goal * totalDays - totals.sale_progress\n  }; // Calcular porcentaje de cumplimiento total\n\n  const totalPercentage = selectedTab === 'day' || selectedTab === 'yesterday' ? calculatePercentage(totals.today_sales, totals.today_goal) // Para hoy/ayer\n  : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth' ? calculatePercentage(totals.sale_progress, totals.period_goal) // Para cualquier mes\n  : calculatePercentage(totals.sale_progress, totals.today_goal * totalDays); // Para semana/rango, meta diaria * total de días\n\n  const DateSelectorButton = styled(Button)(_ref => {\n    let {\n      theme\n    } = _ref;\n    return {\n      borderColor: '#b3256e',\n      color: '#b3256e',\n      minWidth: '220px',\n      display: 'flex',\n      justifyContent: 'space-between',\n      '&:hover': {\n        borderColor: '#9a1e5c',\n        backgroundColor: 'rgba(179, 37, 110, 0.04)'\n      },\n      boxShadow: '0 2px 4px rgba(0,0,0,0.05)',\n      borderRadius: '8px',\n      padding: '8px 16px',\n      transition: 'all 0.2s ease'\n    };\n  });\n  const DateSelectorContainer = styled(Box)(_ref2 => {\n    let {\n      theme\n    } = _ref2;\n    return {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n      padding: '10px 16px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '10px',\n      boxShadow: '0 2px 8px rgba(0,0,0,0.05)',\n      marginBottom: '16px',\n      position: 'relative',\n      justifyContent: 'center',\n      // Cambiado de 'flex-end' a 'center'\n      flexWrap: 'wrap',\n      overflow: 'visible',\n      zIndex: 1\n    };\n  });\n  const CompareButton = styled(Button)(_ref3 => {\n    let {\n      theme,\n      active\n    } = _ref3;\n    return {\n      backgroundColor: active ? '#b3256e' : 'transparent',\n      color: active ? 'white' : '#b3256e',\n      borderColor: '#b3256e',\n      '&:hover': {\n        backgroundColor: active ? '#9a1e5c' : 'rgba(179, 37, 110, 0.04)',\n        borderColor: '#9a1e5c'\n      },\n      fontWeight: active ? 'bold' : 'normal',\n      borderRadius: '8px',\n      padding: '6px 16px',\n      transition: 'all 0.2s ease',\n      boxShadow: active ? '0 2px 5px rgba(179, 37, 110, 0.2)' : 'none'\n    };\n  });\n  const DateRangeActionButton = styled(Button)(_ref4 => {\n    let {\n      theme\n    } = _ref4;\n    return {\n      padding: '8px 16px',\n      fontSize: '1rem',\n      textTransform: 'none',\n      '&:hover': {\n        backgroundColor: 'rgba(179, 37, 110, 0.08)'\n      },\n      '&.MuiButton-containedPrimary': {\n        backgroundColor: '#b3256e',\n        '&:hover': {\n          backgroundColor: '#9a1e5c'\n        }\n      }\n    };\n  });\n  const DateRangeTextField = styled(TextField)(_ref5 => {\n    let {\n      theme\n    } = _ref5;\n    return {\n      '& .MuiOutlinedInput-root': {\n        padding: '8px 12px',\n        fontSize: '1rem',\n        '& fieldset': {\n          borderColor: '#b3256e',\n          borderWidth: '1px'\n        },\n        '&:hover fieldset': {\n          borderColor: '#9a1e5c'\n        },\n        '&.Mui-focused fieldset': {\n          borderColor: '#b3256e'\n        }\n      }\n    };\n  }); // Estilos personalizados para los menús\n\n  const CustomMenuContainer = styled(Box)(_ref6 => {\n    let {\n      theme\n    } = _ref6;\n    return {\n      position: 'absolute',\n      top: '100%',\n      left: 0,\n      backgroundColor: '#ffffff',\n      boxShadow: '0 4px 20px rgba(0,0,0,0.15)',\n      borderRadius: '8px',\n      padding: '8px 0',\n      marginTop: '8px',\n      zIndex: 9999,\n      minWidth: '200px',\n      overflow: 'visible'\n    };\n  });\n  const CustomCalendarContainer = styled(Box)(_ref7 => {\n    let {\n      theme\n    } = _ref7;\n    return {\n      position: 'absolute',\n      top: '100%',\n      left: '50%',\n      transform: 'translateX(-50%)',\n      backgroundColor: '#ffffff',\n      boxShadow: '0 4px 20px rgba(0,0,0,0.15)',\n      borderRadius: '8px',\n      padding: '16px',\n      marginTop: '8px',\n      zIndex: 9999,\n      width: '340px',\n      overflow: 'visible'\n    };\n  });\n  const CustomMenuItem = styled(Box)(_ref8 => {\n    let {\n      theme\n    } = _ref8;\n    return {\n      padding: '10px 16px',\n      cursor: 'pointer',\n      '&:hover': {\n        backgroundColor: 'rgba(179, 37, 110, 0.08)'\n      },\n      transition: 'background-color 0.2s'\n    };\n  }); // Componente para mostrar información de comparación\n\n  const ComparisonInfo = styled(Box)(_ref9 => {\n    let {\n      theme\n    } = _ref9;\n    return {\n      display: 'flex',\n      alignItems: 'center',\n      padding: '6px 12px',\n      borderRadius: '6px',\n      backgroundColor: 'rgba(179, 37, 110, 0.12)',\n      fontSize: '0.85rem',\n      color: '#b3256e',\n      marginLeft: '12px',\n      fontWeight: 500,\n      border: '1px solid rgba(179, 37, 110, 0.2)',\n      boxShadow: '0 1px 3px rgba(0,0,0,0.05)',\n      '& .MuiSvgIcon-root': {\n        fontSize: '1.1rem',\n        marginRight: '6px'\n      }\n    };\n  }); // Función para formatear texto de periodo de comparación\n\n  const getComparisonText = () => {\n    if (!showComparison || !customCompareDate) return ''; // Calcular fecha de fin del periodo comparativo\n\n    const getCompareEndDate = () => {\n      if (selectedTab === 'day') {\n        return customCompareDate;\n      } else if (selectedTab === 'week' || selectedTab === 'thisWeek') {\n        // Para semana actual, calculamos el final como la diferencia entre inicio y fin + customCompareDate\n        const diffTime = dateRange[1].getTime() - dateRange[0].getTime();\n        const compareEndDate = new Date(customCompareDate.getTime() + diffTime);\n        return compareEndDate;\n      } else if (selectedTab === 'lastWeek') {\n        // Para semana pasada, usar el final del periodo de comparación (7 días antes del inicio de los últimos 7 días)\n        const compareEndDate = new Date(today);\n        compareEndDate.setDate(today.getDate() - 7); // 7 días antes de hoy (final del periodo anterior)\n\n        return compareEndDate;\n      } else if (selectedTab === 'month' || selectedTab === 'thisMonth') {\n        const currentMonth = getPeruDate().getMonth();\n        const compareMonth = customCompareDate.getMonth(); // Si estamos comparando con un mes distinto, usar último día hasta la fecha actual\n\n        if (currentMonth !== compareMonth) {\n          // Si estamos a día 15, el mes anterior también termina el 15\n          const currentDay = getPeruDate().getDate();\n          return new Date(customCompareDate.getFullYear(), customCompareDate.getMonth(), Math.min(currentDay, getDaysInMonth(customCompareDate)));\n        } else {\n          // Si es el mismo mes pero año anterior, usar el mismo día que hoy pero en esa fecha\n          return customCompareDate;\n        }\n      } else if (selectedTab === 'lastMonth') {\n        // Obtener el día actual para mantener consistencia en la comparación\n        const currentDay = getPeruDate().getDate(); // Usar el mismo día en el mes anterior o el último día del mes anterior si el mes es más corto\n\n        return new Date(customCompareDate.getFullYear(), customCompareDate.getMonth(), Math.min(currentDay, getDaysInMonth(customCompareDate)));\n      } else if (selectedTab === 'period') {\n        // Para periodo actual (del 1 al día actual)\n        const diffTime = dateRange[1].getTime() - dateRange[0].getTime();\n        const compareEndDate = new Date(customCompareDate.getTime() + diffTime);\n        return compareEndDate;\n      } else {\n        // Para fechas personalizadas, calcular mediante diferencia\n        const diffTime = dateRange[1].getTime() - dateRange[0].getTime();\n        const compareEndDate = new Date(customCompareDate.getTime() + diffTime);\n        return compareEndDate;\n      }\n\n      return compareEndDate;\n    };\n\n    const compareEndDate = getCompareEndDate(); // Usar formato claro según el tipo de periodo\n\n    if (selectedTab === 'day') {\n      return `${formatDate(customCompareDate)}`;\n    } else if (selectedTab === 'yesterday') {\n      return `${formatDate(customCompareDate)}`;\n    } else if (selectedTab === 'week' || selectedTab === 'thisWeek' || selectedTab === 'lastWeek') {\n      // Comprobar si las fechas son el mismo día\n      if (isSameDay(customCompareDate, compareEndDate)) {\n        return `${formatDate(customCompareDate)}`;\n      }\n\n      return `${formatDate(customCompareDate)} al ${formatDate(compareEndDate)}`;\n    } else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth') {\n      const compareMonthName = customCompareDate.toLocaleString('default', {\n        month: 'long',\n        year: 'numeric'\n      });\n      return `${compareMonthName}`;\n    } else if (selectedTab === 'period') {\n      // Comprobar si las fechas son el mismo día\n      if (isSameDay(customCompareDate, compareEndDate)) {\n        return `${formatDate(customCompareDate)}`;\n      }\n\n      return `${formatDate(customCompareDate)} al ${formatDate(compareEndDate)}`;\n    } else {\n      // Para fechas personalizadas, comprobar si es el mismo día\n      if (isSameDay(customCompareDate, compareEndDate)) {\n        return `${formatDate(customCompareDate)}`;\n      }\n\n      return `${formatDate(customCompareDate)} al ${formatDate(compareEndDate)}`;\n    }\n  }; // Función simple para mostrar/ocultar menú de rangos\n\n\n  const toggleDateRangeMenu = () => {\n    setShowDateRangeMenu(!showDateRangeMenu);\n    setShowCalendarMenu(false); // Cerrar el otro menú\n  }; // Función simple para mostrar/ocultar menú de calendario\n\n\n  const toggleCalendarMenu = () => {\n    setShowCalendarMenu(!showCalendarMenu);\n    setShowDateRangeMenu(false); // Cerrar el otro menú\n  }; // Función para cerrar ambos menús\n\n\n  const closeAllMenus = () => {\n    setShowDateRangeMenu(false);\n    setShowCalendarMenu(false);\n  };\n\n  const applyPresetRange = preset => {\n    const today = getTodayDate();\n    let newStartDate = null;\n    let newEndDate = null;\n    let newTab = '';\n\n    switch (preset) {\n      case 'today':\n        newStartDate = today;\n        newEndDate = today;\n        newTab = 'day';\n        break;\n\n      case 'yesterday':\n        newStartDate = new Date(today);\n        newStartDate.setDate(today.getDate() - 1);\n        newEndDate = newStartDate;\n        newTab = 'yesterday';\n        break;\n\n      case 'thisWeek':\n        // Cambiar a últimos 7 días incluyendo hoy\n        newStartDate = new Date(today);\n        newStartDate.setDate(today.getDate() - 6); // 6 días atrás + hoy = 7 días\n\n        newEndDate = today;\n        newTab = 'week';\n        break;\n\n      case 'lastWeek':\n        // Cambiar a los 7 días anteriores a últimos 7 días\n        newStartDate = new Date(today);\n        newStartDate.setDate(today.getDate() - 13); // 7 días antes del inicio de esta semana\n\n        newEndDate = new Date(today);\n        newEndDate.setDate(today.getDate() - 7); // 7 días antes de hoy\n\n        newTab = 'lastWeek';\n        break;\n\n      case 'thisMonth':\n        /* Implementación anterior\r\n        newStartDate = new Date(today.getFullYear(), today.getMonth(), 1);\r\n        newEndDate = today;\r\n        newTab = 'month';  \r\n        */\n        newEndDate = new Date(today);\n        newEndDate.setDate(today.getDate() - 1); // Yesterday  \n\n        if (today.getTime() === getFirstDayOfCurrentMonth().getTime()) {\n          newStartDate = getFirstDayOfGivenMonth(newEndDate);\n        } else {\n          newStartDate = new Date(today.getFullYear(), today.getMonth(), 1);\n        }\n\n        newTab = 'month';\n        break;\n\n      case 'thisPeriod':\n        newStartDate = new Date(today.getFullYear(), today.getMonth(), 1);\n        newEndDate = today;\n        newTab = 'period';\n        break;\n\n      case 'lastMonth':\n        newStartDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);\n        newEndDate = new Date(today.getFullYear(), today.getMonth(), 0);\n        newTab = 'lastMonth';\n        break;\n\n      default:\n        return;\n    } // Desactivar la comparación al cambiar de periodo\n\n\n    if (showComparison) {\n      setShowComparison(false);\n      setCustomCompareDate(null);\n      setCompareData([]);\n    }\n\n    setDateRange([newStartDate, newEndDate]);\n    setSelectedTab(newTab);\n    handleDateRangeMenuClose();\n    toggleDateRangeMenu(false);\n    closeAllMenus();\n  }; // Función para generar un período de comparación inteligente basado en el período actual\n\n\n  const generateSmartComparisonPeriod = () => {\n    const today = getPeruDate();\n    let comparisonStartDate, comparisonEndDate;\n\n    if (dateRange[0] && dateRange[1]) {\n      // Verificar si el rango actual corresponde a \"Periodo Actual\" (del 1 al día actual del mes)\n      const isThisPeriod = dateRange[0].getDate() === 1 && dateRange[0].getMonth() === today.getMonth() && dateRange[0].getFullYear() === today.getFullYear() && dateRange[1].getDate() === today.getDate() && dateRange[1].getMonth() === today.getMonth() && dateRange[1].getFullYear() === today.getFullYear(); // Calcular la duración en días del período seleccionado\n\n      const diffTime = Math.abs(dateRange[1] - dateRange[0]);\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 para incluir ambos días\n      // Verificar si el rango actual corresponde a \"Esta semana\"\n\n      const startOfCurrentWeek = new Date(getTodayDate().setDate(getTodayDate().getDate() - getTodayDate().getDay() + (getTodayDate().getDay() === 0 ? -6 : 1)));\n      const isThisWeek = dateRange[0].getDate() === startOfCurrentWeek.getDate() && dateRange[0].getMonth() === startOfCurrentWeek.getMonth() && dateRange[0].getFullYear() === startOfCurrentWeek.getFullYear() && dateRange[1].getDate() === today.getDate() && dateRange[1].getMonth() === today.getMonth() && dateRange[1].getFullYear() === today.getFullYear(); // Verificar si el rango corresponde a \"Semana pasada\"\n\n      const startOfLastWeek = new Date(getTodayDate().setDate(getTodayDate().getDate() - getTodayDate().getDay() + (getTodayDate().getDay() === 0 ? -6 : 1)));\n      const endOfLastWeek = new Date(getTodayDate().setDate(getTodayDate().getDate() - 7));\n      const isLastWeek = dateRange[0].getDate() === startOfLastWeek.getDate() && dateRange[0].getMonth() === startOfLastWeek.getMonth() && dateRange[0].getFullYear() === startOfLastWeek.getFullYear() && dateRange[1].getDate() === endOfLastWeek.getDate() && dateRange[1].getMonth() === endOfLastWeek.getMonth() && dateRange[1].getFullYear() === endOfLastWeek.getFullYear();\n\n      if (isThisPeriod) {\n        // Si es \"Periodo Actual\", comparar con el mismo rango pero del mes anterior\n        const previousMonth = subMonths(today, 1);\n        comparisonStartDate = new Date(previousMonth.getFullYear(), previousMonth.getMonth(), 1); // Asegurarnos de mantener la misma cantidad de días para el período anterior\n\n        const targetDay = Math.min(today.getDate(), getDaysInMonth(previousMonth));\n        comparisonEndDate = new Date(previousMonth.getFullYear(), previousMonth.getMonth(), targetDay);\n        return [comparisonStartDate, comparisonEndDate, diffDays];\n      } else if (isThisWeek) {\n        // Si es \"Esta semana\", comparar con la semana anterior completa\n        comparisonStartDate = subDays(dateRange[0], 7);\n        comparisonEndDate = subDays(dateRange[1], 7);\n        return [comparisonStartDate, comparisonEndDate, diffDays];\n      } else if (isLastWeek) {\n        // Si es \"Semana pasada\", comparar con la semana anterior a esa\n        comparisonStartDate = subDays(dateRange[0], 7);\n        comparisonEndDate = subDays(dateRange[1], 7);\n        return [comparisonStartDate, comparisonEndDate, diffDays];\n      } else if (selectedTab === 'day' || selectedTab === 'yesterday') {\n        // Si es un solo día, comparar con el día anterior\n        comparisonStartDate = subDays(dateRange[0], 1);\n        comparisonEndDate = comparisonStartDate;\n      } else if (selectedTab === 'month') {\n        /*\r\n        // Si es un mes, comparar con el mes anterior\r\n        comparisonStartDate = subMonths(dateRange[0], 1);\r\n        // Mantener la misma cantidad de días en el mes, o usar el último día del mes anterior\r\n        const lastDayOfPrevMonth = endOfMonth(comparisonStartDate);\r\n        const targetEndDay = Math.min(dateRange[1].getDate(), lastDayOfPrevMonth.getDate());\r\n        comparisonEndDate = new Date(lastDayOfPrevMonth.getFullYear(), lastDayOfPrevMonth.getMonth(), targetEndDay);\r\n        */\n        // Si es mes (1 del mes al día anterior), comparar con mismo rango del mes anterior\n        // Primer día del mes anterior\n        comparisonStartDate = new Date(dateRange[0].getFullYear(), dateRange[0].getMonth() - 1, dateRange[0].getDate()); // Para el día final, necesitamos calcular el mismo día del mes anterior (o el último día si el mes anterior es más corto)\n\n        const daysInPreviousMonth = new Date(dateRange[0].getFullYear(), dateRange[0].getMonth(), 0).getDate();\n        const targetEndDay = Math.min(dateRange[1].getDate(), daysInPreviousMonth);\n        comparisonEndDate = new Date(dateRange[0].getFullYear(), dateRange[0].getMonth() - 1, targetEndDay);\n      } else {\n        // Para cualquier otro período, retroceder exactamente la misma cantidad de días\n        comparisonStartDate = subDays(dateRange[0], diffDays);\n        comparisonEndDate = subDays(dateRange[1], diffDays);\n      }\n\n      return [comparisonStartDate, comparisonEndDate, diffDays];\n    } // En caso de no tener un rango de fechas definido, usar el día actual y comparar con el día anterior\n\n\n    comparisonStartDate = subDays(today, 1);\n    comparisonEndDate = comparisonStartDate;\n    return [comparisonStartDate, comparisonEndDate, 1];\n  }; // Función para calcular la proyección de cierre basada en el ritmo actual\n\n\n  const calculateProjection = store => {\n    if (!store) return {\n      projectedSale: 0,\n      projectedPercentage: 0\n    }; // Obtener la fecha actual de Perú\n\n    const today = getPeruDate(); // Obtener el porcentaje actual de la tienda usando la función existente\n\n    const currentPercentage = calculatePercentage(store); // Para Hoy: proyectar basado en la hora del día\n\n    if (selectedTab === 'day') {\n      // Meta diaria\n      const todayGoal = parseFloat(store.today_goal || 0); // Incrementar el porcentaje en un 20% pero sin limitarlo al 100%\n\n      const projectedPercentage = currentPercentage * 1.2; // Calcular la venta proyectada a partir del porcentaje\n\n      const projectedSale = projectedPercentage / 100 * todayGoal;\n      return {\n        projectedSale,\n        projectedPercentage\n      };\n    } // Para Semana: proyectar basado en días transcurridos de la semana\n    else if (selectedTab === 'week' || selectedTab === 'thisWeek') {\n      // Meta semanal\n      const weeklyGoal = parseFloat(store.period_goal || 0); // Ventas actuales para calcular directamente la proyección\n\n      const currentSales = parseFloat(store.progress_sales || 0); // Siendo sábado, proyectar 20% más de ventas para el cierre de la semana\n\n      const projectedSale = currentSales * 1.2; // CAMBIO: Usar la misma lógica que para period/month para ser consistentes\n      // Aumentar el porcentaje actual directamente en lugar de recalcularlo\n\n      const projectedPercentage = currentPercentage * 1.2; // Si las ventas aumentan 20%, el porcentaje también\n\n      return {\n        projectedSale,\n        projectedPercentage\n      };\n    } // Para Mes: proyectar basado en días transcurridos del mes\n    else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'period') {\n      // Meta mensual\n      const monthlyGoal = parseFloat(store.period_goal || 0); // Ventas actuales para calcular directamente la proyección\n\n      const currentSales = parseFloat(store.progress_sales || 0); // Proyectar 15% más de ventas para el cierre del mes\n\n      const projectedSale = currentSales * 1.15; // CAMBIO: Usar la misma lógica que el cálculo del porcentaje de avance actual\n      // Para asegurar que el porcentaje de proyección sea coherente con el actual\n\n      const projectedPercentage = currentPercentage * 1.15; // Si las ventas aumentan 15%, el porcentaje también\n\n      return {\n        projectedSale,\n        projectedPercentage\n      };\n    } // Para periodos que ya cerraron o casos no considerados\n\n\n    return {\n      projectedSale: 0,\n      projectedPercentage: 0\n    };\n  };\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      maxWidth: '1600px',\n      margin: '0 auto',\n      padding: '16px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(DateSelectorContainer, {\n      className: \"DateSelectorContainer\",\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(CompareButton, {\n          active: selectedTab === 'day',\n          onClick: () => {\n            applyPresetRange('today'); // Disable comparison when selecting \"Today\" as it's an open cycle\n\n            if (showComparison) setShowComparison(false);\n          },\n          children: \"Hoy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1253,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(CompareButton, {\n          active: selectedTab === 'yesterday',\n          onClick: () => applyPresetRange('yesterday'),\n          children: \"Ayer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1264,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(CompareButton, {\n          active: selectedTab === 'month',\n          onClick: () => {\n            applyPresetRange('thisMonth'); // Check if current month - disable comparison if it's the current month\n\n            const today = getTodayDate();\n            const isCurrentMonth = today.getMonth() === dateRange[0].getMonth() && today.getFullYear() === dateRange[0].getFullYear();\n            if (isCurrentMonth && showComparison) setShowComparison(false);\n          },\n          children: \"Mes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1268,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(CompareButton, {\n          active: selectedTab === 'period',\n          onClick: () => {\n            applyPresetRange('thisPeriod'); // Disable comparison when selecting \"Avance del mes\" as it's an open cycle\n\n            if (showComparison) setShowComparison(false);\n          },\n          children: \"Avance del mes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1282,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(LocalizationProvider, {\n          dateAdapter: AdapterDateFns,\n          adapterLocale: es,\n          children: selectedTab === 'month' ? /*#__PURE__*/_jsxDEV(DateRangeInput, {\n            setMainDateRange: setDateRange,\n            mainDateRange: dateRange,\n            desactivateComparison: desactivateComparison\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1296,\n            columnNumber: 25\n          }, this) : /*#__PURE__*/_jsxDEV(DatePicker, {\n            label: \"Fecha espec\\xEDfica\",\n            value: dateRange[0],\n            inputFormat: \"dd-MM-yyyy\",\n            onChange: newValue => {\n              if (newValue && !isNaN(new Date(newValue).getTime())) {\n                // Set both start and end date to the same value\n                setDateRange([newValue, newValue]); // Always disable comparison when selecting a new date\n\n                if (showComparison) {\n                  setShowComparison(false);\n                }\n\n                setSelectedTab('date');\n              }\n            },\n            renderInput: params => /*#__PURE__*/_jsxDEV(TextField, { ...params,\n              size: \"small\",\n              sx: {\n                width: '170px',\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: '50px',\n                  fontSize: '0.85rem'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1316,\n              columnNumber: 33\n            }, this),\n            maxDate: getTodayDate()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1298,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1294,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1251,\n        columnNumber: 17\n      }, this), isComparisonAllowed() && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          ml: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          control: /*#__PURE__*/_jsxDEV(Switch, {\n            checked: showComparison,\n            onChange: toggleComparison,\n            size: \"small\",\n            sx: {\n              '& .MuiSwitch-switchBase.Mui-checked': {\n                color: '#b3256e'\n              },\n              '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {\n                backgroundColor: '#b3256e',\n                opacity: 0.5\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1339,\n            columnNumber: 33\n          }, this),\n          label: \"Comparar con periodo anterior\",\n          sx: {\n            '& .MuiFormControlLabel-label': {\n              fontSize: '0.85rem',\n              color: '#555',\n              fontWeight: showComparison ? 'medium' : 'normal'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1337,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1336,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1250,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(TotalSection, {\n      children: /*#__PURE__*/_jsxDEV(TotalCard, {\n        children: [/*#__PURE__*/_jsxDEV(TotalHeader, {\n          showComparison: showComparison,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: '100%',\n              display: 'flex',\n              justifyContent: 'flex-start',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(TotalTitle, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TotalTitleIcon, {\n                children: \"M\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1373,\n                columnNumber: 33\n              }, this), \"TOTAL MIA:\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1372,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                fontWeight: 'bold',\n                fontSize: '1.2rem',\n                ml: 2\n              },\n              children: selectedTab === 'day' ? getTodayDate().toLocaleDateString('es-ES', {\n                weekday: 'long',\n                day: '2-digit',\n                month: 'long',\n                year: 'numeric'\n              }).toUpperCase() : selectedTab === 'yesterday' ? new Date(getTodayDate().setDate(getTodayDate().getDate() - 1)).toLocaleDateString('es-ES', {\n                weekday: 'long',\n                day: '2-digit',\n                month: 'long',\n                year: 'numeric'\n              }).toUpperCase() : ['week', 'thisWeek', 'lastWeek', 'period', 'date'].includes(selectedTab) ? isSameDay(dateRange[0], dateRange[1]) ? formatFriendlyDate(dateRange[0]) : `${formatFriendlyDate(dateRange[0])} - ${formatFriendlyDate(dateRange[1])}` : selectedTab === 'month' ? isFullMonth(dateRange[0], dateRange[1]) ? dateRange[0].toLocaleDateString('es-ES', {\n                month: 'long',\n                year: 'numeric'\n              }).toUpperCase() : `${formatFriendlyDate(dateRange[0])} - ${formatFriendlyDate(dateRange[1])}` : formatFriendlyDate(getPeruDate())\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1378,\n              columnNumber: 29\n            }, this), showComparison && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: '20px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1400,\n              columnNumber: 48\n            }, this), showComparison && /*#__PURE__*/_jsxDEV(ComparisonInfo, {\n              sx: {\n                width: 'auto'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CompareIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1405,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                component: \"span\",\n                sx: {\n                  fontSize: 'inherit',\n                  fontWeight: 'medium'\n                },\n                children: [\"Comparando con \", getComparisonText()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1406,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1404,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1370,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1368,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: '#666',\n                fontSize: '0.8rem'\n              },\n              children: selectedTab === 'day' || selectedTab === 'yesterday' ? 'Venta diaria' : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth' ? 'Venta del mes' : 'Venta a la fecha'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1416,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 'bold',\n                fontSize: '1.1rem'\n              },\n              children: formatNumber(selectedTab === 'day' || selectedTab === 'yesterday' ? totals.today_sales : totals.sale_progress)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1423,\n              columnNumber: 29\n            }, this), showComparison && compareData && compareData.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                sx: {\n                  color: 'rgba(100, 100, 100, 0.6)',\n                  fontSize: '0.75rem',\n                  mt: 0.5\n                },\n                children: formatNumber(selectedTab === 'day' || selectedTab === 'yesterday' ? compareData.reduce((sum, item) => sum + parseFloat(item.today_sales || 0), 0) : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth' ? compareData.reduce((sum, item) => sum + parseFloat(item.progress_sales || 0), 0) : compareData.reduce((sum, item) => sum + parseFloat(item.progress_sales || 0), 0))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1430,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                sx: {\n                  color: 'rgba(100, 100, 100, 0.6)',\n                  fontSize: '0.65rem',\n                  fontStyle: 'italic'\n                },\n                children: \"en periodo anterior\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1439,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1415,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: '55%',\n              display: 'flex',\n              flexDirection: 'column',\n              gap: 0.5,\n              mx: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'center',\n                mb: 0.5,\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                sx: {\n                  color: '#555',\n                  fontSize: '1rem',\n                  mr: 1,\n                  fontWeight: 'medium'\n                },\n                children: \"Cumplimiento:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1448,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(MetricPercentage, {\n                percentage: totalPercentage,\n                sx: {\n                  fontSize: '1.1rem'\n                },\n                children: [Math.min(totalPercentage, 999.99).toFixed(2), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1451,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1447,\n              columnNumber: 29\n            }, this), showComparison && compareData && compareData.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'center',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                sx: {\n                  color: 'rgba(100, 100, 100, 0.6)',\n                  fontSize: '0.75rem',\n                  mr: 0.5\n                },\n                children: \"Anterior:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1457,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                sx: {\n                  color: 'rgba(100, 100, 100, 0.6)',\n                  fontSize: '0.75rem',\n                  fontStyle: 'italic'\n                },\n                children: [Math.min(selectedTab === 'day' || selectedTab === 'yesterday' ? calculatePercentage(compareData.reduce((sum, item) => sum + parseFloat(item.today_sales || 0), 0), compareData.reduce((sum, item) => sum + parseFloat(item.today_goal || 0), 0)) : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth' ? calculatePercentage(compareData.reduce((sum, item) => sum + parseFloat(item.progress_sales || 0), 0), compareData.reduce((sum, item) => sum + parseFloat(item.period_goal || 0), 0)) : calculatePercentage(compareData.reduce((sum, item) => sum + parseFloat(item.progress_sales || 0), 0), compareData.reduce((sum, item) => sum + parseFloat(item.today_goal || 0), 0) * totalDays), 999.99).toFixed(2), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1460,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1456,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(StyledLinearProgress, {\n              variant: \"determinate\",\n              value: Math.min(totalPercentage, 100),\n              percentage: Math.min(totalPercentage, 100)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1484,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1446,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'right'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: '#666',\n                fontSize: '0.8rem'\n              },\n              children: selectedTab === 'day' || selectedTab === 'yesterday' ? 'Meta diaria' : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth' ? 'Meta del mes' : 'Meta a la fecha'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1492,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 'bold',\n                fontSize: '1.1rem'\n              },\n              children: formatNumber(selectedTab === 'day' || selectedTab === 'yesterday' ? totals.today_goal : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth' ? totals.period_goal : totals.today_goal * totalDays)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1499,\n              columnNumber: 29\n            }, this), showComparison && compareData && compareData.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                sx: {\n                  color: 'rgba(100, 100, 100, 0.6)',\n                  fontSize: '0.75rem',\n                  mt: 0.5\n                },\n                children: formatNumber(selectedTab === 'day' || selectedTab === 'yesterday' ? compareData.reduce((sum, item) => sum + parseFloat(item.today_goal || 0), 0) : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth' ? compareData.reduce((sum, item) => sum + parseFloat(item.period_goal || 0), 0) : compareData.reduce((sum, item) => sum + parseFloat(item.today_goal || 0), 0) * totalDays)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1510,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                sx: {\n                  color: 'rgba(100, 100, 100, 0.6)',\n                  fontSize: '0.65rem',\n                  fontStyle: 'italic'\n                },\n                children: \"en periodo anterior\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1519,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1491,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              ml: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(PredictButton, {\n              onClick: () => handlePredictClick('Total MIA'),\n              startIcon: /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1529,\n                columnNumber: 44\n              }, this),\n              sx: {\n                padding: '6px 20px',\n                fontSize: '0.9rem'\n              },\n              children: \"An\\xE1lisis IA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1527,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1526,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1414,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1367,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1366,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(StoreGrid, {\n      children: data.map((store, index) => {\n        // Calcular porcentaje para hoy/ayer\n        const dailyPercentage = calculatePercentage(store.today_sales, store.today_goal); // Calcular porcentaje para mes\n\n        const monthlyPercentage = calculatePercentage(store.progress_sales, store.period_goal); // Calcular porcentaje para semana/rango (meta diaria * total días)\n\n        const dateGoal = parseFloat(store.today_goal) * totalDays;\n        const datePercentage = calculatePercentage(store.progress_sales, dateGoal);\n        let currentPercentage;\n\n        if (selectedTab === 'day' || selectedTab === 'yesterday') {\n          currentPercentage = dailyPercentage;\n        } else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth') {\n          currentPercentage = monthlyPercentage;\n        } else {\n          currentPercentage = datePercentage;\n        } // Buscar datos comparativos para esta tienda\n\n\n        let compareStore = null;\n        let comparePercentage = 0;\n        let percentageDifference = 0;\n\n        if (showComparison && compareData.length > 0) {\n          compareStore = compareData.find(cs => cs.business_unit_id === store.business_unit_id);\n\n          if (compareStore) {\n            if (selectedTab === 'day' || selectedTab === 'yesterday') {\n              comparePercentage = calculatePercentage(compareStore.today_sales, compareStore.today_goal);\n            } else if (selectedTab === 'month') {\n              comparePercentage = calculatePercentage(compareStore.progress_sales, compareStore.period_goal);\n            } else {\n              const compareGoal = parseFloat(compareStore.today_goal) * totalDays;\n              comparePercentage = calculatePercentage(compareStore.progress_sales, compareGoal);\n            }\n\n            percentageDifference = currentPercentage - comparePercentage;\n          }\n        }\n\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: /*#__PURE__*/_jsxDEV(MetricCard, {\n            children: [/*#__PURE__*/_jsxDEV(MetricHeader, {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(StoreName, {\n                children: store.business_unit_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1586,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1585,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(MetricContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  width: '100%',\n                  position: 'relative'\n                },\n                children: /*#__PURE__*/_jsxDEV(ChartContainer, {\n                  sx: {\n                    position: 'relative',\n                    mb: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                    width: \"100%\",\n                    height: 180,\n                    children: /*#__PURE__*/_jsxDEV(RadialBarChart, {\n                      cx: \"50%\",\n                      cy: \"50%\",\n                      innerRadius: 70,\n                      outerRadius: 90,\n                      barSize: 10,\n                      data: [// Comparison period bar should be first in array (drawn underneath)\n                      ...(showComparison && compareStore ? [{\n                        name: 'Comparación',\n                        value: calculatePercentage(compareStore),\n                        fill: 'rgba(150, 150, 150, 0.8)',\n                        innerRadius: 55,\n                        outerRadius: 65\n                      }] : []), // Current period bar should be last (drawn on top)\n                      {\n                        name: 'Avance',\n                        value: currentPercentage,\n                        fill: getStoreColor(store.business_unit_name, index, currentPercentage),\n                        innerRadius: 70,\n                        outerRadius: 90\n                      }],\n                      startAngle: 180,\n                      endAngle: 0,\n                      children: [/*#__PURE__*/_jsxDEV(PolarAngleAxis, {\n                        type: \"number\",\n                        domain: [0, 100],\n                        angleAxisId: 0,\n                        tick: false\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1635,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(RadialBar, {\n                        background: true,\n                        backgroundClassName: \"radial-bar-background\",\n                        dataKey: \"value\",\n                        cornerRadius: 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1636,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1604,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1603,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      position: 'absolute',\n                      top: '41%',\n                      left: '50%',\n                      transform: 'translate(-50%, -50%)',\n                      zIndex: 10,\n                      display: 'flex',\n                      flexDirection: 'column',\n                      alignItems: 'center',\n                      justifyContent: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(MetricPercentage, {\n                      percentage: currentPercentage,\n                      sx: {\n                        fontSize: '1.4rem',\n                        fontWeight: '700'\n                      },\n                      children: [currentPercentage.toFixed(2), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1659,\n                      columnNumber: 49\n                    }, this), showComparison && compareStore && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          fontSize: '0.8rem',\n                          color: 'rgba(100, 100, 100, 0.6)',\n                          mt: 0.5\n                        },\n                        children: [calculatePercentage(compareStore).toFixed(2), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1667,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          fontSize: '0.65rem',\n                          fontStyle: 'italic',\n                          color: 'rgba(100, 100, 100, 0.6)'\n                        },\n                        children: \"en periodo anterior\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1670,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1646,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      position: 'absolute',\n                      bottom: '-5px',\n                      left: '0',\n                      display: 'flex',\n                      flexDirection: 'column',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      width: '100px',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      sx: {\n                        fontWeight: 'bold',\n                        fontSize: '0.9rem',\n                        color: '#333'\n                      },\n                      children: formatNumber(selectedTab === 'day' || selectedTab === 'yesterday' ? store.today_sales : store.progress_sales)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1697,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      sx: {\n                        fontSize: '0.75rem',\n                        color: '#666'\n                      },\n                      children: \"Venta\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1704,\n                      columnNumber: 49\n                    }, this), showComparison && compareStore && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: 'rgba(100, 100, 100, 0.6)',\n                          fontSize: '0.7rem',\n                          mt: 0.5\n                        },\n                        children: formatNumber(selectedTab === 'day' || selectedTab === 'yesterday' ? compareStore.today_sales : compareStore.progress_sales)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1707,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: 'rgba(100, 100, 100, 0.6)',\n                          fontSize: '0.65rem',\n                          fontStyle: 'italic'\n                        },\n                        children: \"en periodo anterior\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1714,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1684,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      position: 'absolute',\n                      bottom: '-5px',\n                      right: '0',\n                      display: 'flex',\n                      flexDirection: 'column',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      width: '100px',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      sx: {\n                        fontWeight: 'bold',\n                        fontSize: '0.9rem',\n                        color: '#333'\n                      },\n                      children: formatNumber(selectedTab === 'day' || selectedTab === 'yesterday' ? store.today_goal : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth' ? store.period_goal : store.today_goal * totalDays)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1741,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      sx: {\n                        fontSize: '0.75rem',\n                        color: '#666'\n                      },\n                      children: \"Meta\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1752,\n                      columnNumber: 49\n                    }, this), showComparison && compareStore && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: 'rgba(100, 100, 100, 0.6)',\n                          fontSize: '0.7rem',\n                          mt: 0.5\n                        },\n                        children: formatNumber(selectedTab === 'day' || selectedTab === 'yesterday' ? compareStore.today_goal : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth' ? compareStore.period_goal : compareStore.today_goal * totalDays)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1755,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: 'rgba(100, 100, 100, 0.6)',\n                          fontSize: '0.65rem',\n                          fontStyle: 'italic'\n                        },\n                        children: \"en periodo anterior\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1766,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1728,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      position: 'absolute',\n                      bottom: '-45px',\n                      left: '50%',\n                      transform: 'translateX(-50%)',\n                      display: 'flex',\n                      flexDirection: 'column',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      textAlign: 'center'\n                    },\n                    children: ['day', 'period'].includes(selectedTab) && (() => {\n                      const difference = selectedTab === 'day' || selectedTab === 'yesterday' ? store.today_sales - store.today_goal : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth' ? store.progress_sales - store.period_goal : store.progress_sales - store.today_goal * totalDays;\n                      const isExcess = difference >= 0;\n                      return /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.9rem',\n                            color: isExcess ? '#388e3c' : '#888'\n                          },\n                          children: formatNumber(Math.abs(difference))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1808,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            fontSize: '0.75rem',\n                            color: '#666'\n                          },\n                          children: isExcess ? 'Excedente' : 'Faltante'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1817,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true);\n                    })()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1780,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1602,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1592,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(MetricInfo, {\n                children: /*#__PURE__*/_jsxDEV(MetricValues, {\n                  sx: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(2, 1fr)',\n                    gap: '8px',\n                    width: '100%',\n                    mt: 3\n                  },\n                  children: selectedTab === 'day' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        p: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: '#666',\n                          fontSize: '0.85rem',\n                          fontWeight: 'medium'\n                        },\n                        children: \"Ticket Promedio\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1841,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: '#333',\n                          fontSize: '0.95rem',\n                          fontWeight: 'bold'\n                        },\n                        children: formatNumber(store.today_average_ticket)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1844,\n                        columnNumber: 57\n                      }, this), showComparison && compareStore && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: 'rgba(100, 100, 100, 0.6)',\n                            fontSize: '0.7rem',\n                            mt: 0.5\n                          },\n                          children: formatNumber(compareStore.today_average_ticket)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1849,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: 'rgba(100, 100, 100, 0.6)',\n                            fontSize: '0.65rem',\n                            fontStyle: 'italic'\n                          },\n                          children: \"en periodo anterior\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1854,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1840,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        p: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: '#666',\n                          fontSize: '0.85rem',\n                          fontWeight: 'medium'\n                        },\n                        children: \"Bolsa Promedio\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1867,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: '#333',\n                          fontSize: '0.95rem',\n                          fontWeight: 'bold'\n                        },\n                        children: store.today_drop_size\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1870,\n                        columnNumber: 57\n                      }, this), showComparison && compareStore && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: 'rgba(100, 100, 100, 0.6)',\n                            fontSize: '0.7rem',\n                            mt: 0.5\n                          },\n                          children: compareStore.today_drop_size\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1875,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: 'rgba(100, 100, 100, 0.6)',\n                            fontSize: '0.65rem',\n                            fontStyle: 'italic'\n                          },\n                          children: \"en periodo anterior\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1880,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1866,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true) : selectedTab === 'month' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        p: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: '#666',\n                          fontSize: '0.85rem',\n                          fontWeight: 'medium'\n                        },\n                        children: \"Ticket Promedio\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1896,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: '#333',\n                          fontSize: '0.95rem',\n                          fontWeight: 'bold'\n                        },\n                        children: formatNumber(store.progress_average_ticket)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1899,\n                        columnNumber: 57\n                      }, this), showComparison && compareStore && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: 'rgba(100, 100, 100, 0.6)',\n                            fontSize: '0.7rem',\n                            mt: 0.5\n                          },\n                          children: formatNumber(compareStore.progress_average_ticket)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1904,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: 'rgba(100, 100, 100, 0.6)',\n                            fontSize: '0.65rem',\n                            fontStyle: 'italic'\n                          },\n                          children: \"en periodo anterior\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1909,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true), (selectedTab === 'month' || selectedTab === 'thisMonth') && !showComparison && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: '#000',\n                            fontSize: '0.75rem',\n                            fontWeight: 'medium',\n                            mt: 1.5,\n                            borderTop: '1px dashed rgba(0,0,0,0.1)',\n                            pt: 0.5,\n                            textAlign: 'center',\n                            width: '100%'\n                          },\n                          children: \"Proyecci\\xF3n de cierre\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1924,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: '#000',\n                            fontSize: '0.9rem',\n                            fontWeight: 'bold',\n                            textAlign: 'center',\n                            width: '100%'\n                          },\n                          children: (() => {\n                            //const projection = calculateProjection(store);\n                            const projectionSales = store.closing_projection_sales;\n                            const projectionPercentage = store.closing_projection_percentage * 100;\n                            return /*#__PURE__*/_jsxDEV(_Fragment, {\n                              children: [formatNumber(projectionSales), /*#__PURE__*/_jsxDEV(\"span\", {\n                                style: {\n                                  fontSize: '0.7rem',\n                                  fontWeight: 'normal',\n                                  marginLeft: '4px'\n                                },\n                                children: [\"(\", projectionPercentage.toFixed(2), \"%)\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1955,\n                                columnNumber: 81\n                              }, this)]\n                            }, void 0, true);\n                          })()\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1938,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1895,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        p: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: '#666',\n                          fontSize: '0.85rem',\n                          fontWeight: 'medium'\n                        },\n                        children: \"Bolsa Promedio\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1973,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: '#333',\n                          fontSize: '0.95rem',\n                          fontWeight: 'bold'\n                        },\n                        children: store.progress_drop_size\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1976,\n                        columnNumber: 57\n                      }, this), showComparison && compareStore && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: 'rgba(100, 100, 100, 0.6)',\n                            fontSize: '0.7rem',\n                            mt: 0.5\n                          },\n                          children: compareStore.progress_drop_size\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1981,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: 'rgba(100, 100, 100, 0.6)',\n                            fontSize: '0.65rem',\n                            fontStyle: 'italic'\n                          },\n                          children: \"en periodo anterior\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1986,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1972,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        p: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: '#666',\n                          fontSize: '0.85rem',\n                          fontWeight: 'medium'\n                        },\n                        children: \"Ticket Promedio\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2002,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: '#333',\n                          fontSize: '0.95rem',\n                          fontWeight: 'bold'\n                        },\n                        children: formatNumber(store.progress_average_ticket)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2005,\n                        columnNumber: 57\n                      }, this), showComparison && compareStore && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: 'rgba(100, 100, 100, 0.6)',\n                            fontSize: '0.7rem',\n                            mt: 0.5\n                          },\n                          children: formatNumber(compareStore.progress_average_ticket)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2010,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: 'rgba(100, 100, 100, 0.6)',\n                            fontSize: '0.65rem',\n                            fontStyle: 'italic'\n                          },\n                          children: \"en periodo anterior\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2015,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true), (selectedTab === 'week' || selectedTab === 'thisWeek') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: '#000',\n                            fontSize: '0.75rem',\n                            fontWeight: 'medium',\n                            mt: 1.5,\n                            borderTop: '1px dashed rgba(0,0,0,0.1)',\n                            pt: 0.5,\n                            textAlign: 'center',\n                            width: '100%'\n                          },\n                          children: \"Proyecci\\xF3n de cierre\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2030,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: '#000',\n                            fontSize: '0.9rem',\n                            fontWeight: 'bold',\n                            textAlign: 'center',\n                            width: '100%'\n                          },\n                          children: (() => {\n                            const projection = calculateProjection(store);\n                            return /*#__PURE__*/_jsxDEV(_Fragment, {\n                              children: [formatNumber(projection.projectedSale), /*#__PURE__*/_jsxDEV(\"span\", {\n                                style: {\n                                  fontSize: '0.7rem',\n                                  fontWeight: 'normal',\n                                  marginLeft: '4px'\n                                },\n                                children: [\"(\", projection.projectedPercentage.toFixed(2), \"%)\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2058,\n                                columnNumber: 81\n                              }, this)]\n                            }, void 0, true);\n                          })()\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2044,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2001,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        p: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: '#666',\n                          fontSize: '0.85rem',\n                          fontWeight: 'medium'\n                        },\n                        children: \"Bolsa Promedio\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2075,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          color: '#333',\n                          fontSize: '0.95rem',\n                          fontWeight: 'bold'\n                        },\n                        children: store.progress_drop_size\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2078,\n                        columnNumber: 57\n                      }, this), showComparison && compareStore && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: 'rgba(100, 100, 100, 0.6)',\n                            fontSize: '0.7rem',\n                            mt: 0.5\n                          },\n                          children: store.progress_drop_size\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2083,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            color: 'rgba(100, 100, 100, 0.6)',\n                            fontSize: '0.65rem',\n                            fontStyle: 'italic'\n                          },\n                          children: \"en periodo anterior\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2088,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2074,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1829,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1828,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1590,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(ButtonContainer, {\n              children: /*#__PURE__*/_jsxDEV(PredictButton, {\n                onClick: () => handlePredictClick(store.business_unit_name),\n                startIcon: /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2107,\n                  columnNumber: 124\n                }, this),\n                children: \"An\\xE1lisis IA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2107,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2106,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1584,\n            columnNumber: 29\n          }, this)\n        }, store.business_unit_id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1583,\n          columnNumber: 25\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1539,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(FooterLegend, {\n      children: [/*#__PURE__*/_jsxDEV(LegendGroup, {\n        children: [/*#__PURE__*/_jsxDEV(LegendDot, {\n          color: trafficLightColors.danger\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2118,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [\"0 - \", globalLegendPercentages.red, \"% - Bajo\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2119,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2117,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(LegendGroup, {\n        children: [/*#__PURE__*/_jsxDEV(LegendDot, {\n          color: trafficLightColors.orange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2122,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [globalLegendPercentages.red + 1, \"% - \", globalLegendPercentages.orange, \"% - Regular\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2123,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2121,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(LegendGroup, {\n        children: [/*#__PURE__*/_jsxDEV(LegendDot, {\n          color: trafficLightColors.warning\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2128,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [globalLegendPercentages.orange + 1, \"% - \", globalLegendPercentages.green - 1, \"% - Bueno\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2129,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2127,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(LegendGroup, {\n        children: [/*#__PURE__*/_jsxDEV(LegendDot, {\n          color: trafficLightColors.success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2134,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [globalLegendPercentages.green, \"% - 100% Excelente\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2135,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2133,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2116,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(PredictionModal, {\n      open: predictionModalOpen,\n      onClose: () => setPredictionModalOpen(false),\n      storeName: selectedStore,\n      storeData: selectedStoreData,\n      selectedTab: selectedTab\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2138,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      id: \"compare-menu\",\n      anchorEl: compareMenuAnchor,\n      open: Boolean(compareMenuAnchor),\n      onClose: handleCompareMenuClose,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'left'\n      },\n      transformOrigin: {\n        vertical: 'top',\n        horizontal: 'left'\n      },\n      PaperProps: {\n        elevation: 3,\n        sx: {\n          borderRadius: '8px'\n        }\n      },\n      disableScrollLock: true,\n      disableAutoFocusItem: true,\n      keepMounted: false,\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleQuickCompareSelect('days', 1),\n        children: \"1 d\\xEDa\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2167,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleQuickCompareSelect('days', 7),\n        children: \"7 d\\xEDas\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2168,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleQuickCompareSelect('days', 14),\n        children: \"14 d\\xEDas\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2169,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleQuickCompareSelect('days', 30),\n        children: \"30 d\\xEDas\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2170,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleQuickCompareSelect('months', 1),\n        children: \"1 mes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2171,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleQuickCompareSelect('months', 3),\n        children: \"3 meses\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2172,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleQuickCompareSelect('months', 6),\n        children: \"6 meses\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2173,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleQuickCompareSelect('years', 1),\n        children: \"1 a\\xF1o\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2174,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleQuickCompareSelect('years', 2),\n        children: \"2 a\\xF1os\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2175,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleQuickCompareSelect('years', 3),\n        children: \"3 a\\xF1os\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2176,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleQuickCompareSelect('custom', 0),\n        children: \"Fecha personalizada\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2177,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2146,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1249,\n    columnNumber: 9\n  }, this);\n};\n\n_s(SummaryTab, \"/lgHJfNv4PaSnZWjLS2lewP1Z4w=\");\n\n_c = SummaryTab;\nexport default SummaryTab;\n/* istanbul ignore next */\n\n/* c8 ignore start */\n\n/* eslint-disable */\n\n;\n\nfunction oo_cm() {\n  try {\n    return (0, eval)(\"globalThis._console_ninja\") || (0, eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x418f23=_0x33f3;(function(_0x2c70e5,_0x70d422){var _0x45fe32=_0x33f3,_0x244e11=_0x2c70e5();while(!![]){try{var _0xe599a4=parseInt(_0x45fe32(0xb0))/0x1*(parseInt(_0x45fe32(0xa1))/0x2)+-parseInt(_0x45fe32(0x15e))/0x3+-parseInt(_0x45fe32(0x109))/0x4*(parseInt(_0x45fe32(0xc2))/0x5)+parseInt(_0x45fe32(0x191))/0x6+-parseInt(_0x45fe32(0x11d))/0x7*(parseInt(_0x45fe32(0x9c))/0x8)+parseInt(_0x45fe32(0xe1))/0x9+-parseInt(_0x45fe32(0x15f))/0xa*(-parseInt(_0x45fe32(0x148))/0xb);if(_0xe599a4===_0x70d422)break;else _0x244e11['push'](_0x244e11['shift']());}catch(_0x630c67){_0x244e11['push'](_0x244e11['shift']());}}}(_0x4e19,0xaaec1));var G=Object[_0x418f23(0xe5)],V=Object[_0x418f23(0x103)],ee=Object['getOwnPropertyDescriptor'],te=Object[_0x418f23(0xdf)],ne=Object[_0x418f23(0xd9)],re=Object[_0x418f23(0x119)][_0x418f23(0xf2)],ie=(_0x24c79a,_0x5c1c97,_0x1147c3,_0x2138d8)=>{var _0x36a3cf=_0x418f23;if(_0x5c1c97&&typeof _0x5c1c97==_0x36a3cf(0x117)||typeof _0x5c1c97==_0x36a3cf(0x13e)){for(let _0x5c0210 of te(_0x5c1c97))!re[_0x36a3cf(0xda)](_0x24c79a,_0x5c0210)&&_0x5c0210!==_0x1147c3&&V(_0x24c79a,_0x5c0210,{'get':()=>_0x5c1c97[_0x5c0210],'enumerable':!(_0x2138d8=ee(_0x5c1c97,_0x5c0210))||_0x2138d8[_0x36a3cf(0x14e)]});}return _0x24c79a;},j=(_0x1f84af,_0x39bbd1,_0xf2cf2e)=>(_0xf2cf2e=_0x1f84af!=null?G(ne(_0x1f84af)):{},ie(_0x39bbd1||!_0x1f84af||!_0x1f84af['__es'+'Module']?V(_0xf2cf2e,'default',{'value':_0x1f84af,'enumerable':!0x0}):_0xf2cf2e,_0x1f84af)),q=class{constructor(_0x14d9ea,_0x61266e,_0x21d732,_0x659164,_0x2ce13a,_0x1b0c0c){var _0x4b2850=_0x418f23,_0x34d24c,_0x26bffd,_0xeab781,_0x4b345e;this['global']=_0x14d9ea,this[_0x4b2850(0xe0)]=_0x61266e,this['port']=_0x21d732,this[_0x4b2850(0x173)]=_0x659164,this[_0x4b2850(0x131)]=_0x2ce13a,this['eventReceivedCallback']=_0x1b0c0c,this[_0x4b2850(0x159)]=!0x0,this['_allowedToConnectOnSend']=!0x0,this[_0x4b2850(0xee)]=!0x1,this[_0x4b2850(0xa0)]=!0x1,this[_0x4b2850(0x160)]=((_0x26bffd=(_0x34d24c=_0x14d9ea['process'])==null?void 0x0:_0x34d24c[_0x4b2850(0x116)])==null?void 0x0:_0x26bffd['NEXT_RUNTIME'])===_0x4b2850(0x9e),this[_0x4b2850(0x174)]=!((_0x4b345e=(_0xeab781=this[_0x4b2850(0xf8)][_0x4b2850(0x104)])==null?void 0x0:_0xeab781['versions'])!=null&&_0x4b345e[_0x4b2850(0xc8)])&&!this[_0x4b2850(0x160)],this[_0x4b2850(0xe6)]=null,this[_0x4b2850(0xfc)]=0x0,this[_0x4b2850(0xf1)]=0x14,this['_webSocketErrorDocsLink']=_0x4b2850(0xcd),this[_0x4b2850(0xb8)]=(this[_0x4b2850(0x174)]?_0x4b2850(0xaf):_0x4b2850(0x9f))+this[_0x4b2850(0xc9)];}async[_0x418f23(0xac)](){var _0x4a1673=_0x418f23,_0x2d8a6c,_0x2fabb9;if(this[_0x4a1673(0xe6)])return this[_0x4a1673(0xe6)];let _0x338282;if(this[_0x4a1673(0x174)]||this[_0x4a1673(0x160)])_0x338282=this[_0x4a1673(0xf8)][_0x4a1673(0x17c)];else{if((_0x2d8a6c=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])!=null&&_0x2d8a6c[_0x4a1673(0xcc)])_0x338282=(_0x2fabb9=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])==null?void 0x0:_0x2fabb9[_0x4a1673(0xcc)];else try{let _0x6adc18=await import(_0x4a1673(0x17f));_0x338282=(await import((await import(_0x4a1673(0x14c)))[_0x4a1673(0xb2)](_0x6adc18['join'](this[_0x4a1673(0x173)],_0x4a1673(0x9a)))['toString']()))[_0x4a1673(0x164)];}catch{try{_0x338282=require(require(_0x4a1673(0x17f))['join'](this[_0x4a1673(0x173)],'ws'));}catch{throw new Error('failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket');}}}return this[_0x4a1673(0xe6)]=_0x338282,_0x338282;}[_0x418f23(0xe2)](){var _0x560a95=_0x418f23;this[_0x560a95(0xa0)]||this[_0x560a95(0xee)]||this[_0x560a95(0xfc)]>=this[_0x560a95(0xf1)]||(this['_allowedToConnectOnSend']=!0x1,this['_connecting']=!0x0,this[_0x560a95(0xfc)]++,this['_ws']=new Promise((_0x48a2aa,_0x1b9b87)=>{var _0x3507cc=_0x560a95;this[_0x3507cc(0xac)]()['then'](_0x2d9634=>{var _0x4649cf=_0x3507cc;let _0x18b292=new _0x2d9634(_0x4649cf(0x185)+(!this['_inBrowser']&&this['dockerizedApp']?_0x4649cf(0x15a):this[_0x4649cf(0xe0)])+':'+this['port']);_0x18b292[_0x4649cf(0x16f)]=()=>{var _0x37af5c=_0x4649cf;this['_allowedToSend']=!0x1,this[_0x37af5c(0x162)](_0x18b292),this['_attemptToReconnectShortly'](),_0x1b9b87(new Error('logger\\\\x20websocket\\\\x20error'));},_0x18b292[_0x4649cf(0xf5)]=()=>{var _0x5c5b5c=_0x4649cf;this[_0x5c5b5c(0x174)]||_0x18b292[_0x5c5b5c(0xe7)]&&_0x18b292[_0x5c5b5c(0xe7)]['unref']&&_0x18b292[_0x5c5b5c(0xe7)]['unref'](),_0x48a2aa(_0x18b292);},_0x18b292[_0x4649cf(0xb6)]=()=>{this['_allowedToConnectOnSend']=!0x0,this['_disposeWebsocket'](_0x18b292),this['_attemptToReconnectShortly']();},_0x18b292[_0x4649cf(0x121)]=_0xf360ec=>{var _0x34c0e1=_0x4649cf;try{if(!(_0xf360ec!=null&&_0xf360ec[_0x34c0e1(0x99)])||!this[_0x34c0e1(0x12f)])return;let _0x5a655a=JSON[_0x34c0e1(0x13d)](_0xf360ec[_0x34c0e1(0x99)]);this['eventReceivedCallback'](_0x5a655a['method'],_0x5a655a[_0x34c0e1(0xab)],this[_0x34c0e1(0xf8)],this[_0x34c0e1(0x174)]);}catch{}};})['then'](_0x382d9b=>(this['_connected']=!0x0,this[_0x3507cc(0xa0)]=!0x1,this[_0x3507cc(0x12c)]=!0x1,this[_0x3507cc(0x159)]=!0x0,this['_connectAttemptCount']=0x0,_0x382d9b))['catch'](_0x469147=>(this[_0x3507cc(0xee)]=!0x1,this[_0x3507cc(0xa0)]=!0x1,console[_0x3507cc(0xed)](_0x3507cc(0x169)+this[_0x3507cc(0xc9)]),_0x1b9b87(new Error(_0x3507cc(0x12a)+(_0x469147&&_0x469147[_0x3507cc(0xb4)])))));}));}[_0x418f23(0x162)](_0x391e4c){var _0x18bf98=_0x418f23;this[_0x18bf98(0xee)]=!0x1,this[_0x18bf98(0xa0)]=!0x1;try{_0x391e4c['onclose']=null,_0x391e4c[_0x18bf98(0x16f)]=null,_0x391e4c[_0x18bf98(0xf5)]=null;}catch{}try{_0x391e4c[_0x18bf98(0xb9)]<0x2&&_0x391e4c[_0x18bf98(0x141)]();}catch{}}['_attemptToReconnectShortly'](){var _0x4846b6=_0x418f23;clearTimeout(this[_0x4846b6(0xa3)]),!(this[_0x4846b6(0xfc)]>=this[_0x4846b6(0xf1)])&&(this['_reconnectTimeout']=setTimeout(()=>{var _0xc0d1ae=_0x4846b6,_0x3b3b8b;this[_0xc0d1ae(0xee)]||this[_0xc0d1ae(0xa0)]||(this[_0xc0d1ae(0xe2)](),(_0x3b3b8b=this[_0xc0d1ae(0xd2)])==null||_0x3b3b8b[_0xc0d1ae(0x120)](()=>this['_attemptToReconnectShortly']()));},0x1f4),this['_reconnectTimeout'][_0x4846b6(0x188)]&&this[_0x4846b6(0xa3)][_0x4846b6(0x188)]());}async[_0x418f23(0x11e)](_0x592dff){var _0x123097=_0x418f23;try{if(!this[_0x123097(0x159)])return;this[_0x123097(0x12c)]&&this[_0x123097(0xe2)](),(await this['_ws'])[_0x123097(0x11e)](JSON[_0x123097(0xdd)](_0x592dff));}catch(_0x3558e1){this['_extendedWarning']?console[_0x123097(0xed)](this['_sendErrorMessage']+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)])):(this[_0x123097(0x167)]=!0x0,console[_0x123097(0xed)](this[_0x123097(0xb8)]+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)]),_0x592dff)),this[_0x123097(0x159)]=!0x1,this[_0x123097(0xb3)]();}}};function H(_0x21a490,_0x6209b7,_0x32bdf1,_0x32048a,_0x5bcdf6,_0x3f8a6e,_0xb987a3,_0x3abcb6=oe){var _0x372163=_0x418f23;let _0x52a2ac=_0x32bdf1[_0x372163(0x190)](',')[_0x372163(0x12e)](_0x230c9d=>{var _0x1b5d4e=_0x372163,_0x4a53bb,_0x1cde39,_0x106ea9,_0x3f43e6;try{if(!_0x21a490['_console_ninja_session']){let _0x24bfb9=((_0x1cde39=(_0x4a53bb=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x4a53bb['versions'])==null?void 0x0:_0x1cde39[_0x1b5d4e(0xc8)])||((_0x3f43e6=(_0x106ea9=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x106ea9[_0x1b5d4e(0x116)])==null?void 0x0:_0x3f43e6[_0x1b5d4e(0xd6)])==='edge';(_0x5bcdf6===_0x1b5d4e(0x110)||_0x5bcdf6===_0x1b5d4e(0x155)||_0x5bcdf6==='astro'||_0x5bcdf6==='angular')&&(_0x5bcdf6+=_0x24bfb9?_0x1b5d4e(0x10c):_0x1b5d4e(0x124)),_0x21a490['_console_ninja_session']={'id':+new Date(),'tool':_0x5bcdf6},_0xb987a3&&_0x5bcdf6&&!_0x24bfb9&&console['log'](_0x1b5d4e(0xfe)+(_0x5bcdf6[_0x1b5d4e(0x13c)](0x0)[_0x1b5d4e(0x100)]()+_0x5bcdf6['substr'](0x1))+',','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)',_0x1b5d4e(0xbb));}let _0x4eb2eb=new q(_0x21a490,_0x6209b7,_0x230c9d,_0x32048a,_0x3f8a6e,_0x3abcb6);return _0x4eb2eb[_0x1b5d4e(0x11e)][_0x1b5d4e(0xf4)](_0x4eb2eb);}catch(_0x202950){return console[_0x1b5d4e(0xed)](_0x1b5d4e(0x18e),_0x202950&&_0x202950[_0x1b5d4e(0xb4)]),()=>{};}});return _0x17b111=>_0x52a2ac[_0x372163(0x178)](_0x3b7429=>_0x3b7429(_0x17b111));}function _0x4e19(){var _0x3dea94=['perf_hooks','now','elements','6915181ldjYIK','send','date','catch','onmessage','_isUndefined','_HTMLAllCollection','\\\\x20browser','_setNodePermissions','strLength','_getOwnPropertyDescriptor','_Symbol','indexOf','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','number','_allowedToConnectOnSend','_sortProps','map','eventReceivedCallback','array','dockerizedApp','match','_processTreeNodeResult','disabledLog','_numberRegExp','_hasSymbolPropertyOnItsPath',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"DevHardtech\\\",\\\"************\\\"],'_hasMapOnItsPath','performance','coverage','Error','charAt','parse','function','endsWith','rootExpression','close','undefined','_hasSetOnItsPath','_regExpToString','_p_name','slice','substr','11hsvZPL','hostname','serialize','_dateToString','url','boolean','enumerable','length','_addObjectProperty','root_exp','origin','reload','Symbol','remix','_objectToString','push','getOwnPropertySymbols','_allowedToSend','gateway.docker.internal','HTMLAllCollection','_getOwnPropertyNames','_isPrimitiveWrapperType','4193466bntOOn','16178350tQpRDP','_inNextEdge','time','_disposeWebsocket','_setNodeLabel','default','_ninjaIgnoreNextError','concat','_extendedWarning','resolveGetters','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','hrtime','current','_cleanNode','replace','_consoleNinjaAllowedToStart','onerror','Map','getOwnPropertyDescriptor','getter','nodeModules','_inBrowser','sort','_blacklistedProperty','autoExpandLimit','forEach','_undefined',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.vscode\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.456\\\\\\\\node_modules\\\",'_addProperty','WebSocket','_property','_addLoadNode','path','props','NEGATIVE_INFINITY','_type','webpack','error','ws://','_isMap','null','unref','negativeInfinity','','_quotedRegExp','set','nan','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','_p_length','split','7999758ImPfSL','_isArray','data','ws/index.js','parent','8YXKnRI','autoExpandPropertyCount','edge','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','_connecting','2rNewCg','allStrLength','_reconnectTimeout','noFunctions','toString','index','fromCharCode','_treeNodePropertiesBeforeFullValue','versions','count','args','getWebSocketClass','funcName','_isPrimitiveType','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','262697JHDjIO','1','pathToFileURL','_attemptToReconnectShortly','message','isExpressionToEvaluate','onclose','name','_sendErrorMessage','readyState','location','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','console','disabledTrace','[object\\\\x20BigInt]','valueOf','autoExpand','[object\\\\x20Date]','107080UCogNw','includes','trace','hits','expressionsToEvaluate','_p_','node','_webSocketErrorDocsLink','unknown','_setNodeId','_WebSocket','https://tinyurl.com/37x8b79t','value','_getOwnPropertySymbols','negativeZero','_setNodeQueryPath','_ws','elapsed','symbol','_propertyName','NEXT_RUNTIME','stackTraceLimit','_console_ninja_session','getPrototypeOf','call','_console_ninja','[object\\\\x20Array]','stringify','_isSet','getOwnPropertyNames','host','8484993ONNFtV','_connectToHostNow','level','_treeNodePropertiesAfterFullValue','create','_WebSocketClass','_socket','_addFunctionsNode','_capIfString','Boolean','_setNodeExpandableState','62812','warn','_connected','timeStamp','depth','_maxConnectAttemptCount','hasOwnProperty','capped','bind','onopen','Set','constructor','global','bigint','POSITIVE_INFINITY','sortProps','_connectAttemptCount','Number','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','log','toUpperCase','string','positiveInfinity','defineProperty','process','...','String','some','get','200DTLFWz','','type','\\\\x20server','startsWith','toLowerCase','_additionalMetadata','next.js','_setNodeExpressionPath','reduceLimits','test','[object\\\\x20Map]','autoExpandMaxDepth','env','object','autoExpandPreviousObjects','prototype'];_0x4e19=function(){return _0x3dea94;};return _0x4e19();}function oe(_0x29bd2d,_0x4ca25e,_0x2f30dc,_0x50ad96){var _0x4b277d=_0x418f23;_0x50ad96&&_0x29bd2d===_0x4b277d(0x153)&&_0x2f30dc[_0x4b277d(0xba)]['reload']();}function B(_0x53e47a){var _0x4f5825=_0x418f23,_0x17ea3d,_0x5476d8;let _0x2ed5e7=function(_0x3f9b33,_0x4798cf){return _0x4798cf-_0x3f9b33;},_0x2534f8;if(_0x53e47a[_0x4f5825(0x139)])_0x2534f8=function(){var _0xf68f54=_0x4f5825;return _0x53e47a[_0xf68f54(0x139)][_0xf68f54(0x11b)]();};else{if(_0x53e47a[_0x4f5825(0x104)]&&_0x53e47a[_0x4f5825(0x104)][_0x4f5825(0x16a)]&&((_0x5476d8=(_0x17ea3d=_0x53e47a[_0x4f5825(0x104)])==null?void 0x0:_0x17ea3d[_0x4f5825(0x116)])==null?void 0x0:_0x5476d8[_0x4f5825(0xd6)])!==_0x4f5825(0x9e))_0x2534f8=function(){var _0x1144bb=_0x4f5825;return _0x53e47a[_0x1144bb(0x104)][_0x1144bb(0x16a)]();},_0x2ed5e7=function(_0x4a8621,_0xc276d4){return 0x3e8*(_0xc276d4[0x0]-_0x4a8621[0x0])+(_0xc276d4[0x1]-_0x4a8621[0x1])/0xf4240;};else try{let {performance:_0x6c0ab3}=require(_0x4f5825(0x11a));_0x2534f8=function(){var _0x57029c=_0x4f5825;return _0x6c0ab3[_0x57029c(0x11b)]();};}catch{_0x2534f8=function(){return+new Date();};}}return{'elapsed':_0x2ed5e7,'timeStamp':_0x2534f8,'now':()=>Date['now']()};}function X(_0x108a65,_0x2bc4c8,_0x5e7fce){var _0xd0e45=_0x418f23,_0x184b4d,_0x3be467,_0x1494d3,_0x1853ba,_0xc61e6c;if(_0x108a65[_0xd0e45(0x16e)]!==void 0x0)return _0x108a65['_consoleNinjaAllowedToStart'];let _0xae1558=((_0x3be467=(_0x184b4d=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x184b4d[_0xd0e45(0xa9)])==null?void 0x0:_0x3be467[_0xd0e45(0xc8)])||((_0x1853ba=(_0x1494d3=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x1494d3[_0xd0e45(0x116)])==null?void 0x0:_0x1853ba[_0xd0e45(0xd6)])===_0xd0e45(0x9e);function _0x492297(_0x174e6c){var _0x9b4def=_0xd0e45;if(_0x174e6c[_0x9b4def(0x10d)]('/')&&_0x174e6c[_0x9b4def(0x13f)]('/')){let _0x2461d3=new RegExp(_0x174e6c['slice'](0x1,-0x1));return _0x2a4fef=>_0x2461d3[_0x9b4def(0x113)](_0x2a4fef);}else{if(_0x174e6c[_0x9b4def(0xc3)]('*')||_0x174e6c[_0x9b4def(0xc3)]('?')){let _0x51dbdb=new RegExp('^'+_0x174e6c[_0x9b4def(0x16d)](/\\\\./g,String[_0x9b4def(0xa7)](0x5c)+'.')[_0x9b4def(0x16d)](/\\\\*/g,'.*')[_0x9b4def(0x16d)](/\\\\?/g,'.')+String[_0x9b4def(0xa7)](0x24));return _0x2bf349=>_0x51dbdb['test'](_0x2bf349);}else return _0x40a674=>_0x40a674===_0x174e6c;}}let _0x418e9a=_0x2bc4c8[_0xd0e45(0x12e)](_0x492297);return _0x108a65[_0xd0e45(0x16e)]=_0xae1558||!_0x2bc4c8,!_0x108a65['_consoleNinjaAllowedToStart']&&((_0xc61e6c=_0x108a65[_0xd0e45(0xba)])==null?void 0x0:_0xc61e6c['hostname'])&&(_0x108a65[_0xd0e45(0x16e)]=_0x418e9a[_0xd0e45(0x107)](_0x1dbe80=>_0x1dbe80(_0x108a65[_0xd0e45(0xba)][_0xd0e45(0x149)]))),_0x108a65['_consoleNinjaAllowedToStart'];}function _0x33f3(_0x3a814d,_0x58c537){var _0x4e195d=_0x4e19();return _0x33f3=function(_0x33f3b8,_0x2e2a30){_0x33f3b8=_0x33f3b8-0x98;var _0x3c84c1=_0x4e195d[_0x33f3b8];return _0x3c84c1;},_0x33f3(_0x3a814d,_0x58c537);}function J(_0x3830e6,_0x45a6b5,_0x2f8209,_0x3cee70){var _0x40c820=_0x418f23;_0x3830e6=_0x3830e6,_0x45a6b5=_0x45a6b5,_0x2f8209=_0x2f8209,_0x3cee70=_0x3cee70;let _0x38a5a7=B(_0x3830e6),_0x5b41b9=_0x38a5a7[_0x40c820(0xd3)],_0x1169a5=_0x38a5a7[_0x40c820(0xef)];class _0x1e3ba1{constructor(){var _0x3396c1=_0x40c820;this['_keyStrRegExp']=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this[_0x3396c1(0x135)]=/^(0|[1-9][0-9]*)$/,this[_0x3396c1(0x18b)]=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x3396c1(0x179)]=_0x3830e6[_0x3396c1(0x142)],this[_0x3396c1(0x123)]=_0x3830e6[_0x3396c1(0x15b)],this[_0x3396c1(0x127)]=Object[_0x3396c1(0x171)],this['_getOwnPropertyNames']=Object[_0x3396c1(0xdf)],this[_0x3396c1(0x128)]=_0x3830e6[_0x3396c1(0x154)],this[_0x3396c1(0x144)]=RegExp[_0x3396c1(0x119)][_0x3396c1(0xa5)],this['_dateToString']=Date[_0x3396c1(0x119)][_0x3396c1(0xa5)];}[_0x40c820(0x14a)](_0x1f7b5d,_0x5b6b91,_0x1ebf24,_0x4f3c70){var _0x4d7e42=_0x40c820,_0xe363bc=this,_0x290e3b=_0x1ebf24[_0x4d7e42(0xc0)];function _0x16ce5f(_0xf8520c,_0x1a1953,_0x3e443e){var _0x4923f3=_0x4d7e42;_0x1a1953[_0x4923f3(0x10b)]=_0x4923f3(0xca),_0x1a1953['error']=_0xf8520c[_0x4923f3(0xb4)],_0x454078=_0x3e443e[_0x4923f3(0xc8)]['current'],_0x3e443e['node'][_0x4923f3(0x16b)]=_0x1a1953,_0xe363bc['_treeNodePropertiesBeforeFullValue'](_0x1a1953,_0x3e443e);}let _0x1533a9;_0x3830e6[_0x4d7e42(0xbc)]&&(_0x1533a9=_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)],_0x1533a9&&(_0x3830e6['console'][_0x4d7e42(0x184)]=function(){}));try{try{_0x1ebf24[_0x4d7e42(0xe3)]++,_0x1ebf24['autoExpand']&&_0x1ebf24[_0x4d7e42(0x118)]['push'](_0x5b6b91);var _0x55a2c4,_0x5cbc7d,_0x10ebd6,_0x38ec49,_0x46d06f=[],_0x4ef003=[],_0x33c92e,_0xe8efc0=this[_0x4d7e42(0x182)](_0x5b6b91),_0x5b392f=_0xe8efc0===_0x4d7e42(0x130),_0x55d894=!0x1,_0x65caf4=_0xe8efc0===_0x4d7e42(0x13e),_0x512734=this['_isPrimitiveType'](_0xe8efc0),_0x3d6d36=this[_0x4d7e42(0x15d)](_0xe8efc0),_0x2d65b0=_0x512734||_0x3d6d36,_0x4b6f05={},_0x419e4c=0x0,_0x2bca20=!0x1,_0x454078,_0xed6526=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x1ebf24['depth']){if(_0x5b392f){if(_0x5cbc7d=_0x5b6b91['length'],_0x5cbc7d>_0x1ebf24[_0x4d7e42(0x11c)]){for(_0x10ebd6=0x0,_0x38ec49=_0x1ebf24[_0x4d7e42(0x11c)],_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003[_0x4d7e42(0x157)](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));_0x1f7b5d['cappedElements']=!0x0;}else{for(_0x10ebd6=0x0,_0x38ec49=_0x5cbc7d,_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));}_0x1ebf24[_0x4d7e42(0x9d)]+=_0x4ef003[_0x4d7e42(0x14f)];}if(!(_0xe8efc0==='null'||_0xe8efc0==='undefined')&&!_0x512734&&_0xe8efc0!==_0x4d7e42(0x106)&&_0xe8efc0!=='Buffer'&&_0xe8efc0!=='bigint'){var _0xfca776=_0x4f3c70['props']||_0x1ebf24[_0x4d7e42(0x180)];if(this['_isSet'](_0x5b6b91)?(_0x55a2c4=0x0,_0x5b6b91['forEach'](function(_0x1b3730){var _0x29b12d=_0x4d7e42;if(_0x419e4c++,_0x1ebf24['autoExpandPropertyCount']++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24[_0x29b12d(0xb5)]&&_0x1ebf24[_0x29b12d(0xc0)]&&_0x1ebf24[_0x29b12d(0x9d)]>_0x1ebf24[_0x29b12d(0x177)]){_0x2bca20=!0x0;return;}_0x4ef003['push'](_0xe363bc[_0x29b12d(0x17b)](_0x46d06f,_0x5b6b91,_0x29b12d(0xf6),_0x55a2c4++,_0x1ebf24,function(_0x383398){return function(){return _0x383398;};}(_0x1b3730)));})):this[_0x4d7e42(0x186)](_0x5b6b91)&&_0x5b6b91['forEach'](function(_0x4cd1d9,_0x42ee6b){var _0x3c460e=_0x4d7e42;if(_0x419e4c++,_0x1ebf24[_0x3c460e(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x3c460e(0xc0)]&&_0x1ebf24[_0x3c460e(0x9d)]>_0x1ebf24['autoExpandLimit']){_0x2bca20=!0x0;return;}var _0x2a4101=_0x42ee6b[_0x3c460e(0xa5)]();_0x2a4101[_0x3c460e(0x14f)]>0x64&&(_0x2a4101=_0x2a4101[_0x3c460e(0x146)](0x0,0x64)+_0x3c460e(0x105)),_0x4ef003[_0x3c460e(0x157)](_0xe363bc['_addProperty'](_0x46d06f,_0x5b6b91,_0x3c460e(0x170),_0x2a4101,_0x1ebf24,function(_0x1c45bc){return function(){return _0x1c45bc;};}(_0x4cd1d9)));}),!_0x55d894){try{for(_0x33c92e in _0x5b6b91)if(!(_0x5b392f&&_0xed6526['test'](_0x33c92e))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPropertyCount']>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x150)](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}catch{}if(_0x4b6f05[_0x4d7e42(0x18f)]=!0x0,_0x65caf4&&(_0x4b6f05[_0x4d7e42(0x145)]=!0x0),!_0x2bca20){var _0x469d20=[][_0x4d7e42(0x166)](this[_0x4d7e42(0x15c)](_0x5b6b91))[_0x4d7e42(0x166)](this[_0x4d7e42(0xcf)](_0x5b6b91));for(_0x55a2c4=0x0,_0x5cbc7d=_0x469d20[_0x4d7e42(0x14f)];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)if(_0x33c92e=_0x469d20[_0x55a2c4],!(_0x5b392f&&_0xed6526['test'](_0x33c92e[_0x4d7e42(0xa5)]()))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)&&!_0x4b6f05[_0x4d7e42(0xc7)+_0x33c92e[_0x4d7e42(0xa5)]()]){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24[_0x4d7e42(0xb5)]&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24[_0x4d7e42(0x9d)]>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc['_addObjectProperty'](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}}}}if(_0x1f7b5d['type']=_0xe8efc0,_0x2d65b0?(_0x1f7b5d['value']=_0x5b6b91[_0x4d7e42(0xbf)](),this['_capIfString'](_0xe8efc0,_0x1f7b5d,_0x1ebf24,_0x4f3c70)):_0xe8efc0===_0x4d7e42(0x11f)?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x14b)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='bigint'?_0x1f7b5d[_0x4d7e42(0xce)]=_0x5b6b91[_0x4d7e42(0xa5)]():_0xe8efc0==='RegExp'?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x144)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='symbol'&&this[_0x4d7e42(0x128)]?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x128)][_0x4d7e42(0x119)]['toString']['call'](_0x5b6b91):!_0x1ebf24[_0x4d7e42(0xf0)]&&!(_0xe8efc0===_0x4d7e42(0x187)||_0xe8efc0==='undefined')&&(delete _0x1f7b5d[_0x4d7e42(0xce)],_0x1f7b5d[_0x4d7e42(0xf3)]=!0x0),_0x2bca20&&(_0x1f7b5d['cappedProps']=!0x0),_0x454078=_0x1ebf24['node']['current'],_0x1ebf24[_0x4d7e42(0xc8)]['current']=_0x1f7b5d,this[_0x4d7e42(0xa8)](_0x1f7b5d,_0x1ebf24),_0x4ef003[_0x4d7e42(0x14f)]){for(_0x55a2c4=0x0,_0x5cbc7d=_0x4ef003['length'];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)_0x4ef003[_0x55a2c4](_0x55a2c4);}_0x46d06f['length']&&(_0x1f7b5d[_0x4d7e42(0x180)]=_0x46d06f);}catch(_0x54504a){_0x16ce5f(_0x54504a,_0x1f7b5d,_0x1ebf24);}this[_0x4d7e42(0x10f)](_0x5b6b91,_0x1f7b5d),this[_0x4d7e42(0xe4)](_0x1f7b5d,_0x1ebf24),_0x1ebf24[_0x4d7e42(0xc8)][_0x4d7e42(0x16b)]=_0x454078,_0x1ebf24['level']--,_0x1ebf24[_0x4d7e42(0xc0)]=_0x290e3b,_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPreviousObjects']['pop']();}finally{_0x1533a9&&(_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)]=_0x1533a9);}return _0x1f7b5d;}[_0x40c820(0xcf)](_0xd7ad14){var _0x474a44=_0x40c820;return Object[_0x474a44(0x158)]?Object[_0x474a44(0x158)](_0xd7ad14):[];}[_0x40c820(0xde)](_0x5b06ac){var _0x292c99=_0x40c820;return!!(_0x5b06ac&&_0x3830e6[_0x292c99(0xf6)]&&this[_0x292c99(0x156)](_0x5b06ac)==='[object\\\\x20Set]'&&_0x5b06ac[_0x292c99(0x178)]);}['_blacklistedProperty'](_0x10628d,_0x15c227,_0x5a4f15){var _0x152ffd=_0x40c820;return _0x5a4f15[_0x152ffd(0xa4)]?typeof _0x10628d[_0x15c227]==_0x152ffd(0x13e):!0x1;}[_0x40c820(0x182)](_0x13718c){var _0x2c19d1=_0x40c820,_0x225ae1='';return _0x225ae1=typeof _0x13718c,_0x225ae1==='object'?this[_0x2c19d1(0x156)](_0x13718c)==='[object\\\\x20Array]'?_0x225ae1=_0x2c19d1(0x130):this['_objectToString'](_0x13718c)===_0x2c19d1(0xc1)?_0x225ae1=_0x2c19d1(0x11f):this[_0x2c19d1(0x156)](_0x13718c)===_0x2c19d1(0xbe)?_0x225ae1=_0x2c19d1(0xf9):_0x13718c===null?_0x225ae1=_0x2c19d1(0x187):_0x13718c[_0x2c19d1(0xf7)]&&(_0x225ae1=_0x13718c['constructor'][_0x2c19d1(0xb7)]||_0x225ae1):_0x225ae1===_0x2c19d1(0x142)&&this[_0x2c19d1(0x123)]&&_0x13718c instanceof this['_HTMLAllCollection']&&(_0x225ae1=_0x2c19d1(0x15b)),_0x225ae1;}[_0x40c820(0x156)](_0x37617c){var _0xdf3907=_0x40c820;return Object[_0xdf3907(0x119)]['toString'][_0xdf3907(0xda)](_0x37617c);}[_0x40c820(0xae)](_0x26b95b){var _0x3b9373=_0x40c820;return _0x26b95b===_0x3b9373(0x14d)||_0x26b95b===_0x3b9373(0x101)||_0x26b95b===_0x3b9373(0x12b);}['_isPrimitiveWrapperType'](_0x150515){var _0x2539cd=_0x40c820;return _0x150515===_0x2539cd(0xea)||_0x150515==='String'||_0x150515===_0x2539cd(0xfd);}['_addProperty'](_0x1a647e,_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb){var _0x3c4649=this;return function(_0x5c2af7){var _0x865286=_0x33f3,_0x5ceb03=_0x985088[_0x865286(0xc8)][_0x865286(0x16b)],_0x14ad91=_0x985088[_0x865286(0xc8)]['index'],_0x10beb0=_0x985088[_0x865286(0xc8)][_0x865286(0x9b)];_0x985088['node'][_0x865286(0x9b)]=_0x5ceb03,_0x985088['node'][_0x865286(0xa6)]=typeof _0x551a3a==_0x865286(0x12b)?_0x551a3a:_0x5c2af7,_0x1a647e[_0x865286(0x157)](_0x3c4649[_0x865286(0x17d)](_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb)),_0x985088[_0x865286(0xc8)][_0x865286(0x9b)]=_0x10beb0,_0x985088['node']['index']=_0x14ad91;};}[_0x40c820(0x150)](_0x3e6c99,_0x96cdeb,_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b){var _0x102764=_0x40c820,_0xbcca65=this;return _0x96cdeb[_0x102764(0xc7)+_0x5e1ed6[_0x102764(0xa5)]()]=!0x0,function(_0x4f07e2){var _0x11a9a9=_0x102764,_0x41481f=_0x6bb8c1['node'][_0x11a9a9(0x16b)],_0x45aeeb=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)],_0x44cce6=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0x9b)];_0x6bb8c1['node']['parent']=_0x41481f,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x4f07e2,_0x3e6c99['push'](_0xbcca65[_0x11a9a9(0x17d)](_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b)),_0x6bb8c1['node']['parent']=_0x44cce6,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x45aeeb;};}['_property'](_0x5a954c,_0x11a196,_0x34292c,_0x53d319,_0x300135){var _0x3f13ca=_0x40c820,_0x350c39=this;_0x300135||(_0x300135=function(_0x26467a,_0x467a10){return _0x26467a[_0x467a10];});var _0x112124=_0x34292c[_0x3f13ca(0xa5)](),_0x42837e=_0x53d319[_0x3f13ca(0xc6)]||{},_0x265c6d=_0x53d319['depth'],_0x31debf=_0x53d319[_0x3f13ca(0xb5)];try{var _0x3c8586=this[_0x3f13ca(0x186)](_0x5a954c),_0x5579d4=_0x112124;_0x3c8586&&_0x5579d4[0x0]==='\\\\x27'&&(_0x5579d4=_0x5579d4[_0x3f13ca(0x147)](0x1,_0x5579d4[_0x3f13ca(0x14f)]-0x2));var _0x46f777=_0x53d319[_0x3f13ca(0xc6)]=_0x42837e[_0x3f13ca(0xc7)+_0x5579d4];_0x46f777&&(_0x53d319[_0x3f13ca(0xf0)]=_0x53d319[_0x3f13ca(0xf0)]+0x1),_0x53d319[_0x3f13ca(0xb5)]=!!_0x46f777;var _0x14534f=typeof _0x34292c=='symbol',_0x124dd3={'name':_0x14534f||_0x3c8586?_0x112124:this['_propertyName'](_0x112124)};if(_0x14534f&&(_0x124dd3[_0x3f13ca(0xd4)]=!0x0),!(_0x11a196===_0x3f13ca(0x130)||_0x11a196===_0x3f13ca(0x13b))){var _0x4ea27f=this[_0x3f13ca(0x127)](_0x5a954c,_0x34292c);if(_0x4ea27f&&(_0x4ea27f[_0x3f13ca(0x18c)]&&(_0x124dd3['setter']=!0x0),_0x4ea27f[_0x3f13ca(0x108)]&&!_0x46f777&&!_0x53d319['resolveGetters']))return _0x124dd3[_0x3f13ca(0x172)]=!0x0,this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x3214f9;try{_0x3214f9=_0x300135(_0x5a954c,_0x34292c);}catch(_0x3ef7eb){return _0x124dd3={'name':_0x112124,'type':_0x3f13ca(0xca),'error':_0x3ef7eb['message']},this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x4c3356=this[_0x3f13ca(0x182)](_0x3214f9),_0x18e3f8=this[_0x3f13ca(0xae)](_0x4c3356);if(_0x124dd3[_0x3f13ca(0x10b)]=_0x4c3356,_0x18e3f8)this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x7d7701=_0x3f13ca;_0x124dd3['value']=_0x3214f9[_0x7d7701(0xbf)](),!_0x46f777&&_0x350c39[_0x7d7701(0xe9)](_0x4c3356,_0x124dd3,_0x53d319,{});});else{var _0x275cea=_0x53d319[_0x3f13ca(0xc0)]&&_0x53d319['level']<_0x53d319['autoExpandMaxDepth']&&_0x53d319[_0x3f13ca(0x118)][_0x3f13ca(0x129)](_0x3214f9)<0x0&&_0x4c3356!==_0x3f13ca(0x13e)&&_0x53d319['autoExpandPropertyCount']<_0x53d319['autoExpandLimit'];_0x275cea||_0x53d319[_0x3f13ca(0xe3)]<_0x265c6d||_0x46f777?(this['serialize'](_0x124dd3,_0x3214f9,_0x53d319,_0x46f777||{}),this[_0x3f13ca(0x10f)](_0x3214f9,_0x124dd3)):this['_processTreeNodeResult'](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x2b8765=_0x3f13ca;_0x4c3356===_0x2b8765(0x187)||_0x4c3356===_0x2b8765(0x142)||(delete _0x124dd3[_0x2b8765(0xce)],_0x124dd3[_0x2b8765(0xf3)]=!0x0);});}return _0x124dd3;}finally{_0x53d319['expressionsToEvaluate']=_0x42837e,_0x53d319[_0x3f13ca(0xf0)]=_0x265c6d,_0x53d319['isExpressionToEvaluate']=_0x31debf;}}[_0x40c820(0xe9)](_0x3711dd,_0x3273d6,_0x30712a,_0x2bfd2c){var _0x22c791=_0x40c820,_0x3ed3d6=_0x2bfd2c['strLength']||_0x30712a[_0x22c791(0x126)];if((_0x3711dd==='string'||_0x3711dd==='String')&&_0x3273d6[_0x22c791(0xce)]){let _0xffdbb0=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x14f)];_0x30712a['allStrLength']+=_0xffdbb0,_0x30712a[_0x22c791(0xa2)]>_0x30712a['totalStrLength']?(_0x3273d6[_0x22c791(0xf3)]='',delete _0x3273d6[_0x22c791(0xce)]):_0xffdbb0>_0x3ed3d6&&(_0x3273d6[_0x22c791(0xf3)]=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x147)](0x0,_0x3ed3d6),delete _0x3273d6[_0x22c791(0xce)]);}}['_isMap'](_0x261c40){var _0xd2ec63=_0x40c820;return!!(_0x261c40&&_0x3830e6['Map']&&this[_0xd2ec63(0x156)](_0x261c40)===_0xd2ec63(0x114)&&_0x261c40[_0xd2ec63(0x178)]);}[_0x40c820(0xd5)](_0x24e250){var _0x49be73=_0x40c820;if(_0x24e250[_0x49be73(0x132)](/^\\\\d+$/))return _0x24e250;var _0x1d38d;try{_0x1d38d=JSON['stringify'](''+_0x24e250);}catch{_0x1d38d='\\\\x22'+this['_objectToString'](_0x24e250)+'\\\\x22';}return _0x1d38d['match'](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x1d38d=_0x1d38d[_0x49be73(0x147)](0x1,_0x1d38d[_0x49be73(0x14f)]-0x2):_0x1d38d=_0x1d38d[_0x49be73(0x16d)](/'/g,'\\\\x5c\\\\x27')[_0x49be73(0x16d)](/\\\\\\\\\\\"/g,'\\\\x22')[_0x49be73(0x16d)](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x1d38d;}[_0x40c820(0x133)](_0x1b7571,_0x8f439b,_0x2c2981,_0x188fd5){var _0xf97f10=_0x40c820;this[_0xf97f10(0xa8)](_0x1b7571,_0x8f439b),_0x188fd5&&_0x188fd5(),this[_0xf97f10(0x10f)](_0x2c2981,_0x1b7571),this['_treeNodePropertiesAfterFullValue'](_0x1b7571,_0x8f439b);}[_0x40c820(0xa8)](_0x231eb3,_0x5ea482){var _0x31f690=_0x40c820;this['_setNodeId'](_0x231eb3,_0x5ea482),this[_0x31f690(0xd1)](_0x231eb3,_0x5ea482),this[_0x31f690(0x111)](_0x231eb3,_0x5ea482),this[_0x31f690(0x125)](_0x231eb3,_0x5ea482);}[_0x40c820(0xcb)](_0x10d2e1,_0x3c8083){}[_0x40c820(0xd1)](_0x53d949,_0x188c67){}[_0x40c820(0x163)](_0x3f8259,_0x16e80a){}[_0x40c820(0x122)](_0x25a3a3){return _0x25a3a3===this['_undefined'];}[_0x40c820(0xe4)](_0x44987b,_0x4ed592){var _0x37fb3e=_0x40c820;this[_0x37fb3e(0x163)](_0x44987b,_0x4ed592),this[_0x37fb3e(0xeb)](_0x44987b),_0x4ed592[_0x37fb3e(0xfb)]&&this[_0x37fb3e(0x12d)](_0x44987b),this['_addFunctionsNode'](_0x44987b,_0x4ed592),this[_0x37fb3e(0x17e)](_0x44987b,_0x4ed592),this[_0x37fb3e(0x16c)](_0x44987b);}['_additionalMetadata'](_0x1c2784,_0x4c1dde){var _0x4f21c1=_0x40c820;try{_0x1c2784&&typeof _0x1c2784[_0x4f21c1(0x14f)]==_0x4f21c1(0x12b)&&(_0x4c1dde[_0x4f21c1(0x14f)]=_0x1c2784[_0x4f21c1(0x14f)]);}catch{}if(_0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x12b)||_0x4c1dde[_0x4f21c1(0x10b)]==='Number'){if(isNaN(_0x4c1dde[_0x4f21c1(0xce)]))_0x4c1dde[_0x4f21c1(0x18d)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];else switch(_0x4c1dde[_0x4f21c1(0xce)]){case Number[_0x4f21c1(0xfa)]:_0x4c1dde[_0x4f21c1(0x102)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case Number['NEGATIVE_INFINITY']:_0x4c1dde[_0x4f21c1(0x189)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case 0x0:this['_isNegativeZero'](_0x4c1dde[_0x4f21c1(0xce)])&&(_0x4c1dde[_0x4f21c1(0xd0)]=!0x0);break;}}else _0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x13e)&&typeof _0x1c2784['name']==_0x4f21c1(0x101)&&_0x1c2784[_0x4f21c1(0xb7)]&&_0x4c1dde['name']&&_0x1c2784[_0x4f21c1(0xb7)]!==_0x4c1dde[_0x4f21c1(0xb7)]&&(_0x4c1dde[_0x4f21c1(0xad)]=_0x1c2784[_0x4f21c1(0xb7)]);}['_isNegativeZero'](_0x289882){var _0x1b66c9=_0x40c820;return 0x1/_0x289882===Number[_0x1b66c9(0x181)];}['_sortProps'](_0x3992ee){var _0x3db550=_0x40c820;!_0x3992ee[_0x3db550(0x180)]||!_0x3992ee[_0x3db550(0x180)][_0x3db550(0x14f)]||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0x130)||_0x3992ee[_0x3db550(0x10b)]==='Map'||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0xf6)||_0x3992ee[_0x3db550(0x180)][_0x3db550(0x175)](function(_0x57a739,_0x31b40b){var _0x5dcaae=_0x3db550,_0x3d0d50=_0x57a739[_0x5dcaae(0xb7)][_0x5dcaae(0x10e)](),_0xd6d4fc=_0x31b40b[_0x5dcaae(0xb7)]['toLowerCase']();return _0x3d0d50<_0xd6d4fc?-0x1:_0x3d0d50>_0xd6d4fc?0x1:0x0;});}[_0x40c820(0xe8)](_0x12537a,_0x57f3dc){var _0x2884a4=_0x40c820;if(!(_0x57f3dc[_0x2884a4(0xa4)]||!_0x12537a['props']||!_0x12537a[_0x2884a4(0x180)][_0x2884a4(0x14f)])){for(var _0x53c006=[],_0x347d6e=[],_0x52e85a=0x0,_0x273297=_0x12537a['props']['length'];_0x52e85a<_0x273297;_0x52e85a++){var _0x1ee5b3=_0x12537a[_0x2884a4(0x180)][_0x52e85a];_0x1ee5b3[_0x2884a4(0x10b)]==='function'?_0x53c006[_0x2884a4(0x157)](_0x1ee5b3):_0x347d6e['push'](_0x1ee5b3);}if(!(!_0x347d6e[_0x2884a4(0x14f)]||_0x53c006[_0x2884a4(0x14f)]<=0x1)){_0x12537a[_0x2884a4(0x180)]=_0x347d6e;var _0x15f515={'functionsNode':!0x0,'props':_0x53c006};this[_0x2884a4(0xcb)](_0x15f515,_0x57f3dc),this[_0x2884a4(0x163)](_0x15f515,_0x57f3dc),this[_0x2884a4(0xeb)](_0x15f515),this[_0x2884a4(0x125)](_0x15f515,_0x57f3dc),_0x15f515['id']+='\\\\x20f',_0x12537a['props']['unshift'](_0x15f515);}}}['_addLoadNode'](_0x5bea6e,_0x14049e){}[_0x40c820(0xeb)](_0x199084){}[_0x40c820(0x98)](_0xf50c17){var _0x35cb98=_0x40c820;return Array['isArray'](_0xf50c17)||typeof _0xf50c17==_0x35cb98(0x117)&&this['_objectToString'](_0xf50c17)===_0x35cb98(0xdc);}[_0x40c820(0x125)](_0x3ea390,_0x54c209){}[_0x40c820(0x16c)](_0x25cdb9){var _0x1aa0a5=_0x40c820;delete _0x25cdb9[_0x1aa0a5(0x136)],delete _0x25cdb9[_0x1aa0a5(0x143)],delete _0x25cdb9[_0x1aa0a5(0x138)];}['_setNodeExpressionPath'](_0x17f351,_0x40c77e){}}let _0x459cb0=new _0x1e3ba1(),_0x218fe5={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x11fc4c={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x482c8e(_0x50675f,_0x2f7559,_0x19c481,_0x2c8a95,_0x245f16,_0x24484e){var _0x4c67af=_0x40c820;let _0x3ce9b8,_0x1af844;try{_0x1af844=_0x1169a5(),_0x3ce9b8=_0x2f8209[_0x2f7559],!_0x3ce9b8||_0x1af844-_0x3ce9b8['ts']>0x1f4&&_0x3ce9b8[_0x4c67af(0xaa)]&&_0x3ce9b8['time']/_0x3ce9b8[_0x4c67af(0xaa)]<0x64?(_0x2f8209[_0x2f7559]=_0x3ce9b8={'count':0x0,'time':0x0,'ts':_0x1af844},_0x2f8209[_0x4c67af(0xc5)]={}):_0x1af844-_0x2f8209[_0x4c67af(0xc5)]['ts']>0x32&&_0x2f8209['hits']['count']&&_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x161)]/_0x2f8209[_0x4c67af(0xc5)]['count']<0x64&&(_0x2f8209[_0x4c67af(0xc5)]={});let _0x157126=[],_0x1464d6=_0x3ce9b8[_0x4c67af(0x112)]||_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]?_0x11fc4c:_0x218fe5,_0x553948=_0x2ff7b4=>{var _0x3a1316=_0x4c67af;let _0x5d676c={};return _0x5d676c[_0x3a1316(0x180)]=_0x2ff7b4[_0x3a1316(0x180)],_0x5d676c['elements']=_0x2ff7b4['elements'],_0x5d676c[_0x3a1316(0x126)]=_0x2ff7b4[_0x3a1316(0x126)],_0x5d676c['totalStrLength']=_0x2ff7b4['totalStrLength'],_0x5d676c['autoExpandLimit']=_0x2ff7b4[_0x3a1316(0x177)],_0x5d676c[_0x3a1316(0x115)]=_0x2ff7b4['autoExpandMaxDepth'],_0x5d676c['sortProps']=!0x1,_0x5d676c['noFunctions']=!_0x45a6b5,_0x5d676c['depth']=0x1,_0x5d676c[_0x3a1316(0xe3)]=0x0,_0x5d676c['expId']='root_exp_id',_0x5d676c[_0x3a1316(0x140)]=_0x3a1316(0x151),_0x5d676c['autoExpand']=!0x0,_0x5d676c[_0x3a1316(0x118)]=[],_0x5d676c[_0x3a1316(0x9d)]=0x0,_0x5d676c[_0x3a1316(0x168)]=!0x0,_0x5d676c['allStrLength']=0x0,_0x5d676c[_0x3a1316(0xc8)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0x5d676c;};for(var _0x33de8f=0x0;_0x33de8f<_0x245f16[_0x4c67af(0x14f)];_0x33de8f++)_0x157126[_0x4c67af(0x157)](_0x459cb0[_0x4c67af(0x14a)]({'timeNode':_0x50675f===_0x4c67af(0x161)||void 0x0},_0x245f16[_0x33de8f],_0x553948(_0x1464d6),{}));if(_0x50675f==='trace'||_0x50675f===_0x4c67af(0x184)){let _0x1d9735=Error[_0x4c67af(0xd7)];try{Error[_0x4c67af(0xd7)]=0x1/0x0,_0x157126[_0x4c67af(0x157)](_0x459cb0['serialize']({'stackNode':!0x0},new Error()['stack'],_0x553948(_0x1464d6),{'strLength':0x1/0x0}));}finally{Error[_0x4c67af(0xd7)]=_0x1d9735;}}return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':_0x157126,'id':_0x2f7559,'context':_0x24484e}]};}catch(_0x2d5a77){return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':[{'type':_0x4c67af(0xca),'error':_0x2d5a77&&_0x2d5a77[_0x4c67af(0xb4)]}],'id':_0x2f7559,'context':_0x24484e}]};}finally{try{if(_0x3ce9b8&&_0x1af844){let _0xff386f=_0x1169a5();_0x3ce9b8[_0x4c67af(0xaa)]++,_0x3ce9b8['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x3ce9b8['ts']=_0xff386f,_0x2f8209[_0x4c67af(0xc5)]['count']++,_0x2f8209[_0x4c67af(0xc5)]['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x2f8209['hits']['ts']=_0xff386f,(_0x3ce9b8[_0x4c67af(0xaa)]>0x32||_0x3ce9b8[_0x4c67af(0x161)]>0x64)&&(_0x3ce9b8['reduceLimits']=!0x0),(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0xaa)]>0x3e8||_0x2f8209[_0x4c67af(0xc5)]['time']>0x12c)&&(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]=!0x0);}}catch{}}}return _0x482c8e;}((_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x296e29,_0x567fe9,_0x14adfa,_0x6b3989,_0x593945,_0x42f609)=>{var _0x543ef9=_0x418f23;if(_0x12a02f[_0x543ef9(0xdb)])return _0x12a02f[_0x543ef9(0xdb)];if(!X(_0x12a02f,_0x14adfa,_0x1164b7))return _0x12a02f[_0x543ef9(0xdb)]={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x12a02f[_0x543ef9(0xdb)];let _0x5a7d78=B(_0x12a02f),_0x236b4f=_0x5a7d78[_0x543ef9(0xd3)],_0x57b9d9=_0x5a7d78['timeStamp'],_0x163b61=_0x5a7d78[_0x543ef9(0x11b)],_0x384cd9={'hits':{},'ts':{}},_0x9c7997=J(_0x12a02f,_0x6b3989,_0x384cd9,_0x296e29),_0x3ffb36=_0xa1ec34=>{_0x384cd9['ts'][_0xa1ec34]=_0x57b9d9();},_0x4ce4d2=(_0x173258,_0x2e0c6d)=>{var _0x3b6e53=_0x543ef9;let _0x2b64c1=_0x384cd9['ts'][_0x2e0c6d];if(delete _0x384cd9['ts'][_0x2e0c6d],_0x2b64c1){let _0x557981=_0x236b4f(_0x2b64c1,_0x57b9d9());_0x593a90(_0x9c7997(_0x3b6e53(0x161),_0x173258,_0x163b61(),_0x50e896,[_0x557981],_0x2e0c6d));}},_0x46c5f3=_0x1f105e=>{var _0x1152c8=_0x543ef9,_0x4a2783;return _0x1164b7===_0x1152c8(0x110)&&_0x12a02f[_0x1152c8(0x152)]&&((_0x4a2783=_0x1f105e==null?void 0x0:_0x1f105e[_0x1152c8(0xab)])==null?void 0x0:_0x4a2783[_0x1152c8(0x14f)])&&(_0x1f105e[_0x1152c8(0xab)][0x0][_0x1152c8(0x152)]=_0x12a02f[_0x1152c8(0x152)]),_0x1f105e;};_0x12a02f[_0x543ef9(0xdb)]={'consoleLog':(_0x204f4b,_0x3e1804)=>{var _0x309615=_0x543ef9;_0x12a02f['console'][_0x309615(0xff)][_0x309615(0xb7)]!==_0x309615(0x134)&&_0x593a90(_0x9c7997(_0x309615(0xff),_0x204f4b,_0x163b61(),_0x50e896,_0x3e1804));},'consoleTrace':(_0x267a3f,_0x51c339)=>{var _0x4c4943=_0x543ef9,_0x33fd8c,_0x24e61f;_0x12a02f[_0x4c4943(0xbc)][_0x4c4943(0xff)]['name']!==_0x4c4943(0xbd)&&((_0x24e61f=(_0x33fd8c=_0x12a02f[_0x4c4943(0x104)])==null?void 0x0:_0x33fd8c['versions'])!=null&&_0x24e61f[_0x4c4943(0xc8)]&&(_0x12a02f[_0x4c4943(0x165)]=!0x0),_0x593a90(_0x46c5f3(_0x9c7997(_0x4c4943(0xc4),_0x267a3f,_0x163b61(),_0x50e896,_0x51c339))));},'consoleError':(_0xf7f1fc,_0x1384d7)=>{var _0x28d83c=_0x543ef9;_0x12a02f['_ninjaIgnoreNextError']=!0x0,_0x593a90(_0x46c5f3(_0x9c7997(_0x28d83c(0x184),_0xf7f1fc,_0x163b61(),_0x50e896,_0x1384d7)));},'consoleTime':_0x2ad865=>{_0x3ffb36(_0x2ad865);},'consoleTimeEnd':(_0x3c91cf,_0x308c8b)=>{_0x4ce4d2(_0x308c8b,_0x3c91cf);},'autoLog':(_0x4bbc9f,_0x3599a3)=>{var _0x598cfa=_0x543ef9;_0x593a90(_0x9c7997(_0x598cfa(0xff),_0x3599a3,_0x163b61(),_0x50e896,[_0x4bbc9f]));},'autoLogMany':(_0x158592,_0x29b77d)=>{var _0x425f64=_0x543ef9;_0x593a90(_0x9c7997(_0x425f64(0xff),_0x158592,_0x163b61(),_0x50e896,_0x29b77d));},'autoTrace':(_0x3f5f9d,_0xc378ab)=>{var _0x377a7d=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x377a7d(0xc4),_0xc378ab,_0x163b61(),_0x50e896,[_0x3f5f9d])));},'autoTraceMany':(_0x2c6f73,_0x35405b)=>{var _0x4f4e7f=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x4f4e7f(0xc4),_0x2c6f73,_0x163b61(),_0x50e896,_0x35405b)));},'autoTime':(_0x4915d6,_0xaaf0db,_0x4c1f1e)=>{_0x3ffb36(_0x4c1f1e);},'autoTimeEnd':(_0x397624,_0x436d57,_0x47b9b8)=>{_0x4ce4d2(_0x436d57,_0x47b9b8);},'coverage':_0x45a646=>{var _0x1e9860=_0x543ef9;_0x593a90({'method':_0x1e9860(0x13a),'version':_0x296e29,'args':[{'id':_0x45a646}]});}};let _0x593a90=H(_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x593945,_0x42f609),_0x50e896=_0x12a02f[_0x543ef9(0xd8)];return _0x12a02f[_0x543ef9(0xdb)];})(globalThis,'127.0.0.1',_0x418f23(0xec),_0x418f23(0x17a),_0x418f23(0x183),'1.0.0','1751470503250',_0x418f23(0x137),_0x418f23(0x18a),_0x418f23(0x10a),_0x418f23(0xb1));\");\n  } catch (e) {}\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_oo(\n/**@type{any}**/\ni) {\n  for (var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    v[_key - 1] = arguments[_key];\n  }\n\n  try {\n    oo_cm().consoleLog(i, v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_tr(\n/**@type{any}**/\ni) {\n  for (var _len2 = arguments.length, v = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    v[_key2 - 1] = arguments[_key2];\n  }\n\n  try {\n    oo_cm().consoleTrace(i, v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_tx(\n/**@type{any}**/\ni) {\n  for (var _len3 = arguments.length, v = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n    v[_key3 - 1] = arguments[_key3];\n  }\n\n  try {\n    oo_cm().consoleError(i, v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_ts(\n/**@type{any}**/\nv) {\n  try {\n    oo_cm().consoleTime(v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_te(\n/**@type{any}**/\nv,\n/**@type{any}**/\ni) {\n  try {\n    oo_cm().consoleTimeEnd(v, i);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/\n\nvar _c;\n\n$RefreshReg$(_c, \"SummaryTab\");", "map": {"version": 3, "sources": ["D:/www/SWF/sian-web-frontend/src/views/commercial/salesDashboard/components/SummaryTab.jsx"], "names": ["React", "useState", "useEffect", "Box", "Typography", "styled", "CircularProgress", "Backdrop", "<PERSON><PERSON>", "TextField", "MenuItem", "IconButton", "<PERSON><PERSON>", "Divider", "Switch", "FormControlLabel", "<PERSON><PERSON>", "AdapterDateFns", "LocalizationProvider", "DatePicker", "es", "startOfMonth", "endOfMonth", "startOfWeek", "endOfWeek", "subDays", "subMonths", "isSameDay", "format", "getDaysInMonth", "addDays", "getSalesDashboardGoalsPromise", "PredictionModal", "CalendarToday", "CalendarTodayIcon", "Schedule", "ScheduleIcon", "Clear", "ClearIcon", "CompareArrows", "CompareIcon", "ExpandMore", "ExpandMoreIcon", "updateGlobalPercentages", "trafficLightColors", "LegendGroup", "getStoreColor", "getStatusLabel", "MetricPercentage", "StoreGrid", "StoreName", "MetricCard", "Metric<PERSON><PERSON><PERSON>", "MetricTitle", "MetricDate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MetricInfo", "ChartContainer", "<PERSON>ric<PERSON><PERSON><PERSON>", "MetricValue", "ChartLegend", "LegendItem", "LegendDot", "TotalCard", "TotalHeader", "TotalTitle", "TotalTitleIcon", "TotalStats", "StatItem", "StatLabel", "StatValue", "ProgressBarContainer", "ProgressLabel", "StyledLinearProgress", "PredictButton", "ButtonContainer", "UpdatedAt", "TotalSection", "FooterLegend", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ResponsiveContainer", "PolarAngleAxis", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "DateRangeInput", "formatFriendlyDate", "getFirstDayOfCurrentMonth", "getFirstDayOfGivenMonth", "getTodayDate", "getTodayDateTime", "is<PERSON>ull<PERSON>onth", "globalLegendPercentages", "green", "yellow", "orange", "red", "SummaryTab", "data", "setData", "compareData", "setCompareData", "loading", "setLoading", "compareLoading", "setCompareLoading", "apiMode", "setApiMode", "selectedTab", "setSelectedTab", "date<PERSON><PERSON><PERSON>", "setDateRange", "dateRangePopperOpen", "setDateRangePopperOpen", "predictionModalOpen", "setPredictionModalOpen", "selectedStore", "setSelectedStore", "selectedStoreData", "setSelectedStoreData", "showComparison", "setShowComparison", "comparePeriod", "setComparePeriod", "customCompareDate", "setCustomCompareDate", "anchorEl", "setAnchorEl", "compareMenuAnchor", "setCompareMenuAnchor", "originalTab", "setOriginalTab", "compareTicketCount", "setCompareTicketCount", "showDateRangeMenu", "setShowDateRangeMenu", "showCalendarMenu", "setShowCalendarMenu", "date1", "date2", "getDate", "getMonth", "getFullYear", "isComparisonAllowed", "today", "isCurrentMonth", "formatPeruDate", "date", "d", "Date", "year", "month", "String", "padStart", "day", "getPeruDate", "now", "peruDate", "toLocaleString", "timeZone", "calculatePercentage", "salesOrStore", "goal", "storeData", "salesValue", "goalValue", "parseFloat", "today_sales", "today_goal", "progress_sales", "period_goal", "totalDays", "sales", "targetGoal", "handleDateRangeMenuClose", "handleCompareMenuOpen", "event", "currentTarget", "handleCompareMenuClose", "handleQuickCompareSelect", "periodType", "value", "newDate", "setDate", "setMonth", "setFullYear", "fetchComparisonData", "handleDatePickerChange", "diffTime", "Math", "abs", "diffDays", "ceil", "days", "specificDate", "endDate", "params", "date_from", "date_to", "daysInMonth", "min", "compStart", "compEnd", "generateSmartComparisonPeriod", "compareEndDate", "compareDate", "toString", "compareStartDate", "response", "compareTickets", "reduce", "acc", "store", "parseInt", "today_tickets", "totalResults", "calculateTotals", "comparePercentageTotal", "sale_progress", "error", "console", "oo_tx", "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "newValue", "comparisonStartDate", "comparisonEndDate", "desactivateComparison", "fetchData", "formattedDate", "yesterday", "weekStart", "lastWeekStart", "lastWeekEnd", "startOfCurrentMonth", "lastDayOfStartMonth", "warn", "firstDayOfMonth", "legend_percentages", "setTimeout", "handlePredictClick", "storeName", "business_unit_name", "totals", "query_date", "selected_tab", "date_goal", "dateTotals", "elapsed_days", "elapsedDays", "find", "dateGoal", "color", "zIndex", "theme", "drawer", "backgroundColor", "display", "flexDirection", "alignItems", "gap", "fontWeight", "formatNumber", "undefined", "minimumFractionDigits", "maximumFractionDigits", "formatDate", "dateString", "calculateDateRangeInfo", "normalizeDate", "setHours", "getDayDifference", "start", "end", "normalizedToday", "startDate", "currentDate", "lastM<PERSON>h", "length", "today_average_ticket", "remaining", "totalPercentage", "DateSelectorButton", "borderColor", "min<PERSON><PERSON><PERSON>", "justifyContent", "boxShadow", "borderRadius", "padding", "transition", "DateSelectorContainer", "marginBottom", "position", "flexWrap", "overflow", "CompareButton", "active", "DateRangeActionButton", "fontSize", "textTransform", "DateRangeTextField", "borderWidth", "CustomMenuContainer", "top", "left", "marginTop", "CustomCalendarContainer", "transform", "width", "CustomMenuItem", "cursor", "ComparisonInfo", "marginLeft", "border", "marginRight", "getComparisonText", "getCompareEndDate", "getTime", "currentMonth", "compareMonth", "currentDay", "compareMonthName", "toggleDateRangeMenu", "toggleCalendarMenu", "closeAllMenus", "applyPresetRange", "preset", "newStartDate", "newEndDate", "newTab", "isThis<PERSON><PERSON><PERSON>", "startOfCurrentWeek", "getDay", "isThisWeek", "startOfLastWeek", "endOfLastWeek", "isLastWeek", "previousMonth", "targetDay", "daysInPreviousMonth", "targetEndDay", "calculateProjection", "projectedSale", "projectedPercentage", "currentPercentage", "todayGoal", "weeklyGoal", "currentSales", "monthlyGoal", "max<PERSON><PERSON><PERSON>", "margin", "isNaN", "ml", "opacity", "mb", "toLocaleDateString", "weekday", "toUpperCase", "includes", "mt", "sum", "item", "fontStyle", "mx", "mr", "toFixed", "textAlign", "map", "index", "dailyPercentage", "monthlyPercentage", "datePercentage", "compareStore", "comparePercentage", "percentageDifference", "cs", "business_unit_id", "compareGoal", "name", "fill", "innerRadius", "outerRadius", "bottom", "right", "difference", "isExcess", "gridTemplateColumns", "p", "today_drop_size", "progress_average_ticket", "borderTop", "pt", "projectionSales", "closing_projection_sales", "projectionPercentage", "closing_projection_percentage", "progress_drop_size", "projection", "danger", "warning", "success", "Boolean", "vertical", "horizontal", "elevation", "sx", "oo_cm", "eval", "e", "oo_oo", "i", "v", "consoleLog", "oo_tr", "consoleTrace", "consoleError", "oo_ts", "consoleTime", "oo_te", "consoleTimeEnd"], "mappings": ";;;AAAA;AACA,OAAOA,KAAP,IAAgBC,QAAhB,EAA0BC,SAA1B,QAA2C,OAA3C;AACA,SACIC,GADJ,EAEIC,UAFJ,EAGIC,MAHJ,EAIIC,gBAJJ,EAKIC,QALJ,EAMIC,MANJ,EAOIC,SAPJ,EAQIC,QARJ,EASIC,UATJ,EAUIC,IAVJ,EAWIC,OAXJ,EAYIC,MAZJ,EAaIC,gBAbJ,EAcIC,KAdJ,QAeO,eAfP;AAgBA,SAASC,cAAT,QAA+B,oCAA/B;AACA,SAASC,oBAAT,EAA+BC,UAA/B,QAAiD,qBAAjD;AACA,SAASC,EAAT,QAAmB,iBAAnB;AACA,SAASC,YAAT,EAAuBC,UAAvB,EAAmCC,WAAnC,EAAgDC,SAAhD,EAA2DC,OAA3D,EAAoEC,SAApE,EAA+EC,SAA/E,EAA0FC,MAA1F,EAAkGC,cAAlG,EAAkHC,OAAlH,QAAiI,UAAjI;AACA,SAASC,6BAAT,QAA8C,yBAA9C;AACA,OAAOC,eAAP,MAA4B,mBAA5B;AACA,SACIC,aAAa,IAAIC,iBADrB,EAEIC,QAAQ,IAAIC,YAFhB,EAGIC,KAAK,IAAIC,SAHb,EAIIC,aAAa,IAAIC,WAJrB,EAKIC,UAAU,IAAIC,cALlB,QAMO,qBANP;AAOA,SACIC,uBADJ,EAEIC,kBAFJ,EAGIC,WAHJ,EAIIC,aAJJ,EAKIC,cALJ,EAMIC,gBANJ,EAOIC,SAPJ,EAQIC,SARJ,EASIC,UATJ,EAUIC,YAVJ,EAWIC,WAXJ,EAYIC,UAZJ,EAaIC,aAbJ,EAcIC,UAdJ,EAeIC,cAfJ,EAgBIC,YAhBJ,EAiBIC,WAjBJ,EAkBIC,WAlBJ,EAmBIC,UAnBJ,EAoBIC,SApBJ,EAqBIC,SArBJ,EAsBIC,WAtBJ,EAuBIC,UAvBJ,EAwBIC,cAxBJ,EAyBIC,UAzBJ,EA0BIC,QA1BJ,EA2BIC,SA3BJ,EA4BIC,SA5BJ,EA6BIC,oBA7BJ,EA8BIC,aA9BJ,EA+BIC,oBA/BJ,EAgCIC,aAhCJ,EAiCIC,eAjCJ,EAkCIC,SAlCJ,EAmCIC,YAnCJ,EAoCIC,YApCJ,QAqCO,sBArCP;AAsCA,SAASC,cAAT,EAAyBC,SAAzB,EAAoCC,mBAApC,EAAyDC,cAAzD,EAAyEC,QAAzE,EAAmFC,GAAnF,EAAwFC,KAAxF,EAA+FC,KAA/F,EAAsGC,OAAtG,QAAqH,UAArH;AACA,OAAOC,cAAP,MAA2B,kBAA3B;AACA,SAASC,kBAAT,EAA6BC,yBAA7B,EAAwDC,uBAAxD,EAAiFC,YAAjF,EAA+FC,gBAA/F,EAAiHC,WAAjH,QAAoI,aAApI;;;AAEA,IAAIC,uBAAuB,GAAG;AAC1BC,EAAAA,KAAK,EAAE,EADmB;AAE1BC,EAAAA,MAAM,EAAE,EAFkB;AAG1BC,EAAAA,MAAM,EAAE,EAHkB;AAI1BC,EAAAA,GAAG,EAAE;AAJqB,CAA9B;;AAOA,MAAMC,UAAU,GAAG,MAAM;AAAA;;AACrB,QAAM,CAACC,IAAD,EAAOC,OAAP,IAAkBrG,QAAQ,CAAC,EAAD,CAAhC;AACA,QAAM,CAACsG,WAAD,EAAcC,cAAd,IAAgCvG,QAAQ,CAAC,EAAD,CAA9C,CAFqB,CAE+B;;AACpD,QAAM,CAACwG,OAAD,EAAUC,UAAV,IAAwBzG,QAAQ,CAAC,IAAD,CAAtC;AACA,QAAM,CAAC0G,cAAD,EAAiBC,iBAAjB,IAAsC3G,QAAQ,CAAC,KAAD,CAApD;AACA,QAAM,CAAC4G,OAAD,EAAUC,UAAV,IAAwB7G,QAAQ,CAAC,CAAD,CAAtC,CALqB,CAKsB;;AAC3C,QAAM,CAAC8G,WAAD,EAAcC,cAAd,IAAgC/G,QAAQ,CAAC,KAAD,CAA9C,CANqB,CAMkC;;AACvD,QAAM,CAACgH,SAAD,EAAYC,YAAZ,IAA4BjH,QAAQ,CAAC,CAAC2F,YAAY,EAAb,EAAiBA,YAAY,EAA7B,CAAD,CAA1C,CAPqB,CAOyD;;AAC9E,QAAM,CAACuB,mBAAD,EAAsBC,sBAAtB,IAAgDnH,QAAQ,CAAC,KAAD,CAA9D;AACA,QAAM,CAACoH,mBAAD,EAAsBC,sBAAtB,IAAgDrH,QAAQ,CAAC,KAAD,CAA9D;AACA,QAAM,CAACsH,aAAD,EAAgBC,gBAAhB,IAAoCvH,QAAQ,CAAC,IAAD,CAAlD;AACA,QAAM,CAACwH,iBAAD,EAAoBC,oBAApB,IAA4CzH,QAAQ,CAAC,IAAD,CAA1D;AACA,QAAM,CAAC0H,cAAD,EAAiBC,iBAAjB,IAAsC3H,QAAQ,CAAC,KAAD,CAApD,CAZqB,CAYwC;;AAC7D,QAAM,CAAC4H,aAAD,EAAgBC,gBAAhB,IAAoC7H,QAAQ,CAAC,CAAD,CAAlD,CAbqB,CAakC;;AACvD,QAAM,CAAC8H,iBAAD,EAAoBC,oBAApB,IAA4C/H,QAAQ,CAAC,IAAD,CAA1D;AACA,QAAM,CAACgI,QAAD,EAAWC,WAAX,IAA0BjI,QAAQ,CAAC,IAAD,CAAxC;AACA,QAAM,CAACkI,iBAAD,EAAoBC,oBAApB,IAA4CnI,QAAQ,CAAC,IAAD,CAA1D;AACA,QAAM,CAACoI,WAAD,EAAcC,cAAd,IAAgCrI,QAAQ,CAAC,KAAD,CAA9C,CAjBqB,CAiBkC;;AACvD,QAAM,CAACsI,kBAAD,EAAqBC,qBAArB,IAA8CvI,QAAQ,CAAC,CAAD,CAA5D,CAlBqB,CAoBrB;;AACA,QAAM,CAACwI,iBAAD,EAAoBC,oBAApB,IAA4CzI,QAAQ,CAAC,KAAD,CAA1D;AACA,QAAM,CAAC0I,gBAAD,EAAmBC,mBAAnB,IAA0C3I,QAAQ,CAAC,KAAD,CAAxD,CAtBqB,CAwBrB;;AACA,QAAM0B,SAAS,GAAG,CAACkH,KAAD,EAAQC,KAAR,KAAkB;AAChC,QAAI,CAACD,KAAD,IAAU,CAACC,KAAf,EAAsB,OAAO,KAAP;AACtB,WAAOD,KAAK,CAACE,OAAN,OAAoBD,KAAK,CAACC,OAAN,EAApB,IAAuCF,KAAK,CAACG,QAAN,OAAqBF,KAAK,CAACE,QAAN,EAA5D,IAAgFH,KAAK,CAACI,WAAN,OAAwBH,KAAK,CAACG,WAAN,EAA/G;AACH,GAHD,CAzBqB,CA8BrB;;;AACA,QAAMC,mBAAmB,GAAG,MAAM;AAC9B,UAAMC,KAAK,GAAGvD,YAAY,EAA1B,CAD8B,CAG9B;;AACA,QAAImB,WAAW,KAAK,KAApB,EAA2B,OAAO,KAAP,CAJG,CAM9B;;AACA,QAAIA,WAAW,KAAK,QAApB,EAA8B,OAAO,KAAP,CAPA,CAS9B;;AACA,QAAIA,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA/C,EAA4D;AACxD,aAAO,IAAP;AACA,YAAMqC,cAAc,GAAGD,KAAK,CAACH,QAAN,OAAqB/B,SAAS,CAAC,CAAD,CAAT,CAAa+B,QAAb,EAArB,IAAgDG,KAAK,CAACF,WAAN,OAAwBhC,SAAS,CAAC,CAAD,CAAT,CAAagC,WAAb,EAA/F;AACA,aAAO,CAACG,cAAR;AACH,KAd6B,CAgB9B;;;AACA,QAAIrC,WAAW,KAAK,MAApB,EAA4B;AACxB,aAAO,CAACpF,SAAS,CAACsF,SAAS,CAAC,CAAD,CAAV,EAAekC,KAAf,CAAjB;AACH,KAnB6B,CAqB9B;;;AACA,WAAO,IAAP;AACH,GAvBD,CA/BqB,CAwDrB;;;AACA,QAAME,cAAc,GAAIC,IAAD,IAAU;AAC7B,QAAI,CAACA,IAAL,EAAW,OAAO,EAAP,CADkB,CAE7B;;AACA,UAAMC,CAAC,GAAG,IAAIC,IAAJ,CAASF,IAAT,CAAV,CAH6B,CAK7B;;AACA,UAAMG,IAAI,GAAGF,CAAC,CAACN,WAAF,EAAb;AACA,UAAMS,KAAK,GAAGC,MAAM,CAACJ,CAAC,CAACP,QAAF,KAAe,CAAhB,CAAN,CAAyBY,QAAzB,CAAkC,CAAlC,EAAqC,GAArC,CAAd;AACA,UAAMC,GAAG,GAAGF,MAAM,CAACJ,CAAC,CAACR,OAAF,EAAD,CAAN,CAAoBa,QAApB,CAA6B,CAA7B,EAAgC,GAAhC,CAAZ,CAR6B,CAU7B;;AACA,WAAQ,GAAEH,IAAK,IAAGC,KAAM,IAAGG,GAAI,EAA/B;AACH,GAZD,CAzDqB,CAuErB;;;AACA,QAAMC,WAAW,GAAG,MAAM;AACtB;AACA,UAAMC,GAAG,GAAGlE,gBAAgB,EAA5B,CAFsB,CAItB;AACA;;AACA,UAAMmE,QAAQ,GAAG,IAAIR,IAAJ,CAASO,GAAG,CAACE,cAAJ,CAAmB,OAAnB,EAA4B;AAAEC,MAAAA,QAAQ,EAAE;AAAZ,KAA5B,CAAT,CAAjB;AAEA,WAAOF,QAAP;AACH,GATD,CAxEqB,CAmFrB;;;AACA,QAAMG,mBAAmB,GAAG,UAACC,YAAD,EAA+B;AAAA,QAAhBC,IAAgB,uEAAT,IAAS;;AACvD;AACA,QAAI,OAAOD,YAAP,KAAwB,QAAxB,IAAoCA,YAAY,KAAK,IAAzD,EAA+D;AAC3D,YAAME,SAAS,GAAGF,YAAlB;AACA,UAAI,CAACE,SAAL,EAAgB,OAAO,CAAP;AAEhB,UAAIC,UAAJ,EAAgBC,SAAhB;;AAEA,UAAIzD,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAA7C,EAA0D;AACtDwD,QAAAA,UAAU,GAAGE,UAAU,CAACH,SAAS,CAACI,WAAV,IAAyB,CAA1B,CAAvB;AACAF,QAAAA,SAAS,GAAGC,UAAU,CAACH,SAAS,CAACK,UAAV,IAAwB,CAAzB,CAAtB;AACH,OAHD,MAGO,IAAI5D,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA3C,IAA0DA,WAAW,KAAK,WAA9E,EAA2F;AAC9FwD,QAAAA,UAAU,GAAGE,UAAU,CAACH,SAAS,CAACM,cAAV,IAA4B,CAA7B,CAAvB;AACAJ,QAAAA,SAAS,GAAGC,UAAU,CAACH,SAAS,CAACO,WAAV,IAAyB,CAA1B,CAAtB;AACH,OAHM,MAGA;AACH;AACAN,QAAAA,UAAU,GAAGE,UAAU,CAACH,SAAS,CAACM,cAAV,IAA4B,CAA7B,CAAvB;AACAJ,QAAAA,SAAS,GAAGC,UAAU,CAACH,SAAS,CAACK,UAAV,IAAwB,CAAzB,CAAV,GAAwCG,SAApD;AACH;;AAED,UAAIN,SAAS,KAAK,CAAlB,EAAqB,OAAO,CAAP;AACrB,aAAQD,UAAU,GAAGC,SAAd,GAA2B,GAAlC;AACH,KApBD,CAqBA;AArBA,SAsBK;AACD,YAAMO,KAAK,GAAGN,UAAU,CAACL,YAAY,IAAI,CAAjB,CAAxB;AACA,YAAMY,UAAU,GAAGP,UAAU,CAACJ,IAAI,IAAI,CAAT,CAA7B;AACA,UAAI,CAACW,UAAD,IAAeA,UAAU,KAAK,CAAlC,EAAqC,OAAO,CAAP;AACrC,aAAQD,KAAK,GAAGC,UAAT,GAAuB,GAA9B;AACH;AACJ,GA9BD,CApFqB,CAoHrB;;;AACA,QAAMC,wBAAwB,GAAG,MAAM;AACnC7D,IAAAA,sBAAsB,CAAC,KAAD,CAAtB;AACH,GAFD,CArHqB,CAyHrB;;;AACA,QAAM8D,qBAAqB,GAAIC,KAAD,IAAW;AACrC/C,IAAAA,oBAAoB,CAAC+C,KAAK,CAACC,aAAP,CAApB;AACH,GAFD,CA1HqB,CA8HrB;;;AACA,QAAMC,sBAAsB,GAAG,MAAM;AACjCjD,IAAAA,oBAAoB,CAAC,IAAD,CAApB;AACH,GAFD,CA/HqB,CAmIrB;;;AACA,QAAMkD,wBAAwB,GAAG,CAACC,UAAD,EAAaC,KAAb,KAAuB;AACpD1D,IAAAA,gBAAgB,CAAC0D,KAAD,CAAhB,CADoD,CAEpD;;AACA,UAAMrC,KAAK,GAAGW,WAAW,EAAzB;AACA,QAAI2B,OAAO,GAAG,IAAIjC,IAAJ,CAASL,KAAT,CAAd;;AAEA,QAAIoC,UAAU,KAAK,MAAnB,EAA2B;AACvBE,MAAAA,OAAO,CAACC,OAAR,CAAgBvC,KAAK,CAACJ,OAAN,KAAkByC,KAAlC;AACH,KAFD,MAEO,IAAID,UAAU,KAAK,QAAnB,EAA6B;AAChCE,MAAAA,OAAO,CAACE,QAAR,CAAiBxC,KAAK,CAACH,QAAN,KAAmBwC,KAApC;AACH,KAFM,MAEA,IAAID,UAAU,KAAK,OAAnB,EAA4B;AAC/BE,MAAAA,OAAO,CAACG,WAAR,CAAoBzC,KAAK,CAACF,WAAN,KAAsBuC,KAA1C;AACH,KAZmD,CAcpD;;;AACA,QAAIzE,WAAW,KAAK,OAApB,EAA6B;AACzB;AACA0E,MAAAA,OAAO,GAAG,IAAIjC,IAAJ,CAASiC,OAAO,CAACxC,WAAR,EAAT,EAAgCwC,OAAO,CAACzC,QAAR,EAAhC,EAAoD,CAApD,CAAV;AACH;;AAEDhB,IAAAA,oBAAoB,CAACyD,OAAD,CAApB;AACAJ,IAAAA,sBAAsB;;AAEtB,QAAI1D,cAAJ,EAAoB;AAChBkE,MAAAA,mBAAmB,CAACL,KAAD,EAAQC,OAAR,CAAnB;AACH;AACJ,GA1BD,CApIqB,CAgKrB;;;AACA,QAAMK,sBAAsB,GAAIL,OAAD,IAAa;AACxC,QAAIA,OAAJ,EAAa;AACTzD,MAAAA,oBAAoB,CAACyD,OAAD,CAApB,CADS,CAET;;AACA,YAAMtC,KAAK,GAAGW,WAAW,EAAzB;AACA,YAAMiC,QAAQ,GAAGC,IAAI,CAACC,GAAL,CAAS9C,KAAK,GAAGsC,OAAjB,CAAjB;AACA,YAAMS,QAAQ,GAAGF,IAAI,CAACG,IAAL,CAAUJ,QAAQ,IAAI,OAAO,EAAP,GAAY,EAAZ,GAAiB,EAArB,CAAlB,CAAjB;AACAjE,MAAAA,gBAAgB,CAACoE,QAAD,CAAhB;;AAEA,UAAIvE,cAAJ,EAAoB;AAChBkE,QAAAA,mBAAmB,CAACK,QAAD,EAAWT,OAAX,CAAnB;AACH;AACJ;;AACDvD,IAAAA,WAAW,CAAC,IAAD,CAAX,CAbwC,CAarB;AACtB,GAdD,CAjKqB,CAiLrB;;;AACA,QAAM2D,mBAAmB,GAAG,gBAAOO,IAAP,EAAaC,YAAb,EAA8C;AAAA,QAAnBC,OAAmB,uEAAT,IAAS;AACtE1F,IAAAA,iBAAiB,CAAC,IAAD,CAAjB;;AACA,QAAI;AACA,YAAM2F,MAAM,GAAG,EAAf;AACA,YAAMpD,KAAK,GAAGW,WAAW,EAAzB;;AAEA,UAAIuC,YAAJ,EAAkB;AACd;AACA,YAAItF,WAAW,KAAK,KAApB,EAA2B;AACvBwF,UAAAA,MAAM,CAACjD,IAAP,GAAcD,cAAc,CAACgD,YAAD,CAA5B;AACH,SAFD,MAEO,IAAItF,WAAW,KAAK,OAApB,EAA6B;AAChC;AACA;AACA;AACA,cAAIsF,YAAY,IAAIC,OAApB,EAA6B;AACzB;AACAC,YAAAA,MAAM,CAACC,SAAP,GAAmBnD,cAAc,CAACgD,YAAD,CAAjC;AACAE,YAAAA,MAAM,CAACE,OAAP,GAAiBpD,cAAc,CAACiD,OAAD,CAA/B;AACH,WAJD,MAIO,IAAID,YAAJ,EAAkB;AACrB;AACA,kBAAMhL,YAAY,GAAG,IAAImI,IAAJ,CAAS6C,YAAY,CAACpD,WAAb,EAAT,EAAqCoD,YAAY,CAACrD,QAAb,EAArC,EAA8D,CAA9D,CAArB;AACA,kBAAM0D,WAAW,GAAG,IAAIlD,IAAJ,CAAS6C,YAAY,CAACpD,WAAb,EAAT,EAAqCoD,YAAY,CAACrD,QAAb,KAA0B,CAA/D,EAAkE,CAAlE,EAAqED,OAArE,EAApB;AACA,kBAAMzH,UAAU,GAAG,IAAIkI,IAAJ,CACf6C,YAAY,CAACpD,WAAb,EADe,EAEfoD,YAAY,CAACrD,QAAb,EAFe,EAGfgD,IAAI,CAACW,GAAL,CAASD,WAAT,EAAsBzF,SAAS,CAAC,CAAD,CAAT,CAAa8B,OAAb,EAAtB,CAHe,CAAnB;AAMAwD,YAAAA,MAAM,CAACC,SAAP,GAAmBnD,cAAc,CAAChI,YAAD,CAAjC;AACAkL,YAAAA,MAAM,CAACE,OAAP,GAAiBpD,cAAc,CAAC/H,UAAD,CAA/B;AACH,WAZM,MAYA;AACH;AACA,kBAAM,CAACsL,SAAD,EAAYC,OAAZ,IAAuBC,6BAA6B,EAA1D;AACAP,YAAAA,MAAM,CAACC,SAAP,GAAmBnD,cAAc,CAACuD,SAAD,CAAjC;AACAL,YAAAA,MAAM,CAACE,OAAP,GAAiBpD,cAAc,CAACwD,OAAD,CAA/B;AACH;AACJ,SA1BM,MA0BA;AACH;AACA,gBAAME,cAAc,GAAGT,OAAO,IAAID,YAAlC;AACAE,UAAAA,MAAM,CAACC,SAAP,GAAmBnD,cAAc,CAACgD,YAAD,CAAjC;AACAE,UAAAA,MAAM,CAACE,OAAP,GAAiBpD,cAAc,CAAC0D,cAAD,CAA/B;AACH;AACJ,OApCD,MAoCO;AACH;AACA,cAAMC,WAAW,GAAGvL,OAAO,CAAC0H,KAAD,EAAQiD,IAAR,CAA3B;;AACA,YAAIrF,WAAW,KAAK,KAApB,EAA2B;AACvBwF,UAAAA,MAAM,CAACjD,IAAP,GAAcD,cAAc,CAAC2D,WAAD,CAA5B;AACH,SAFD,MAEO,IAAIjG,WAAW,KAAK,OAApB,EAA6B;AAChCwF,UAAAA,MAAM,CAAC7C,KAAP,GAAe,CAACsD,WAAW,CAAChE,QAAZ,KAAyB,CAA1B,EAA6BiE,QAA7B,EAAf;AACAV,UAAAA,MAAM,CAAC9C,IAAP,GAAcuD,WAAW,CAAC/D,WAAZ,GAA0BgE,QAA1B,EAAd;AACH,SAHM,MAGA;AACH;AACA,gBAAMlB,QAAQ,GAAGC,IAAI,CAACC,GAAL,CAAShF,SAAS,CAAC,CAAD,CAAT,GAAeA,SAAS,CAAC,CAAD,CAAjC,CAAjB;AACA,gBAAMiF,QAAQ,GAAGF,IAAI,CAACG,IAAL,CAAUJ,QAAQ,IAAI,OAAO,EAAP,GAAY,EAAZ,GAAiB,EAArB,CAAlB,CAAjB;AAEA,gBAAMmB,gBAAgB,GAAGzL,OAAO,CAACwF,SAAS,CAAC,CAAD,CAAV,EAAemF,IAAf,CAAhC;AACA,gBAAMW,cAAc,GAAGtL,OAAO,CAACwF,SAAS,CAAC,CAAD,CAAV,EAAemF,IAAf,CAA9B;AAEAG,UAAAA,MAAM,CAACC,SAAP,GAAmBnD,cAAc,CAAC6D,gBAAD,CAAjC;AACAX,UAAAA,MAAM,CAACE,OAAP,GAAiBpD,cAAc,CAAC0D,cAAD,CAA/B;AACH;AACJ;;AAED,YAAMI,QAAQ,GAAG,MAAMpL,6BAA6B,CAACwK,MAAD,CAApD;AACA/F,MAAAA,cAAc,CAAC2G,QAAQ,CAAC9G,IAAV,CAAd,CA9DA,CAgEA;;AACA,YAAM+G,cAAc,GAAGD,QAAQ,CAAC9G,IAAT,CAAcgH,MAAd,CAAqB,CAACC,GAAD,EAAMC,KAAN,KAAgBD,GAAG,GAAGE,QAAQ,CAACD,KAAK,CAACE,aAAN,IAAuB,CAAxB,CAAnD,EAA+E,CAA/E,CAAvB;AACAjF,MAAAA,qBAAqB,CAAC4E,cAAD,CAArB,CAlEA,CAoEA;;AACA,YAAMM,YAAY,GAAGC,eAAe,CAACR,QAAQ,CAAC9G,IAAV,CAApC,CArEA,CAsEA;AAEA;;AACA,UAAIuH,sBAAJ;;AACA,UAAI7G,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAA7C,EAA0D;AACtD6G,QAAAA,sBAAsB,GAAGzD,mBAAmB,CAACuD,YAAY,CAAChD,WAAd,EAA2BgD,YAAY,CAAC/C,UAAxC,CAA5C;AACH,OAFD,MAEO,IAAI5D,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA3C,IAA0DA,WAAW,KAAK,WAA9E,EAA2F;AAC9F6G,QAAAA,sBAAsB,GAAGzD,mBAAmB,CAACuD,YAAY,CAACG,aAAd,EAA6BH,YAAY,CAAC7C,WAA1C,CAA5C;AACH,OAFM,MAEA;AACH+C,QAAAA,sBAAsB,GAAGzD,mBAAmB,CAACuD,YAAY,CAACG,aAAd,EAA6BH,YAAY,CAAC/C,UAAb,GAA0BG,SAAvD,CAA5C;AACH,OAhFD,CAiFA;;AACH,KAlFD,CAkFE,OAAOgD,KAAP,EAAc;AACZ;AAAoBC,MAAAA,OAAO,CAACD,KAAR,CAAc,GAAGE,KAAK,CAAE,6BAAF,EAA+B,iCAA/B,EAAkEF,KAAlE,CAAtB;AACvB,KApFD,SAoFU;AACNlH,MAAAA,iBAAiB,CAAC,KAAD,CAAjB;AACH;AACJ,GAzFD,CAlLqB,CA6QrB;;;AACA,QAAMqH,gBAAgB,GAAG,MAAM;AAC3B,UAAMC,QAAQ,GAAG,CAACvG,cAAlB;AACAC,IAAAA,iBAAiB,CAACsG,QAAD,CAAjB;;AAEA,QAAIA,QAAJ,EAAc;AACV;AACA5F,MAAAA,cAAc,CAACvB,WAAD,CAAd,CAFU,CAIV;;AACA,YAAM,CAACoH,mBAAD,EAAsBC,iBAAtB,EAAyClC,QAAzC,IAAqDY,6BAA6B,EAAxF,CALU,CAOV;;AACA9E,MAAAA,oBAAoB,CAACmG,mBAAD,CAApB;AACArG,MAAAA,gBAAgB,CAACoE,QAAD,CAAhB,CATU,CAWV;;AACAL,MAAAA,mBAAmB,CAACK,QAAD,EAAWiC,mBAAX,EAAgCC,iBAAhC,CAAnB;AACH,KAbD,MAaO;AACH;AACApG,MAAAA,oBAAoB,CAAC,IAAD,CAApB;AACAxB,MAAAA,cAAc,CAAC,EAAD,CAAd;AACH;AACJ,GAtBD;;AAwBA,QAAM6H,qBAAqB,GAAG,MAAM;AAChCzG,IAAAA,iBAAiB,CAAC,KAAD,CAAjB;AACAI,IAAAA,oBAAoB,CAAC,IAAD,CAApB;AACAF,IAAAA,gBAAgB,CAAC,CAAD,CAAhB;AACAtB,IAAAA,cAAc,CAAC,EAAD,CAAd;AACH,GALD;;AAOAtG,EAAAA,SAAS,CAAC,MAAM;AACZ,UAAMoO,SAAS,GAAG,YAAY;AAC1B5H,MAAAA,UAAU,CAAC,IAAD,CAAV,CAD0B,CACR;;AAClB,UAAI;AACA,YAAIyG,QAAJ;;AACA,YAAItG,OAAO,KAAK,CAAhB,EAAmB;AACf;AACA,gBAAM0F,MAAM,GAAG,EAAf,CAFe,CAIf;;AACA,gBAAMpD,KAAK,GAAGW,WAAW,EAAzB;AACA,gBAAMyE,aAAa,GAAGlF,cAAc,CAACF,KAAD,CAApC,CANe,CAM8B;;AAE7C,cAAIpC,WAAW,KAAK,KAApB,EAA2B;AACvB;AACA;AACA,gBAAIE,SAAS,CAAC,CAAD,CAAT,IAAgBA,SAAS,CAAC,CAAD,CAAzB,IAAgCtF,SAAS,CAACsF,SAAS,CAAC,CAAD,CAAV,EAAeA,SAAS,CAAC,CAAD,CAAxB,CAA7C,EAA2E;AACvEsF,cAAAA,MAAM,CAACjD,IAAP,GAAcD,cAAc,CAACpC,SAAS,CAAC,CAAD,CAAV,CAA5B;AACH,aAFD,MAEO;AACHsF,cAAAA,MAAM,CAACjD,IAAP,GAAciF,aAAd;AACH;AACJ,WARD,MAQO,IAAIxH,WAAW,KAAK,WAApB,EAAiC;AACpC;AACA,kBAAMyH,SAAS,GAAG/M,OAAO,CAAC0H,KAAD,EAAQ,CAAR,CAAzB;AACAoD,YAAAA,MAAM,CAACjD,IAAP,GAAcD,cAAc,CAACmF,SAAD,CAA5B;AACH,WAJM,MAIA,IAAIzH,WAAW,KAAK,MAAhB,IAA0BA,WAAW,KAAK,UAA9C,EAA0D;AAC7D;AACA,kBAAM0H,SAAS,GAAG,IAAIjF,IAAJ,CAASL,KAAT,CAAlB;AACAsF,YAAAA,SAAS,CAAC/C,OAAV,CAAkBvC,KAAK,CAACJ,OAAN,KAAkB,CAApC,EAH6D,CAGrB;;AACxCwD,YAAAA,MAAM,CAACC,SAAP,GAAmBnD,cAAc,CAACoF,SAAD,CAAjC;AACAlC,YAAAA,MAAM,CAACE,OAAP,GAAiBpD,cAAc,CAACF,KAAD,CAA/B;AACH,WANM,MAMA,IAAIpC,WAAW,KAAK,UAApB,EAAgC;AACnC;AACA,kBAAM2H,aAAa,GAAG,IAAIlF,IAAJ,CAASL,KAAT,CAAtB;AACAuF,YAAAA,aAAa,CAAChD,OAAd,CAAsBvC,KAAK,CAACJ,OAAN,KAAkB,EAAxC,EAHmC,CAGU;;AAC7C,kBAAM4F,WAAW,GAAG,IAAInF,IAAJ,CAASL,KAAT,CAApB;AACAwF,YAAAA,WAAW,CAACjD,OAAZ,CAAoBvC,KAAK,CAACJ,OAAN,KAAkB,CAAtC,EALmC,CAKO;;AAC1CwD,YAAAA,MAAM,CAACC,SAAP,GAAmBnD,cAAc,CAACqF,aAAD,CAAjC;AACAnC,YAAAA,MAAM,CAACE,OAAP,GAAiBpD,cAAc,CAACsF,WAAD,CAA/B;AACH,WARM,MAQA,IAAI5H,WAAW,KAAK,QAAhB,IAA4BA,WAAW,KAAK,YAAhD,EAA8D;AACjE;AACA,kBAAM6H,mBAAmB,GAAGvN,YAAY,CAAC8H,KAAD,CAAxC;AACAoD,YAAAA,MAAM,CAACC,SAAP,GAAmBnD,cAAc,CAACuF,mBAAD,CAAjC;AACArC,YAAAA,MAAM,CAACE,OAAP,GAAiBpD,cAAc,CAACF,KAAD,CAA/B;AACH,WALM,MAKA,IAAIpC,WAAW,KAAK,OAApB,EAA6B;AAEhC;AACA,gBAAIE,SAAS,CAAC,CAAD,CAAT,IAAgBA,SAAS,CAAC,CAAD,CAA7B,EAAkC;AAC9BsF,cAAAA,MAAM,CAACC,SAAP,GAAmBnD,cAAc,CAACpC,SAAS,CAAC,CAAD,CAAV,CAAjC;AACAsF,cAAAA,MAAM,CAACE,OAAP,GAAiBpD,cAAc,CAACpC,SAAS,CAAC,CAAD,CAAV,CAA/B;AACH,aAHD,MAGO;AACH;AACAsF,cAAAA,MAAM,CAAC7C,KAAP,GAAe,CAACP,KAAK,CAACH,QAAN,KAAmB,CAApB,EAAuBiE,QAAvB,EAAf;AACAV,cAAAA,MAAM,CAAC9C,IAAP,GAAcN,KAAK,CAACF,WAAN,GAAoBgE,QAApB,EAAd;AACH;AACJ,WAXM,MAWA,IAAIlG,WAAW,KAAK,MAApB,EAA4B;AAC/B;AACA,gBAAIE,SAAS,CAAC,CAAD,CAAT,IAAgBA,SAAS,CAAC,CAAD,CAA7B,EAAkC;AAC9B;AACA;AACA,kBACIA,SAAS,CAAC,CAAD,CAAT,CAAa+B,QAAb,OAA4B/B,SAAS,CAAC,CAAD,CAAT,CAAa+B,QAAb,EAA5B,IACA/B,SAAS,CAAC,CAAD,CAAT,CAAagC,WAAb,OAA+BhC,SAAS,CAAC,CAAD,CAAT,CAAagC,WAAb,EAFnC,EAGE;AACEsD,gBAAAA,MAAM,CAACC,SAAP,GAAmBnD,cAAc,CAACpC,SAAS,CAAC,CAAD,CAAV,CAAjC;AACAsF,gBAAAA,MAAM,CAACE,OAAP,GAAiBpD,cAAc,CAACpC,SAAS,CAAC,CAAD,CAAV,CAA/B;AACH,eAND,MAMO;AACH;AACA;AACA,sBAAM4H,mBAAmB,GAAGvN,UAAU,CAAC2F,SAAS,CAAC,CAAD,CAAV,CAAtC;AACAsF,gBAAAA,MAAM,CAACC,SAAP,GAAmBnD,cAAc,CAACpC,SAAS,CAAC,CAAD,CAAV,CAAjC;AACAsF,gBAAAA,MAAM,CAACE,OAAP,GAAiBpD,cAAc,CAACwF,mBAAD,CAA/B;AACAd,gBAAAA,OAAO,CAACe,IAAR,CACI,sGADJ;AAGH;AACJ,aAnBD,MAmBO;AACH;AACA,oBAAMC,eAAe,GAAG,IAAIvF,IAAJ,CAASL,KAAK,CAACF,WAAN,EAAT,EAA8BE,KAAK,CAACH,QAAN,EAA9B,EAAgD,CAAhD,CAAxB;AACAuD,cAAAA,MAAM,CAACC,SAAP,GAAmBnD,cAAc,CAAC0F,eAAD,CAAjC;AACAxC,cAAAA,MAAM,CAACE,OAAP,GAAiB8B,aAAjB;AACH;AACJ,WA7Ec,CA+Ef;AACA;;;AACApB,UAAAA,QAAQ,GAAG,MAAMpL,6BAA6B,CAACwK,MAAD,CAA9C;AACAjG,UAAAA,OAAO,CAAC6G,QAAQ,CAAC9G,IAAV,CAAP;;AACA,cAAI8G,QAAQ,CAAC6B,kBAAb,EAAiC;AAC7BrM,YAAAA,uBAAuB,CAACwK,QAAQ,CAAC6B,kBAAV,CAAvB;AACH;AACJ;AACJ,OAzFD,CAyFE,OAAOlB,KAAP,EAAc;AACZ;AAAoBC,QAAAA,OAAO,CAACD,KAAR,CAAc,GAAGE,KAAK,CAAE,6BAAF,EAA+B,sBAA/B,EAAuDF,KAAvD,CAAtB,EADR,CAEZ;;AACA,YAAIjH,OAAO,KAAK,CAAhB,EAAmB;AACfC,UAAAA,UAAU,CAAC,CAAD,CAAV;AACH;AACJ,OA/FD,SA+FU;AACN;AACAmI,QAAAA,UAAU,CAAC,MAAM;AACbvI,UAAAA,UAAU,CAAC,KAAD,CAAV;AACH,SAFS,EAEP,GAFO,CAAV;AAGH;AACJ,KAvGD;;AAyGA4H,IAAAA,SAAS;AACZ,GA3GQ,EA2GN,CAACzH,OAAD,EAAUE,WAAV,EAAuBE,SAAvB,CA3GM,CAAT,CA7SqB,CAwZkB;;AAEvC,QAAMiI,kBAAkB,GAAIC,SAAD,IAAe;AACtC;AACA,QAAI7E,SAAJ;;AACA,QAAI6E,SAAS,KAAK,WAAlB,EAA+B;AAC3B;AACA,UAAIpI,WAAW,KAAK,KAApB,EAA2B;AAAA;;AACvBuD,QAAAA,SAAS,GAAG;AACR8E,UAAAA,kBAAkB,EAAE,WADZ;AAER1E,UAAAA,WAAW,EAAE2E,MAAM,CAAC3E,WAFZ;AAGRC,UAAAA,UAAU,EAAE0E,MAAM,CAAC1E,UAHX;AAIRC,UAAAA,cAAc,EAAEyE,MAAM,CAACxB,aAJf;AAI8B;AACtChD,UAAAA,WAAW,EAAEwE,MAAM,CAACxE,WALZ;AAKyB;AACjCyE,UAAAA,UAAU,YAAEjJ,IAAI,CAAC,CAAD,CAAN,2CAAE,OAASiJ,UANb;AAORC,UAAAA,YAAY,EAAE;AAPN,SAAZ;AASH,OAVD,MAUO,IAAIxI,WAAW,KAAK,OAApB,EAA6B;AAAA;;AAChCuD,QAAAA,SAAS,GAAG;AACR8E,UAAAA,kBAAkB,EAAE,WADZ;AAER1E,UAAAA,WAAW,EAAE2E,MAAM,CAAC3E,WAFZ;AAEyB;AACjCC,UAAAA,UAAU,EAAE0E,MAAM,CAAC1E,UAHX;AAGuB;AAC/BC,UAAAA,cAAc,EAAEyE,MAAM,CAACxB,aAJf;AAKRhD,UAAAA,WAAW,EAAEwE,MAAM,CAACxE,WALZ;AAMRyE,UAAAA,UAAU,aAAEjJ,IAAI,CAAC,CAAD,CAAN,4CAAE,QAASiJ,UANb;AAORC,UAAAA,YAAY,EAAE;AAPN,SAAZ;AASH,OAVM,MAUA;AAAA;;AACH;AACAjF,QAAAA,SAAS,GAAG;AACR8E,UAAAA,kBAAkB,EAAE,WADZ;AAER1E,UAAAA,WAAW,EAAE2E,MAAM,CAAC3E,WAFZ;AAEyB;AACjCC,UAAAA,UAAU,EAAE0E,MAAM,CAAC1E,UAHX;AAGuB;AAC/BC,UAAAA,cAAc,EAAEyE,MAAM,CAACxB,aAJf;AAI8B;AACtChD,UAAAA,WAAW,EAAEwE,MAAM,CAACxE,WALZ;AAKyB;AACjC2E,UAAAA,SAAS,EAAEC,UAAU,CAACpF,IANd;AAMoB;AAC5BqF,UAAAA,YAAY,EAAEC,WAPN;AAOmB;AAC3BL,UAAAA,UAAU,aAAEjJ,IAAI,CAAC,CAAD,CAAN,4CAAE,QAASiJ,UARb;AASRC,UAAAA,YAAY,EAAE;AATN,SAAZ;AAWH;AACJ,KApCD,MAoCO;AACH;AACA,YAAMhC,KAAK,GAAGlH,IAAI,CAACuJ,IAAL,CAAWrC,KAAD,IAAWA,KAAK,CAAC6B,kBAAN,KAA6BD,SAAlD,CAAd;;AAEA,UAAI5B,KAAJ,EAAW;AACP,YAAIxG,WAAW,KAAK,KAApB,EAA2B;AACvBuD,UAAAA,SAAS,GAAG,EACR,GAAGiD,KADK;AAERgC,YAAAA,YAAY,EAAE;AAFN,WAAZ;AAIH,SALD,MAKO,IAAIxI,WAAW,KAAK,OAApB,EAA6B;AAChCuD,UAAAA,SAAS,GAAG,EACR,GAAGiD,KADK;AAERgC,YAAAA,YAAY,EAAE;AAFN,WAAZ;AAIH,SALM,MAKA;AACH;AACA,gBAAMM,QAAQ,GAAGpF,UAAU,CAAC8C,KAAK,CAAC5C,UAAP,CAAV,GAA+BgF,WAAhD;AACArF,UAAAA,SAAS,GAAG,EACR,GAAGiD,KADK;AAERiC,YAAAA,SAAS,EAAEK,QAFH;AAEa;AACrBH,YAAAA,YAAY,EAAEC,WAHN;AAGmB;AAC3BJ,YAAAA,YAAY,EAAE;AAJN,WAAZ;AAMH;AACJ;AACJ;;AAED,QAAIjF,SAAJ,EAAe;AACX9C,MAAAA,gBAAgB,CAAC2H,SAAD,CAAhB;AACAzH,MAAAA,oBAAoB,CAAC4C,SAAD,CAApB;AACAhD,MAAAA,sBAAsB,CAAC,IAAD,CAAtB;AACH;AACJ,GAxED;;AA0EA,MAAIb,OAAJ,EAAa;AACT,wBACI,QAAC,QAAD;AACI,MAAA,EAAE,EAAE;AACAqJ,QAAAA,KAAK,EAAE,MADP;AAEAC,QAAAA,MAAM,EAAGC,KAAD,IAAWA,KAAK,CAACD,MAAN,CAAaE,MAAb,GAAsB,CAFzC;AAGAC,QAAAA,eAAe,EAAE;AAHjB,OADR;AAMI,MAAA,IAAI,EAAEzJ,OANV;AAAA,6BAQI,QAAC,GAAD;AACI,QAAA,EAAE,EAAE;AACA0J,UAAAA,OAAO,EAAE,MADT;AAEAC,UAAAA,aAAa,EAAE,QAFf;AAGAC,UAAAA,UAAU,EAAE,QAHZ;AAIAC,UAAAA,GAAG,EAAE;AAJL,SADR;AAAA,gCAQI,QAAC,gBAAD;AACI,UAAA,IAAI,EAAE,EADV;AAEI,UAAA,SAAS,EAAE,CAFf;AAGI,UAAA,EAAE,EAAE;AACAR,YAAAA,KAAK,EAAE;AADP;AAHR;AAAA;AAAA;AAAA;AAAA,gBARJ,eAeI,QAAC,UAAD;AACI,UAAA,OAAO,EAAC,IADZ;AAEI,UAAA,EAAE,EAAE;AACAA,YAAAA,KAAK,EAAE,SADP;AAEAS,YAAAA,UAAU,EAAE;AAFZ,WAFR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAfJ;AAAA;AAAA;AAAA;AAAA;AAAA;AARJ;AAAA;AAAA;AAAA;AAAA,YADJ;AAoCH,GAzgBoB,CA2gBrB;;;AACA,QAAMC,YAAY,GAAIhF,KAAD,IAAW;AAC5B,QAAIA,KAAK,KAAKiF,SAAV,IAAuBjF,KAAK,KAAK,IAArC,EAA2C,OAAO,SAAP;AAC3C,WAAQ,MAAKf,UAAU,CAACe,KAAD,CAAV,CAAkBvB,cAAlB,CAAiC,OAAjC,EAA0C;AACnDyG,MAAAA,qBAAqB,EAAE,CAD4B;AAEnDC,MAAAA,qBAAqB,EAAE;AAF4B,KAA1C,CAGV,EAHH;AAIH,GAND;;AAQA,QAAMC,UAAU,GAAIC,UAAD,IAAgB;AAC/B,UAAMvH,IAAI,GAAG,IAAIE,IAAJ,CAASqH,UAAT,CAAb;AACA,UAAMhH,GAAG,GAAGP,IAAI,CAACP,OAAL,GAAekE,QAAf,GAA0BrD,QAA1B,CAAmC,CAAnC,EAAsC,GAAtC,CAAZ;AACA,UAAMF,KAAK,GAAG,CAACJ,IAAI,CAACN,QAAL,KAAkB,CAAnB,EAAsBiE,QAAtB,GAAiCrD,QAAjC,CAA0C,CAA1C,EAA6C,GAA7C,CAAd;AACA,UAAMH,IAAI,GAAGH,IAAI,CAACL,WAAL,EAAb;AACA,WAAQ,GAAEY,GAAI,IAAGH,KAAM,IAAGD,IAAK,EAA/B;AACH,GAND,CAphBqB,CA4hBrB;;;AACA,QAAMqH,sBAAsB,GAAG,MAAM;AACjC;AACA,QAAI,CAAC7J,SAAS,CAAC,CAAD,CAAV,IAAiB,CAACA,SAAS,CAAC,CAAD,CAA/B,EAAoC,OAAO;AAAE0I,MAAAA,WAAW,EAAE,CAAf;AAAkB7E,MAAAA,SAAS,EAAE;AAA7B,KAAP,CAFH,CAIjC;;AACA,UAAMiG,aAAa,GAAIzH,IAAD,IAAU;AAC5B,YAAMC,CAAC,GAAG,IAAIC,IAAJ,CAASF,IAAT,CAAV;AACAC,MAAAA,CAAC,CAACyH,QAAF,CAAW,CAAX,EAAc,CAAd,EAAiB,CAAjB,EAAoB,CAApB;AACA,aAAOzH,CAAP;AACH,KAJD,CALiC,CAWjC;;;AACA,UAAM0H,gBAAgB,GAAG,CAACC,KAAD,EAAQC,GAAR,KAAgB;AACrC,YAAMpF,QAAQ,GAAGC,IAAI,CAACC,GAAL,CAASkF,GAAG,GAAGD,KAAf,CAAjB;AACA,aAAOlF,IAAI,CAACG,IAAL,CAAUJ,QAAQ,IAAI,OAAO,EAAP,GAAY,EAAZ,GAAiB,EAArB,CAAlB,IAA8C,CAArD,CAFqC,CAEmB;AAC3D,KAHD;;AAKA,UAAM5C,KAAK,GAAGW,WAAW,EAAzB;AACA,UAAMsH,eAAe,GAAGL,aAAa,CAAC5H,KAAD,CAArC,CAlBiC,CAoBjC;;AACA,QAAIkI,SAAJ,EAAe/E,OAAf,EAAwBgF,WAAxB;;AACA,QAAIvK,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAA7C,EAA0D;AACtD;AACA,aAAO;AAAE4I,QAAAA,WAAW,EAAE,CAAf;AAAkB7E,QAAAA,SAAS,EAAE;AAA7B,OAAP;AACH,KAHD,MAGO,IAAI/D,WAAW,KAAK,MAAhB,IAA0BA,WAAW,KAAK,UAA9C,EAA0D;AAC7D;AACAsK,MAAAA,SAAS,GAAGN,aAAa,CAAC,IAAIvH,IAAJ,CAASL,KAAT,CAAD,CAAzB;AACAkI,MAAAA,SAAS,CAAC3F,OAAV,CAAkB2F,SAAS,CAACtI,OAAV,KAAsB,CAAxC,EAH6D,CAGjB;;AAC5CuD,MAAAA,OAAO,GAAGyE,aAAa,CAAC5H,KAAD,CAAvB;AACAmI,MAAAA,WAAW,GAAGF,eAAd;AACH,KANM,MAMA,IAAIrK,WAAW,KAAK,UAApB,EAAgC;AACnC;AACAsK,MAAAA,SAAS,GAAGN,aAAa,CAAC,IAAIvH,IAAJ,CAASL,KAAT,CAAD,CAAzB;AACAkI,MAAAA,SAAS,CAAC3F,OAAV,CAAkB2F,SAAS,CAACtI,OAAV,KAAsB,EAAxC;AACAuD,MAAAA,OAAO,GAAGyE,aAAa,CAAC,IAAIvH,IAAJ,CAASL,KAAT,CAAD,CAAvB;AACAmD,MAAAA,OAAO,CAACZ,OAAR,CAAgBY,OAAO,CAACvD,OAAR,KAAoB,CAApC;AACAuI,MAAAA,WAAW,GAAGhF,OAAd,CANmC,CAMZ;AAC1B,KAPM,MAOA,IAAIvF,WAAW,KAAK,QAAhB,IAA4BA,WAAW,KAAK,YAAhD,EAA8D;AACjE;AACAsK,MAAAA,SAAS,GAAGN,aAAa,CAAC1P,YAAY,CAAC8H,KAAD,CAAb,CAAzB;AACAmD,MAAAA,OAAO,GAAGyE,aAAa,CAAC5H,KAAD,CAAvB;AACAmI,MAAAA,WAAW,GAAGF,eAAd;AACH,KALM,MAKA,IAAIrK,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA3C,IAA0DA,WAAW,KAAK,WAA9E,EAA2F;AAC9F;AACAsK,MAAAA,SAAS,GAAGN,aAAa,CAAC1P,YAAY,CAAC8H,KAAD,CAAb,CAAzB;AACAmD,MAAAA,OAAO,GAAGyE,aAAa,CAAC5H,KAAD,CAAvB;AACAmI,MAAAA,WAAW,GAAGF,eAAd;AACH,KALM,MAKA,IAAIrK,WAAW,KAAK,WAApB,EAAiC;AACpC;AACA,YAAMwK,SAAS,GAAG7P,SAAS,CAACyH,KAAD,EAAQ,CAAR,CAA3B;AACAkI,MAAAA,SAAS,GAAGN,aAAa,CAAC1P,YAAY,CAACkQ,SAAD,CAAb,CAAzB;AACAjF,MAAAA,OAAO,GAAGyE,aAAa,CAACzP,UAAU,CAACiQ,SAAD,CAAX,CAAvB;AACAD,MAAAA,WAAW,GAAGhF,OAAd,CALoC,CAKb;AAC1B,KANM,MAMA;AACH;AACA+E,MAAAA,SAAS,GAAGN,aAAa,CAAC9J,SAAS,CAAC,CAAD,CAAV,CAAzB;AACAqF,MAAAA,OAAO,GAAGyE,aAAa,CAAC9J,SAAS,CAAC,CAAD,CAAV,CAAvB;AACAqK,MAAAA,WAAW,GAAGF,eAAd;AACH,KA3DgC,CA6DjC;;;AACA,UAAMtG,SAAS,GAAGmG,gBAAgB,CAACI,SAAD,EAAY/E,OAAZ,CAAlC;AACA,QAAIqD,WAAJ;;AACA,QAAI2B,WAAW,GAAGhF,OAAlB,EAA2B;AACvB;AACAqD,MAAAA,WAAW,GAAG7E,SAAd;AACH,KAHD,MAGO,IAAIwG,WAAW,GAAGD,SAAlB,EAA6B;AAChC;AACA1B,MAAAA,WAAW,GAAG,CAAd;AACH,KAHM,MAGA;AACH;AACAA,MAAAA,WAAW,GAAGsB,gBAAgB,CAACI,SAAD,EAAYC,WAAZ,CAA9B;AACH;;AAED,WAAO;AAAE3B,MAAAA,WAAF;AAAe7E,MAAAA;AAAf,KAAP;AACH,GA5ED;;AA8EA,QAAM;AAAE6E,IAAAA,WAAF;AAAe7E,IAAAA;AAAf,MAA6BgG,sBAAsB,EAAzD,CA3mBqB,CA6mBrB;;AACA,QAAMnD,eAAe,GAAG,MAAM;AAC1B,QAAI,CAACtH,IAAD,IAASA,IAAI,CAACmL,MAAL,KAAgB,CAA7B,EACI,OAAO;AACH9G,MAAAA,WAAW,EAAE,CADV;AAEHC,MAAAA,UAAU,EAAE,CAFT;AAGHE,MAAAA,WAAW,EAAE,CAHV;AAIHgD,MAAAA,aAAa,EAAE,CAJZ;AAKH4D,MAAAA,oBAAoB,EAAE,CALnB;AAMHhE,MAAAA,aAAa,EAAE;AANZ,KAAP;AASJ,WAAOpH,IAAI,CAACgH,MAAL,CACH,CAACC,GAAD,EAAMC,KAAN,KAAgB;AACZ,aAAO;AACH7C,QAAAA,WAAW,EAAE4C,GAAG,CAAC5C,WAAJ,GAAkBD,UAAU,CAAC8C,KAAK,CAAC7C,WAAN,IAAqB,CAAtB,CADtC;AAEHC,QAAAA,UAAU,EAAE2C,GAAG,CAAC3C,UAAJ,GAAiBF,UAAU,CAAC8C,KAAK,CAAC5C,UAAN,IAAoB,CAArB,CAFpC;AAGHE,QAAAA,WAAW,EAAEyC,GAAG,CAACzC,WAAJ,GAAkBJ,UAAU,CAAC8C,KAAK,CAAC1C,WAAN,IAAqB,CAAtB,CAHtC;AAIHgD,QAAAA,aAAa,EAAEP,GAAG,CAACO,aAAJ,GAAoBpD,UAAU,CAAC8C,KAAK,CAAC3C,cAAN,IAAwB,CAAzB,CAJ1C;AAKH6G,QAAAA,oBAAoB,EAAEhH,UAAU,CAAC8C,KAAK,CAACkE,oBAAN,IAA8B,CAA/B,CAL7B;AAMHhE,QAAAA,aAAa,EAAEH,GAAG,CAACG,aAAJ,GAAoBD,QAAQ,CAACD,KAAK,CAACE,aAAN,IAAuB,CAAxB;AANxC,OAAP;AAQH,KAVE,EAWH;AACI/C,MAAAA,WAAW,EAAE,CADjB;AAEIC,MAAAA,UAAU,EAAE,CAFhB;AAGIE,MAAAA,WAAW,EAAE,CAHjB;AAIIgD,MAAAA,aAAa,EAAE,CAJnB;AAKI4D,MAAAA,oBAAoB,EAAE,CAL1B;AAMIhE,MAAAA,aAAa,EAAE;AANnB,KAXG,CAAP;AAoBH,GA/BD,CA9mBqB,CA+oBrB;;;AACA,QAAM4B,MAAM,GAAG1B,eAAe,EAA9B,CAhpBqB,CAkpBrB;;AACA,QAAM8B,UAAU,GAAG;AACfpF,IAAAA,IAAI,EAAEgF,MAAM,CAAC1E,UAAP,GAAoBG,SADX;AAEf4G,IAAAA,SAAS,EAAErC,MAAM,CAAC1E,UAAP,GAAoBG,SAApB,GAAgCuE,MAAM,CAACxB;AAFnC,GAAnB,CAnpBqB,CAwpBrB;;AACA,QAAM8D,eAAe,GACjB5K,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAAzC,GACMoD,mBAAmB,CAACkF,MAAM,CAAC3E,WAAR,EAAqB2E,MAAM,CAAC1E,UAA5B,CADzB,CACiE;AADjE,IAEM5D,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA3C,IAA0DA,WAAW,KAAK,WAA1E,GACAoD,mBAAmB,CAACkF,MAAM,CAACxB,aAAR,EAAuBwB,MAAM,CAACxE,WAA9B,CADnB,CAC8D;AAD9D,IAEAV,mBAAmB,CAACkF,MAAM,CAACxB,aAAR,EAAuBwB,MAAM,CAAC1E,UAAP,GAAoBG,SAA3C,CAL7B,CAzpBqB,CA8pB+D;;AAEpF,QAAM8G,kBAAkB,GAAGvR,MAAM,CAACG,MAAD,CAAN,CAAe;AAAA,QAAC;AAAEwP,MAAAA;AAAF,KAAD;AAAA,WAAgB;AACtD6B,MAAAA,WAAW,EAAE,SADyC;AAEtD/B,MAAAA,KAAK,EAAE,SAF+C;AAGtDgC,MAAAA,QAAQ,EAAE,OAH4C;AAItD3B,MAAAA,OAAO,EAAE,MAJ6C;AAKtD4B,MAAAA,cAAc,EAAE,eALsC;AAMtD,iBAAW;AACPF,QAAAA,WAAW,EAAE,SADN;AAEP3B,QAAAA,eAAe,EAAE;AAFV,OAN2C;AAUtD8B,MAAAA,SAAS,EAAE,4BAV2C;AAWtDC,MAAAA,YAAY,EAAE,KAXwC;AAYtDC,MAAAA,OAAO,EAAE,UAZ6C;AAatDC,MAAAA,UAAU,EAAE;AAb0C,KAAhB;AAAA,GAAf,CAA3B;AAgBA,QAAMC,qBAAqB,GAAG/R,MAAM,CAACF,GAAD,CAAN,CAAY;AAAA,QAAC;AAAE6P,MAAAA;AAAF,KAAD;AAAA,WAAgB;AACtDG,MAAAA,OAAO,EAAE,MAD6C;AAEtDE,MAAAA,UAAU,EAAE,QAF0C;AAGtDC,MAAAA,GAAG,EAAE,MAHiD;AAItD4B,MAAAA,OAAO,EAAE,WAJ6C;AAKtDhC,MAAAA,eAAe,EAAE,SALqC;AAMtD+B,MAAAA,YAAY,EAAE,MANwC;AAOtDD,MAAAA,SAAS,EAAE,4BAP2C;AAQtDK,MAAAA,YAAY,EAAE,MARwC;AAStDC,MAAAA,QAAQ,EAAE,UAT4C;AAUtDP,MAAAA,cAAc,EAAE,QAVsC;AAU5B;AAC1BQ,MAAAA,QAAQ,EAAE,MAX4C;AAYtDC,MAAAA,QAAQ,EAAE,SAZ4C;AAatDzC,MAAAA,MAAM,EAAE;AAb8C,KAAhB;AAAA,GAAZ,CAA9B;AAgBA,QAAM0C,aAAa,GAAGpS,MAAM,CAACG,MAAD,CAAN,CAAe;AAAA,QAAC;AAAEwP,MAAAA,KAAF;AAAS0C,MAAAA;AAAT,KAAD;AAAA,WAAwB;AACzDxC,MAAAA,eAAe,EAAEwC,MAAM,GAAG,SAAH,GAAe,aADmB;AAEzD5C,MAAAA,KAAK,EAAE4C,MAAM,GAAG,OAAH,GAAa,SAF+B;AAGzDb,MAAAA,WAAW,EAAE,SAH4C;AAIzD,iBAAW;AACP3B,QAAAA,eAAe,EAAEwC,MAAM,GAAG,SAAH,GAAe,0BAD/B;AAEPb,QAAAA,WAAW,EAAE;AAFN,OAJ8C;AAQzDtB,MAAAA,UAAU,EAAEmC,MAAM,GAAG,MAAH,GAAY,QAR2B;AASzDT,MAAAA,YAAY,EAAE,KAT2C;AAUzDC,MAAAA,OAAO,EAAE,UAVgD;AAWzDC,MAAAA,UAAU,EAAE,eAX6C;AAYzDH,MAAAA,SAAS,EAAEU,MAAM,GAAG,mCAAH,GAAyC;AAZD,KAAxB;AAAA,GAAf,CAAtB;AAeA,QAAMC,qBAAqB,GAAGtS,MAAM,CAACG,MAAD,CAAN,CAAe;AAAA,QAAC;AAAEwP,MAAAA;AAAF,KAAD;AAAA,WAAgB;AACzDkC,MAAAA,OAAO,EAAE,UADgD;AAEzDU,MAAAA,QAAQ,EAAE,MAF+C;AAGzDC,MAAAA,aAAa,EAAE,MAH0C;AAIzD,iBAAW;AACP3C,QAAAA,eAAe,EAAE;AADV,OAJ8C;AAOzD,sCAAgC;AAC5BA,QAAAA,eAAe,EAAE,SADW;AAE5B,mBAAW;AACPA,UAAAA,eAAe,EAAE;AADV;AAFiB;AAPyB,KAAhB;AAAA,GAAf,CAA9B;AAeA,QAAM4C,kBAAkB,GAAGzS,MAAM,CAACI,SAAD,CAAN,CAAkB;AAAA,QAAC;AAAEuP,MAAAA;AAAF,KAAD;AAAA,WAAgB;AACzD,kCAA4B;AACxBkC,QAAAA,OAAO,EAAE,UADe;AAExBU,QAAAA,QAAQ,EAAE,MAFc;AAGxB,sBAAc;AACVf,UAAAA,WAAW,EAAE,SADH;AAEVkB,UAAAA,WAAW,EAAE;AAFH,SAHU;AAOxB,4BAAoB;AAChBlB,UAAAA,WAAW,EAAE;AADG,SAPI;AAUxB,kCAA0B;AACtBA,UAAAA,WAAW,EAAE;AADS;AAVF;AAD6B,KAAhB;AAAA,GAAlB,CAA3B,CA9tBqB,CA+uBrB;;AACA,QAAMmB,mBAAmB,GAAG3S,MAAM,CAACF,GAAD,CAAN,CAAY;AAAA,QAAC;AAAE6P,MAAAA;AAAF,KAAD;AAAA,WAAgB;AACpDsC,MAAAA,QAAQ,EAAE,UAD0C;AAEpDW,MAAAA,GAAG,EAAE,MAF+C;AAGpDC,MAAAA,IAAI,EAAE,CAH8C;AAIpDhD,MAAAA,eAAe,EAAE,SAJmC;AAKpD8B,MAAAA,SAAS,EAAE,6BALyC;AAMpDC,MAAAA,YAAY,EAAE,KANsC;AAOpDC,MAAAA,OAAO,EAAE,OAP2C;AAQpDiB,MAAAA,SAAS,EAAE,KARyC;AASpDpD,MAAAA,MAAM,EAAE,IAT4C;AAUpD+B,MAAAA,QAAQ,EAAE,OAV0C;AAWpDU,MAAAA,QAAQ,EAAE;AAX0C,KAAhB;AAAA,GAAZ,CAA5B;AAcA,QAAMY,uBAAuB,GAAG/S,MAAM,CAACF,GAAD,CAAN,CAAY;AAAA,QAAC;AAAE6P,MAAAA;AAAF,KAAD;AAAA,WAAgB;AACxDsC,MAAAA,QAAQ,EAAE,UAD8C;AAExDW,MAAAA,GAAG,EAAE,MAFmD;AAGxDC,MAAAA,IAAI,EAAE,KAHkD;AAIxDG,MAAAA,SAAS,EAAE,kBAJ6C;AAKxDnD,MAAAA,eAAe,EAAE,SALuC;AAMxD8B,MAAAA,SAAS,EAAE,6BAN6C;AAOxDC,MAAAA,YAAY,EAAE,KAP0C;AAQxDC,MAAAA,OAAO,EAAE,MAR+C;AASxDiB,MAAAA,SAAS,EAAE,KAT6C;AAUxDpD,MAAAA,MAAM,EAAE,IAVgD;AAWxDuD,MAAAA,KAAK,EAAE,OAXiD;AAYxDd,MAAAA,QAAQ,EAAE;AAZ8C,KAAhB;AAAA,GAAZ,CAAhC;AAeA,QAAMe,cAAc,GAAGlT,MAAM,CAACF,GAAD,CAAN,CAAY;AAAA,QAAC;AAAE6P,MAAAA;AAAF,KAAD;AAAA,WAAgB;AAC/CkC,MAAAA,OAAO,EAAE,WADsC;AAE/CsB,MAAAA,MAAM,EAAE,SAFuC;AAG/C,iBAAW;AACPtD,QAAAA,eAAe,EAAE;AADV,OAHoC;AAM/CiC,MAAAA,UAAU,EAAE;AANmC,KAAhB;AAAA,GAAZ,CAAvB,CA7wBqB,CAsxBrB;;AACA,QAAMsB,cAAc,GAAGpT,MAAM,CAACF,GAAD,CAAN,CAAY;AAAA,QAAC;AAAE6P,MAAAA;AAAF,KAAD;AAAA,WAAgB;AAC/CG,MAAAA,OAAO,EAAE,MADsC;AAE/CE,MAAAA,UAAU,EAAE,QAFmC;AAG/C6B,MAAAA,OAAO,EAAE,UAHsC;AAI/CD,MAAAA,YAAY,EAAE,KAJiC;AAK/C/B,MAAAA,eAAe,EAAE,0BAL8B;AAM/C0C,MAAAA,QAAQ,EAAE,SANqC;AAO/C9C,MAAAA,KAAK,EAAE,SAPwC;AAQ/C4D,MAAAA,UAAU,EAAE,MARmC;AAS/CnD,MAAAA,UAAU,EAAE,GATmC;AAU/CoD,MAAAA,MAAM,EAAE,mCAVuC;AAW/C3B,MAAAA,SAAS,EAAE,4BAXoC;AAY/C,4BAAsB;AAClBY,QAAAA,QAAQ,EAAE,QADQ;AAElBgB,QAAAA,WAAW,EAAE;AAFK;AAZyB,KAAhB;AAAA,GAAZ,CAAvB,CAvxBqB,CAyyBrB;;AACA,QAAMC,iBAAiB,GAAG,MAAM;AAC5B,QAAI,CAAClM,cAAD,IAAmB,CAACI,iBAAxB,EAA2C,OAAO,EAAP,CADf,CAG5B;;AACA,UAAM+L,iBAAiB,GAAG,MAAM;AAC5B,UAAI/M,WAAW,KAAK,KAApB,EAA2B;AACvB,eAAOgB,iBAAP;AACH,OAFD,MAEO,IAAIhB,WAAW,KAAK,MAAhB,IAA0BA,WAAW,KAAK,UAA9C,EAA0D;AAC7D;AACA,cAAMgF,QAAQ,GAAG9E,SAAS,CAAC,CAAD,CAAT,CAAa8M,OAAb,KAAyB9M,SAAS,CAAC,CAAD,CAAT,CAAa8M,OAAb,EAA1C;AACA,cAAMhH,cAAc,GAAG,IAAIvD,IAAJ,CAASzB,iBAAiB,CAACgM,OAAlB,KAA8BhI,QAAvC,CAAvB;AACA,eAAOgB,cAAP;AACH,OALM,MAKA,IAAIhG,WAAW,KAAK,UAApB,EAAgC;AACnC;AACA,cAAMgG,cAAc,GAAG,IAAIvD,IAAJ,CAASL,KAAT,CAAvB;AACA4D,QAAAA,cAAc,CAACrB,OAAf,CAAuBvC,KAAK,CAACJ,OAAN,KAAkB,CAAzC,EAHmC,CAGU;;AAC7C,eAAOgE,cAAP;AACH,OALM,MAKA,IAAIhG,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA/C,EAA4D;AAC/D,cAAMiN,YAAY,GAAGlK,WAAW,GAAGd,QAAd,EAArB;AACA,cAAMiL,YAAY,GAAGlM,iBAAiB,CAACiB,QAAlB,EAArB,CAF+D,CAG/D;;AACA,YAAIgL,YAAY,KAAKC,YAArB,EAAmC;AAC/B;AACA,gBAAMC,UAAU,GAAGpK,WAAW,GAAGf,OAAd,EAAnB;AACA,iBAAO,IAAIS,IAAJ,CACHzB,iBAAiB,CAACkB,WAAlB,EADG,EAEHlB,iBAAiB,CAACiB,QAAlB,EAFG,EAGHgD,IAAI,CAACW,GAAL,CAASuH,UAAT,EAAqBrS,cAAc,CAACkG,iBAAD,CAAnC,CAHG,CAAP;AAKH,SARD,MAQO;AACH;AACA,iBAAOA,iBAAP;AACH;AACJ,OAhBM,MAgBA,IAAIhB,WAAW,KAAK,WAApB,EAAiC;AACpC;AACA,cAAMmN,UAAU,GAAGpK,WAAW,GAAGf,OAAd,EAAnB,CAFoC,CAGpC;;AACA,eAAO,IAAIS,IAAJ,CACHzB,iBAAiB,CAACkB,WAAlB,EADG,EAEHlB,iBAAiB,CAACiB,QAAlB,EAFG,EAGHgD,IAAI,CAACW,GAAL,CAASuH,UAAT,EAAqBrS,cAAc,CAACkG,iBAAD,CAAnC,CAHG,CAAP;AAKH,OATM,MASA,IAAIhB,WAAW,KAAK,QAApB,EAA8B;AACjC;AACA,cAAMgF,QAAQ,GAAG9E,SAAS,CAAC,CAAD,CAAT,CAAa8M,OAAb,KAAyB9M,SAAS,CAAC,CAAD,CAAT,CAAa8M,OAAb,EAA1C;AACA,cAAMhH,cAAc,GAAG,IAAIvD,IAAJ,CAASzB,iBAAiB,CAACgM,OAAlB,KAA8BhI,QAAvC,CAAvB;AACA,eAAOgB,cAAP;AACH,OALM,MAKA;AACH;AACA,cAAMhB,QAAQ,GAAG9E,SAAS,CAAC,CAAD,CAAT,CAAa8M,OAAb,KAAyB9M,SAAS,CAAC,CAAD,CAAT,CAAa8M,OAAb,EAA1C;AACA,cAAMhH,cAAc,GAAG,IAAIvD,IAAJ,CAASzB,iBAAiB,CAACgM,OAAlB,KAA8BhI,QAAvC,CAAvB;AACA,eAAOgB,cAAP;AACH;;AAED,aAAOA,cAAP;AACH,KAnDD;;AAqDA,UAAMA,cAAc,GAAG+G,iBAAiB,EAAxC,CAzD4B,CA2D5B;;AACA,QAAI/M,WAAW,KAAK,KAApB,EAA2B;AACvB,aAAQ,GAAE6J,UAAU,CAAC7I,iBAAD,CAAoB,EAAxC;AACH,KAFD,MAEO,IAAIhB,WAAW,KAAK,WAApB,EAAiC;AACpC,aAAQ,GAAE6J,UAAU,CAAC7I,iBAAD,CAAoB,EAAxC;AACH,KAFM,MAEA,IAAIhB,WAAW,KAAK,MAAhB,IAA0BA,WAAW,KAAK,UAA1C,IAAwDA,WAAW,KAAK,UAA5E,EAAwF;AAC3F;AACA,UAAIpF,SAAS,CAACoG,iBAAD,EAAoBgF,cAApB,CAAb,EAAkD;AAC9C,eAAQ,GAAE6D,UAAU,CAAC7I,iBAAD,CAAoB,EAAxC;AACH;;AACD,aAAQ,GAAE6I,UAAU,CAAC7I,iBAAD,CAAoB,OAAM6I,UAAU,CAAC7D,cAAD,CAAiB,EAAzE;AACH,KANM,MAMA,IAAIhG,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA3C,IAA0DA,WAAW,KAAK,WAA9E,EAA2F;AAC9F,YAAMoN,gBAAgB,GAAGpM,iBAAiB,CAACkC,cAAlB,CAAiC,SAAjC,EAA4C;AACjEP,QAAAA,KAAK,EAAE,MAD0D;AAEjED,QAAAA,IAAI,EAAE;AAF2D,OAA5C,CAAzB;AAIA,aAAQ,GAAE0K,gBAAiB,EAA3B;AACH,KANM,MAMA,IAAIpN,WAAW,KAAK,QAApB,EAA8B;AACjC;AACA,UAAIpF,SAAS,CAACoG,iBAAD,EAAoBgF,cAApB,CAAb,EAAkD;AAC9C,eAAQ,GAAE6D,UAAU,CAAC7I,iBAAD,CAAoB,EAAxC;AACH;;AACD,aAAQ,GAAE6I,UAAU,CAAC7I,iBAAD,CAAoB,OAAM6I,UAAU,CAAC7D,cAAD,CAAiB,EAAzE;AACH,KANM,MAMA;AACH;AACA,UAAIpL,SAAS,CAACoG,iBAAD,EAAoBgF,cAApB,CAAb,EAAkD;AAC9C,eAAQ,GAAE6D,UAAU,CAAC7I,iBAAD,CAAoB,EAAxC;AACH;;AACD,aAAQ,GAAE6I,UAAU,CAAC7I,iBAAD,CAAoB,OAAM6I,UAAU,CAAC7D,cAAD,CAAiB,EAAzE;AACH;AACJ,GAzFD,CA1yBqB,CAq4BrB;;;AACA,QAAMqH,mBAAmB,GAAG,MAAM;AAC9B1L,IAAAA,oBAAoB,CAAC,CAACD,iBAAF,CAApB;AACAG,IAAAA,mBAAmB,CAAC,KAAD,CAAnB,CAF8B,CAEF;AAC/B,GAHD,CAt4BqB,CA24BrB;;;AACA,QAAMyL,kBAAkB,GAAG,MAAM;AAC7BzL,IAAAA,mBAAmB,CAAC,CAACD,gBAAF,CAAnB;AACAD,IAAAA,oBAAoB,CAAC,KAAD,CAApB,CAF6B,CAEA;AAChC,GAHD,CA54BqB,CAi5BrB;;;AACA,QAAM4L,aAAa,GAAG,MAAM;AACxB5L,IAAAA,oBAAoB,CAAC,KAAD,CAApB;AACAE,IAAAA,mBAAmB,CAAC,KAAD,CAAnB;AACH,GAHD;;AAKA,QAAM2L,gBAAgB,GAAIC,MAAD,IAAY;AACjC,UAAMrL,KAAK,GAAGvD,YAAY,EAA1B;AACA,QAAI6O,YAAY,GAAG,IAAnB;AACA,QAAIC,UAAU,GAAG,IAAjB;AACA,QAAIC,MAAM,GAAG,EAAb;;AAEA,YAAQH,MAAR;AACI,WAAK,OAAL;AACIC,QAAAA,YAAY,GAAGtL,KAAf;AACAuL,QAAAA,UAAU,GAAGvL,KAAb;AACAwL,QAAAA,MAAM,GAAG,KAAT;AACA;;AACJ,WAAK,WAAL;AACIF,QAAAA,YAAY,GAAG,IAAIjL,IAAJ,CAASL,KAAT,CAAf;AACAsL,QAAAA,YAAY,CAAC/I,OAAb,CAAqBvC,KAAK,CAACJ,OAAN,KAAkB,CAAvC;AACA2L,QAAAA,UAAU,GAAGD,YAAb;AACAE,QAAAA,MAAM,GAAG,WAAT;AACA;;AACJ,WAAK,UAAL;AACI;AACAF,QAAAA,YAAY,GAAG,IAAIjL,IAAJ,CAASL,KAAT,CAAf;AACAsL,QAAAA,YAAY,CAAC/I,OAAb,CAAqBvC,KAAK,CAACJ,OAAN,KAAkB,CAAvC,EAHJ,CAG+C;;AAC3C2L,QAAAA,UAAU,GAAGvL,KAAb;AACAwL,QAAAA,MAAM,GAAG,MAAT;AACA;;AACJ,WAAK,UAAL;AACI;AACAF,QAAAA,YAAY,GAAG,IAAIjL,IAAJ,CAASL,KAAT,CAAf;AACAsL,QAAAA,YAAY,CAAC/I,OAAb,CAAqBvC,KAAK,CAACJ,OAAN,KAAkB,EAAvC,EAHJ,CAGgD;;AAC5C2L,QAAAA,UAAU,GAAG,IAAIlL,IAAJ,CAASL,KAAT,CAAb;AACAuL,QAAAA,UAAU,CAAChJ,OAAX,CAAmBvC,KAAK,CAACJ,OAAN,KAAkB,CAArC,EALJ,CAK6C;;AACzC4L,QAAAA,MAAM,GAAG,UAAT;AACA;;AACJ,WAAK,WAAL;AACI;AAChB;AACA;AACA;AACA;AACeD,QAAAA,UAAU,GAAG,IAAIlL,IAAJ,CAASL,KAAT,CAAb;AACAuL,QAAAA,UAAU,CAAChJ,OAAX,CAAmBvC,KAAK,CAACJ,OAAN,KAAkB,CAArC,EAPH,CAO4C;;AACzC,YAAGI,KAAK,CAAC4K,OAAN,OAAoBrO,yBAAyB,GAAGqO,OAA5B,EAAvB,EAA6D;AACxDU,UAAAA,YAAY,GAAG9O,uBAAuB,CAAC+O,UAAD,CAAtC;AACH,SAFF,MAEM;AACDD,UAAAA,YAAY,GAAG,IAAIjL,IAAJ,CAASL,KAAK,CAACF,WAAN,EAAT,EAA8BE,KAAK,CAACH,QAAN,EAA9B,EAAgD,CAAhD,CAAf;AACH;;AACD2L,QAAAA,MAAM,GAAG,OAAT;AACA;;AACJ,WAAK,YAAL;AACIF,QAAAA,YAAY,GAAG,IAAIjL,IAAJ,CAASL,KAAK,CAACF,WAAN,EAAT,EAA8BE,KAAK,CAACH,QAAN,EAA9B,EAAgD,CAAhD,CAAf;AACA0L,QAAAA,UAAU,GAAGvL,KAAb;AACAwL,QAAAA,MAAM,GAAG,QAAT;AACA;;AACJ,WAAK,WAAL;AACIF,QAAAA,YAAY,GAAG,IAAIjL,IAAJ,CAASL,KAAK,CAACF,WAAN,EAAT,EAA8BE,KAAK,CAACH,QAAN,KAAmB,CAAjD,EAAoD,CAApD,CAAf;AACA0L,QAAAA,UAAU,GAAG,IAAIlL,IAAJ,CAASL,KAAK,CAACF,WAAN,EAAT,EAA8BE,KAAK,CAACH,QAAN,EAA9B,EAAgD,CAAhD,CAAb;AACA2L,QAAAA,MAAM,GAAG,WAAT;AACA;;AACJ;AACI;AArDR,KANiC,CA8DjC;;;AACA,QAAIhN,cAAJ,EAAoB;AAChBC,MAAAA,iBAAiB,CAAC,KAAD,CAAjB;AACAI,MAAAA,oBAAoB,CAAC,IAAD,CAApB;AACAxB,MAAAA,cAAc,CAAC,EAAD,CAAd;AACH;;AAEDU,IAAAA,YAAY,CAAC,CAACuN,YAAD,EAAeC,UAAf,CAAD,CAAZ;AACA1N,IAAAA,cAAc,CAAC2N,MAAD,CAAd;AACA1J,IAAAA,wBAAwB;AACxBmJ,IAAAA,mBAAmB,CAAC,KAAD,CAAnB;AACAE,IAAAA,aAAa;AAChB,GA1ED,CAv5BqB,CAm+BrB;;;AACA,QAAMxH,6BAA6B,GAAG,MAAM;AACxC,UAAM3D,KAAK,GAAGW,WAAW,EAAzB;AACA,QAAIqE,mBAAJ,EAAyBC,iBAAzB;;AAEA,QAAInH,SAAS,CAAC,CAAD,CAAT,IAAgBA,SAAS,CAAC,CAAD,CAA7B,EAAkC;AAC9B;AACA,YAAM2N,YAAY,GACd3N,SAAS,CAAC,CAAD,CAAT,CAAa8B,OAAb,OAA2B,CAA3B,IACA9B,SAAS,CAAC,CAAD,CAAT,CAAa+B,QAAb,OAA4BG,KAAK,CAACH,QAAN,EAD5B,IAEA/B,SAAS,CAAC,CAAD,CAAT,CAAagC,WAAb,OAA+BE,KAAK,CAACF,WAAN,EAF/B,IAGAhC,SAAS,CAAC,CAAD,CAAT,CAAa8B,OAAb,OAA2BI,KAAK,CAACJ,OAAN,EAH3B,IAIA9B,SAAS,CAAC,CAAD,CAAT,CAAa+B,QAAb,OAA4BG,KAAK,CAACH,QAAN,EAJ5B,IAKA/B,SAAS,CAAC,CAAD,CAAT,CAAagC,WAAb,OAA+BE,KAAK,CAACF,WAAN,EANnC,CAF8B,CAU9B;;AACA,YAAM8C,QAAQ,GAAGC,IAAI,CAACC,GAAL,CAAShF,SAAS,CAAC,CAAD,CAAT,GAAeA,SAAS,CAAC,CAAD,CAAjC,CAAjB;AACA,YAAMiF,QAAQ,GAAGF,IAAI,CAACG,IAAL,CAAUJ,QAAQ,IAAI,OAAO,EAAP,GAAY,EAAZ,GAAiB,EAArB,CAAlB,IAA8C,CAA/D,CAZ8B,CAYoC;AAElE;;AACA,YAAM8I,kBAAkB,GAAG,IAAIrL,IAAJ,CACvB5D,YAAY,GAAG8F,OAAf,CAAuB9F,YAAY,GAAGmD,OAAf,KAA2BnD,YAAY,GAAGkP,MAAf,EAA3B,IAAsDlP,YAAY,GAAGkP,MAAf,OAA4B,CAA5B,GAAgC,CAAC,CAAjC,GAAqC,CAA3F,CAAvB,CADuB,CAA3B;AAGA,YAAMC,UAAU,GACZ9N,SAAS,CAAC,CAAD,CAAT,CAAa8B,OAAb,OAA2B8L,kBAAkB,CAAC9L,OAAnB,EAA3B,IACA9B,SAAS,CAAC,CAAD,CAAT,CAAa+B,QAAb,OAA4B6L,kBAAkB,CAAC7L,QAAnB,EAD5B,IAEA/B,SAAS,CAAC,CAAD,CAAT,CAAagC,WAAb,OAA+B4L,kBAAkB,CAAC5L,WAAnB,EAF/B,IAGAhC,SAAS,CAAC,CAAD,CAAT,CAAa8B,OAAb,OAA2BI,KAAK,CAACJ,OAAN,EAH3B,IAIA9B,SAAS,CAAC,CAAD,CAAT,CAAa+B,QAAb,OAA4BG,KAAK,CAACH,QAAN,EAJ5B,IAKA/B,SAAS,CAAC,CAAD,CAAT,CAAagC,WAAb,OAA+BE,KAAK,CAACF,WAAN,EANnC,CAlB8B,CA0B9B;;AACA,YAAM+L,eAAe,GAAG,IAAIxL,IAAJ,CACpB5D,YAAY,GAAG8F,OAAf,CAAuB9F,YAAY,GAAGmD,OAAf,KAA2BnD,YAAY,GAAGkP,MAAf,EAA3B,IAAsDlP,YAAY,GAAGkP,MAAf,OAA4B,CAA5B,GAAgC,CAAC,CAAjC,GAAqC,CAA3F,CAAvB,CADoB,CAAxB;AAGA,YAAMG,aAAa,GAAG,IAAIzL,IAAJ,CAAS5D,YAAY,GAAG8F,OAAf,CAAuB9F,YAAY,GAAGmD,OAAf,KAA2B,CAAlD,CAAT,CAAtB;AACA,YAAMmM,UAAU,GACZjO,SAAS,CAAC,CAAD,CAAT,CAAa8B,OAAb,OAA2BiM,eAAe,CAACjM,OAAhB,EAA3B,IACA9B,SAAS,CAAC,CAAD,CAAT,CAAa+B,QAAb,OAA4BgM,eAAe,CAAChM,QAAhB,EAD5B,IAEA/B,SAAS,CAAC,CAAD,CAAT,CAAagC,WAAb,OAA+B+L,eAAe,CAAC/L,WAAhB,EAF/B,IAGAhC,SAAS,CAAC,CAAD,CAAT,CAAa8B,OAAb,OAA2BkM,aAAa,CAAClM,OAAd,EAH3B,IAIA9B,SAAS,CAAC,CAAD,CAAT,CAAa+B,QAAb,OAA4BiM,aAAa,CAACjM,QAAd,EAJ5B,IAKA/B,SAAS,CAAC,CAAD,CAAT,CAAagC,WAAb,OAA+BgM,aAAa,CAAChM,WAAd,EANnC;;AAQA,UAAI2L,YAAJ,EAAkB;AACd;AACA,cAAMO,aAAa,GAAGzT,SAAS,CAACyH,KAAD,EAAQ,CAAR,CAA/B;AACAgF,QAAAA,mBAAmB,GAAG,IAAI3E,IAAJ,CAAS2L,aAAa,CAAClM,WAAd,EAAT,EAAsCkM,aAAa,CAACnM,QAAd,EAAtC,EAAgE,CAAhE,CAAtB,CAHc,CAKd;;AACA,cAAMoM,SAAS,GAAGpJ,IAAI,CAACW,GAAL,CAASxD,KAAK,CAACJ,OAAN,EAAT,EAA0BlH,cAAc,CAACsT,aAAD,CAAxC,CAAlB;AACA/G,QAAAA,iBAAiB,GAAG,IAAI5E,IAAJ,CAAS2L,aAAa,CAAClM,WAAd,EAAT,EAAsCkM,aAAa,CAACnM,QAAd,EAAtC,EAAgEoM,SAAhE,CAApB;AAEA,eAAO,CAACjH,mBAAD,EAAsBC,iBAAtB,EAAyClC,QAAzC,CAAP;AACH,OAVD,MAUO,IAAI6I,UAAJ,EAAgB;AACnB;AACA5G,QAAAA,mBAAmB,GAAG1M,OAAO,CAACwF,SAAS,CAAC,CAAD,CAAV,EAAe,CAAf,CAA7B;AACAmH,QAAAA,iBAAiB,GAAG3M,OAAO,CAACwF,SAAS,CAAC,CAAD,CAAV,EAAe,CAAf,CAA3B;AACA,eAAO,CAACkH,mBAAD,EAAsBC,iBAAtB,EAAyClC,QAAzC,CAAP;AACH,OALM,MAKA,IAAIgJ,UAAJ,EAAgB;AACnB;AACA/G,QAAAA,mBAAmB,GAAG1M,OAAO,CAACwF,SAAS,CAAC,CAAD,CAAV,EAAe,CAAf,CAA7B;AACAmH,QAAAA,iBAAiB,GAAG3M,OAAO,CAACwF,SAAS,CAAC,CAAD,CAAV,EAAe,CAAf,CAA3B;AACA,eAAO,CAACkH,mBAAD,EAAsBC,iBAAtB,EAAyClC,QAAzC,CAAP;AACH,OALM,MAKA,IAAInF,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAA7C,EAA0D;AAC7D;AACAoH,QAAAA,mBAAmB,GAAG1M,OAAO,CAACwF,SAAS,CAAC,CAAD,CAAV,EAAe,CAAf,CAA7B;AACAmH,QAAAA,iBAAiB,GAAGD,mBAApB;AACH,OAJM,MAIA,IAAIpH,WAAW,KAAK,OAApB,EAA6B;AAChC;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACgB;AACA;AACAoH,QAAAA,mBAAmB,GAAG,IAAI3E,IAAJ,CAASvC,SAAS,CAAC,CAAD,CAAT,CAAagC,WAAb,EAAT,EAAqChC,SAAS,CAAC,CAAD,CAAT,CAAa+B,QAAb,KAA0B,CAA/D,EAAkE/B,SAAS,CAAC,CAAD,CAAT,CAAa8B,OAAb,EAAlE,CAAtB,CAXgC,CAahC;;AACA,cAAMsM,mBAAmB,GAAG,IAAI7L,IAAJ,CAASvC,SAAS,CAAC,CAAD,CAAT,CAAagC,WAAb,EAAT,EAAqChC,SAAS,CAAC,CAAD,CAAT,CAAa+B,QAAb,EAArC,EAA8D,CAA9D,EAAiED,OAAjE,EAA5B;AACA,cAAMuM,YAAY,GAAGtJ,IAAI,CAACW,GAAL,CAAS1F,SAAS,CAAC,CAAD,CAAT,CAAa8B,OAAb,EAAT,EAAiCsM,mBAAjC,CAArB;AAEAjH,QAAAA,iBAAiB,GAAG,IAAI5E,IAAJ,CAASvC,SAAS,CAAC,CAAD,CAAT,CAAagC,WAAb,EAAT,EAAqChC,SAAS,CAAC,CAAD,CAAT,CAAa+B,QAAb,KAA0B,CAA/D,EAAkEsM,YAAlE,CAApB;AACH,OAlBM,MAkBA;AACH;AACAnH,QAAAA,mBAAmB,GAAG1M,OAAO,CAACwF,SAAS,CAAC,CAAD,CAAV,EAAeiF,QAAf,CAA7B;AACAkC,QAAAA,iBAAiB,GAAG3M,OAAO,CAACwF,SAAS,CAAC,CAAD,CAAV,EAAeiF,QAAf,CAA3B;AACH;;AAED,aAAO,CAACiC,mBAAD,EAAsBC,iBAAtB,EAAyClC,QAAzC,CAAP;AACH,KA5FuC,CA8FxC;;;AACAiC,IAAAA,mBAAmB,GAAG1M,OAAO,CAAC0H,KAAD,EAAQ,CAAR,CAA7B;AACAiF,IAAAA,iBAAiB,GAAGD,mBAApB;AACA,WAAO,CAACA,mBAAD,EAAsBC,iBAAtB,EAAyC,CAAzC,CAAP;AACH,GAlGD,CAp+BqB,CAwkCrB;;;AACA,QAAMmH,mBAAmB,GAAIhI,KAAD,IAAW;AACnC,QAAI,CAACA,KAAL,EAAY,OAAO;AAAEiI,MAAAA,aAAa,EAAE,CAAjB;AAAoBC,MAAAA,mBAAmB,EAAE;AAAzC,KAAP,CADuB,CAGnC;;AACA,UAAMtM,KAAK,GAAGW,WAAW,EAAzB,CAJmC,CAMnC;;AACA,UAAM4L,iBAAiB,GAAGvL,mBAAmB,CAACoD,KAAD,CAA7C,CAPmC,CASnC;;AACA,QAAIxG,WAAW,KAAK,KAApB,EAA2B;AACvB;AACA,YAAM4O,SAAS,GAAGlL,UAAU,CAAC8C,KAAK,CAAC5C,UAAN,IAAoB,CAArB,CAA5B,CAFuB,CAIvB;;AACA,YAAM8K,mBAAmB,GAAGC,iBAAiB,GAAG,GAAhD,CALuB,CAOvB;;AACA,YAAMF,aAAa,GAAIC,mBAAmB,GAAG,GAAvB,GAA8BE,SAApD;AAEA,aAAO;AACHH,QAAAA,aADG;AAEHC,QAAAA;AAFG,OAAP;AAIH,KAdD,CAeA;AAfA,SAgBK,IAAI1O,WAAW,KAAK,MAAhB,IAA0BA,WAAW,KAAK,UAA9C,EAA0D;AAC3D;AACA,YAAM6O,UAAU,GAAGnL,UAAU,CAAC8C,KAAK,CAAC1C,WAAN,IAAqB,CAAtB,CAA7B,CAF2D,CAI3D;;AACA,YAAMgL,YAAY,GAAGpL,UAAU,CAAC8C,KAAK,CAAC3C,cAAN,IAAwB,CAAzB,CAA/B,CAL2D,CAO3D;;AACA,YAAM4K,aAAa,GAAGK,YAAY,GAAG,GAArC,CAR2D,CAU3D;AACA;;AACA,YAAMJ,mBAAmB,GAAGC,iBAAiB,GAAG,GAAhD,CAZ2D,CAYN;;AAErD,aAAO;AACHF,QAAAA,aADG;AAEHC,QAAAA;AAFG,OAAP;AAIH,KAlBI,CAmBL;AAnBK,SAoBA,IAAI1O,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA3C,IAA0DA,WAAW,KAAK,QAA9E,EAAwF;AACzF;AACA,YAAM+O,WAAW,GAAGrL,UAAU,CAAC8C,KAAK,CAAC1C,WAAN,IAAqB,CAAtB,CAA9B,CAFyF,CAIzF;;AACA,YAAMgL,YAAY,GAAGpL,UAAU,CAAC8C,KAAK,CAAC3C,cAAN,IAAwB,CAAzB,CAA/B,CALyF,CAOzF;;AACA,YAAM4K,aAAa,GAAGK,YAAY,GAAG,IAArC,CARyF,CAUzF;AACA;;AACA,YAAMJ,mBAAmB,GAAGC,iBAAiB,GAAG,IAAhD,CAZyF,CAYnC;;AAEtD,aAAO;AACHF,QAAAA,aADG;AAEHC,QAAAA;AAFG,OAAP;AAIH,KAhEkC,CAkEnC;;;AACA,WAAO;AAAED,MAAAA,aAAa,EAAE,CAAjB;AAAoBC,MAAAA,mBAAmB,EAAE;AAAzC,KAAP;AACH,GApED;;AAsEA,sBACI,QAAC,GAAD;AAAK,IAAA,EAAE,EAAE;AAAEM,MAAAA,QAAQ,EAAE,QAAZ;AAAsBC,MAAAA,MAAM,EAAE,QAA9B;AAAwC9D,MAAAA,OAAO,EAAE;AAAjD,KAAT;AAAA,4BACI,QAAC,qBAAD;AAAuB,MAAA,SAAS,EAAC,uBAAjC;AAAA,8BACI,QAAC,GAAD;AAAK,QAAA,EAAE,EAAE;AAAE/B,UAAAA,OAAO,EAAE,MAAX;AAAmBE,UAAAA,UAAU,EAAE,QAA/B;AAAyCC,UAAAA,GAAG,EAAE;AAA9C,SAAT;AAAA,gCAEI,QAAC,aAAD;AACI,UAAA,MAAM,EAAEvJ,WAAW,KAAK,KAD5B;AAEI,UAAA,OAAO,EAAE,MAAM;AACXwN,YAAAA,gBAAgB,CAAC,OAAD,CAAhB,CADW,CAEX;;AACA,gBAAI5M,cAAJ,EAAoBC,iBAAiB,CAAC,KAAD,CAAjB;AACvB,WANL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAFJ,eAaI,QAAC,aAAD;AAAe,UAAA,MAAM,EAAEb,WAAW,KAAK,WAAvC;AAAoD,UAAA,OAAO,EAAE,MAAMwN,gBAAgB,CAAC,WAAD,CAAnF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAbJ,eAiBI,QAAC,aAAD;AACI,UAAA,MAAM,EAAExN,WAAW,KAAK,OAD5B;AAEI,UAAA,OAAO,EAAE,MAAM;AACXwN,YAAAA,gBAAgB,CAAC,WAAD,CAAhB,CADW,CAEX;;AACA,kBAAMpL,KAAK,GAAGvD,YAAY,EAA1B;AACA,kBAAMwD,cAAc,GAChBD,KAAK,CAACH,QAAN,OAAqB/B,SAAS,CAAC,CAAD,CAAT,CAAa+B,QAAb,EAArB,IAAgDG,KAAK,CAACF,WAAN,OAAwBhC,SAAS,CAAC,CAAD,CAAT,CAAagC,WAAb,EAD5E;AAEA,gBAAIG,cAAc,IAAIzB,cAAtB,EAAsCC,iBAAiB,CAAC,KAAD,CAAjB;AACzC,WATL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAjBJ,eA+BI,QAAC,aAAD;AACI,UAAA,MAAM,EAAEb,WAAW,KAAK,QAD5B;AAEI,UAAA,OAAO,EAAE,MAAM;AACXwN,YAAAA,gBAAgB,CAAC,YAAD,CAAhB,CADW,CAEX;;AACA,gBAAI5M,cAAJ,EAAoBC,iBAAiB,CAAC,KAAD,CAAjB;AACvB,WANL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBA/BJ,eA2CI,QAAC,oBAAD;AAAsB,UAAA,WAAW,EAAE3G,cAAnC;AAAmD,UAAA,aAAa,EAAEG,EAAlE;AAAA,oBACC2F,WAAW,KAAK,OAAhB,gBACG,QAAC,cAAD;AAAgB,YAAA,gBAAgB,EAAEG,YAAlC;AAAgD,YAAA,aAAa,EAAED,SAA/D;AAA0E,YAAA,qBAAqB,EAAEoH;AAAjG;AAAA;AAAA;AAAA;AAAA,kBADH,gBAGG,QAAC,UAAD;AACI,YAAA,KAAK,EAAC,qBADV;AAEI,YAAA,KAAK,EAAEpH,SAAS,CAAC,CAAD,CAFpB;AAGI,YAAA,WAAW,EAAC,YAHhB;AAII,YAAA,QAAQ,EAAGiH,QAAD,IAAc;AACpB,kBAAIA,QAAQ,IAAI,CAAC+H,KAAK,CAAC,IAAIzM,IAAJ,CAAS0E,QAAT,EAAmB6F,OAAnB,EAAD,CAAtB,EAAsD;AAClD;AACA7M,gBAAAA,YAAY,CAAC,CAACgH,QAAD,EAAWA,QAAX,CAAD,CAAZ,CAFkD,CAIlD;;AACA,oBAAIvG,cAAJ,EAAoB;AAChBC,kBAAAA,iBAAiB,CAAC,KAAD,CAAjB;AACH;;AAEDZ,gBAAAA,cAAc,CAAC,MAAD,CAAd;AACH;AACJ,aAhBL;AAiBI,YAAA,WAAW,EAAGuF,MAAD,iBACT,QAAC,SAAD,OACQA,MADR;AAEI,cAAA,IAAI,EAAC,OAFT;AAGI,cAAA,EAAE,EAAE;AACA+G,gBAAAA,KAAK,EAAE,OADP;AAEA,4CAA4B;AACxBrB,kBAAAA,YAAY,EAAE,MADU;AAExBW,kBAAAA,QAAQ,EAAE;AAFc;AAF5B;AAHR;AAAA;AAAA;AAAA;AAAA,oBAlBR;AA8BI,YAAA,OAAO,EAAEhN,YAAY;AA9BzB;AAAA;AAAA;AAAA;AAAA;AAJJ;AAAA;AAAA;AAAA;AAAA,gBA3CJ;AAAA;AAAA;AAAA;AAAA;AAAA,cADJ,EAqFKsD,mBAAmB,mBAChB,QAAC,GAAD;AAAK,QAAA,EAAE,EAAE;AAAEiH,UAAAA,OAAO,EAAE,MAAX;AAAmBE,UAAAA,UAAU,EAAE,QAA/B;AAAyC6F,UAAAA,EAAE,EAAE;AAA7C,SAAT;AAAA,+BACI,QAAC,gBAAD;AACI,UAAA,OAAO,eACH,QAAC,MAAD;AACI,YAAA,OAAO,EAAEvO,cADb;AAEI,YAAA,QAAQ,EAAEsG,gBAFd;AAGI,YAAA,IAAI,EAAC,OAHT;AAII,YAAA,EAAE,EAAE;AACA,qDAAuC;AACnC6B,gBAAAA,KAAK,EAAE;AAD4B,eADvC;AAIA,wEAA0D;AACtDI,gBAAAA,eAAe,EAAE,SADqC;AAEtDiG,gBAAAA,OAAO,EAAE;AAF6C;AAJ1D;AAJR;AAAA;AAAA;AAAA;AAAA,kBAFR;AAiBI,UAAA,KAAK,EAAC,+BAjBV;AAkBI,UAAA,EAAE,EAAE;AACA,4CAAgC;AAC5BvD,cAAAA,QAAQ,EAAE,SADkB;AAE5B9C,cAAAA,KAAK,EAAE,MAFqB;AAG5BS,cAAAA,UAAU,EAAE5I,cAAc,GAAG,QAAH,GAAc;AAHZ;AADhC;AAlBR;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,cAtFR;AAAA;AAAA;AAAA;AAAA;AAAA,YADJ,eAqHI,QAAC,YAAD;AAAA,6BACI,QAAC,SAAD;AAAA,gCACI,QAAC,WAAD;AAAa,UAAA,cAAc,EAAEA,cAA7B;AAAA,iCAEI,QAAC,GAAD;AAAK,YAAA,EAAE,EAAE;AAAE2L,cAAAA,KAAK,EAAE,MAAT;AAAiBnD,cAAAA,OAAO,EAAE,MAA1B;AAAkC4B,cAAAA,cAAc,EAAE,YAAlD;AAAgE1B,cAAAA,UAAU,EAAE,QAA5E;AAAsF+F,cAAAA,EAAE,EAAE;AAA1F,aAAT;AAAA,oCAEI,QAAC,UAAD;AAAY,cAAA,EAAE,EAAE;AAAEjG,gBAAAA,OAAO,EAAE,MAAX;AAAmBE,gBAAAA,UAAU,EAAE;AAA/B,eAAhB;AAAA,sCACI,QAAC,cAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBADJ;AAAA;AAAA;AAAA;AAAA;AAAA,oBAFJ,eAQI,QAAC,UAAD;AAAY,cAAA,EAAE,EAAE;AAACE,gBAAAA,UAAU,EAAE,MAAb;AAAqBqC,gBAAAA,QAAQ,EAAE,QAA/B;AAAyCsD,gBAAAA,EAAE,EAAE;AAA7C,eAAhB;AAAA,wBAEQnP,WAAW,KAAK,KAAhB,GACMnB,YAAY,GAAGyQ,kBAAf,CAAkC,OAAlC,EAA2C;AAAEC,gBAAAA,OAAO,EAAE,MAAX;AAAmBzM,gBAAAA,GAAG,EAAE,SAAxB;AAAmCH,gBAAAA,KAAK,EAAE,MAA1C;AAAkDD,gBAAAA,IAAI,EAAE;AAAxD,eAA3C,EAAgH8M,WAAhH,EADN,GAEMxP,WAAW,KAAK,WAAhB,GACA,IAAIyC,IAAJ,CAAS5D,YAAY,GAAG8F,OAAf,CAAuB9F,YAAY,GAAGmD,OAAf,KAA2B,CAAlD,CAAT,EAA+DsN,kBAA/D,CAAkF,OAAlF,EAA2F;AACzFC,gBAAAA,OAAO,EAAE,MADgF;AACxEzM,gBAAAA,GAAG,EAAE,SADmE;AACxDH,gBAAAA,KAAK,EAAE,MADiD;AACzCD,gBAAAA,IAAI,EAAE;AADmC,eAA3F,EAEC8M,WAFD,EADA,GAIA,CAAC,MAAD,EAAS,UAAT,EAAqB,UAArB,EAAiC,QAAjC,EAA2C,MAA3C,EAAmDC,QAAnD,CAA4DzP,WAA5D,IACApF,SAAS,CAACsF,SAAS,CAAC,CAAD,CAAV,EAAeA,SAAS,CAAC,CAAD,CAAxB,CAAT,GACIxB,kBAAkB,CAACwB,SAAS,CAAC,CAAD,CAAV,CADtB,GAEK,GAAExB,kBAAkB,CAACwB,SAAS,CAAC,CAAD,CAAV,CAAe,MAAKxB,kBAAkB,CAACwB,SAAS,CAAC,CAAD,CAAV,CAAe,EAH9E,GAIAF,WAAW,KAAK,OAAhB,GACAjB,WAAW,CAACmB,SAAS,CAAC,CAAD,CAAV,EAAeA,SAAS,CAAC,CAAD,CAAxB,CAAX,GACIA,SAAS,CAAC,CAAD,CAAT,CAAaoP,kBAAb,CAAgC,OAAhC,EAAyC;AAAE3M,gBAAAA,KAAK,EAAE,MAAT;AAAiBD,gBAAAA,IAAI,EAAE;AAAvB,eAAzC,EAA6E8M,WAA7E,EADJ,GAEK,GAAE9Q,kBAAkB,CAACwB,SAAS,CAAC,CAAD,CAAV,CAAe,MAAKxB,kBAAkB,CAACwB,SAAS,CAAC,CAAD,CAAV,CAAe,EAH9E,GAIAxB,kBAAkB,CAACqE,WAAW,EAAZ;AAhBhC;AAAA;AAAA;AAAA;AAAA,oBARJ,EA8BKnC,cAAc,iBAAI,QAAC,GAAD;AAAK,cAAA,EAAE,EAAE;AAAE2L,gBAAAA,KAAK,EAAE;AAAT;AAAT;AAAA;AAAA;AAAA;AAAA,oBA9BvB,EAiCK3L,cAAc,iBACX,QAAC,cAAD;AAAgB,cAAA,EAAE,EAAE;AAAE2L,gBAAAA,KAAK,EAAE;AAAT,eAApB;AAAA,sCACI,QAAC,WAAD;AAAA;AAAA;AAAA;AAAA,sBADJ,eAEI,QAAC,UAAD;AAAY,gBAAA,SAAS,EAAC,MAAtB;AAA6B,gBAAA,EAAE,EAAE;AAAEV,kBAAAA,QAAQ,EAAE,SAAZ;AAAuBrC,kBAAAA,UAAU,EAAE;AAAnC,iBAAjC;AAAA,8CACoBsD,iBAAiB,EADrC;AAAA;AAAA;AAAA;AAAA;AAAA,sBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,oBAlCR;AAAA;AAAA;AAAA;AAAA;AAAA;AAFJ;AAAA;AAAA;AAAA;AAAA,gBADJ,eA+CI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAE1D,YAAAA,OAAO,EAAE,MAAX;AAAmB4B,YAAAA,cAAc,EAAE,eAAnC;AAAoD1B,YAAAA,UAAU,EAAE,QAAhE;AAA0E+F,YAAAA,EAAE,EAAE;AAA9E,WAAT;AAAA,kCACI,QAAC,GAAD;AAAA,oCACI,QAAC,UAAD;AAAY,cAAA,OAAO,EAAC,OAApB;AAA4B,cAAA,EAAE,EAAE;AAAEtG,gBAAAA,KAAK,EAAE,MAAT;AAAiB8C,gBAAAA,QAAQ,EAAE;AAA3B,eAAhC;AAAA,wBACK7L,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAAzC,GACK,cADL,GAEKA,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA3C,IAA0DA,WAAW,KAAK,WAA1E,GACA,eADA,GAEA;AALV;AAAA;AAAA;AAAA;AAAA,oBADJ,eAQI,QAAC,UAAD;AAAY,cAAA,OAAO,EAAC,IAApB;AAAyB,cAAA,EAAE,EAAE;AAAEwJ,gBAAAA,UAAU,EAAE,MAAd;AAAsBqC,gBAAAA,QAAQ,EAAE;AAAhC,eAA7B;AAAA,wBACKpC,YAAY,CACTzJ,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAAzC,GAAuDsI,MAAM,CAAC3E,WAA9D,GAA4E2E,MAAM,CAACxB,aAD1E;AADjB;AAAA;AAAA;AAAA;AAAA,oBARJ,EAaKlG,cAAc,IAAIpB,WAAlB,IAAiCA,WAAW,CAACiL,MAAZ,GAAqB,CAAtD,iBACG;AAAA,sCACI,QAAC,UAAD;AAAY,gBAAA,EAAE,EAAE;AAAE1B,kBAAAA,KAAK,EAAE,0BAAT;AAAqC8C,kBAAAA,QAAQ,EAAE,SAA/C;AAA0D6D,kBAAAA,EAAE,EAAE;AAA9D,iBAAhB;AAAA,0BACKjG,YAAY,CACTzJ,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAAzC,GACMR,WAAW,CAAC8G,MAAZ,CAAmB,CAACqJ,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGjM,UAAU,CAACkM,IAAI,CAACjM,WAAL,IAAoB,CAArB,CAAlD,EAA2E,CAA3E,CADN,GAEM3D,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA3C,IAA0DA,WAAW,KAAK,WAA1E,GACAR,WAAW,CAAC8G,MAAZ,CAAmB,CAACqJ,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGjM,UAAU,CAACkM,IAAI,CAAC/L,cAAL,IAAuB,CAAxB,CAAlD,EAA8E,CAA9E,CADA,GAEArE,WAAW,CAAC8G,MAAZ,CAAmB,CAACqJ,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGjM,UAAU,CAACkM,IAAI,CAAC/L,cAAL,IAAuB,CAAxB,CAAlD,EAA8E,CAA9E,CALG;AADjB;AAAA;AAAA;AAAA;AAAA,sBADJ,eAUI,QAAC,UAAD;AAAY,gBAAA,EAAE,EAAE;AAAEkF,kBAAAA,KAAK,EAAE,0BAAT;AAAqC8C,kBAAAA,QAAQ,EAAE,SAA/C;AAA0DgE,kBAAAA,SAAS,EAAE;AAArE,iBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAVJ;AAAA,4BAdR;AAAA;AAAA;AAAA;AAAA;AAAA,kBADJ,eAgCI,QAAC,GAAD;AAAK,YAAA,EAAE,EAAE;AAAEtD,cAAAA,KAAK,EAAE,KAAT;AAAgBnD,cAAAA,OAAO,EAAE,MAAzB;AAAiCC,cAAAA,aAAa,EAAE,QAAhD;AAA0DE,cAAAA,GAAG,EAAE,GAA/D;AAAoEuG,cAAAA,EAAE,EAAE;AAAxE,aAAT;AAAA,oCACI,QAAC,GAAD;AAAK,cAAA,EAAE,EAAE;AAAE1G,gBAAAA,OAAO,EAAE,MAAX;AAAmB4B,gBAAAA,cAAc,EAAE,QAAnC;AAA6CqE,gBAAAA,EAAE,EAAE,GAAjD;AAAsD/F,gBAAAA,UAAU,EAAE;AAAlE,eAAT;AAAA,sCACI,QAAC,UAAD;AAAY,gBAAA,OAAO,EAAC,OAApB;AAA4B,gBAAA,EAAE,EAAE;AAAEP,kBAAAA,KAAK,EAAE,MAAT;AAAiB8C,kBAAAA,QAAQ,EAAE,MAA3B;AAAmCkE,kBAAAA,EAAE,EAAE,CAAvC;AAA0CvG,kBAAAA,UAAU,EAAE;AAAtD,iBAAhC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBADJ,eAII,QAAC,gBAAD;AAAkB,gBAAA,UAAU,EAAEoB,eAA9B;AAA+C,gBAAA,EAAE,EAAE;AAAEiB,kBAAAA,QAAQ,EAAE;AAAZ,iBAAnD;AAAA,2BACK5G,IAAI,CAACW,GAAL,CAASgF,eAAT,EAA0B,MAA1B,EAAkCoF,OAAlC,CAA0C,CAA1C,CADL;AAAA;AAAA;AAAA;AAAA;AAAA,sBAJJ;AAAA;AAAA;AAAA;AAAA;AAAA,oBADJ,EASKpP,cAAc,IAAIpB,WAAlB,IAAiCA,WAAW,CAACiL,MAAZ,GAAqB,CAAtD,iBACG,QAAC,GAAD;AAAK,cAAA,EAAE,EAAE;AAAErB,gBAAAA,OAAO,EAAE,MAAX;AAAmB4B,gBAAAA,cAAc,EAAE,QAAnC;AAA6C1B,gBAAAA,UAAU,EAAE;AAAzD,eAAT;AAAA,sCACI,QAAC,UAAD;AAAY,gBAAA,EAAE,EAAE;AAAEP,kBAAAA,KAAK,EAAE,0BAAT;AAAqC8C,kBAAAA,QAAQ,EAAE,SAA/C;AAA0DkE,kBAAAA,EAAE,EAAE;AAA9D,iBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBADJ,eAII,QAAC,UAAD;AAAY,gBAAA,EAAE,EAAE;AAAEhH,kBAAAA,KAAK,EAAE,0BAAT;AAAqC8C,kBAAAA,QAAQ,EAAE,SAA/C;AAA0DgE,kBAAAA,SAAS,EAAE;AAArE,iBAAhB;AAAA,2BACK5K,IAAI,CAACW,GAAL,CACG5F,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAAzC,GACMoD,mBAAmB,CACf5D,WAAW,CAAC8G,MAAZ,CAAmB,CAACqJ,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGjM,UAAU,CAACkM,IAAI,CAACjM,WAAL,IAAoB,CAArB,CAAlD,EAA2E,CAA3E,CADe,EAEfnE,WAAW,CAAC8G,MAAZ,CAAmB,CAACqJ,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGjM,UAAU,CAACkM,IAAI,CAAChM,UAAL,IAAmB,CAApB,CAAlD,EAA0E,CAA1E,CAFe,CADzB,GAKM5D,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA3C,IAA0DA,WAAW,KAAK,WAA1E,GACAoD,mBAAmB,CACf5D,WAAW,CAAC8G,MAAZ,CAAmB,CAACqJ,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGjM,UAAU,CAACkM,IAAI,CAAC/L,cAAL,IAAuB,CAAxB,CAAlD,EAA8E,CAA9E,CADe,EAEfrE,WAAW,CAAC8G,MAAZ,CAAmB,CAACqJ,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGjM,UAAU,CAACkM,IAAI,CAAC9L,WAAL,IAAoB,CAArB,CAAlD,EAA2E,CAA3E,CAFe,CADnB,GAKAV,mBAAmB,CACf5D,WAAW,CAAC8G,MAAZ,CAAmB,CAACqJ,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGjM,UAAU,CAACkM,IAAI,CAAC/L,cAAL,IAAuB,CAAxB,CAAlD,EAA8E,CAA9E,CADe,EAEfrE,WAAW,CAAC8G,MAAZ,CAAmB,CAACqJ,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGjM,UAAU,CAACkM,IAAI,CAAChM,UAAL,IAAmB,CAApB,CAAlD,EAA0E,CAA1E,IACIG,SAHW,CAX5B,EAgBG,MAhBH,EAiBCiM,OAjBD,CAiBS,CAjBT,CADL;AAAA;AAAA;AAAA;AAAA;AAAA,sBAJJ;AAAA;AAAA;AAAA;AAAA;AAAA,oBAVR,eAsCI,QAAC,oBAAD;AACI,cAAA,OAAO,EAAC,aADZ;AAEI,cAAA,KAAK,EAAE/K,IAAI,CAACW,GAAL,CAASgF,eAAT,EAA0B,GAA1B,CAFX;AAGI,cAAA,UAAU,EAAE3F,IAAI,CAACW,GAAL,CAASgF,eAAT,EAA0B,GAA1B;AAHhB;AAAA;AAAA;AAAA;AAAA,oBAtCJ;AAAA;AAAA;AAAA;AAAA;AAAA,kBAhCJ,eA6EI,QAAC,GAAD;AAAK,YAAA,EAAE,EAAE;AAAEqF,cAAAA,SAAS,EAAE;AAAb,aAAT;AAAA,oCACI,QAAC,UAAD;AAAY,cAAA,OAAO,EAAC,OAApB;AAA4B,cAAA,EAAE,EAAE;AAAElH,gBAAAA,KAAK,EAAE,MAAT;AAAiB8C,gBAAAA,QAAQ,EAAE;AAA3B,eAAhC;AAAA,wBACK7L,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAAzC,GACK,aADL,GAEKA,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA3C,IAA0DA,WAAW,KAAK,WAA1E,GACA,cADA,GAEA;AALV;AAAA;AAAA;AAAA;AAAA,oBADJ,eAQI,QAAC,UAAD;AAAY,cAAA,OAAO,EAAC,IAApB;AAAyB,cAAA,EAAE,EAAE;AAAEwJ,gBAAAA,UAAU,EAAE,MAAd;AAAsBqC,gBAAAA,QAAQ,EAAE;AAAhC,eAA7B;AAAA,wBACKpC,YAAY,CACTzJ,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAAzC,GACMsI,MAAM,CAAC1E,UADb,GAEM5D,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA3C,IAA0DA,WAAW,KAAK,WAA1E,GACAsI,MAAM,CAACxE,WADP,GAEAwE,MAAM,CAAC1E,UAAP,GAAoBG,SALjB;AADjB;AAAA;AAAA;AAAA;AAAA,oBARJ,EAiBKnD,cAAc,IAAIpB,WAAlB,IAAiCA,WAAW,CAACiL,MAAZ,GAAqB,CAAtD,iBACG;AAAA,sCACI,QAAC,UAAD;AAAY,gBAAA,EAAE,EAAE;AAAE1B,kBAAAA,KAAK,EAAE,0BAAT;AAAqC8C,kBAAAA,QAAQ,EAAE,SAA/C;AAA0D6D,kBAAAA,EAAE,EAAE;AAA9D,iBAAhB;AAAA,0BACKjG,YAAY,CACTzJ,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAAzC,GACMR,WAAW,CAAC8G,MAAZ,CAAmB,CAACqJ,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGjM,UAAU,CAACkM,IAAI,CAAChM,UAAL,IAAmB,CAApB,CAAlD,EAA0E,CAA1E,CADN,GAEM5D,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA3C,IAA0DA,WAAW,KAAK,WAA1E,GACAR,WAAW,CAAC8G,MAAZ,CAAmB,CAACqJ,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGjM,UAAU,CAACkM,IAAI,CAAC9L,WAAL,IAAoB,CAArB,CAAlD,EAA2E,CAA3E,CADA,GAEAtE,WAAW,CAAC8G,MAAZ,CAAmB,CAACqJ,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGjM,UAAU,CAACkM,IAAI,CAAChM,UAAL,IAAmB,CAApB,CAAlD,EAA0E,CAA1E,IAA+EG,SAL5E;AADjB;AAAA;AAAA;AAAA;AAAA,sBADJ,eAUI,QAAC,UAAD;AAAY,gBAAA,EAAE,EAAE;AAAEgF,kBAAAA,KAAK,EAAE,0BAAT;AAAqC8C,kBAAAA,QAAQ,EAAE,SAA/C;AAA0DgE,kBAAAA,SAAS,EAAE;AAArE,iBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAVJ;AAAA,4BAlBR;AAAA;AAAA;AAAA;AAAA;AAAA,kBA7EJ,eAgHI,QAAC,GAAD;AAAK,YAAA,EAAE,EAAE;AAAEV,cAAAA,EAAE,EAAE;AAAN,aAAT;AAAA,mCACI,QAAC,aAAD;AACI,cAAA,OAAO,EAAE,MAAMhH,kBAAkB,CAAC,WAAD,CADrC;AAEI,cAAA,SAAS,eAAE;AAAA;AAAA;AAAA;AAAA,sBAFf;AAGI,cAAA,EAAE,EAAE;AAAEgD,gBAAAA,OAAO,EAAE,UAAX;AAAuBU,gBAAAA,QAAQ,EAAE;AAAjC,eAHR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,kBAhHJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBA/CJ;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,YArHJ,eAkSI,QAAC,SAAD;AAAA,gBACKvM,IAAI,CAAC4Q,GAAL,CAAS,CAAC1J,KAAD,EAAQ2J,KAAR,KAAkB;AACxB;AACA,cAAMC,eAAe,GAAGhN,mBAAmB,CAACoD,KAAK,CAAC7C,WAAP,EAAoB6C,KAAK,CAAC5C,UAA1B,CAA3C,CAFwB,CAIxB;;AACA,cAAMyM,iBAAiB,GAAGjN,mBAAmB,CAACoD,KAAK,CAAC3C,cAAP,EAAuB2C,KAAK,CAAC1C,WAA7B,CAA7C,CALwB,CAOxB;;AACA,cAAMgF,QAAQ,GAAGpF,UAAU,CAAC8C,KAAK,CAAC5C,UAAP,CAAV,GAA+BG,SAAhD;AACA,cAAMuM,cAAc,GAAGlN,mBAAmB,CAACoD,KAAK,CAAC3C,cAAP,EAAuBiF,QAAvB,CAA1C;AAEA,YAAI6F,iBAAJ;;AACA,YAAI3O,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAA7C,EAA0D;AACtD2O,UAAAA,iBAAiB,GAAGyB,eAApB;AACH,SAFD,MAEO,IAAIpQ,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA3C,IAA0DA,WAAW,KAAK,WAA9E,EAA2F;AAC9F2O,UAAAA,iBAAiB,GAAG0B,iBAApB;AACH,SAFM,MAEA;AACH1B,UAAAA,iBAAiB,GAAG2B,cAApB;AACH,SAlBuB,CAoBxB;;;AACA,YAAIC,YAAY,GAAG,IAAnB;AACA,YAAIC,iBAAiB,GAAG,CAAxB;AACA,YAAIC,oBAAoB,GAAG,CAA3B;;AAEA,YAAI7P,cAAc,IAAIpB,WAAW,CAACiL,MAAZ,GAAqB,CAA3C,EAA8C;AAC1C8F,UAAAA,YAAY,GAAG/Q,WAAW,CAACqJ,IAAZ,CAAkB6H,EAAD,IAAQA,EAAE,CAACC,gBAAH,KAAwBnK,KAAK,CAACmK,gBAAvD,CAAf;;AAEA,cAAIJ,YAAJ,EAAkB;AACd,gBAAIvQ,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAA7C,EAA0D;AACtDwQ,cAAAA,iBAAiB,GAAGpN,mBAAmB,CAACmN,YAAY,CAAC5M,WAAd,EAA2B4M,YAAY,CAAC3M,UAAxC,CAAvC;AACH,aAFD,MAEO,IAAI5D,WAAW,KAAK,OAApB,EAA6B;AAChCwQ,cAAAA,iBAAiB,GAAGpN,mBAAmB,CAACmN,YAAY,CAAC1M,cAAd,EAA8B0M,YAAY,CAACzM,WAA3C,CAAvC;AACH,aAFM,MAEA;AACH,oBAAM8M,WAAW,GAAGlN,UAAU,CAAC6M,YAAY,CAAC3M,UAAd,CAAV,GAAsCG,SAA1D;AACAyM,cAAAA,iBAAiB,GAAGpN,mBAAmB,CAACmN,YAAY,CAAC1M,cAAd,EAA8B+M,WAA9B,CAAvC;AACH;;AAEDH,YAAAA,oBAAoB,GAAG9B,iBAAiB,GAAG6B,iBAA3C;AACH;AACJ;;AAED,4BACI,QAAC,GAAD;AAAA,iCACI,QAAC,UAAD;AAAA,oCACI,QAAC,YAAD;AAAc,cAAA,KAAK,EAAE;AAAEpH,gBAAAA,OAAO,EAAE,MAAX;AAAmBC,gBAAAA,aAAa,EAAE,QAAlC;AAA4CC,gBAAAA,UAAU,EAAE;AAAxD,eAArB;AAAA,qCACI,QAAC,SAAD;AAAA,0BAAY9C,KAAK,CAAC6B;AAAlB;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,oBADJ,eAMI,QAAC,aAAD;AAAA,sCAEI,QAAC,GAAD;AACI,gBAAA,EAAE,EAAE;AACAe,kBAAAA,OAAO,EAAE,MADT;AAEAC,kBAAAA,aAAa,EAAE,QAFf;AAGAC,kBAAAA,UAAU,EAAE,QAHZ;AAIAiD,kBAAAA,KAAK,EAAE,MAJP;AAKAhB,kBAAAA,QAAQ,EAAE;AALV,iBADR;AAAA,uCAUI,QAAC,cAAD;AAAgB,kBAAA,EAAE,EAAE;AAAEA,oBAAAA,QAAQ,EAAE,UAAZ;AAAwB8D,oBAAAA,EAAE,EAAE;AAA5B,mBAApB;AAAA,0CACI,QAAC,mBAAD;AAAqB,oBAAA,KAAK,EAAC,MAA3B;AAAkC,oBAAA,MAAM,EAAE,GAA1C;AAAA,2CACI,QAAC,cAAD;AACI,sBAAA,EAAE,EAAC,KADP;AAEI,sBAAA,EAAE,EAAC,KAFP;AAGI,sBAAA,WAAW,EAAE,EAHjB;AAII,sBAAA,WAAW,EAAE,EAJjB;AAKI,sBAAA,OAAO,EAAE,EALb;AAMI,sBAAA,IAAI,EAAE,CACF;AACA,0BAAIzO,cAAc,IAAI2P,YAAlB,GACE,CACI;AACIM,wBAAAA,IAAI,EAAE,aADV;AAEIpM,wBAAAA,KAAK,EAAErB,mBAAmB,CAACmN,YAAD,CAF9B;AAGIO,wBAAAA,IAAI,EAAE,0BAHV;AAIIC,wBAAAA,WAAW,EAAE,EAJjB;AAKIC,wBAAAA,WAAW,EAAE;AALjB,uBADJ,CADF,GAUE,EAVN,CAFE,EAaF;AACA;AACIH,wBAAAA,IAAI,EAAE,QADV;AAEIpM,wBAAAA,KAAK,EAAEkK,iBAFX;AAGImC,wBAAAA,IAAI,EAAE/U,aAAa,CAACyK,KAAK,CAAC6B,kBAAP,EAA2B8H,KAA3B,EAAkCxB,iBAAlC,CAHvB;AAIIoC,wBAAAA,WAAW,EAAE,EAJjB;AAKIC,wBAAAA,WAAW,EAAE;AALjB,uBAdE,CANV;AA4BI,sBAAA,UAAU,EAAE,GA5BhB;AA6BI,sBAAA,QAAQ,EAAE,CA7Bd;AAAA,8CA+BI,QAAC,cAAD;AAAgB,wBAAA,IAAI,EAAC,QAArB;AAA8B,wBAAA,MAAM,EAAE,CAAC,CAAD,EAAI,GAAJ,CAAtC;AAAgD,wBAAA,WAAW,EAAE,CAA7D;AAAgE,wBAAA,IAAI,EAAE;AAAtE;AAAA;AAAA;AAAA;AAAA,8BA/BJ,eAgCI,QAAC,SAAD;AACI,wBAAA,UAAU,MADd;AAEI,wBAAA,mBAAmB,EAAC,uBAFxB;AAGI,wBAAA,OAAO,EAAC,OAHZ;AAII,wBAAA,YAAY,EAAE;AAJlB;AAAA;AAAA;AAAA;AAAA,8BAhCJ;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,0BADJ,eA4CI,QAAC,GAAD;AACI,oBAAA,EAAE,EAAE;AACAzF,sBAAAA,QAAQ,EAAE,UADV;AAEAW,sBAAAA,GAAG,EAAE,KAFL;AAGAC,sBAAAA,IAAI,EAAE,KAHN;AAIAG,sBAAAA,SAAS,EAAE,uBAJX;AAKAtD,sBAAAA,MAAM,EAAE,EALR;AAMAI,sBAAAA,OAAO,EAAE,MANT;AAOAC,sBAAAA,aAAa,EAAE,QAPf;AAQAC,sBAAAA,UAAU,EAAE,QARZ;AASA0B,sBAAAA,cAAc,EAAE;AAThB,qBADR;AAAA,4CAaI,QAAC,gBAAD;AACI,sBAAA,UAAU,EAAE2D,iBADhB;AAEI,sBAAA,EAAE,EAAE;AAAE9C,wBAAAA,QAAQ,EAAE,QAAZ;AAAsBrC,wBAAAA,UAAU,EAAE;AAAlC,uBAFR;AAAA,iCAIKmF,iBAAiB,CAACqB,OAAlB,CAA0B,CAA1B,CAJL;AAAA;AAAA;AAAA;AAAA;AAAA,4BAbJ,EAmBKpP,cAAc,IAAI2P,YAAlB,iBACG;AAAA,8CACI,QAAC,UAAD;AAAY,wBAAA,EAAE,EAAE;AAAE1E,0BAAAA,QAAQ,EAAE,QAAZ;AAAsB9C,0BAAAA,KAAK,EAAE,0BAA7B;AAAyD2G,0BAAAA,EAAE,EAAE;AAA7D,yBAAhB;AAAA,mCACKtM,mBAAmB,CAACmN,YAAD,CAAnB,CAAkCP,OAAlC,CAA0C,CAA1C,CADL;AAAA;AAAA;AAAA;AAAA;AAAA,8BADJ,eAII,QAAC,UAAD;AACI,wBAAA,EAAE,EAAE;AACAnE,0BAAAA,QAAQ,EAAE,SADV;AAEAgE,0BAAAA,SAAS,EAAE,QAFX;AAGA9G,0BAAAA,KAAK,EAAE;AAHP,yBADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BAJJ;AAAA,oCApBR;AAAA;AAAA;AAAA;AAAA;AAAA,0BA5CJ,eAkFI,QAAC,GAAD;AACI,oBAAA,EAAE,EAAE;AACAwC,sBAAAA,QAAQ,EAAE,UADV;AAEA0F,sBAAAA,MAAM,EAAE,MAFR;AAGA9E,sBAAAA,IAAI,EAAE,GAHN;AAIA/C,sBAAAA,OAAO,EAAE,MAJT;AAKAC,sBAAAA,aAAa,EAAE,QALf;AAMAC,sBAAAA,UAAU,EAAE,QANZ;AAOA0B,sBAAAA,cAAc,EAAE,QAPhB;AAQAuB,sBAAAA,KAAK,EAAE,OARP;AASA0D,sBAAAA,SAAS,EAAE;AATX,qBADR;AAAA,4CAaI,QAAC,UAAD;AAAY,sBAAA,EAAE,EAAE;AAAEzG,wBAAAA,UAAU,EAAE,MAAd;AAAsBqC,wBAAAA,QAAQ,EAAE,QAAhC;AAA0C9C,wBAAAA,KAAK,EAAE;AAAjD,uBAAhB;AAAA,gCACKU,YAAY,CACTzJ,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAAzC,GACMwG,KAAK,CAAC7C,WADZ,GAEM6C,KAAK,CAAC3C,cAHH;AADjB;AAAA;AAAA;AAAA;AAAA,4BAbJ,eAoBI,QAAC,UAAD;AAAY,sBAAA,EAAE,EAAE;AAAEgI,wBAAAA,QAAQ,EAAE,SAAZ;AAAuB9C,wBAAAA,KAAK,EAAE;AAA9B,uBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4BApBJ,EAqBKnI,cAAc,IAAI2P,YAAlB,iBACG;AAAA,8CACI,QAAC,UAAD;AAAY,wBAAA,EAAE,EAAE;AAAExH,0BAAAA,KAAK,EAAE,0BAAT;AAAqC8C,0BAAAA,QAAQ,EAAE,QAA/C;AAAyD6D,0BAAAA,EAAE,EAAE;AAA7D,yBAAhB;AAAA,kCACKjG,YAAY,CACTzJ,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAAzC,GACMuQ,YAAY,CAAC5M,WADnB,GAEM4M,YAAY,CAAC1M,cAHV;AADjB;AAAA;AAAA;AAAA;AAAA,8BADJ,eAQI,QAAC,UAAD;AACI,wBAAA,EAAE,EAAE;AACAkF,0BAAAA,KAAK,EAAE,0BADP;AAEA8C,0BAAAA,QAAQ,EAAE,SAFV;AAGAgE,0BAAAA,SAAS,EAAE;AAHX,yBADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BARJ;AAAA,oCAtBR;AAAA;AAAA;AAAA;AAAA;AAAA,0BAlFJ,eA8HI,QAAC,GAAD;AACI,oBAAA,EAAE,EAAE;AACAtE,sBAAAA,QAAQ,EAAE,UADV;AAEA0F,sBAAAA,MAAM,EAAE,MAFR;AAGAC,sBAAAA,KAAK,EAAE,GAHP;AAIA9H,sBAAAA,OAAO,EAAE,MAJT;AAKAC,sBAAAA,aAAa,EAAE,QALf;AAMAC,sBAAAA,UAAU,EAAE,QANZ;AAOA0B,sBAAAA,cAAc,EAAE,QAPhB;AAQAuB,sBAAAA,KAAK,EAAE,OARP;AASA0D,sBAAAA,SAAS,EAAE;AATX,qBADR;AAAA,4CAaI,QAAC,UAAD;AAAY,sBAAA,EAAE,EAAE;AAAEzG,wBAAAA,UAAU,EAAE,MAAd;AAAsBqC,wBAAAA,QAAQ,EAAE,QAAhC;AAA0C9C,wBAAAA,KAAK,EAAE;AAAjD,uBAAhB;AAAA,gCACKU,YAAY,CACTzJ,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAAzC,GACMwG,KAAK,CAAC5C,UADZ,GAEM5D,WAAW,KAAK,OAAhB,IACAA,WAAW,KAAK,WADhB,IAEAA,WAAW,KAAK,WAFhB,GAGAwG,KAAK,CAAC1C,WAHN,GAIA0C,KAAK,CAAC5C,UAAN,GAAmBG,SAPhB;AADjB;AAAA;AAAA;AAAA;AAAA,4BAbJ,eAwBI,QAAC,UAAD;AAAY,sBAAA,EAAE,EAAE;AAAE8H,wBAAAA,QAAQ,EAAE,SAAZ;AAAuB9C,wBAAAA,KAAK,EAAE;AAA9B,uBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4BAxBJ,EAyBKnI,cAAc,IAAI2P,YAAlB,iBACG;AAAA,8CACI,QAAC,UAAD;AAAY,wBAAA,EAAE,EAAE;AAAExH,0BAAAA,KAAK,EAAE,0BAAT;AAAqC8C,0BAAAA,QAAQ,EAAE,QAA/C;AAAyD6D,0BAAAA,EAAE,EAAE;AAA7D,yBAAhB;AAAA,kCACKjG,YAAY,CACTzJ,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAAzC,GACMuQ,YAAY,CAAC3M,UADnB,GAEM5D,WAAW,KAAK,OAAhB,IACAA,WAAW,KAAK,WADhB,IAEAA,WAAW,KAAK,WAFhB,GAGAuQ,YAAY,CAACzM,WAHb,GAIAyM,YAAY,CAAC3M,UAAb,GAA0BG,SAPvB;AADjB;AAAA;AAAA;AAAA;AAAA,8BADJ,eAYI,QAAC,UAAD;AACI,wBAAA,EAAE,EAAE;AACAgF,0BAAAA,KAAK,EAAE,0BADP;AAEA8C,0BAAAA,QAAQ,EAAE,SAFV;AAGAgE,0BAAAA,SAAS,EAAE;AAHX,yBADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BAZJ;AAAA,oCA1BR;AAAA;AAAA;AAAA;AAAA;AAAA,0BA9HJ,eAkLI,QAAC,GAAD;AACI,oBAAA,EAAE,EAAE;AACAtE,sBAAAA,QAAQ,EAAE,UADV;AAEA0F,sBAAAA,MAAM,EAAE,OAFR;AAGA9E,sBAAAA,IAAI,EAAE,KAHN;AAIAG,sBAAAA,SAAS,EAAE,kBAJX;AAKAlD,sBAAAA,OAAO,EAAE,MALT;AAMAC,sBAAAA,aAAa,EAAE,QANf;AAOAC,sBAAAA,UAAU,EAAE,QAPZ;AAQA0B,sBAAAA,cAAc,EAAE,QARhB;AASAiF,sBAAAA,SAAS,EAAE;AATX,qBADR;AAAA,8BAaK,CAAC,KAAD,EAAQ,QAAR,EAAkBR,QAAlB,CAA2BzP,WAA3B,KACG,CAAC,MAAM;AACH,4BAAMmR,UAAU,GACZnR,WAAW,KAAK,KAAhB,IAAyBA,WAAW,KAAK,WAAzC,GACMwG,KAAK,CAAC7C,WAAN,GAAoB6C,KAAK,CAAC5C,UADhC,GAEM5D,WAAW,KAAK,OAAhB,IACAA,WAAW,KAAK,WADhB,IAEAA,WAAW,KAAK,WAFhB,GAGAwG,KAAK,CAAC3C,cAAN,GAAuB2C,KAAK,CAAC1C,WAH7B,GAIA0C,KAAK,CAAC3C,cAAN,GAAuB2C,KAAK,CAAC5C,UAAN,GAAmBG,SAPpD;AASA,4BAAMqN,QAAQ,GAAGD,UAAU,IAAI,CAA/B;AAEA,0CACI;AAAA,gDACI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AACA3H,4BAAAA,UAAU,EAAE,MADZ;AAEAqC,4BAAAA,QAAQ,EAAE,QAFV;AAGA9C,4BAAAA,KAAK,EAAEqI,QAAQ,GAAG,SAAH,GAAe;AAH9B,2BADR;AAAA,oCAOK3H,YAAY,CAACxE,IAAI,CAACC,GAAL,CAASiM,UAAT,CAAD;AAPjB;AAAA;AAAA;AAAA;AAAA,gCADJ,eAUI,QAAC,UAAD;AAAY,0BAAA,EAAE,EAAE;AAAEtF,4BAAAA,QAAQ,EAAE,SAAZ;AAAuB9C,4BAAAA,KAAK,EAAE;AAA9B,2BAAhB;AAAA,oCACKqI,QAAQ,GAAG,WAAH,GAAiB;AAD9B;AAAA;AAAA;AAAA;AAAA,gCAVJ;AAAA,sCADJ;AAgBH,qBA5BD;AAdR;AAAA;AAAA;AAAA;AAAA,0BAlLJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAVJ;AAAA;AAAA;AAAA;AAAA,sBAFJ,eA8OI,QAAC,UAAD;AAAA,uCACI,QAAC,YAAD;AACI,kBAAA,EAAE,EAAE;AACAhI,oBAAAA,OAAO,EAAE,MADT;AAEAiI,oBAAAA,mBAAmB,EAAE,gBAFrB;AAGA9H,oBAAAA,GAAG,EAAE,KAHL;AAIAgD,oBAAAA,KAAK,EAAE,MAJP;AAKAmD,oBAAAA,EAAE,EAAE;AALJ,mBADR;AAAA,4BASK1P,WAAW,KAAK,KAAhB,gBACG;AAAA,4CACI,QAAC,GAAD;AAAK,sBAAA,EAAE,EAAE;AAAEoJ,wBAAAA,OAAO,EAAE,MAAX;AAAmBC,wBAAAA,aAAa,EAAE,QAAlC;AAA4CiI,wBAAAA,CAAC,EAAE;AAA/C,uBAAT;AAAA,8CACI,QAAC,UAAD;AAAY,wBAAA,EAAE,EAAE;AAAEvI,0BAAAA,KAAK,EAAE,MAAT;AAAiB8C,0BAAAA,QAAQ,EAAE,SAA3B;AAAsCrC,0BAAAA,UAAU,EAAE;AAAlD,yBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BADJ,eAII,QAAC,UAAD;AAAY,wBAAA,EAAE,EAAE;AAAET,0BAAAA,KAAK,EAAE,MAAT;AAAiB8C,0BAAAA,QAAQ,EAAE,SAA3B;AAAsCrC,0BAAAA,UAAU,EAAE;AAAlD,yBAAhB;AAAA,kCACKC,YAAY,CAACjD,KAAK,CAACkE,oBAAP;AADjB;AAAA;AAAA;AAAA;AAAA,8BAJJ,EAOK9J,cAAc,IAAI2P,YAAlB,iBACG;AAAA,gDACI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AAAExH,4BAAAA,KAAK,EAAE,0BAAT;AAAqC8C,4BAAAA,QAAQ,EAAE,QAA/C;AAAyD6D,4BAAAA,EAAE,EAAE;AAA7D,2BADR;AAAA,oCAGKjG,YAAY,CAAC8G,YAAY,CAAC7F,oBAAd;AAHjB;AAAA;AAAA;AAAA;AAAA,gCADJ,eAMI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AACA3B,4BAAAA,KAAK,EAAE,0BADP;AAEA8C,4BAAAA,QAAQ,EAAE,SAFV;AAGAgE,4BAAAA,SAAS,EAAE;AAHX,2BADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCANJ;AAAA,sCARR;AAAA;AAAA;AAAA;AAAA;AAAA,4BADJ,eA2BI,QAAC,GAAD;AAAK,sBAAA,EAAE,EAAE;AAAEzG,wBAAAA,OAAO,EAAE,MAAX;AAAmBC,wBAAAA,aAAa,EAAE,QAAlC;AAA4CiI,wBAAAA,CAAC,EAAE;AAA/C,uBAAT;AAAA,8CACI,QAAC,UAAD;AAAY,wBAAA,EAAE,EAAE;AAAEvI,0BAAAA,KAAK,EAAE,MAAT;AAAiB8C,0BAAAA,QAAQ,EAAE,SAA3B;AAAsCrC,0BAAAA,UAAU,EAAE;AAAlD,yBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BADJ,eAII,QAAC,UAAD;AAAY,wBAAA,EAAE,EAAE;AAAET,0BAAAA,KAAK,EAAE,MAAT;AAAiB8C,0BAAAA,QAAQ,EAAE,SAA3B;AAAsCrC,0BAAAA,UAAU,EAAE;AAAlD,yBAAhB;AAAA,kCACKhD,KAAK,CAAC+K;AADX;AAAA;AAAA;AAAA;AAAA,8BAJJ,EAOK3Q,cAAc,IAAI2P,YAAlB,iBACG;AAAA,gDACI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AAAExH,4BAAAA,KAAK,EAAE,0BAAT;AAAqC8C,4BAAAA,QAAQ,EAAE,QAA/C;AAAyD6D,4BAAAA,EAAE,EAAE;AAA7D,2BADR;AAAA,oCAGKa,YAAY,CAACgB;AAHlB;AAAA;AAAA;AAAA;AAAA,gCADJ,eAMI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AACAxI,4BAAAA,KAAK,EAAE,0BADP;AAEA8C,4BAAAA,QAAQ,EAAE,SAFV;AAGAgE,4BAAAA,SAAS,EAAE;AAHX,2BADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCANJ;AAAA,sCARR;AAAA;AAAA;AAAA;AAAA;AAAA,4BA3BJ;AAAA,kCADH,GAuDG7P,WAAW,KAAK,OAAhB,gBACA;AAAA,4CACI,QAAC,GAAD;AAAK,sBAAA,EAAE,EAAE;AAAEoJ,wBAAAA,OAAO,EAAE,MAAX;AAAmBC,wBAAAA,aAAa,EAAE,QAAlC;AAA4CiI,wBAAAA,CAAC,EAAE;AAA/C,uBAAT;AAAA,8CACI,QAAC,UAAD;AAAY,wBAAA,EAAE,EAAE;AAAEvI,0BAAAA,KAAK,EAAE,MAAT;AAAiB8C,0BAAAA,QAAQ,EAAE,SAA3B;AAAsCrC,0BAAAA,UAAU,EAAE;AAAlD,yBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BADJ,eAII,QAAC,UAAD;AAAY,wBAAA,EAAE,EAAE;AAAET,0BAAAA,KAAK,EAAE,MAAT;AAAiB8C,0BAAAA,QAAQ,EAAE,SAA3B;AAAsCrC,0BAAAA,UAAU,EAAE;AAAlD,yBAAhB;AAAA,kCACKC,YAAY,CAACjD,KAAK,CAACgL,uBAAP;AADjB;AAAA;AAAA;AAAA;AAAA,8BAJJ,EAOK5Q,cAAc,IAAI2P,YAAlB,iBACG;AAAA,gDACI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AAAExH,4BAAAA,KAAK,EAAE,0BAAT;AAAqC8C,4BAAAA,QAAQ,EAAE,QAA/C;AAAyD6D,4BAAAA,EAAE,EAAE;AAA7D,2BADR;AAAA,oCAGKjG,YAAY,CAAC8G,YAAY,CAACiB,uBAAd;AAHjB;AAAA;AAAA;AAAA;AAAA,gCADJ,eAMI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AACAzI,4BAAAA,KAAK,EAAE,0BADP;AAEA8C,4BAAAA,QAAQ,EAAE,SAFV;AAGAgE,4BAAAA,SAAS,EAAE;AAHX,2BADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCANJ;AAAA,sCARR,EA2BK,CAAC7P,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,WAA5C,KAA4D,CAACY,cAA7D,iBACG;AAAA,gDACI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AACAmI,4BAAAA,KAAK,EAAE,MADP;AAEA8C,4BAAAA,QAAQ,EAAE,SAFV;AAGArC,4BAAAA,UAAU,EAAE,QAHZ;AAIAkG,4BAAAA,EAAE,EAAE,GAJJ;AAKA+B,4BAAAA,SAAS,EAAE,4BALX;AAMAC,4BAAAA,EAAE,EAAE,GANJ;AAOAzB,4BAAAA,SAAS,EAAE,QAPX;AAQA1D,4BAAAA,KAAK,EAAE;AARP,2BADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCADJ,eAeI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AACAxD,4BAAAA,KAAK,EAAE,MADP;AAEA8C,4BAAAA,QAAQ,EAAE,QAFV;AAGArC,4BAAAA,UAAU,EAAE,MAHZ;AAIAyG,4BAAAA,SAAS,EAAE,QAJX;AAKA1D,4BAAAA,KAAK,EAAE;AALP,2BADR;AAAA,oCASK,CAAC,MAAM;AACJ;AACA,kCAAMoF,eAAe,GAAGnL,KAAK,CAACoL,wBAA9B;AACA,kCAAMC,oBAAoB,GACtBrL,KAAK,CAACsL,6BAAN,GAAsC,GAD1C;AAEA,gDACI;AAAA,yCACKrI,YAAY,CAACkI,eAAD,CADjB,eAEI;AACI,gCAAA,KAAK,EAAE;AACH9F,kCAAAA,QAAQ,EAAE,QADP;AAEHrC,kCAAAA,UAAU,EAAE,QAFT;AAGHmD,kCAAAA,UAAU,EAAE;AAHT,iCADX;AAAA,gDAOMkF,oBAAoB,CAAC7B,OAArB,CAA6B,CAA7B,CAPN;AAAA;AAAA;AAAA;AAAA;AAAA,sCAFJ;AAAA,4CADJ;AAcH,2BAnBA;AATL;AAAA;AAAA;AAAA;AAAA,gCAfJ;AAAA,sCA5BR;AAAA;AAAA;AAAA;AAAA;AAAA,4BADJ,eA8EI,QAAC,GAAD;AAAK,sBAAA,EAAE,EAAE;AAAE5G,wBAAAA,OAAO,EAAE,MAAX;AAAmBC,wBAAAA,aAAa,EAAE,QAAlC;AAA4CiI,wBAAAA,CAAC,EAAE;AAA/C,uBAAT;AAAA,8CACI,QAAC,UAAD;AAAY,wBAAA,EAAE,EAAE;AAAEvI,0BAAAA,KAAK,EAAE,MAAT;AAAiB8C,0BAAAA,QAAQ,EAAE,SAA3B;AAAsCrC,0BAAAA,UAAU,EAAE;AAAlD,yBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BADJ,eAII,QAAC,UAAD;AAAY,wBAAA,EAAE,EAAE;AAAET,0BAAAA,KAAK,EAAE,MAAT;AAAiB8C,0BAAAA,QAAQ,EAAE,SAA3B;AAAsCrC,0BAAAA,UAAU,EAAE;AAAlD,yBAAhB;AAAA,kCACKhD,KAAK,CAACuL;AADX;AAAA;AAAA;AAAA;AAAA,8BAJJ,EAOKnR,cAAc,IAAI2P,YAAlB,iBACG;AAAA,gDACI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AAAExH,4BAAAA,KAAK,EAAE,0BAAT;AAAqC8C,4BAAAA,QAAQ,EAAE,QAA/C;AAAyD6D,4BAAAA,EAAE,EAAE;AAA7D,2BADR;AAAA,oCAGKa,YAAY,CAACwB;AAHlB;AAAA;AAAA;AAAA;AAAA,gCADJ,eAMI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AACAhJ,4BAAAA,KAAK,EAAE,0BADP;AAEA8C,4BAAAA,QAAQ,EAAE,SAFV;AAGAgE,4BAAAA,SAAS,EAAE;AAHX,2BADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCANJ;AAAA,sCARR;AAAA;AAAA;AAAA;AAAA;AAAA,4BA9EJ;AAAA,kCADA,gBA2GA;AAAA,4CACI,QAAC,GAAD;AAAK,sBAAA,EAAE,EAAE;AAAEzG,wBAAAA,OAAO,EAAE,MAAX;AAAmBC,wBAAAA,aAAa,EAAE,QAAlC;AAA4CiI,wBAAAA,CAAC,EAAE;AAA/C,uBAAT;AAAA,8CACI,QAAC,UAAD;AAAY,wBAAA,EAAE,EAAE;AAAEvI,0BAAAA,KAAK,EAAE,MAAT;AAAiB8C,0BAAAA,QAAQ,EAAE,SAA3B;AAAsCrC,0BAAAA,UAAU,EAAE;AAAlD,yBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BADJ,eAII,QAAC,UAAD;AAAY,wBAAA,EAAE,EAAE;AAAET,0BAAAA,KAAK,EAAE,MAAT;AAAiB8C,0BAAAA,QAAQ,EAAE,SAA3B;AAAsCrC,0BAAAA,UAAU,EAAE;AAAlD,yBAAhB;AAAA,kCACKC,YAAY,CAACjD,KAAK,CAACgL,uBAAP;AADjB;AAAA;AAAA;AAAA;AAAA,8BAJJ,EAOK5Q,cAAc,IAAI2P,YAAlB,iBACG;AAAA,gDACI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AAAExH,4BAAAA,KAAK,EAAE,0BAAT;AAAqC8C,4BAAAA,QAAQ,EAAE,QAA/C;AAAyD6D,4BAAAA,EAAE,EAAE;AAA7D,2BADR;AAAA,oCAGKjG,YAAY,CAAC8G,YAAY,CAACiB,uBAAd;AAHjB;AAAA;AAAA;AAAA;AAAA,gCADJ,eAMI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AACAzI,4BAAAA,KAAK,EAAE,0BADP;AAEA8C,4BAAAA,QAAQ,EAAE,SAFV;AAGAgE,4BAAAA,SAAS,EAAE;AAHX,2BADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCANJ;AAAA,sCARR,EA2BK,CAAC7P,WAAW,KAAK,MAAhB,IAA0BA,WAAW,KAAK,UAA3C,kBACG;AAAA,gDACI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AACA+I,4BAAAA,KAAK,EAAE,MADP;AAEA8C,4BAAAA,QAAQ,EAAE,SAFV;AAGArC,4BAAAA,UAAU,EAAE,QAHZ;AAIAkG,4BAAAA,EAAE,EAAE,GAJJ;AAKA+B,4BAAAA,SAAS,EAAE,4BALX;AAMAC,4BAAAA,EAAE,EAAE,GANJ;AAOAzB,4BAAAA,SAAS,EAAE,QAPX;AAQA1D,4BAAAA,KAAK,EAAE;AARP,2BADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCADJ,eAeI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AACAxD,4BAAAA,KAAK,EAAE,MADP;AAEA8C,4BAAAA,QAAQ,EAAE,QAFV;AAGArC,4BAAAA,UAAU,EAAE,MAHZ;AAIAyG,4BAAAA,SAAS,EAAE,QAJX;AAKA1D,4BAAAA,KAAK,EAAE;AALP,2BADR;AAAA,oCASK,CAAC,MAAM;AACJ,kCAAMyF,UAAU,GAAGxD,mBAAmB,CAAChI,KAAD,CAAtC;AACA,gDACI;AAAA,yCACKiD,YAAY,CAACuI,UAAU,CAACvD,aAAZ,CADjB,eAEI;AACI,gCAAA,KAAK,EAAE;AACH5C,kCAAAA,QAAQ,EAAE,QADP;AAEHrC,kCAAAA,UAAU,EAAE,QAFT;AAGHmD,kCAAAA,UAAU,EAAE;AAHT,iCADX;AAAA,gDAOMqF,UAAU,CAACtD,mBAAX,CAA+BsB,OAA/B,CAAuC,CAAvC,CAPN;AAAA;AAAA;AAAA;AAAA;AAAA,sCAFJ;AAAA,4CADJ;AAcH,2BAhBA;AATL;AAAA;AAAA;AAAA;AAAA,gCAfJ;AAAA,sCA5BR;AAAA;AAAA;AAAA;AAAA;AAAA,4BADJ,eA0EI,QAAC,GAAD;AAAK,sBAAA,EAAE,EAAE;AAAE5G,wBAAAA,OAAO,EAAE,MAAX;AAAmBC,wBAAAA,aAAa,EAAE,QAAlC;AAA4CiI,wBAAAA,CAAC,EAAE;AAA/C,uBAAT;AAAA,8CACI,QAAC,UAAD;AAAY,wBAAA,EAAE,EAAE;AAAEvI,0BAAAA,KAAK,EAAE,MAAT;AAAiB8C,0BAAAA,QAAQ,EAAE,SAA3B;AAAsCrC,0BAAAA,UAAU,EAAE;AAAlD,yBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BADJ,eAII,QAAC,UAAD;AAAY,wBAAA,EAAE,EAAE;AAAET,0BAAAA,KAAK,EAAE,MAAT;AAAiB8C,0BAAAA,QAAQ,EAAE,SAA3B;AAAsCrC,0BAAAA,UAAU,EAAE;AAAlD,yBAAhB;AAAA,kCACKhD,KAAK,CAACuL;AADX;AAAA;AAAA;AAAA;AAAA,8BAJJ,EAOKnR,cAAc,IAAI2P,YAAlB,iBACG;AAAA,gDACI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AAAExH,4BAAAA,KAAK,EAAE,0BAAT;AAAqC8C,4BAAAA,QAAQ,EAAE,QAA/C;AAAyD6D,4BAAAA,EAAE,EAAE;AAA7D,2BADR;AAAA,oCAGKlJ,KAAK,CAACuL;AAHX;AAAA;AAAA;AAAA;AAAA,gCADJ,eAMI,QAAC,UAAD;AACI,0BAAA,EAAE,EAAE;AACAhJ,4BAAAA,KAAK,EAAE,0BADP;AAEA8C,4BAAAA,QAAQ,EAAE,SAFV;AAGAgE,4BAAAA,SAAS,EAAE;AAHX,2BADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCANJ;AAAA,sCARR;AAAA;AAAA;AAAA;AAAA;AAAA,4BA1EJ;AAAA;AA3KR;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,sBA9OJ;AAAA;AAAA;AAAA;AAAA;AAAA,oBANJ,eA0gBI,QAAC,eAAD;AAAA,qCACI,QAAC,aAAD;AAAe,gBAAA,OAAO,EAAE,MAAM1H,kBAAkB,CAAC3B,KAAK,CAAC6B,kBAAP,CAAhD;AAA4E,gBAAA,SAAS,eAAE;AAAA;AAAA;AAAA;AAAA,wBAAvF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,oBA1gBJ;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ,WAAU7B,KAAK,CAACmK,gBAAhB;AAAA;AAAA;AAAA;AAAA,gBADJ;AAohBH,OA9jBA;AADL;AAAA;AAAA;AAAA;AAAA,YAlSJ,eAm2BI,QAAC,YAAD;AAAA,8BACI,QAAC,WAAD;AAAA,gCACI,QAAC,SAAD;AAAW,UAAA,KAAK,EAAE9U,kBAAkB,CAACoW;AAArC;AAAA;AAAA;AAAA;AAAA,gBADJ,eAEI,QAAC,UAAD;AAAY,UAAA,OAAO,EAAC,OAApB;AAAA,6BAAiCjT,uBAAuB,CAACI,GAAzD;AAAA;AAAA;AAAA;AAAA;AAAA,gBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,cADJ,eAKI,QAAC,WAAD;AAAA,gCACI,QAAC,SAAD;AAAW,UAAA,KAAK,EAAEvD,kBAAkB,CAACsD;AAArC;AAAA;AAAA;AAAA;AAAA,gBADJ,eAEI,QAAC,UAAD;AAAY,UAAA,OAAO,EAAC,OAApB;AAAA,qBACKH,uBAAuB,CAACI,GAAxB,GAA8B,CADnC,UAC0CJ,uBAAuB,CAACG,MADlE;AAAA;AAAA;AAAA;AAAA;AAAA,gBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,cALJ,eAWI,QAAC,WAAD;AAAA,gCACI,QAAC,SAAD;AAAW,UAAA,KAAK,EAAEtD,kBAAkB,CAACqW;AAArC;AAAA;AAAA;AAAA;AAAA,gBADJ,eAEI,QAAC,UAAD;AAAY,UAAA,OAAO,EAAC,OAApB;AAAA,qBACKlT,uBAAuB,CAACG,MAAxB,GAAiC,CADtC,UAC6CH,uBAAuB,CAACC,KAAxB,GAAgC,CAD7E;AAAA;AAAA;AAAA;AAAA;AAAA,gBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,cAXJ,eAiBI,QAAC,WAAD;AAAA,gCACI,QAAC,SAAD;AAAW,UAAA,KAAK,EAAEpD,kBAAkB,CAACsW;AAArC;AAAA;AAAA;AAAA;AAAA,gBADJ,eAEI,QAAC,UAAD;AAAY,UAAA,OAAO,EAAC,OAApB;AAAA,qBAA6BnT,uBAAuB,CAACC,KAArD;AAAA;AAAA;AAAA;AAAA;AAAA,gBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,cAjBJ;AAAA;AAAA;AAAA;AAAA;AAAA,YAn2BJ,eAy3BI,QAAC,eAAD;AACI,MAAA,IAAI,EAAEqB,mBADV;AAEI,MAAA,OAAO,EAAE,MAAMC,sBAAsB,CAAC,KAAD,CAFzC;AAGI,MAAA,SAAS,EAAEC,aAHf;AAII,MAAA,SAAS,EAAEE,iBAJf;AAKI,MAAA,WAAW,EAAEV;AALjB;AAAA;AAAA;AAAA;AAAA,YAz3BJ,eAi4BI,QAAC,IAAD;AACI,MAAA,EAAE,EAAC,cADP;AAEI,MAAA,QAAQ,EAAEoB,iBAFd;AAGI,MAAA,IAAI,EAAEgR,OAAO,CAAChR,iBAAD,CAHjB;AAII,MAAA,OAAO,EAAEkD,sBAJb;AAKI,MAAA,YAAY,EAAE;AACV+N,QAAAA,QAAQ,EAAE,QADA;AAEVC,QAAAA,UAAU,EAAE;AAFF,OALlB;AASI,MAAA,eAAe,EAAE;AACbD,QAAAA,QAAQ,EAAE,KADG;AAEbC,QAAAA,UAAU,EAAE;AAFC,OATrB;AAaI,MAAA,UAAU,EAAE;AACRC,QAAAA,SAAS,EAAE,CADH;AAERC,QAAAA,EAAE,EAAE;AAAEtH,UAAAA,YAAY,EAAE;AAAhB;AAFI,OAbhB;AAiBI,MAAA,iBAAiB,MAjBrB;AAkBI,MAAA,oBAAoB,MAlBxB;AAmBI,MAAA,WAAW,EAAE,KAnBjB;AAAA,8BAqBI,QAAC,QAAD;AAAU,QAAA,OAAO,EAAE,MAAM3G,wBAAwB,CAAC,MAAD,EAAS,CAAT,CAAjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cArBJ,eAsBI,QAAC,QAAD;AAAU,QAAA,OAAO,EAAE,MAAMA,wBAAwB,CAAC,MAAD,EAAS,CAAT,CAAjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAtBJ,eAuBI,QAAC,QAAD;AAAU,QAAA,OAAO,EAAE,MAAMA,wBAAwB,CAAC,MAAD,EAAS,EAAT,CAAjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAvBJ,eAwBI,QAAC,QAAD;AAAU,QAAA,OAAO,EAAE,MAAMA,wBAAwB,CAAC,MAAD,EAAS,EAAT,CAAjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAxBJ,eAyBI,QAAC,QAAD;AAAU,QAAA,OAAO,EAAE,MAAMA,wBAAwB,CAAC,QAAD,EAAW,CAAX,CAAjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAzBJ,eA0BI,QAAC,QAAD;AAAU,QAAA,OAAO,EAAE,MAAMA,wBAAwB,CAAC,QAAD,EAAW,CAAX,CAAjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cA1BJ,eA2BI,QAAC,QAAD;AAAU,QAAA,OAAO,EAAE,MAAMA,wBAAwB,CAAC,QAAD,EAAW,CAAX,CAAjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cA3BJ,eA4BI,QAAC,QAAD;AAAU,QAAA,OAAO,EAAE,MAAMA,wBAAwB,CAAC,OAAD,EAAU,CAAV,CAAjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cA5BJ,eA6BI,QAAC,QAAD;AAAU,QAAA,OAAO,EAAE,MAAMA,wBAAwB,CAAC,OAAD,EAAU,CAAV,CAAjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cA7BJ,eA8BI,QAAC,QAAD;AAAU,QAAA,OAAO,EAAE,MAAMA,wBAAwB,CAAC,OAAD,EAAU,CAAV,CAAjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cA9BJ,eA+BI,QAAC,QAAD;AAAU,QAAA,OAAO,EAAE,MAAMA,wBAAwB,CAAC,QAAD,EAAW,CAAX,CAAjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cA/BJ;AAAA;AAAA;AAAA;AAAA;AAAA,YAj4BJ;AAAA;AAAA;AAAA;AAAA;AAAA,UADJ;AAq6BH,CApjED;;GAAMlF,U;;KAAAA,U;AAqjEN,eAAeA,UAAf;AACA;;AAA0B;;AAAqB;;AAAoB;;AAAC,SAASoT,KAAT,GAAgB;AAAC,MAAG;AAAC,WAAO,CAAC,GAAEC,IAAH,EAAS,2BAAT,KAAyC,CAAC,GAAEA,IAAH,EAAS,onuCAAT,CAAhD;AAAgruC,GAApruC,CAAoruC,OAAMC,CAAN,EAAQ,CAAE;AAAC;;AAAA;AAAC;;AAA0B,SAASC,KAAT;AAAe;AAAgBC,CAA/B,EAAsD;AAAA,oCAAFC,CAAE;AAAFA,IAAAA,CAAE;AAAA;;AAAC,MAAG;AAACL,IAAAA,KAAK,GAAGM,UAAR,CAAmBF,CAAnB,EAAsBC,CAAtB;AAA0B,GAA9B,CAA8B,OAAMH,CAAN,EAAQ,CAAE;;AAAC,SAAOG,CAAP;AAAS;;AAAA;AAAC;;AAA0B,SAASE,KAAT;AAAe;AAAgBH,CAA/B,EAAsD;AAAA,qCAAFC,CAAE;AAAFA,IAAAA,CAAE;AAAA;;AAAC,MAAG;AAACL,IAAAA,KAAK,GAAGQ,YAAR,CAAqBJ,CAArB,EAAwBC,CAAxB;AAA4B,GAAhC,CAAgC,OAAMH,CAAN,EAAQ,CAAE;;AAAC,SAAOG,CAAP;AAAS;;AAAA;AAAC;;AAA0B,SAAS7L,KAAT;AAAe;AAAgB4L,CAA/B,EAAsD;AAAA,qCAAFC,CAAE;AAAFA,IAAAA,CAAE;AAAA;;AAAC,MAAG;AAACL,IAAAA,KAAK,GAAGS,YAAR,CAAqBL,CAArB,EAAwBC,CAAxB;AAA4B,GAAhC,CAAgC,OAAMH,CAAN,EAAQ,CAAE;;AAAC,SAAOG,CAAP;AAAS;;AAAA;AAAC;;AAA0B,SAASK,KAAT;AAAe;AAAgBL,CAA/B,EAAiC;AAAC,MAAG;AAACL,IAAAA,KAAK,GAAGW,WAAR,CAAoBN,CAApB;AAAwB,GAA5B,CAA4B,OAAMH,CAAN,EAAQ,CAAE;;AAAC,SAAOG,CAAP;AAAU;;AAAA;AAAC;;AAA0B,SAASO,KAAT;AAAe;AAAgBP,CAA/B;AAAkC;AAAgBD,CAAlD,EAAoD;AAAC,MAAG;AAACJ,IAAAA,KAAK,GAAGa,cAAR,CAAuBR,CAAvB,EAA0BD,CAA1B;AAA8B,GAAlC,CAAkC,OAAMF,CAAN,EAAQ,CAAE;;AAAC,SAAOG,CAAP;AAAU;;AAAA;AAAC", "sourcesContent": ["/* eslint-disable */\r\nimport React, { useState, useEffect } from 'react';\r\nimport {\r\n    Box,\r\n    Typography,\r\n    styled,\r\n    CircularProgress,\r\n    Backdrop,\r\n    Button,\r\n    TextField,\r\n    MenuItem,\r\n    IconButton,\r\n    Menu,\r\n    Divider,\r\n    Switch,\r\n    FormControlLabel,\r\n    Stack\r\n} from '@mui/material';\r\nimport { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\r\nimport { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';\r\nimport { es } from 'date-fns/locale';\r\nimport { startOfMonth, endOfMonth, startOfWeek, endOfWeek, subDays, subMonths, isSameDay, format, getDaysInMonth, addDays } from 'date-fns';\r\nimport { getSalesDashboardGoalsPromise } from 'services/salesDashboard';\r\nimport PredictionModal from './PredictionModal';\r\nimport {\r\n    CalendarToday as CalendarTodayIcon,\r\n    Schedule as ScheduleIcon,\r\n    Clear as ClearIcon,\r\n    CompareArrows as CompareIcon,\r\n    ExpandMore as ExpandMoreIcon\r\n} from '@mui/icons-material';\r\nimport {\r\n    updateGlobalPercentages,\r\n    trafficLightColors,\r\n    LegendGroup,\r\n    getStoreColor,\r\n    getStatusLabel,\r\n    MetricPercentage,\r\n    StoreGrid,\r\n    StoreName,\r\n    MetricCard,\r\n    MetricHeader,\r\n    MetricTitle,\r\n    MetricDate,\r\n    MetricContent,\r\n    MetricInfo,\r\n    ChartContainer,\r\n    MetricValues,\r\n    MetricValue,\r\n    ChartLegend,\r\n    LegendItem,\r\n    LegendDot,\r\n    TotalCard,\r\n    TotalHeader,\r\n    TotalTitle,\r\n    TotalTitleIcon,\r\n    TotalStats,\r\n    StatItem,\r\n    StatLabel,\r\n    StatValue,\r\n    ProgressBarContainer,\r\n    ProgressLabel,\r\n    StyledLinearProgress,\r\n    PredictButton,\r\n    ButtonContainer,\r\n    UpdatedAt,\r\n    TotalSection,\r\n    FooterLegend\r\n} from './summary/components';\r\nimport { RadialBarChart, RadialBar, ResponsiveContainer, PolarAngleAxis, BarChart, Bar, XAxis, YAxis, Tooltip } from 'recharts';\r\nimport DateRangeInput from './DateRangeInput';\r\nimport { formatFriendlyDate, getFirstDayOfCurrentMonth, getFirstDayOfGivenMonth, getTodayDate, getTodayDateTime, isFullMonth } from 'utils/dates';\r\n\r\nlet globalLegendPercentages = {\r\n    green: 80,\r\n    yellow: 51,\r\n    orange: 50,\r\n    red: 25\r\n};\r\n\r\nconst SummaryTab = () => {\r\n    const [data, setData] = useState([]);\r\n    const [compareData, setCompareData] = useState([]); // Datos del periodo para comparar\r\n    const [loading, setLoading] = useState(true);\r\n    const [compareLoading, setCompareLoading] = useState(false);\r\n    const [apiMode, setApiMode] = useState(0); // 0 para la api oficial, 1 para api url completa\r\n    const [selectedTab, setSelectedTab] = useState('day'); // 'day', 'month', 'date'\r\n    const [dateRange, setDateRange] = useState([getTodayDate(), getTodayDate()]); // [startDate, endDate]\r\n    const [dateRangePopperOpen, setDateRangePopperOpen] = useState(false);\r\n    const [predictionModalOpen, setPredictionModalOpen] = useState(false);\r\n    const [selectedStore, setSelectedStore] = useState(null);\r\n    const [selectedStoreData, setSelectedStoreData] = useState(null);\r\n    const [showComparison, setShowComparison] = useState(false); // Controla si se muestra la comparación\r\n    const [comparePeriod, setComparePeriod] = useState(1); // Número de periodos hacia atrás para comparar\r\n    const [customCompareDate, setCustomCompareDate] = useState(null);\r\n    const [anchorEl, setAnchorEl] = useState(null);\r\n    const [compareMenuAnchor, setCompareMenuAnchor] = useState(null);\r\n    const [originalTab, setOriginalTab] = useState('day'); // Guardar la pestaña original antes de la comparación\r\n    const [compareTicketCount, setCompareTicketCount] = useState(0);\r\n\r\n    // Estado para los menús personalizados\r\n    const [showDateRangeMenu, setShowDateRangeMenu] = useState(false);\r\n    const [showCalendarMenu, setShowCalendarMenu] = useState(false);\r\n\r\n    // Función para comprobar si dos fechas son el mismo día\r\n    const isSameDay = (date1, date2) => {\r\n        if (!date1 || !date2) return false;\r\n        return date1.getDate() === date2.getDate() && date1.getMonth() === date2.getMonth() && date1.getFullYear() === date2.getFullYear();\r\n    };\r\n\r\n    // Función para determinar si el periodo actual permite comparación\r\n    const isComparisonAllowed = () => {\r\n        const today = getTodayDate();\r\n\r\n        // \"Hoy\" no permite comparación\r\n        if (selectedTab === 'day') return false;\r\n\r\n        // \"Avance del mes\" no permite comparación\r\n        if (selectedTab === 'period') return false;\r\n\r\n        // \"Mes\" solo permite comparación si NO es el mes actual\r\n        if (selectedTab === 'month' || selectedTab === 'thisMonth') {\r\n            return true;\r\n            const isCurrentMonth = today.getMonth() === dateRange[0].getMonth() && today.getFullYear() === dateRange[0].getFullYear();\r\n            return !isCurrentMonth;\r\n        }\r\n\r\n        // Fecha personalizada solo permite comparación si NO es hoy\r\n        if (selectedTab === 'date') {\r\n            return !isSameDay(dateRange[0], today);\r\n        }\r\n\r\n        // El resto de periodos (ayer, mes pasado, etc.) sí permiten comparación\r\n        return true;\r\n    };\r\n\r\n    // Función para formatear fechas correctamente para Perú (UTC-5)\r\n    const formatPeruDate = (date) => {\r\n        if (!date) return '';\r\n        // Creamos una nueva fecha para no modificar la original\r\n        const d = new Date(date);\r\n\r\n        // Obtenemos año, mes y día en formato peruano\r\n        const year = d.getFullYear();\r\n        const month = String(d.getMonth() + 1).padStart(2, '0');\r\n        const day = String(d.getDate()).padStart(2, '0');\r\n\r\n        // Retornamos en formato YYYY-MM-DD\r\n        return `${year}-${month}-${day}`;\r\n    };\r\n\r\n    // Función para obtener la fecha actual considerando horario de Perú\r\n    const getPeruDate = () => {\r\n        // Crear una nueva fecha que represente el momento actual\r\n        const now = getTodayDateTime();\r\n\r\n        // Ajustar a la zona horaria de Perú (UTC-5)\r\n        // Esto asegura que la fecha sea correcta para Perú\r\n        const peruDate = new Date(now.toLocaleString('en-US', { timeZone: 'America/Lima' }));\r\n\r\n        return peruDate;\r\n    };\r\n\r\n    // Función para calcular el porcentaje de avance de ventas\r\n    const calculatePercentage = (salesOrStore, goal = null) => {\r\n        // Si se pasa un objeto store completo\r\n        if (typeof salesOrStore === 'object' && salesOrStore !== null) {\r\n            const storeData = salesOrStore;\r\n            if (!storeData) return 0;\r\n\r\n            let salesValue, goalValue;\r\n\r\n            if (selectedTab === 'day' || selectedTab === 'yesterday') {\r\n                salesValue = parseFloat(storeData.today_sales || 0);\r\n                goalValue = parseFloat(storeData.today_goal || 0);\r\n            } else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth') {\r\n                salesValue = parseFloat(storeData.progress_sales || 0);\r\n                goalValue = parseFloat(storeData.period_goal || 0);\r\n            } else {\r\n                // Para semana actual o semana pasada\r\n                salesValue = parseFloat(storeData.progress_sales || 0);\r\n                goalValue = parseFloat(storeData.today_goal || 0) * totalDays;\r\n            }\r\n\r\n            if (goalValue === 0) return 0;\r\n            return (salesValue / goalValue) * 100;\r\n        }\r\n        // Si se pasan valores numéricos directamente\r\n        else {\r\n            const sales = parseFloat(salesOrStore || 0);\r\n            const targetGoal = parseFloat(goal || 0);\r\n            if (!targetGoal || targetGoal === 0) return 0;\r\n            return (sales / targetGoal) * 100;\r\n        }\r\n    };\r\n\r\n    // Nueva función para cerrar el menú de opciones de rango de fechas\r\n    const handleDateRangeMenuClose = () => {\r\n        setDateRangePopperOpen(false);\r\n    };\r\n\r\n    // Maneja la apertura del menú de comparación\r\n    const handleCompareMenuOpen = (event) => {\r\n        setCompareMenuAnchor(event.currentTarget);\r\n    };\r\n\r\n    // Maneja el cierre del menú de comparación\r\n    const handleCompareMenuClose = () => {\r\n        setCompareMenuAnchor(null);\r\n    };\r\n\r\n    // Maneja la selección de una opción de comparación rápida\r\n    const handleQuickCompareSelect = (periodType, value) => {\r\n        setComparePeriod(value);\r\n        // Calcular la fecha correspondiente basada en la selección\r\n        const today = getPeruDate();\r\n        let newDate = new Date(today);\r\n\r\n        if (periodType === 'days') {\r\n            newDate.setDate(today.getDate() - value);\r\n        } else if (periodType === 'months') {\r\n            newDate.setMonth(today.getMonth() - value);\r\n        } else if (periodType === 'years') {\r\n            newDate.setFullYear(today.getFullYear() - value);\r\n        }\r\n\r\n        // Ajustar la fecha según el tipo de vista seleccionada\r\n        if (selectedTab === 'month') {\r\n            // Para vista mensual, asegurarse de que sea el primer día del mes\r\n            newDate = new Date(newDate.getFullYear(), newDate.getMonth(), 1);\r\n        }\r\n\r\n        setCustomCompareDate(newDate);\r\n        handleCompareMenuClose();\r\n\r\n        if (showComparison) {\r\n            fetchComparisonData(value, newDate);\r\n        }\r\n    };\r\n\r\n    // Maneja la selección de fecha personalizada\r\n    const handleDatePickerChange = (newDate) => {\r\n        if (newDate) {\r\n            setCustomCompareDate(newDate);\r\n            // Calculamos el offset aproximado en días para mantener la compatibilidad\r\n            const today = getPeruDate();\r\n            const diffTime = Math.abs(today - newDate);\r\n            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n            setComparePeriod(diffDays);\r\n\r\n            if (showComparison) {\r\n                fetchComparisonData(diffDays, newDate);\r\n            }\r\n        }\r\n        setAnchorEl(null); // cerrar el selector de fecha\r\n    };\r\n\r\n    // Actualizar fetchComparisonData para manejar rangos de fechas\r\n    const fetchComparisonData = async (days, specificDate, endDate = null) => {\r\n        setCompareLoading(true);\r\n        try {\r\n            const params = {};\r\n            const today = getPeruDate();\r\n\r\n            if (specificDate) {\r\n                // Si se proporciona una fecha específica, la usamos directamente\r\n                if (selectedTab === 'day') {\r\n                    params.date = formatPeruDate(specificDate);\r\n                } else if (selectedTab === 'month') {\r\n                    //params.month = (specificDate.getMonth() + 1).toString();\r\n                    //params.year = specificDate.getFullYear().toString();\r\n                    // Usar rango de fechas en lugar de month/year para mayor precisión en la comparación\r\n                    if (specificDate && endDate) {\r\n                        // Si se proporcionan fechas específicas de inicio y fin para la comparación\r\n                        params.date_from = formatPeruDate(specificDate);\r\n                        params.date_to = formatPeruDate(endDate);\r\n                    } else if (specificDate) {\r\n                        // Si solo se proporciona una fecha, calcular el rango del mes anterior\r\n                        const startOfMonth = new Date(specificDate.getFullYear(), specificDate.getMonth(), 1);\r\n                        const daysInMonth = new Date(specificDate.getFullYear(), specificDate.getMonth() + 1, 0).getDate();\r\n                        const endOfMonth = new Date(\r\n                            specificDate.getFullYear(),\r\n                            specificDate.getMonth(),\r\n                            Math.min(daysInMonth, dateRange[1].getDate())\r\n                        );\r\n\r\n                        params.date_from = formatPeruDate(startOfMonth);\r\n                        params.date_to = formatPeruDate(endOfMonth);\r\n                    } else {\r\n                        // Si no se proporciona fecha, usar los mismos rangos que generados en generateSmartComparisonPeriod\r\n                        const [compStart, compEnd] = generateSmartComparisonPeriod();\r\n                        params.date_from = formatPeruDate(compStart);\r\n                        params.date_to = formatPeruDate(compEnd);\r\n                    }\r\n                } else {\r\n                    // Para rango de fechas, usar fecha_desde y fecha_hasta\r\n                    const compareEndDate = endDate || specificDate;\r\n                    params.date_from = formatPeruDate(specificDate);\r\n                    params.date_to = formatPeruDate(compareEndDate);\r\n                }\r\n            } else {\r\n                // Cálculo basado en días hacia atrás\r\n                const compareDate = subDays(today, days);\r\n                if (selectedTab === 'day') {\r\n                    params.date = formatPeruDate(compareDate);\r\n                } else if (selectedTab === 'month') {\r\n                    params.month = (compareDate.getMonth() + 1).toString();\r\n                    params.year = compareDate.getFullYear().toString();\r\n                } else {\r\n                    // Para rango de fechas, calcular un rango equivalente en el pasado\r\n                    const diffTime = Math.abs(dateRange[1] - dateRange[0]);\r\n                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n\r\n                    const compareStartDate = subDays(dateRange[0], days);\r\n                    const compareEndDate = subDays(dateRange[1], days);\r\n\r\n                    params.date_from = formatPeruDate(compareStartDate);\r\n                    params.date_to = formatPeruDate(compareEndDate);\r\n                }\r\n            }\r\n\r\n            const response = await getSalesDashboardGoalsPromise(params);\r\n            setCompareData(response.data);\r\n\r\n            // Calcular la cantidad de tickets para la comparación\r\n            const compareTickets = response.data.reduce((acc, store) => acc + parseInt(store.today_tickets || 0), 0);\r\n            setCompareTicketCount(compareTickets);\r\n\r\n            // Calcular totales para el periodo de comparación\r\n            const totalResults = calculateTotals(response.data);\r\n            // setCompareTotals(totalResults);\r\n\r\n            // Calcular porcentaje total de cumplimiento para comparación\r\n            let comparePercentageTotal;\r\n            if (selectedTab === 'day' || selectedTab === 'yesterday') {\r\n                comparePercentageTotal = calculatePercentage(totalResults.today_sales, totalResults.today_goal);\r\n            } else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth') {\r\n                comparePercentageTotal = calculatePercentage(totalResults.sale_progress, totalResults.period_goal);\r\n            } else {\r\n                comparePercentageTotal = calculatePercentage(totalResults.sale_progress, totalResults.today_goal * totalDays);\r\n            }\r\n            // setCompareTotalPercentage(comparePercentageTotal);\r\n        } catch (error) {\r\n            /* eslint-disable */console.error(...oo_tx(`1047047346_344_12_344_67_11`,'Error fetching comparison data:', error));\r\n        } finally {\r\n            setCompareLoading(false);\r\n        }\r\n    };\r\n\r\n    // Función para alternar la vista de comparación\r\n    const toggleComparison = () => {\r\n        const newValue = !showComparison;\r\n        setShowComparison(newValue);\r\n\r\n        if (newValue) {\r\n            // Guardar la pestaña original\r\n            setOriginalTab(selectedTab);\r\n\r\n            // Generar periodo de comparación inteligente\r\n            const [comparisonStartDate, comparisonEndDate, diffDays] = generateSmartComparisonPeriod();\r\n\r\n            // Usar la fecha de inicio como fecha de comparación principal\r\n            setCustomCompareDate(comparisonStartDate);\r\n            setComparePeriod(diffDays);\r\n\r\n            // Obtener datos de comparación basados en el periodo generado\r\n            fetchComparisonData(diffDays, comparisonStartDate, comparisonEndDate);\r\n        } else {\r\n            // Limpiar comparación\r\n            setCustomCompareDate(null);\r\n            setCompareData([]);\r\n        }\r\n    };\r\n\r\n    const desactivateComparison = () => {\r\n        setShowComparison(false);\r\n        setCustomCompareDate(null);\r\n        setComparePeriod(0);\r\n        setCompareData([]);\r\n    }\r\n\r\n    useEffect(() => {\r\n        const fetchData = async () => {\r\n            setLoading(true); // Activar loading al inicio\r\n            try {\r\n                let response;\r\n                if (apiMode === 0) {\r\n                    // Crear un objeto con los parámetros según la pestaña seleccionada\r\n                    const params = {};\r\n\r\n                    // Obtener la fecha actual para usar en las consultas (usando zona horaria de Perú)\r\n                    const today = getPeruDate();\r\n                    const formattedDate = formatPeruDate(today); // YYYY-MM-DD con zona horaria de Perú\r\n\r\n                    if (selectedTab === 'day') {\r\n                        // Para compatibilidad con modo 'día', usar solo una fecha\r\n                        // Si es un rango del mismo día, usar ese día, sino usar la fecha actual\r\n                        if (dateRange[0] && dateRange[1] && isSameDay(dateRange[0], dateRange[1])) {\r\n                            params.date = formatPeruDate(dateRange[0]);\r\n                        } else {\r\n                            params.date = formattedDate;\r\n                        }\r\n                    } else if (selectedTab === 'yesterday') {\r\n                        // Para el caso específico de \"Ayer\"\r\n                        const yesterday = subDays(today, 1);\r\n                        params.date = formatPeruDate(yesterday);\r\n                    } else if (selectedTab === 'week' || selectedTab === 'thisWeek') {\r\n                        // Para el caso de \"Esta semana\" (últimos 7 días)\r\n                        const weekStart = new Date(today);\r\n                        weekStart.setDate(today.getDate() - 6); // 6 días atrás + hoy = 7 días\r\n                        params.date_from = formatPeruDate(weekStart);\r\n                        params.date_to = formatPeruDate(today);\r\n                    } else if (selectedTab === 'lastWeek') {\r\n                        // Para semana pasada (7 días anteriores a la semana actual)\r\n                        const lastWeekStart = new Date(today);\r\n                        lastWeekStart.setDate(today.getDate() - 13); // 7 días antes del inicio de los últimos 7 días\r\n                        const lastWeekEnd = new Date(today);\r\n                        lastWeekEnd.setDate(today.getDate() - 7); // 7 días antes de hoy\r\n                        params.date_from = formatPeruDate(lastWeekStart);\r\n                        params.date_to = formatPeruDate(lastWeekEnd);\r\n                    } else if (selectedTab === 'period' || selectedTab === 'thisPeriod') {\r\n                        // Para este periodo, desde inicio del mes hasta hoy\r\n                        const startOfCurrentMonth = startOfMonth(today);\r\n                        params.date_from = formatPeruDate(startOfCurrentMonth);\r\n                        params.date_to = formatPeruDate(today);\r\n                    } else if (selectedTab === 'month') {\r\n\r\n                        // Usar rango de fechas en lugar de mes/año para mayor precisión\r\n                        if (dateRange[0] && dateRange[1]) {\r\n                            params.date_from = formatPeruDate(dateRange[0]);\r\n                            params.date_to = formatPeruDate(dateRange[1]);\r\n                        } else {\r\n                            // Fallback al mes actual si no hay rango definido\r\n                            params.month = (today.getMonth() + 1).toString();\r\n                            params.year = today.getFullYear().toString();\r\n                        }\r\n                    } else if (selectedTab === 'date') {\r\n                        // Configurar parámetros para consulta por rango de fechas\r\n                        if (dateRange[0] && dateRange[1]) {\r\n                            // Validación adicional para asegurarse de que las fechas estén en el mismo mes/año\r\n                            // si estamos en modo 'date' pero el API requiere fechas del mismo mes\r\n                            if (\r\n                                dateRange[0].getMonth() === dateRange[1].getMonth() &&\r\n                                dateRange[0].getFullYear() === dateRange[1].getFullYear()\r\n                            ) {\r\n                                params.date_from = formatPeruDate(dateRange[0]);\r\n                                params.date_to = formatPeruDate(dateRange[1]);\r\n                            } else {\r\n                                // Si se requieren fechas del mismo mes pero no lo son, ajustar el rango\r\n                                // al último día del mes de la fecha inicial\r\n                                const lastDayOfStartMonth = endOfMonth(dateRange[0]);\r\n                                params.date_from = formatPeruDate(dateRange[0]);\r\n                                params.date_to = formatPeruDate(lastDayOfStartMonth);\r\n                                console.warn(\r\n                                    'Las fechas deben ser del mismo mes para la API. Ajustando fecha final al último día del mes inicial.'\r\n                                );\r\n                            }\r\n                        } else {\r\n                            // Comportamiento por defecto (desde inicio de mes hasta hoy)\r\n                            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\r\n                            params.date_from = formatPeruDate(firstDayOfMonth);\r\n                            params.date_to = formattedDate;\r\n                        }\r\n                    }\r\n\r\n                    // params.business_unit_id = ...;\r\n                    // params.store_id = ...;\r\n                    response = await getSalesDashboardGoalsPromise(params);\r\n                    setData(response.data);\r\n                    if (response.legend_percentages) {\r\n                        updateGlobalPercentages(response.legend_percentages);\r\n                    }\r\n                }\r\n            } catch (error) {\r\n                /* eslint-disable */console.error(...oo_tx(`1047047346_475_16_475_60_11`,'Error fetching data:', error));\r\n                // Si falla la primera API, intentar con la segunda\r\n                if (apiMode === 0) {\r\n                    setApiMode(1);\r\n                }\r\n            } finally {\r\n                // Agregar delay para evitar parpadeos\r\n                setTimeout(() => {\r\n                    setLoading(false);\r\n                }, 500);\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, [apiMode, selectedTab, dateRange]); // Agregar dateRange como dependencia\r\n\r\n    const handlePredictClick = (storeName) => {\r\n        // Encontrar los datos de la tienda seleccionada\r\n        let storeData;\r\n        if (storeName === 'Total MIA') {\r\n            // Para el caso de Total MIA, creamos un objeto con los totales\r\n            if (selectedTab === 'day') {\r\n                storeData = {\r\n                    business_unit_name: 'Total MIA',\r\n                    today_sales: totals.today_sales,\r\n                    today_goal: totals.today_goal,\r\n                    progress_sales: totals.sale_progress, // Mantener para compatibilidad\r\n                    period_goal: totals.period_goal, // Mantener para compatibilidad\r\n                    query_date: data[0]?.query_date,\r\n                    selected_tab: 'day'\r\n                };\r\n            } else if (selectedTab === 'month') {\r\n                storeData = {\r\n                    business_unit_name: 'Total MIA',\r\n                    today_sales: totals.today_sales, // Mantener para compatibilidad\r\n                    today_goal: totals.today_goal, // Mantener para compatibilidad\r\n                    progress_sales: totals.sale_progress,\r\n                    period_goal: totals.period_goal,\r\n                    query_date: data[0]?.query_date,\r\n                    selected_tab: 'month'\r\n                };\r\n            } else {\r\n                // Para el tab 'date', calculamos la meta ajustada por días transcurridos\r\n                storeData = {\r\n                    business_unit_name: 'Total MIA',\r\n                    today_sales: totals.today_sales, // Mantener para compatibilidad\r\n                    today_goal: totals.today_goal, // Meta diaria original\r\n                    progress_sales: totals.sale_progress, // Ventas acumuladas\r\n                    period_goal: totals.period_goal, // Mantener para compatibilidad\r\n                    date_goal: dateTotals.goal, // Meta ajustada por días\r\n                    elapsed_days: elapsedDays, // Días transcurridos\r\n                    query_date: data[0]?.query_date,\r\n                    selected_tab: 'date'\r\n                };\r\n            }\r\n        } else {\r\n            // Para tiendas individuales, buscamos en el array de datos\r\n            const store = data.find((store) => store.business_unit_name === storeName);\r\n\r\n            if (store) {\r\n                if (selectedTab === 'day') {\r\n                    storeData = {\r\n                        ...store,\r\n                        selected_tab: 'day'\r\n                    };\r\n                } else if (selectedTab === 'month') {\r\n                    storeData = {\r\n                        ...store,\r\n                        selected_tab: 'month'\r\n                    };\r\n                } else {\r\n                    // Para el tab 'date', calculamos la meta ajustada por días transcurridos\r\n                    const dateGoal = parseFloat(store.today_goal) * elapsedDays;\r\n                    storeData = {\r\n                        ...store,\r\n                        date_goal: dateGoal, // Meta ajustada por días\r\n                        elapsed_days: elapsedDays, // Días transcurridos\r\n                        selected_tab: 'date'\r\n                    };\r\n                }\r\n            }\r\n        }\r\n\r\n        if (storeData) {\r\n            setSelectedStore(storeName);\r\n            setSelectedStoreData(storeData);\r\n            setPredictionModalOpen(true);\r\n        }\r\n    };\r\n\r\n    if (loading) {\r\n        return (\r\n            <Backdrop\r\n                sx={{\r\n                    color: '#fff',\r\n                    zIndex: (theme) => theme.zIndex.drawer + 1,\r\n                    backgroundColor: 'rgba(255, 255, 255, 0.8)'\r\n                }}\r\n                open={loading}\r\n            >\r\n                <Box\r\n                    sx={{\r\n                        display: 'flex',\r\n                        flexDirection: 'column',\r\n                        alignItems: 'center',\r\n                        gap: 2\r\n                    }}\r\n                >\r\n                    <CircularProgress\r\n                        size={50}\r\n                        thickness={4}\r\n                        sx={{\r\n                            color: '#b3256e'\r\n                        }}\r\n                    />\r\n                    <Typography\r\n                        variant=\"h6\"\r\n                        sx={{\r\n                            color: '#b3256e',\r\n                            fontWeight: 'bold'\r\n                        }}\r\n                    >\r\n                        Cargando datos...\r\n                    </Typography>\r\n                </Box>\r\n            </Backdrop>\r\n        );\r\n    }\r\n\r\n    // Formato para montos con símbolo de moneda\r\n    const formatNumber = (value) => {\r\n        if (value === undefined || value === null) return 'S/ 0.00';\r\n        return `S/ ${parseFloat(value).toLocaleString('es-PE', {\r\n            minimumFractionDigits: 2,\r\n            maximumFractionDigits: 2\r\n        })}`;\r\n    };\r\n\r\n    const formatDate = (dateString) => {\r\n        const date = new Date(dateString);\r\n        const day = date.getDate().toString().padStart(2, '0');\r\n        const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n        const year = date.getFullYear();\r\n        return `${day}/${month}/${year}`;\r\n    };\r\n\r\n    // Cálculo de días transcurridos y días totales para el rango seleccionado\r\n    const calculateDateRangeInfo = () => {\r\n        // Si no hay rango de fechas, devolver valores por defecto\r\n        if (!dateRange[0] || !dateRange[1]) return { elapsedDays: 1, totalDays: 1 };\r\n\r\n        // Función para normalizar una fecha (ponerla a medianoche)\r\n        const normalizeDate = (date) => {\r\n            const d = new Date(date);\r\n            d.setHours(0, 0, 0, 0);\r\n            return d;\r\n        };\r\n\r\n        // Calcular la diferencia en días entre dos fechas normalizadas\r\n        const getDayDifference = (start, end) => {\r\n            const diffTime = Math.abs(end - start);\r\n            return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 para incluir ambos días\r\n        };\r\n\r\n        const today = getPeruDate();\r\n        const normalizedToday = normalizeDate(today);\r\n\r\n        // Determinar el rango según la pestaña seleccionada\r\n        let startDate, endDate, currentDate;\r\n        if (selectedTab === 'day' || selectedTab === 'yesterday') {\r\n            // Para día, el rango es solo un día\r\n            return { elapsedDays: 1, totalDays: 1 };\r\n        } else if (selectedTab === 'week' || selectedTab === 'thisWeek') {\r\n            // Para semana, mostrar los últimos 7 días incluido hoy\r\n            startDate = normalizeDate(new Date(today));\r\n            startDate.setDate(startDate.getDate() - 6); // 6 días atrás + hoy = 7 días\r\n            endDate = normalizeDate(today);\r\n            currentDate = normalizedToday;\r\n        } else if (selectedTab === 'lastWeek') {\r\n            // Para semana pasada, los 7 días anteriores a la semana actual\r\n            startDate = normalizeDate(new Date(today));\r\n            startDate.setDate(startDate.getDate() - 13);\r\n            endDate = normalizeDate(new Date(today));\r\n            endDate.setDate(endDate.getDate() - 7);\r\n            currentDate = endDate; // Ya pasaron todos los días\r\n        } else if (selectedTab === 'period' || selectedTab === 'thisPeriod') {\r\n            // Para este periodo, desde inicio del mes hasta hoy\r\n            startDate = normalizeDate(startOfMonth(today));\r\n            endDate = normalizeDate(today);\r\n            currentDate = normalizedToday;\r\n        } else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth') {\r\n            // Para este mes, desde inicio del mes hasta hoy\r\n            startDate = normalizeDate(startOfMonth(today));\r\n            endDate = normalizeDate(today);\r\n            currentDate = normalizedToday;\r\n        } else if (selectedTab === 'lastMonth') {\r\n            // Para mes pasado, todo el mes anterior\r\n            const lastMonth = subMonths(today, 1);\r\n            startDate = normalizeDate(startOfMonth(lastMonth));\r\n            endDate = normalizeDate(endOfMonth(lastMonth));\r\n            currentDate = endDate; // Ya pasaron todos los días\r\n        } else {\r\n            // Para otros casos o rango personalizado\r\n            startDate = normalizeDate(dateRange[0]);\r\n            endDate = normalizeDate(dateRange[1]);\r\n            currentDate = normalizedToday;\r\n        }\r\n\r\n        // Cálculo de días\r\n        const totalDays = getDayDifference(startDate, endDate);\r\n        let elapsedDays;\r\n        if (currentDate > endDate) {\r\n            // Si la fecha actual es posterior al final del rango, todos los días han transcurrido\r\n            elapsedDays = totalDays;\r\n        } else if (currentDate < startDate) {\r\n            // Si la fecha actual es anterior al inicio del rango, aún no ha transcurrido ningún día\r\n            elapsedDays = 0;\r\n        } else {\r\n            // Si la fecha actual está dentro del rango, calcular los días transcurridos\r\n            elapsedDays = getDayDifference(startDate, currentDate);\r\n        }\r\n\r\n        return { elapsedDays, totalDays };\r\n    };\r\n\r\n    const { elapsedDays, totalDays } = calculateDateRangeInfo();\r\n\r\n    // Calcular totales para tarjetas y gráficos\r\n    const calculateTotals = () => {\r\n        if (!data || data.length === 0)\r\n            return {\r\n                today_sales: 0,\r\n                today_goal: 0,\r\n                period_goal: 0,\r\n                sale_progress: 0,\r\n                today_average_ticket: 0,\r\n                today_tickets: 0\r\n            };\r\n\r\n        return data.reduce(\r\n            (acc, store) => {\r\n                return {\r\n                    today_sales: acc.today_sales + parseFloat(store.today_sales || 0),\r\n                    today_goal: acc.today_goal + parseFloat(store.today_goal || 0),\r\n                    period_goal: acc.period_goal + parseFloat(store.period_goal || 0),\r\n                    sale_progress: acc.sale_progress + parseFloat(store.progress_sales || 0),\r\n                    today_average_ticket: parseFloat(store.today_average_ticket || 0),\r\n                    today_tickets: acc.today_tickets + parseInt(store.today_tickets || 0)\r\n                };\r\n            },\r\n            {\r\n                today_sales: 0,\r\n                today_goal: 0,\r\n                period_goal: 0,\r\n                sale_progress: 0,\r\n                today_average_ticket: 0,\r\n                today_tickets: 0\r\n            }\r\n        );\r\n    };\r\n\r\n    // Calcular totales\r\n    const totals = calculateTotals();\r\n\r\n    // Calcular los totales para el tab de fecha\r\n    const dateTotals = {\r\n        goal: totals.today_goal * totalDays,\r\n        remaining: totals.today_goal * totalDays - totals.sale_progress\r\n    };\r\n\r\n    // Calcular porcentaje de cumplimiento total\r\n    const totalPercentage =\r\n        selectedTab === 'day' || selectedTab === 'yesterday'\r\n            ? calculatePercentage(totals.today_sales, totals.today_goal) // Para hoy/ayer\r\n            : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth'\r\n            ? calculatePercentage(totals.sale_progress, totals.period_goal) // Para cualquier mes\r\n            : calculatePercentage(totals.sale_progress, totals.today_goal * totalDays); // Para semana/rango, meta diaria * total de días\r\n\r\n    const DateSelectorButton = styled(Button)(({ theme }) => ({\r\n        borderColor: '#b3256e',\r\n        color: '#b3256e',\r\n        minWidth: '220px',\r\n        display: 'flex',\r\n        justifyContent: 'space-between',\r\n        '&:hover': {\r\n            borderColor: '#9a1e5c',\r\n            backgroundColor: 'rgba(179, 37, 110, 0.04)'\r\n        },\r\n        boxShadow: '0 2px 4px rgba(0,0,0,0.05)',\r\n        borderRadius: '8px',\r\n        padding: '8px 16px',\r\n        transition: 'all 0.2s ease'\r\n    }));\r\n\r\n    const DateSelectorContainer = styled(Box)(({ theme }) => ({\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        gap: '12px',\r\n        padding: '10px 16px',\r\n        backgroundColor: '#f8f9fa',\r\n        borderRadius: '10px',\r\n        boxShadow: '0 2px 8px rgba(0,0,0,0.05)',\r\n        marginBottom: '16px',\r\n        position: 'relative',\r\n        justifyContent: 'center', // Cambiado de 'flex-end' a 'center'\r\n        flexWrap: 'wrap',\r\n        overflow: 'visible',\r\n        zIndex: 1\r\n    }));\r\n\r\n    const CompareButton = styled(Button)(({ theme, active }) => ({\r\n        backgroundColor: active ? '#b3256e' : 'transparent',\r\n        color: active ? 'white' : '#b3256e',\r\n        borderColor: '#b3256e',\r\n        '&:hover': {\r\n            backgroundColor: active ? '#9a1e5c' : 'rgba(179, 37, 110, 0.04)',\r\n            borderColor: '#9a1e5c'\r\n        },\r\n        fontWeight: active ? 'bold' : 'normal',\r\n        borderRadius: '8px',\r\n        padding: '6px 16px',\r\n        transition: 'all 0.2s ease',\r\n        boxShadow: active ? '0 2px 5px rgba(179, 37, 110, 0.2)' : 'none'\r\n    }));\r\n\r\n    const DateRangeActionButton = styled(Button)(({ theme }) => ({\r\n        padding: '8px 16px',\r\n        fontSize: '1rem',\r\n        textTransform: 'none',\r\n        '&:hover': {\r\n            backgroundColor: 'rgba(179, 37, 110, 0.08)'\r\n        },\r\n        '&.MuiButton-containedPrimary': {\r\n            backgroundColor: '#b3256e',\r\n            '&:hover': {\r\n                backgroundColor: '#9a1e5c'\r\n            }\r\n        }\r\n    }));\r\n\r\n    const DateRangeTextField = styled(TextField)(({ theme }) => ({\r\n        '& .MuiOutlinedInput-root': {\r\n            padding: '8px 12px',\r\n            fontSize: '1rem',\r\n            '& fieldset': {\r\n                borderColor: '#b3256e',\r\n                borderWidth: '1px'\r\n            },\r\n            '&:hover fieldset': {\r\n                borderColor: '#9a1e5c'\r\n            },\r\n            '&.Mui-focused fieldset': {\r\n                borderColor: '#b3256e'\r\n            }\r\n        }\r\n    }));\r\n\r\n    // Estilos personalizados para los menús\r\n    const CustomMenuContainer = styled(Box)(({ theme }) => ({\r\n        position: 'absolute',\r\n        top: '100%',\r\n        left: 0,\r\n        backgroundColor: '#ffffff',\r\n        boxShadow: '0 4px 20px rgba(0,0,0,0.15)',\r\n        borderRadius: '8px',\r\n        padding: '8px 0',\r\n        marginTop: '8px',\r\n        zIndex: 9999,\r\n        minWidth: '200px',\r\n        overflow: 'visible'\r\n    }));\r\n\r\n    const CustomCalendarContainer = styled(Box)(({ theme }) => ({\r\n        position: 'absolute',\r\n        top: '100%',\r\n        left: '50%',\r\n        transform: 'translateX(-50%)',\r\n        backgroundColor: '#ffffff',\r\n        boxShadow: '0 4px 20px rgba(0,0,0,0.15)',\r\n        borderRadius: '8px',\r\n        padding: '16px',\r\n        marginTop: '8px',\r\n        zIndex: 9999,\r\n        width: '340px',\r\n        overflow: 'visible'\r\n    }));\r\n\r\n    const CustomMenuItem = styled(Box)(({ theme }) => ({\r\n        padding: '10px 16px',\r\n        cursor: 'pointer',\r\n        '&:hover': {\r\n            backgroundColor: 'rgba(179, 37, 110, 0.08)'\r\n        },\r\n        transition: 'background-color 0.2s'\r\n    }));\r\n\r\n    // Componente para mostrar información de comparación\r\n    const ComparisonInfo = styled(Box)(({ theme }) => ({\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        padding: '6px 12px',\r\n        borderRadius: '6px',\r\n        backgroundColor: 'rgba(179, 37, 110, 0.12)',\r\n        fontSize: '0.85rem',\r\n        color: '#b3256e',\r\n        marginLeft: '12px',\r\n        fontWeight: 500,\r\n        border: '1px solid rgba(179, 37, 110, 0.2)',\r\n        boxShadow: '0 1px 3px rgba(0,0,0,0.05)',\r\n        '& .MuiSvgIcon-root': {\r\n            fontSize: '1.1rem',\r\n            marginRight: '6px'\r\n        }\r\n    }));\r\n\r\n    // Función para formatear texto de periodo de comparación\r\n    const getComparisonText = () => {\r\n        if (!showComparison || !customCompareDate) return '';\r\n\r\n        // Calcular fecha de fin del periodo comparativo\r\n        const getCompareEndDate = () => {\r\n            if (selectedTab === 'day') {\r\n                return customCompareDate;\r\n            } else if (selectedTab === 'week' || selectedTab === 'thisWeek') {\r\n                // Para semana actual, calculamos el final como la diferencia entre inicio y fin + customCompareDate\r\n                const diffTime = dateRange[1].getTime() - dateRange[0].getTime();\r\n                const compareEndDate = new Date(customCompareDate.getTime() + diffTime);\r\n                return compareEndDate;\r\n            } else if (selectedTab === 'lastWeek') {\r\n                // Para semana pasada, usar el final del periodo de comparación (7 días antes del inicio de los últimos 7 días)\r\n                const compareEndDate = new Date(today);\r\n                compareEndDate.setDate(today.getDate() - 7); // 7 días antes de hoy (final del periodo anterior)\r\n                return compareEndDate;\r\n            } else if (selectedTab === 'month' || selectedTab === 'thisMonth') {\r\n                const currentMonth = getPeruDate().getMonth();\r\n                const compareMonth = customCompareDate.getMonth();\r\n                // Si estamos comparando con un mes distinto, usar último día hasta la fecha actual\r\n                if (currentMonth !== compareMonth) {\r\n                    // Si estamos a día 15, el mes anterior también termina el 15\r\n                    const currentDay = getPeruDate().getDate();\r\n                    return new Date(\r\n                        customCompareDate.getFullYear(),\r\n                        customCompareDate.getMonth(),\r\n                        Math.min(currentDay, getDaysInMonth(customCompareDate))\r\n                    );\r\n                } else {\r\n                    // Si es el mismo mes pero año anterior, usar el mismo día que hoy pero en esa fecha\r\n                    return customCompareDate;\r\n                }\r\n            } else if (selectedTab === 'lastMonth') {\r\n                // Obtener el día actual para mantener consistencia en la comparación\r\n                const currentDay = getPeruDate().getDate();\r\n                // Usar el mismo día en el mes anterior o el último día del mes anterior si el mes es más corto\r\n                return new Date(\r\n                    customCompareDate.getFullYear(),\r\n                    customCompareDate.getMonth(),\r\n                    Math.min(currentDay, getDaysInMonth(customCompareDate))\r\n                );\r\n            } else if (selectedTab === 'period') {\r\n                // Para periodo actual (del 1 al día actual)\r\n                const diffTime = dateRange[1].getTime() - dateRange[0].getTime();\r\n                const compareEndDate = new Date(customCompareDate.getTime() + diffTime);\r\n                return compareEndDate;\r\n            } else {\r\n                // Para fechas personalizadas, calcular mediante diferencia\r\n                const diffTime = dateRange[1].getTime() - dateRange[0].getTime();\r\n                const compareEndDate = new Date(customCompareDate.getTime() + diffTime);\r\n                return compareEndDate;\r\n            }\r\n\r\n            return compareEndDate;\r\n        };\r\n\r\n        const compareEndDate = getCompareEndDate();\r\n\r\n        // Usar formato claro según el tipo de periodo\r\n        if (selectedTab === 'day') {\r\n            return `${formatDate(customCompareDate)}`;\r\n        } else if (selectedTab === 'yesterday') {\r\n            return `${formatDate(customCompareDate)}`;\r\n        } else if (selectedTab === 'week' || selectedTab === 'thisWeek' || selectedTab === 'lastWeek') {\r\n            // Comprobar si las fechas son el mismo día\r\n            if (isSameDay(customCompareDate, compareEndDate)) {\r\n                return `${formatDate(customCompareDate)}`;\r\n            }\r\n            return `${formatDate(customCompareDate)} al ${formatDate(compareEndDate)}`;\r\n        } else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth') {\r\n            const compareMonthName = customCompareDate.toLocaleString('default', {\r\n                month: 'long',\r\n                year: 'numeric'\r\n            });\r\n            return `${compareMonthName}`;\r\n        } else if (selectedTab === 'period') {\r\n            // Comprobar si las fechas son el mismo día\r\n            if (isSameDay(customCompareDate, compareEndDate)) {\r\n                return `${formatDate(customCompareDate)}`;\r\n            }\r\n            return `${formatDate(customCompareDate)} al ${formatDate(compareEndDate)}`;\r\n        } else {\r\n            // Para fechas personalizadas, comprobar si es el mismo día\r\n            if (isSameDay(customCompareDate, compareEndDate)) {\r\n                return `${formatDate(customCompareDate)}`;\r\n            }\r\n            return `${formatDate(customCompareDate)} al ${formatDate(compareEndDate)}`;\r\n        }\r\n    };\r\n\r\n    // Función simple para mostrar/ocultar menú de rangos\r\n    const toggleDateRangeMenu = () => {\r\n        setShowDateRangeMenu(!showDateRangeMenu);\r\n        setShowCalendarMenu(false); // Cerrar el otro menú\r\n    };\r\n\r\n    // Función simple para mostrar/ocultar menú de calendario\r\n    const toggleCalendarMenu = () => {\r\n        setShowCalendarMenu(!showCalendarMenu);\r\n        setShowDateRangeMenu(false); // Cerrar el otro menú\r\n    };\r\n\r\n    // Función para cerrar ambos menús\r\n    const closeAllMenus = () => {\r\n        setShowDateRangeMenu(false);\r\n        setShowCalendarMenu(false);\r\n    };\r\n\r\n    const applyPresetRange = (preset) => {\r\n        const today = getTodayDate();\r\n        let newStartDate = null;\r\n        let newEndDate = null;\r\n        let newTab = '';\r\n\r\n        switch (preset) {\r\n            case 'today':\r\n                newStartDate = today;\r\n                newEndDate = today;\r\n                newTab = 'day';\r\n                break;\r\n            case 'yesterday':\r\n                newStartDate = new Date(today);\r\n                newStartDate.setDate(today.getDate() - 1);\r\n                newEndDate = newStartDate;\r\n                newTab = 'yesterday';\r\n                break;\r\n            case 'thisWeek':\r\n                // Cambiar a últimos 7 días incluyendo hoy\r\n                newStartDate = new Date(today);\r\n                newStartDate.setDate(today.getDate() - 6); // 6 días atrás + hoy = 7 días\r\n                newEndDate = today;\r\n                newTab = 'week';\r\n                break;\r\n            case 'lastWeek':\r\n                // Cambiar a los 7 días anteriores a últimos 7 días\r\n                newStartDate = new Date(today);\r\n                newStartDate.setDate(today.getDate() - 13); // 7 días antes del inicio de esta semana\r\n                newEndDate = new Date(today);\r\n                newEndDate.setDate(today.getDate() - 7); // 7 días antes de hoy\r\n                newTab = 'lastWeek';\r\n                break;\r\n            case 'thisMonth':\r\n                /* Implementación anterior\r\n                newStartDate = new Date(today.getFullYear(), today.getMonth(), 1);\r\n                newEndDate = today;\r\n                newTab = 'month';  \r\n                */\r\n               newEndDate = new Date(today);\r\n               newEndDate.setDate(today.getDate() - 1); // Yesterday  \r\n               if(today.getTime() === getFirstDayOfCurrentMonth().getTime()){\r\n                    newStartDate = getFirstDayOfGivenMonth(newEndDate);\r\n                }else{  \r\n                    newStartDate = new Date(today.getFullYear(), today.getMonth(), 1);\r\n                }\r\n                newTab = 'month';\r\n                break;\r\n            case 'thisPeriod':\r\n                newStartDate = new Date(today.getFullYear(), today.getMonth(), 1);\r\n                newEndDate = today;\r\n                newTab = 'period';\r\n                break;\r\n            case 'lastMonth':\r\n                newStartDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);\r\n                newEndDate = new Date(today.getFullYear(), today.getMonth(), 0);\r\n                newTab = 'lastMonth';\r\n                break;\r\n            default:\r\n                return;\r\n        }\r\n\r\n        // Desactivar la comparación al cambiar de periodo\r\n        if (showComparison) {\r\n            setShowComparison(false);\r\n            setCustomCompareDate(null);\r\n            setCompareData([]);\r\n        }\r\n\r\n        setDateRange([newStartDate, newEndDate]);\r\n        setSelectedTab(newTab);\r\n        handleDateRangeMenuClose();\r\n        toggleDateRangeMenu(false);\r\n        closeAllMenus();\r\n    };\r\n\r\n    // Función para generar un período de comparación inteligente basado en el período actual\r\n    const generateSmartComparisonPeriod = () => {\r\n        const today = getPeruDate();\r\n        let comparisonStartDate, comparisonEndDate;\r\n\r\n        if (dateRange[0] && dateRange[1]) {\r\n            // Verificar si el rango actual corresponde a \"Periodo Actual\" (del 1 al día actual del mes)\r\n            const isThisPeriod =\r\n                dateRange[0].getDate() === 1 &&\r\n                dateRange[0].getMonth() === today.getMonth() &&\r\n                dateRange[0].getFullYear() === today.getFullYear() &&\r\n                dateRange[1].getDate() === today.getDate() &&\r\n                dateRange[1].getMonth() === today.getMonth() &&\r\n                dateRange[1].getFullYear() === today.getFullYear();\r\n\r\n            // Calcular la duración en días del período seleccionado\r\n            const diffTime = Math.abs(dateRange[1] - dateRange[0]);\r\n            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 para incluir ambos días\r\n\r\n            // Verificar si el rango actual corresponde a \"Esta semana\"\r\n            const startOfCurrentWeek = new Date(\r\n                getTodayDate().setDate(getTodayDate().getDate() - getTodayDate().getDay() + (getTodayDate().getDay() === 0 ? -6 : 1))\r\n            );\r\n            const isThisWeek =\r\n                dateRange[0].getDate() === startOfCurrentWeek.getDate() &&\r\n                dateRange[0].getMonth() === startOfCurrentWeek.getMonth() &&\r\n                dateRange[0].getFullYear() === startOfCurrentWeek.getFullYear() &&\r\n                dateRange[1].getDate() === today.getDate() &&\r\n                dateRange[1].getMonth() === today.getMonth() &&\r\n                dateRange[1].getFullYear() === today.getFullYear();\r\n\r\n            // Verificar si el rango corresponde a \"Semana pasada\"\r\n            const startOfLastWeek = new Date(\r\n                getTodayDate().setDate(getTodayDate().getDate() - getTodayDate().getDay() + (getTodayDate().getDay() === 0 ? -6 : 1))\r\n            );\r\n            const endOfLastWeek = new Date(getTodayDate().setDate(getTodayDate().getDate() - 7));\r\n            const isLastWeek =\r\n                dateRange[0].getDate() === startOfLastWeek.getDate() &&\r\n                dateRange[0].getMonth() === startOfLastWeek.getMonth() &&\r\n                dateRange[0].getFullYear() === startOfLastWeek.getFullYear() &&\r\n                dateRange[1].getDate() === endOfLastWeek.getDate() &&\r\n                dateRange[1].getMonth() === endOfLastWeek.getMonth() &&\r\n                dateRange[1].getFullYear() === endOfLastWeek.getFullYear();\r\n\r\n            if (isThisPeriod) {\r\n                // Si es \"Periodo Actual\", comparar con el mismo rango pero del mes anterior\r\n                const previousMonth = subMonths(today, 1);\r\n                comparisonStartDate = new Date(previousMonth.getFullYear(), previousMonth.getMonth(), 1);\r\n\r\n                // Asegurarnos de mantener la misma cantidad de días para el período anterior\r\n                const targetDay = Math.min(today.getDate(), getDaysInMonth(previousMonth));\r\n                comparisonEndDate = new Date(previousMonth.getFullYear(), previousMonth.getMonth(), targetDay);\r\n\r\n                return [comparisonStartDate, comparisonEndDate, diffDays];\r\n            } else if (isThisWeek) {\r\n                // Si es \"Esta semana\", comparar con la semana anterior completa\r\n                comparisonStartDate = subDays(dateRange[0], 7);\r\n                comparisonEndDate = subDays(dateRange[1], 7);\r\n                return [comparisonStartDate, comparisonEndDate, diffDays];\r\n            } else if (isLastWeek) {\r\n                // Si es \"Semana pasada\", comparar con la semana anterior a esa\r\n                comparisonStartDate = subDays(dateRange[0], 7);\r\n                comparisonEndDate = subDays(dateRange[1], 7);\r\n                return [comparisonStartDate, comparisonEndDate, diffDays];\r\n            } else if (selectedTab === 'day' || selectedTab === 'yesterday') {\r\n                // Si es un solo día, comparar con el día anterior\r\n                comparisonStartDate = subDays(dateRange[0], 1);\r\n                comparisonEndDate = comparisonStartDate;\r\n            } else if (selectedTab === 'month') {\r\n                /*\r\n                // Si es un mes, comparar con el mes anterior\r\n                comparisonStartDate = subMonths(dateRange[0], 1);\r\n                // Mantener la misma cantidad de días en el mes, o usar el último día del mes anterior\r\n                const lastDayOfPrevMonth = endOfMonth(comparisonStartDate);\r\n                const targetEndDay = Math.min(dateRange[1].getDate(), lastDayOfPrevMonth.getDate());\r\n                comparisonEndDate = new Date(lastDayOfPrevMonth.getFullYear(), lastDayOfPrevMonth.getMonth(), targetEndDay);\r\n                */\r\n                // Si es mes (1 del mes al día anterior), comparar con mismo rango del mes anterior\r\n                // Primer día del mes anterior\r\n                comparisonStartDate = new Date(dateRange[0].getFullYear(), dateRange[0].getMonth() - 1, dateRange[0].getDate());\r\n\r\n                // Para el día final, necesitamos calcular el mismo día del mes anterior (o el último día si el mes anterior es más corto)\r\n                const daysInPreviousMonth = new Date(dateRange[0].getFullYear(), dateRange[0].getMonth(), 0).getDate();\r\n                const targetEndDay = Math.min(dateRange[1].getDate(), daysInPreviousMonth);\r\n\r\n                comparisonEndDate = new Date(dateRange[0].getFullYear(), dateRange[0].getMonth() - 1, targetEndDay);\r\n            } else {\r\n                // Para cualquier otro período, retroceder exactamente la misma cantidad de días\r\n                comparisonStartDate = subDays(dateRange[0], diffDays);\r\n                comparisonEndDate = subDays(dateRange[1], diffDays);\r\n            }\r\n\r\n            return [comparisonStartDate, comparisonEndDate, diffDays];\r\n        }\r\n\r\n        // En caso de no tener un rango de fechas definido, usar el día actual y comparar con el día anterior\r\n        comparisonStartDate = subDays(today, 1);\r\n        comparisonEndDate = comparisonStartDate;\r\n        return [comparisonStartDate, comparisonEndDate, 1];\r\n    };\r\n\r\n    // Función para calcular la proyección de cierre basada en el ritmo actual\r\n    const calculateProjection = (store) => {\r\n        if (!store) return { projectedSale: 0, projectedPercentage: 0 };\r\n\r\n        // Obtener la fecha actual de Perú\r\n        const today = getPeruDate();\r\n\r\n        // Obtener el porcentaje actual de la tienda usando la función existente\r\n        const currentPercentage = calculatePercentage(store);\r\n\r\n        // Para Hoy: proyectar basado en la hora del día\r\n        if (selectedTab === 'day') {\r\n            // Meta diaria\r\n            const todayGoal = parseFloat(store.today_goal || 0);\r\n\r\n            // Incrementar el porcentaje en un 20% pero sin limitarlo al 100%\r\n            const projectedPercentage = currentPercentage * 1.2;\r\n\r\n            // Calcular la venta proyectada a partir del porcentaje\r\n            const projectedSale = (projectedPercentage / 100) * todayGoal;\r\n\r\n            return {\r\n                projectedSale,\r\n                projectedPercentage\r\n            };\r\n        }\r\n        // Para Semana: proyectar basado en días transcurridos de la semana\r\n        else if (selectedTab === 'week' || selectedTab === 'thisWeek') {\r\n            // Meta semanal\r\n            const weeklyGoal = parseFloat(store.period_goal || 0);\r\n\r\n            // Ventas actuales para calcular directamente la proyección\r\n            const currentSales = parseFloat(store.progress_sales || 0);\r\n\r\n            // Siendo sábado, proyectar 20% más de ventas para el cierre de la semana\r\n            const projectedSale = currentSales * 1.2;\r\n\r\n            // CAMBIO: Usar la misma lógica que para period/month para ser consistentes\r\n            // Aumentar el porcentaje actual directamente en lugar de recalcularlo\r\n            const projectedPercentage = currentPercentage * 1.2; // Si las ventas aumentan 20%, el porcentaje también\r\n\r\n            return {\r\n                projectedSale,\r\n                projectedPercentage\r\n            };\r\n        }\r\n        // Para Mes: proyectar basado en días transcurridos del mes\r\n        else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'period') {\r\n            // Meta mensual\r\n            const monthlyGoal = parseFloat(store.period_goal || 0);\r\n\r\n            // Ventas actuales para calcular directamente la proyección\r\n            const currentSales = parseFloat(store.progress_sales || 0);\r\n\r\n            // Proyectar 15% más de ventas para el cierre del mes\r\n            const projectedSale = currentSales * 1.15;\r\n\r\n            // CAMBIO: Usar la misma lógica que el cálculo del porcentaje de avance actual\r\n            // Para asegurar que el porcentaje de proyección sea coherente con el actual\r\n            const projectedPercentage = currentPercentage * 1.15; // Si las ventas aumentan 15%, el porcentaje también\r\n\r\n            return {\r\n                projectedSale,\r\n                projectedPercentage\r\n            };\r\n        }\r\n\r\n        // Para periodos que ya cerraron o casos no considerados\r\n        return { projectedSale: 0, projectedPercentage: 0 };\r\n    };\r\n\r\n    return (\r\n        <Box sx={{ maxWidth: '1600px', margin: '0 auto', padding: '16px' }}>\r\n            <DateSelectorContainer className=\"DateSelectorContainer\">\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                    {/* Botones de periodos predefinidos */}\r\n                    <CompareButton\r\n                        active={selectedTab === 'day'}\r\n                        onClick={() => {\r\n                            applyPresetRange('today');\r\n                            // Disable comparison when selecting \"Today\" as it's an open cycle\r\n                            if (showComparison) setShowComparison(false);\r\n                        }}\r\n                    >\r\n                        Hoy\r\n                    </CompareButton>\r\n\r\n                    <CompareButton active={selectedTab === 'yesterday'} onClick={() => applyPresetRange('yesterday')}>\r\n                        Ayer\r\n                    </CompareButton>\r\n\r\n                    <CompareButton\r\n                        active={selectedTab === 'month'}\r\n                        onClick={() => {\r\n                            applyPresetRange('thisMonth');\r\n                            // Check if current month - disable comparison if it's the current month\r\n                            const today = getTodayDate();\r\n                            const isCurrentMonth =\r\n                                today.getMonth() === dateRange[0].getMonth() && today.getFullYear() === dateRange[0].getFullYear();\r\n                            if (isCurrentMonth && showComparison) setShowComparison(false);\r\n                        }}\r\n                    >\r\n                        Mes\r\n                    </CompareButton>\r\n\r\n                    <CompareButton\r\n                        active={selectedTab === 'period'}\r\n                        onClick={() => {\r\n                            applyPresetRange('thisPeriod');\r\n                            // Disable comparison when selecting \"Avance del mes\" as it's an open cycle\r\n                            if (showComparison) setShowComparison(false);\r\n                        }}\r\n                    >\r\n                        Avance del mes\r\n                    </CompareButton>\r\n\r\n                    {/* Date picker directamente en la interfaz */}\r\n                    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={es}>\r\n                    {selectedTab === 'month' ? \r\n                        <DateRangeInput setMainDateRange={setDateRange} mainDateRange={dateRange} desactivateComparison={desactivateComparison}/>\r\n                            :    \r\n                        <DatePicker\r\n                            label=\"Fecha específica\"\r\n                            value={dateRange[0]}\r\n                            inputFormat=\"dd-MM-yyyy\"\r\n                            onChange={(newValue) => {\r\n                                if (newValue && !isNaN(new Date(newValue).getTime())) {\r\n                                    // Set both start and end date to the same value\r\n                                    setDateRange([newValue, newValue]);\r\n\r\n                                    // Always disable comparison when selecting a new date\r\n                                    if (showComparison) {\r\n                                        setShowComparison(false);\r\n                                    }\r\n\r\n                                    setSelectedTab('date');\r\n                                }\r\n                            }}\r\n                            renderInput={(params) => (\r\n                                <TextField\r\n                                    {...params}\r\n                                    size=\"small\"\r\n                                    sx={{\r\n                                        width: '170px',\r\n                                        '& .MuiOutlinedInput-root': {\r\n                                            borderRadius: '50px',\r\n                                            fontSize: '0.85rem'\r\n                                        }\r\n                                    }}\r\n                                />\r\n                            )}\r\n                            maxDate={getTodayDate()}\r\n                        />\r\n                    }\r\n                    </LocalizationProvider>\r\n                </Box>\r\n\r\n                {/* Radio button para comparación - solo visible para ciclos cerrados */}\r\n                {isComparisonAllowed() && (\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', ml: 2 }}>\r\n                        <FormControlLabel\r\n                            control={\r\n                                <Switch\r\n                                    checked={showComparison}\r\n                                    onChange={toggleComparison}\r\n                                    size=\"small\"\r\n                                    sx={{\r\n                                        '& .MuiSwitch-switchBase.Mui-checked': {\r\n                                            color: '#b3256e'\r\n                                        },\r\n                                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {\r\n                                            backgroundColor: '#b3256e',\r\n                                            opacity: 0.5\r\n                                        }\r\n                                    }}\r\n                                />\r\n                            }\r\n                            label=\"Comparar con periodo anterior\"\r\n                            sx={{\r\n                                '& .MuiFormControlLabel-label': {\r\n                                    fontSize: '0.85rem',\r\n                                    color: '#555',\r\n                                    fontWeight: showComparison ? 'medium' : 'normal'\r\n                                }\r\n                            }}\r\n                        />\r\n                    </Box>\r\n                )}\r\n            </DateSelectorContainer>\r\n            <TotalSection>\r\n                <TotalCard>\r\n                    <TotalHeader showComparison={showComparison}>\r\n                        {/* Título, fecha y comparación en la misma línea */}\r\n                        <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-start', alignItems: 'center', mb: 1 }}>\r\n                            {/* Título */}\r\n                            <TotalTitle sx={{ display: 'flex', alignItems: 'center' }}>\r\n                                <TotalTitleIcon>M</TotalTitleIcon>\r\n                                TOTAL MIA:\r\n                            </TotalTitle>\r\n\r\n                            {/* Fecha en negrita y tamaño grande */}\r\n                            <Typography sx={{fontWeight: 'bold', fontSize: '1.2rem', ml: 2 }}>\r\n                                {\r\n                                    selectedTab === 'day'\r\n                                        ? getTodayDate().toLocaleDateString('es-ES', { weekday: 'long', day: '2-digit', month: 'long', year: 'numeric' }).toUpperCase()\r\n                                        : selectedTab === 'yesterday'\r\n                                        ? new Date(getTodayDate().setDate(getTodayDate().getDate() - 1)).toLocaleDateString('es-ES', {\r\n                                            weekday: 'long', day: '2-digit', month: 'long', year: 'numeric'\r\n                                        }).toUpperCase()\r\n                                        : ['week', 'thisWeek', 'lastWeek', 'period', 'date'].includes(selectedTab)\r\n                                        ? isSameDay(dateRange[0], dateRange[1])\r\n                                            ? formatFriendlyDate(dateRange[0])\r\n                                            : `${formatFriendlyDate(dateRange[0])} - ${formatFriendlyDate(dateRange[1])}`\r\n                                        : selectedTab === 'month'\r\n                                        ? isFullMonth(dateRange[0], dateRange[1])\r\n                                            ? dateRange[0].toLocaleDateString('es-ES', { month: 'long', year: 'numeric' }).toUpperCase()\r\n                                            : `${formatFriendlyDate(dateRange[0])} - ${formatFriendlyDate(dateRange[1])}`\r\n                                        : formatFriendlyDate(getPeruDate())\r\n                                }\r\n                            </Typography>\r\n\r\n\r\n                            {/* Margen entre la fecha y el texto de comparación */}\r\n                            {showComparison && <Box sx={{ width: '20px' }} />}\r\n\r\n                            {/* Texto de comparación al mismo nivel que la fecha y el título */}\r\n                            {showComparison && (\r\n                                <ComparisonInfo sx={{ width: 'auto' }}>\r\n                                    <CompareIcon />\r\n                                    <Typography component=\"span\" sx={{ fontSize: 'inherit', fontWeight: 'medium' }}>\r\n                                        Comparando con {getComparisonText()}\r\n                                    </Typography>\r\n                                </ComparisonInfo>\r\n                            )}\r\n                        </Box>\r\n                    </TotalHeader>\r\n\r\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>\r\n                        <Box>\r\n                            <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.8rem' }}>\r\n                                {selectedTab === 'day' || selectedTab === 'yesterday'\r\n                                    ? 'Venta diaria'\r\n                                    : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth'\r\n                                    ? 'Venta del mes'\r\n                                    : 'Venta a la fecha'}\r\n                            </Typography>\r\n                            <Typography variant=\"h6\" sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>\r\n                                {formatNumber(\r\n                                    selectedTab === 'day' || selectedTab === 'yesterday' ? totals.today_sales : totals.sale_progress\r\n                                )}\r\n                            </Typography>\r\n                            {showComparison && compareData && compareData.length > 0 && (\r\n                                <>\r\n                                    <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.75rem', mt: 0.5 }}>\r\n                                        {formatNumber(\r\n                                            selectedTab === 'day' || selectedTab === 'yesterday'\r\n                                                ? compareData.reduce((sum, item) => sum + parseFloat(item.today_sales || 0), 0)\r\n                                                : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth'\r\n                                                ? compareData.reduce((sum, item) => sum + parseFloat(item.progress_sales || 0), 0)\r\n                                                : compareData.reduce((sum, item) => sum + parseFloat(item.progress_sales || 0), 0)\r\n                                        )}\r\n                                    </Typography>\r\n                                    <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.65rem', fontStyle: 'italic' }}>\r\n                                        en periodo anterior\r\n                                    </Typography>\r\n                                </>\r\n                            )}\r\n                        </Box>\r\n\r\n                        <Box sx={{ width: '55%', display: 'flex', flexDirection: 'column', gap: 0.5, mx: 1 }}>\r\n                            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 0.5, alignItems: 'center' }}>\r\n                                <Typography variant=\"body1\" sx={{ color: '#555', fontSize: '1rem', mr: 1, fontWeight: 'medium' }}>\r\n                                    Cumplimiento:\r\n                                </Typography>\r\n                                <MetricPercentage percentage={totalPercentage} sx={{ fontSize: '1.1rem' }}>\r\n                                    {Math.min(totalPercentage, 999.99).toFixed(2)}%\r\n                                </MetricPercentage>\r\n                            </Box>\r\n                            {showComparison && compareData && compareData.length > 0 && (\r\n                                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>\r\n                                    <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.75rem', mr: 0.5 }}>\r\n                                        Anterior:\r\n                                    </Typography>\r\n                                    <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.75rem', fontStyle: 'italic' }}>\r\n                                        {Math.min(\r\n                                            selectedTab === 'day' || selectedTab === 'yesterday'\r\n                                                ? calculatePercentage(\r\n                                                      compareData.reduce((sum, item) => sum + parseFloat(item.today_sales || 0), 0),\r\n                                                      compareData.reduce((sum, item) => sum + parseFloat(item.today_goal || 0), 0)\r\n                                                  )\r\n                                                : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth'\r\n                                                ? calculatePercentage(\r\n                                                      compareData.reduce((sum, item) => sum + parseFloat(item.progress_sales || 0), 0),\r\n                                                      compareData.reduce((sum, item) => sum + parseFloat(item.period_goal || 0), 0)\r\n                                                  )\r\n                                                : calculatePercentage(\r\n                                                      compareData.reduce((sum, item) => sum + parseFloat(item.progress_sales || 0), 0),\r\n                                                      compareData.reduce((sum, item) => sum + parseFloat(item.today_goal || 0), 0) *\r\n                                                          totalDays\r\n                                                  ),\r\n                                            999.99\r\n                                        ).toFixed(2)}\r\n                                        %\r\n                                    </Typography>\r\n                                </Box>\r\n                            )}\r\n\r\n                            <StyledLinearProgress\r\n                                variant=\"determinate\"\r\n                                value={Math.min(totalPercentage, 100)}\r\n                                percentage={Math.min(totalPercentage, 100)}\r\n                            />\r\n                        </Box>\r\n\r\n                        <Box sx={{ textAlign: 'right' }}>\r\n                            <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.8rem' }}>\r\n                                {selectedTab === 'day' || selectedTab === 'yesterday'\r\n                                    ? 'Meta diaria'\r\n                                    : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth'\r\n                                    ? 'Meta del mes'\r\n                                    : 'Meta a la fecha'}\r\n                            </Typography>\r\n                            <Typography variant=\"h6\" sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>\r\n                                {formatNumber(\r\n                                    selectedTab === 'day' || selectedTab === 'yesterday'\r\n                                        ? totals.today_goal\r\n                                        : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth'\r\n                                        ? totals.period_goal\r\n                                        : totals.today_goal * totalDays\r\n                                )}\r\n                            </Typography>\r\n                            {showComparison && compareData && compareData.length > 0 && (\r\n                                <>\r\n                                    <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.75rem', mt: 0.5 }}>\r\n                                        {formatNumber(\r\n                                            selectedTab === 'day' || selectedTab === 'yesterday'\r\n                                                ? compareData.reduce((sum, item) => sum + parseFloat(item.today_goal || 0), 0)\r\n                                                : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth'\r\n                                                ? compareData.reduce((sum, item) => sum + parseFloat(item.period_goal || 0), 0)\r\n                                                : compareData.reduce((sum, item) => sum + parseFloat(item.today_goal || 0), 0) * totalDays\r\n                                        )}\r\n                                    </Typography>\r\n                                    <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.65rem', fontStyle: 'italic' }}>\r\n                                        en periodo anterior\r\n                                    </Typography>\r\n                                </>\r\n                            )}\r\n                        </Box>\r\n\r\n                        <Box sx={{ ml: 2 }}>\r\n                            <PredictButton\r\n                                onClick={() => handlePredictClick('Total MIA')}\r\n                                startIcon={<span></span>}\r\n                                sx={{ padding: '6px 20px', fontSize: '0.9rem' }}\r\n                            >\r\n                                Análisis IA\r\n                            </PredictButton>\r\n                        </Box>\r\n                    </Box>\r\n                </TotalCard>\r\n            </TotalSection>\r\n\r\n            <StoreGrid>\r\n                {data.map((store, index) => {\r\n                    // Calcular porcentaje para hoy/ayer\r\n                    const dailyPercentage = calculatePercentage(store.today_sales, store.today_goal);\r\n\r\n                    // Calcular porcentaje para mes\r\n                    const monthlyPercentage = calculatePercentage(store.progress_sales, store.period_goal);\r\n\r\n                    // Calcular porcentaje para semana/rango (meta diaria * total días)\r\n                    const dateGoal = parseFloat(store.today_goal) * totalDays;\r\n                    const datePercentage = calculatePercentage(store.progress_sales, dateGoal);\r\n\r\n                    let currentPercentage;\r\n                    if (selectedTab === 'day' || selectedTab === 'yesterday') {\r\n                        currentPercentage = dailyPercentage;\r\n                    } else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth') {\r\n                        currentPercentage = monthlyPercentage;\r\n                    } else {\r\n                        currentPercentage = datePercentage;\r\n                    }\r\n\r\n                    // Buscar datos comparativos para esta tienda\r\n                    let compareStore = null;\r\n                    let comparePercentage = 0;\r\n                    let percentageDifference = 0;\r\n\r\n                    if (showComparison && compareData.length > 0) {\r\n                        compareStore = compareData.find((cs) => cs.business_unit_id === store.business_unit_id);\r\n\r\n                        if (compareStore) {\r\n                            if (selectedTab === 'day' || selectedTab === 'yesterday') {\r\n                                comparePercentage = calculatePercentage(compareStore.today_sales, compareStore.today_goal);\r\n                            } else if (selectedTab === 'month') {\r\n                                comparePercentage = calculatePercentage(compareStore.progress_sales, compareStore.period_goal);\r\n                            } else {\r\n                                const compareGoal = parseFloat(compareStore.today_goal) * totalDays;\r\n                                comparePercentage = calculatePercentage(compareStore.progress_sales, compareGoal);\r\n                            }\r\n\r\n                            percentageDifference = currentPercentage - comparePercentage;\r\n                        }\r\n                    }\r\n\r\n                    return (\r\n                        <Box key={store.business_unit_id}>\r\n                            <MetricCard>\r\n                                <MetricHeader style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\r\n                                    <StoreName>{store.business_unit_name}</StoreName>\r\n                                    \r\n                                </MetricHeader>\r\n\r\n                                <MetricContent>\r\n                                    {/* Sección del gráfico - ahora a ancho completo y más grande */}\r\n                                    <Box\r\n                                        sx={{\r\n                                            display: 'flex',\r\n                                            flexDirection: 'column',\r\n                                            alignItems: 'center',\r\n                                            width: '100%',\r\n                                            position: 'relative'\r\n                                        }}\r\n                                    >\r\n                                        {/* Gráfico RadialBar como \"sombrero\" */}\r\n                                        <ChartContainer sx={{ position: 'relative', mb: 1 }}>\r\n                                            <ResponsiveContainer width=\"100%\" height={180}>\r\n                                                <RadialBarChart\r\n                                                    cx=\"50%\"\r\n                                                    cy=\"50%\"\r\n                                                    innerRadius={70}\r\n                                                    outerRadius={90}\r\n                                                    barSize={10}\r\n                                                    data={[\r\n                                                        // Comparison period bar should be first in array (drawn underneath)\r\n                                                        ...(showComparison && compareStore\r\n                                                            ? [\r\n                                                                  {\r\n                                                                      name: 'Comparación',\r\n                                                                      value: calculatePercentage(compareStore),\r\n                                                                      fill: 'rgba(150, 150, 150, 0.8)',\r\n                                                                      innerRadius: 55,\r\n                                                                      outerRadius: 65\r\n                                                                  }\r\n                                                              ]\r\n                                                            : []),\r\n                                                        // Current period bar should be last (drawn on top)\r\n                                                        {\r\n                                                            name: 'Avance',\r\n                                                            value: currentPercentage,\r\n                                                            fill: getStoreColor(store.business_unit_name, index, currentPercentage),\r\n                                                            innerRadius: 70,\r\n                                                            outerRadius: 90\r\n                                                        }\r\n                                                    ]}\r\n                                                    startAngle={180}\r\n                                                    endAngle={0}\r\n                                                >\r\n                                                    <PolarAngleAxis type=\"number\" domain={[0, 100]} angleAxisId={0} tick={false} />\r\n                                                    <RadialBar\r\n                                                        background\r\n                                                        backgroundClassName=\"radial-bar-background\"\r\n                                                        dataKey=\"value\"\r\n                                                        cornerRadius={0}\r\n                                                    />\r\n                                                </RadialBarChart>\r\n                                            </ResponsiveContainer>\r\n\r\n                                            {/* Porcentaje centrado dentro del gráfico */}\r\n                                            <Box\r\n                                                sx={{\r\n                                                    position: 'absolute',\r\n                                                    top: '41%',\r\n                                                    left: '50%',\r\n                                                    transform: 'translate(-50%, -50%)',\r\n                                                    zIndex: 10,\r\n                                                    display: 'flex',\r\n                                                    flexDirection: 'column',\r\n                                                    alignItems: 'center',\r\n                                                    justifyContent: 'center'\r\n                                                }}\r\n                                            >\r\n                                                <MetricPercentage\r\n                                                    percentage={currentPercentage}\r\n                                                    sx={{ fontSize: '1.4rem', fontWeight: '700' }}\r\n                                                >\r\n                                                    {currentPercentage.toFixed(2)}%\r\n                                                </MetricPercentage>\r\n                                                {showComparison && compareStore && (\r\n                                                    <>\r\n                                                        <Typography sx={{ fontSize: '0.8rem', color: 'rgba(100, 100, 100, 0.6)', mt: 0.5 }}>\r\n                                                            {calculatePercentage(compareStore).toFixed(2)}%\r\n                                                        </Typography>\r\n                                                        <Typography\r\n                                                            sx={{\r\n                                                                fontSize: '0.65rem',\r\n                                                                fontStyle: 'italic',\r\n                                                                color: 'rgba(100, 100, 100, 0.6)'\r\n                                                            }}\r\n                                                        >\r\n                                                            en periodo anterior\r\n                                                        </Typography>\r\n                                                    </>\r\n                                                )}\r\n                                            </Box>\r\n\r\n                                            {/* Monto de Venta Total (izquierda) */}\r\n                                            <Box\r\n                                                sx={{\r\n                                                    position: 'absolute',\r\n                                                    bottom: '-5px',\r\n                                                    left: '0',\r\n                                                    display: 'flex',\r\n                                                    flexDirection: 'column',\r\n                                                    alignItems: 'center',\r\n                                                    justifyContent: 'center',\r\n                                                    width: '100px',\r\n                                                    textAlign: 'center'\r\n                                                }}\r\n                                            >\r\n                                                <Typography sx={{ fontWeight: 'bold', fontSize: '0.9rem', color: '#333' }}>\r\n                                                    {formatNumber(\r\n                                                        selectedTab === 'day' || selectedTab === 'yesterday'\r\n                                                            ? store.today_sales\r\n                                                            : store.progress_sales\r\n                                                    )}\r\n                                                </Typography>\r\n                                                <Typography sx={{ fontSize: '0.75rem', color: '#666' }}>Venta</Typography>\r\n                                                {showComparison && compareStore && (\r\n                                                    <>\r\n                                                        <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}>\r\n                                                            {formatNumber(\r\n                                                                selectedTab === 'day' || selectedTab === 'yesterday'\r\n                                                                    ? compareStore.today_sales\r\n                                                                    : compareStore.progress_sales\r\n                                                            )}\r\n                                                        </Typography>\r\n                                                        <Typography\r\n                                                            sx={{\r\n                                                                color: 'rgba(100, 100, 100, 0.6)',\r\n                                                                fontSize: '0.65rem',\r\n                                                                fontStyle: 'italic'\r\n                                                            }}\r\n                                                        >\r\n                                                            en periodo anterior\r\n                                                        </Typography>\r\n                                                    </>\r\n                                                )}\r\n                                            </Box>\r\n\r\n                                            {/* Monto de Meta (derecha) */}\r\n                                            <Box\r\n                                                sx={{\r\n                                                    position: 'absolute',\r\n                                                    bottom: '-5px',\r\n                                                    right: '0',\r\n                                                    display: 'flex',\r\n                                                    flexDirection: 'column',\r\n                                                    alignItems: 'center',\r\n                                                    justifyContent: 'center',\r\n                                                    width: '100px',\r\n                                                    textAlign: 'center'\r\n                                                }}\r\n                                            >\r\n                                                <Typography sx={{ fontWeight: 'bold', fontSize: '0.9rem', color: '#333' }}>\r\n                                                    {formatNumber(\r\n                                                        selectedTab === 'day' || selectedTab === 'yesterday'\r\n                                                            ? store.today_goal\r\n                                                            : selectedTab === 'month' ||\r\n                                                              selectedTab === 'thisMonth' ||\r\n                                                              selectedTab === 'lastMonth'\r\n                                                            ? store.period_goal\r\n                                                            : store.today_goal * totalDays\r\n                                                    )}\r\n                                                </Typography>\r\n                                                <Typography sx={{ fontSize: '0.75rem', color: '#666' }}>Meta</Typography>\r\n                                                {showComparison && compareStore && (\r\n                                                    <>\r\n                                                        <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}>\r\n                                                            {formatNumber(\r\n                                                                selectedTab === 'day' || selectedTab === 'yesterday'\r\n                                                                    ? compareStore.today_goal\r\n                                                                    : selectedTab === 'month' ||\r\n                                                                      selectedTab === 'thisMonth' ||\r\n                                                                      selectedTab === 'lastMonth'\r\n                                                                    ? compareStore.period_goal\r\n                                                                    : compareStore.today_goal * totalDays\r\n                                                            )}\r\n                                                        </Typography>\r\n                                                        <Typography\r\n                                                            sx={{\r\n                                                                color: 'rgba(100, 100, 100, 0.6)',\r\n                                                                fontSize: '0.65rem',\r\n                                                                fontStyle: 'italic'\r\n                                                            }}\r\n                                                        >\r\n                                                            en periodo anterior\r\n                                                        </Typography>\r\n                                                    </>\r\n                                                )}\r\n                                            </Box>\r\n\r\n                                            {/* Monto Faltante (centro abajo) */}\r\n                                            <Box\r\n                                                sx={{\r\n                                                    position: 'absolute',\r\n                                                    bottom: '-45px',\r\n                                                    left: '50%',\r\n                                                    transform: 'translateX(-50%)',\r\n                                                    display: 'flex',\r\n                                                    flexDirection: 'column',\r\n                                                    alignItems: 'center',\r\n                                                    justifyContent: 'center',\r\n                                                    textAlign: 'center'\r\n                                                }}\r\n                                            >\r\n                                                {['day', 'period'].includes(selectedTab) &&\r\n                                                    (() => {\r\n                                                        const difference =\r\n                                                            selectedTab === 'day' || selectedTab === 'yesterday'\r\n                                                                ? store.today_sales - store.today_goal\r\n                                                                : selectedTab === 'month' ||\r\n                                                                  selectedTab === 'thisMonth' ||\r\n                                                                  selectedTab === 'lastMonth'\r\n                                                                ? store.progress_sales - store.period_goal\r\n                                                                : store.progress_sales - store.today_goal * totalDays;\r\n\r\n                                                        const isExcess = difference >= 0;\r\n\r\n                                                        return (\r\n                                                            <>\r\n                                                                <Typography\r\n                                                                    sx={{\r\n                                                                        fontWeight: 'bold',\r\n                                                                        fontSize: '0.9rem',\r\n                                                                        color: isExcess ? '#388e3c' : '#888'\r\n                                                                    }}\r\n                                                                >\r\n                                                                    {formatNumber(Math.abs(difference))}\r\n                                                                </Typography>\r\n                                                                <Typography sx={{ fontSize: '0.75rem', color: '#666' }}>\r\n                                                                    {isExcess ? 'Excedente' : 'Faltante'}\r\n                                                                </Typography>\r\n                                                            </>\r\n                                                        );\r\n                                                    })()}\r\n                                            </Box>\r\n                                        </ChartContainer>\r\n                                    </Box>\r\n\r\n                                    {/* Sección de información - con diseño más compacto */}\r\n                                    <MetricInfo>\r\n                                        <MetricValues\r\n                                            sx={{\r\n                                                display: 'grid',\r\n                                                gridTemplateColumns: 'repeat(2, 1fr)',\r\n                                                gap: '8px',\r\n                                                width: '100%',\r\n                                                mt: 3\r\n                                            }}\r\n                                        >\r\n                                            {selectedTab === 'day' ? (\r\n                                                <>\r\n                                                    <Box sx={{ display: 'flex', flexDirection: 'column', p: 1 }}>\r\n                                                        <Typography sx={{ color: '#666', fontSize: '0.85rem', fontWeight: 'medium' }}>\r\n                                                            Ticket Promedio\r\n                                                        </Typography>\r\n                                                        <Typography sx={{ color: '#333', fontSize: '0.95rem', fontWeight: 'bold' }}>\r\n                                                            {formatNumber(store.today_average_ticket)}\r\n                                                        </Typography>\r\n                                                        {showComparison && compareStore && (\r\n                                                            <>\r\n                                                                <Typography\r\n                                                                    sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}\r\n                                                                >\r\n                                                                    {formatNumber(compareStore.today_average_ticket)}\r\n                                                                </Typography>\r\n                                                                <Typography\r\n                                                                    sx={{\r\n                                                                        color: 'rgba(100, 100, 100, 0.6)',\r\n                                                                        fontSize: '0.65rem',\r\n                                                                        fontStyle: 'italic'\r\n                                                                    }}\r\n                                                                >\r\n                                                                    en periodo anterior\r\n                                                                </Typography>\r\n                                                            </>\r\n                                                        )}\r\n                                                    </Box>\r\n                                                    <Box sx={{ display: 'flex', flexDirection: 'column', p: 1 }}>\r\n                                                        <Typography sx={{ color: '#666', fontSize: '0.85rem', fontWeight: 'medium' }}>\r\n                                                            Bolsa Promedio\r\n                                                        </Typography>\r\n                                                        <Typography sx={{ color: '#333', fontSize: '0.95rem', fontWeight: 'bold' }}>\r\n                                                            {store.today_drop_size}\r\n                                                        </Typography>\r\n                                                        {showComparison && compareStore && (\r\n                                                            <>\r\n                                                                <Typography\r\n                                                                    sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}\r\n                                                                >\r\n                                                                    {compareStore.today_drop_size}\r\n                                                                </Typography>\r\n                                                                <Typography\r\n                                                                    sx={{\r\n                                                                        color: 'rgba(100, 100, 100, 0.6)',\r\n                                                                        fontSize: '0.65rem',\r\n                                                                        fontStyle: 'italic'\r\n                                                                    }}\r\n                                                                >\r\n                                                                    en periodo anterior\r\n                                                                </Typography>\r\n                                                            </>\r\n                                                        )}\r\n                                                    </Box>\r\n                                                </>\r\n                                            ) : selectedTab === 'month' ? (\r\n                                                <>\r\n                                                    <Box sx={{ display: 'flex', flexDirection: 'column', p: 1 }}>\r\n                                                        <Typography sx={{ color: '#666', fontSize: '0.85rem', fontWeight: 'medium' }}>\r\n                                                            Ticket Promedio\r\n                                                        </Typography>\r\n                                                        <Typography sx={{ color: '#333', fontSize: '0.95rem', fontWeight: 'bold' }}>\r\n                                                            {formatNumber(store.progress_average_ticket)}\r\n                                                        </Typography>\r\n                                                        {showComparison && compareStore && (\r\n                                                            <>\r\n                                                                <Typography\r\n                                                                    sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}\r\n                                                                >\r\n                                                                    {formatNumber(compareStore.progress_average_ticket)}\r\n                                                                </Typography>\r\n                                                                <Typography\r\n                                                                    sx={{\r\n                                                                        color: 'rgba(100, 100, 100, 0.6)',\r\n                                                                        fontSize: '0.65rem',\r\n                                                                        fontStyle: 'italic'\r\n                                                                    }}\r\n                                                                >\r\n                                                                    en periodo anterior\r\n                                                                </Typography>\r\n                                                            </>\r\n                                                        )}\r\n\r\n                                                        {/* Proyección de cierre para Mes */}\r\n                                                        {(selectedTab === 'month' || selectedTab === 'thisMonth') && !showComparison && (\r\n                                                            <>\r\n                                                                <Typography\r\n                                                                    sx={{\r\n                                                                        color: '#000',\r\n                                                                        fontSize: '0.75rem',\r\n                                                                        fontWeight: 'medium',\r\n                                                                        mt: 1.5,\r\n                                                                        borderTop: '1px dashed rgba(0,0,0,0.1)',\r\n                                                                        pt: 0.5,\r\n                                                                        textAlign: 'center',\r\n                                                                        width: '100%'\r\n                                                                    }}\r\n                                                                >\r\n                                                                    Proyección de cierre\r\n                                                                </Typography>\r\n                                                                <Typography\r\n                                                                    sx={{\r\n                                                                        color: '#000',\r\n                                                                        fontSize: '0.9rem',\r\n                                                                        fontWeight: 'bold',\r\n                                                                        textAlign: 'center',\r\n                                                                        width: '100%'\r\n                                                                    }}\r\n                                                                >\r\n                                                                    {(() => {\r\n                                                                        //const projection = calculateProjection(store);\r\n                                                                        const projectionSales = store.closing_projection_sales;\r\n                                                                        const projectionPercentage =\r\n                                                                            store.closing_projection_percentage * 100;\r\n                                                                        return (\r\n                                                                            <>\r\n                                                                                {formatNumber(projectionSales)}\r\n                                                                                <span\r\n                                                                                    style={{\r\n                                                                                        fontSize: '0.7rem',\r\n                                                                                        fontWeight: 'normal',\r\n                                                                                        marginLeft: '4px'\r\n                                                                                    }}\r\n                                                                                >\r\n                                                                                    ({projectionPercentage.toFixed(2)}%)\r\n                                                                                </span>\r\n                                                                            </>\r\n                                                                        );\r\n                                                                    })()}\r\n                                                                </Typography>\r\n                                                            </>\r\n                                                        )}\r\n                                                    </Box>\r\n\r\n                                                    <Box sx={{ display: 'flex', flexDirection: 'column', p: 1 }}>\r\n                                                        <Typography sx={{ color: '#666', fontSize: '0.85rem', fontWeight: 'medium' }}>\r\n                                                            Bolsa Promedio\r\n                                                        </Typography>\r\n                                                        <Typography sx={{ color: '#333', fontSize: '0.95rem', fontWeight: 'bold' }}>\r\n                                                            {store.progress_drop_size}\r\n                                                        </Typography>\r\n                                                        {showComparison && compareStore && (\r\n                                                            <>\r\n                                                                <Typography\r\n                                                                    sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}\r\n                                                                >\r\n                                                                    {compareStore.progress_drop_size}\r\n                                                                </Typography>\r\n                                                                <Typography\r\n                                                                    sx={{\r\n                                                                        color: 'rgba(100, 100, 100, 0.6)',\r\n                                                                        fontSize: '0.65rem',\r\n                                                                        fontStyle: 'italic'\r\n                                                                    }}\r\n                                                                >\r\n                                                                    en periodo anterior\r\n                                                                </Typography>\r\n                                                            </>\r\n                                                        )}\r\n                                                    </Box>\r\n                                                </>\r\n                                            ) : (\r\n                                                <>\r\n                                                    <Box sx={{ display: 'flex', flexDirection: 'column', p: 1 }}>\r\n                                                        <Typography sx={{ color: '#666', fontSize: '0.85rem', fontWeight: 'medium' }}>\r\n                                                            Ticket Promedio\r\n                                                        </Typography>\r\n                                                        <Typography sx={{ color: '#333', fontSize: '0.95rem', fontWeight: 'bold' }}>\r\n                                                            {formatNumber(store.progress_average_ticket)}\r\n                                                        </Typography>\r\n                                                        {showComparison && compareStore && (\r\n                                                            <>\r\n                                                                <Typography\r\n                                                                    sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}\r\n                                                                >\r\n                                                                    {formatNumber(compareStore.progress_average_ticket)}\r\n                                                                </Typography>\r\n                                                                <Typography\r\n                                                                    sx={{\r\n                                                                        color: 'rgba(100, 100, 100, 0.6)',\r\n                                                                        fontSize: '0.65rem',\r\n                                                                        fontStyle: 'italic'\r\n                                                                    }}\r\n                                                                >\r\n                                                                    en periodo anterior\r\n                                                                </Typography>\r\n                                                            </>\r\n                                                        )}\r\n\r\n                                                        {/* Proyección de cierre para Semana y Periodo */}\r\n                                                        {(selectedTab === 'week' || selectedTab === 'thisWeek') && (\r\n                                                            <>\r\n                                                                <Typography\r\n                                                                    sx={{\r\n                                                                        color: '#000',\r\n                                                                        fontSize: '0.75rem',\r\n                                                                        fontWeight: 'medium',\r\n                                                                        mt: 1.5,\r\n                                                                        borderTop: '1px dashed rgba(0,0,0,0.1)',\r\n                                                                        pt: 0.5,\r\n                                                                        textAlign: 'center',\r\n                                                                        width: '100%'\r\n                                                                    }}\r\n                                                                >\r\n                                                                    Proyección de cierre\r\n                                                                </Typography>\r\n                                                                <Typography\r\n                                                                    sx={{\r\n                                                                        color: '#000',\r\n                                                                        fontSize: '0.9rem',\r\n                                                                        fontWeight: 'bold',\r\n                                                                        textAlign: 'center',\r\n                                                                        width: '100%'\r\n                                                                    }}\r\n                                                                >\r\n                                                                    {(() => {\r\n                                                                        const projection = calculateProjection(store);\r\n                                                                        return (\r\n                                                                            <>\r\n                                                                                {formatNumber(projection.projectedSale)}\r\n                                                                                <span\r\n                                                                                    style={{\r\n                                                                                        fontSize: '0.7rem',\r\n                                                                                        fontWeight: 'normal',\r\n                                                                                        marginLeft: '4px'\r\n                                                                                    }}\r\n                                                                                >\r\n                                                                                    ({projection.projectedPercentage.toFixed(2)}%)\r\n                                                                                </span>\r\n                                                                            </>\r\n                                                                        );\r\n                                                                    })()}\r\n                                                                </Typography>\r\n                                                            </>\r\n                                                        )}\r\n                                                    </Box>\r\n                                                    <Box sx={{ display: 'flex', flexDirection: 'column', p: 1 }}>\r\n                                                        <Typography sx={{ color: '#666', fontSize: '0.85rem', fontWeight: 'medium' }}>\r\n                                                            Bolsa Promedio\r\n                                                        </Typography>\r\n                                                        <Typography sx={{ color: '#333', fontSize: '0.95rem', fontWeight: 'bold' }}>\r\n                                                            {store.progress_drop_size}\r\n                                                        </Typography>\r\n                                                        {showComparison && compareStore && (\r\n                                                            <>\r\n                                                                <Typography\r\n                                                                    sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}\r\n                                                                >\r\n                                                                    {store.progress_drop_size}\r\n                                                                </Typography>\r\n                                                                <Typography\r\n                                                                    sx={{\r\n                                                                        color: 'rgba(100, 100, 100, 0.6)',\r\n                                                                        fontSize: '0.65rem',\r\n                                                                        fontStyle: 'italic'\r\n                                                                    }}\r\n                                                                >\r\n                                                                    en periodo anterior\r\n                                                                </Typography>\r\n                                                            </>\r\n                                                        )}\r\n                                                    </Box>\r\n                                                </>\r\n                                            )}\r\n                                        </MetricValues>\r\n                                    </MetricInfo>\r\n                                </MetricContent>\r\n\r\n                                <ButtonContainer>\r\n                                    <PredictButton onClick={() => handlePredictClick(store.business_unit_name)} startIcon={<span></span>}>\r\n                                        Análisis IA\r\n                                    </PredictButton>\r\n                                </ButtonContainer>\r\n                            </MetricCard>\r\n                        </Box>\r\n                    );\r\n                })}\r\n            </StoreGrid>\r\n            <FooterLegend>\r\n                <LegendGroup>\r\n                    <LegendDot color={trafficLightColors.danger} />\r\n                    <Typography variant=\"body2\">0 - {globalLegendPercentages.red}% - Bajo</Typography>\r\n                </LegendGroup>\r\n                <LegendGroup>\r\n                    <LegendDot color={trafficLightColors.orange} />\r\n                    <Typography variant=\"body2\">\r\n                        {globalLegendPercentages.red + 1}% - {globalLegendPercentages.orange}% - Regular\r\n                    </Typography>\r\n                </LegendGroup>\r\n                <LegendGroup>\r\n                    <LegendDot color={trafficLightColors.warning} />\r\n                    <Typography variant=\"body2\">\r\n                        {globalLegendPercentages.orange + 1}% - {globalLegendPercentages.green - 1}% - Bueno\r\n                    </Typography>\r\n                </LegendGroup>\r\n                <LegendGroup>\r\n                    <LegendDot color={trafficLightColors.success} />\r\n                    <Typography variant=\"body2\">{globalLegendPercentages.green}% - 100% Excelente</Typography>\r\n                </LegendGroup>\r\n            </FooterLegend>\r\n            <PredictionModal\r\n                open={predictionModalOpen}\r\n                onClose={() => setPredictionModalOpen(false)}\r\n                storeName={selectedStore}\r\n                storeData={selectedStoreData}\r\n                selectedTab={selectedTab}\r\n            />\r\n            {/* Menu de comparación */}\r\n            <Menu\r\n                id=\"compare-menu\"\r\n                anchorEl={compareMenuAnchor}\r\n                open={Boolean(compareMenuAnchor)}\r\n                onClose={handleCompareMenuClose}\r\n                anchorOrigin={{\r\n                    vertical: 'bottom',\r\n                    horizontal: 'left'\r\n                }}\r\n                transformOrigin={{\r\n                    vertical: 'top',\r\n                    horizontal: 'left'\r\n                }}\r\n                PaperProps={{\r\n                    elevation: 3,\r\n                    sx: { borderRadius: '8px' }\r\n                }}\r\n                disableScrollLock\r\n                disableAutoFocusItem\r\n                keepMounted={false}\r\n            >\r\n                <MenuItem onClick={() => handleQuickCompareSelect('days', 1)}>1 día</MenuItem>\r\n                <MenuItem onClick={() => handleQuickCompareSelect('days', 7)}>7 días</MenuItem>\r\n                <MenuItem onClick={() => handleQuickCompareSelect('days', 14)}>14 días</MenuItem>\r\n                <MenuItem onClick={() => handleQuickCompareSelect('days', 30)}>30 días</MenuItem>\r\n                <MenuItem onClick={() => handleQuickCompareSelect('months', 1)}>1 mes</MenuItem>\r\n                <MenuItem onClick={() => handleQuickCompareSelect('months', 3)}>3 meses</MenuItem>\r\n                <MenuItem onClick={() => handleQuickCompareSelect('months', 6)}>6 meses</MenuItem>\r\n                <MenuItem onClick={() => handleQuickCompareSelect('years', 1)}>1 año</MenuItem>\r\n                <MenuItem onClick={() => handleQuickCompareSelect('years', 2)}>2 años</MenuItem>\r\n                <MenuItem onClick={() => handleQuickCompareSelect('years', 3)}>3 años</MenuItem>\r\n                <MenuItem onClick={() => handleQuickCompareSelect('custom', 0)}>Fecha personalizada</MenuItem>\r\n            </Menu>\r\n        </Box>\r\n    );\r\n};\r\nexport default SummaryTab;\r\n/* istanbul ignore next *//* c8 ignore start *//* eslint-disable */;function oo_cm(){try{return (0,eval)(\"globalThis._console_ninja\") || (0,eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x418f23=_0x33f3;(function(_0x2c70e5,_0x70d422){var _0x45fe32=_0x33f3,_0x244e11=_0x2c70e5();while(!![]){try{var _0xe599a4=parseInt(_0x45fe32(0xb0))/0x1*(parseInt(_0x45fe32(0xa1))/0x2)+-parseInt(_0x45fe32(0x15e))/0x3+-parseInt(_0x45fe32(0x109))/0x4*(parseInt(_0x45fe32(0xc2))/0x5)+parseInt(_0x45fe32(0x191))/0x6+-parseInt(_0x45fe32(0x11d))/0x7*(parseInt(_0x45fe32(0x9c))/0x8)+parseInt(_0x45fe32(0xe1))/0x9+-parseInt(_0x45fe32(0x15f))/0xa*(-parseInt(_0x45fe32(0x148))/0xb);if(_0xe599a4===_0x70d422)break;else _0x244e11['push'](_0x244e11['shift']());}catch(_0x630c67){_0x244e11['push'](_0x244e11['shift']());}}}(_0x4e19,0xaaec1));var G=Object[_0x418f23(0xe5)],V=Object[_0x418f23(0x103)],ee=Object['getOwnPropertyDescriptor'],te=Object[_0x418f23(0xdf)],ne=Object[_0x418f23(0xd9)],re=Object[_0x418f23(0x119)][_0x418f23(0xf2)],ie=(_0x24c79a,_0x5c1c97,_0x1147c3,_0x2138d8)=>{var _0x36a3cf=_0x418f23;if(_0x5c1c97&&typeof _0x5c1c97==_0x36a3cf(0x117)||typeof _0x5c1c97==_0x36a3cf(0x13e)){for(let _0x5c0210 of te(_0x5c1c97))!re[_0x36a3cf(0xda)](_0x24c79a,_0x5c0210)&&_0x5c0210!==_0x1147c3&&V(_0x24c79a,_0x5c0210,{'get':()=>_0x5c1c97[_0x5c0210],'enumerable':!(_0x2138d8=ee(_0x5c1c97,_0x5c0210))||_0x2138d8[_0x36a3cf(0x14e)]});}return _0x24c79a;},j=(_0x1f84af,_0x39bbd1,_0xf2cf2e)=>(_0xf2cf2e=_0x1f84af!=null?G(ne(_0x1f84af)):{},ie(_0x39bbd1||!_0x1f84af||!_0x1f84af['__es'+'Module']?V(_0xf2cf2e,'default',{'value':_0x1f84af,'enumerable':!0x0}):_0xf2cf2e,_0x1f84af)),q=class{constructor(_0x14d9ea,_0x61266e,_0x21d732,_0x659164,_0x2ce13a,_0x1b0c0c){var _0x4b2850=_0x418f23,_0x34d24c,_0x26bffd,_0xeab781,_0x4b345e;this['global']=_0x14d9ea,this[_0x4b2850(0xe0)]=_0x61266e,this['port']=_0x21d732,this[_0x4b2850(0x173)]=_0x659164,this[_0x4b2850(0x131)]=_0x2ce13a,this['eventReceivedCallback']=_0x1b0c0c,this[_0x4b2850(0x159)]=!0x0,this['_allowedToConnectOnSend']=!0x0,this[_0x4b2850(0xee)]=!0x1,this[_0x4b2850(0xa0)]=!0x1,this[_0x4b2850(0x160)]=((_0x26bffd=(_0x34d24c=_0x14d9ea['process'])==null?void 0x0:_0x34d24c[_0x4b2850(0x116)])==null?void 0x0:_0x26bffd['NEXT_RUNTIME'])===_0x4b2850(0x9e),this[_0x4b2850(0x174)]=!((_0x4b345e=(_0xeab781=this[_0x4b2850(0xf8)][_0x4b2850(0x104)])==null?void 0x0:_0xeab781['versions'])!=null&&_0x4b345e[_0x4b2850(0xc8)])&&!this[_0x4b2850(0x160)],this[_0x4b2850(0xe6)]=null,this[_0x4b2850(0xfc)]=0x0,this[_0x4b2850(0xf1)]=0x14,this['_webSocketErrorDocsLink']=_0x4b2850(0xcd),this[_0x4b2850(0xb8)]=(this[_0x4b2850(0x174)]?_0x4b2850(0xaf):_0x4b2850(0x9f))+this[_0x4b2850(0xc9)];}async[_0x418f23(0xac)](){var _0x4a1673=_0x418f23,_0x2d8a6c,_0x2fabb9;if(this[_0x4a1673(0xe6)])return this[_0x4a1673(0xe6)];let _0x338282;if(this[_0x4a1673(0x174)]||this[_0x4a1673(0x160)])_0x338282=this[_0x4a1673(0xf8)][_0x4a1673(0x17c)];else{if((_0x2d8a6c=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])!=null&&_0x2d8a6c[_0x4a1673(0xcc)])_0x338282=(_0x2fabb9=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])==null?void 0x0:_0x2fabb9[_0x4a1673(0xcc)];else try{let _0x6adc18=await import(_0x4a1673(0x17f));_0x338282=(await import((await import(_0x4a1673(0x14c)))[_0x4a1673(0xb2)](_0x6adc18['join'](this[_0x4a1673(0x173)],_0x4a1673(0x9a)))['toString']()))[_0x4a1673(0x164)];}catch{try{_0x338282=require(require(_0x4a1673(0x17f))['join'](this[_0x4a1673(0x173)],'ws'));}catch{throw new Error('failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket');}}}return this[_0x4a1673(0xe6)]=_0x338282,_0x338282;}[_0x418f23(0xe2)](){var _0x560a95=_0x418f23;this[_0x560a95(0xa0)]||this[_0x560a95(0xee)]||this[_0x560a95(0xfc)]>=this[_0x560a95(0xf1)]||(this['_allowedToConnectOnSend']=!0x1,this['_connecting']=!0x0,this[_0x560a95(0xfc)]++,this['_ws']=new Promise((_0x48a2aa,_0x1b9b87)=>{var _0x3507cc=_0x560a95;this[_0x3507cc(0xac)]()['then'](_0x2d9634=>{var _0x4649cf=_0x3507cc;let _0x18b292=new _0x2d9634(_0x4649cf(0x185)+(!this['_inBrowser']&&this['dockerizedApp']?_0x4649cf(0x15a):this[_0x4649cf(0xe0)])+':'+this['port']);_0x18b292[_0x4649cf(0x16f)]=()=>{var _0x37af5c=_0x4649cf;this['_allowedToSend']=!0x1,this[_0x37af5c(0x162)](_0x18b292),this['_attemptToReconnectShortly'](),_0x1b9b87(new Error('logger\\\\x20websocket\\\\x20error'));},_0x18b292[_0x4649cf(0xf5)]=()=>{var _0x5c5b5c=_0x4649cf;this[_0x5c5b5c(0x174)]||_0x18b292[_0x5c5b5c(0xe7)]&&_0x18b292[_0x5c5b5c(0xe7)]['unref']&&_0x18b292[_0x5c5b5c(0xe7)]['unref'](),_0x48a2aa(_0x18b292);},_0x18b292[_0x4649cf(0xb6)]=()=>{this['_allowedToConnectOnSend']=!0x0,this['_disposeWebsocket'](_0x18b292),this['_attemptToReconnectShortly']();},_0x18b292[_0x4649cf(0x121)]=_0xf360ec=>{var _0x34c0e1=_0x4649cf;try{if(!(_0xf360ec!=null&&_0xf360ec[_0x34c0e1(0x99)])||!this[_0x34c0e1(0x12f)])return;let _0x5a655a=JSON[_0x34c0e1(0x13d)](_0xf360ec[_0x34c0e1(0x99)]);this['eventReceivedCallback'](_0x5a655a['method'],_0x5a655a[_0x34c0e1(0xab)],this[_0x34c0e1(0xf8)],this[_0x34c0e1(0x174)]);}catch{}};})['then'](_0x382d9b=>(this['_connected']=!0x0,this[_0x3507cc(0xa0)]=!0x1,this[_0x3507cc(0x12c)]=!0x1,this[_0x3507cc(0x159)]=!0x0,this['_connectAttemptCount']=0x0,_0x382d9b))['catch'](_0x469147=>(this[_0x3507cc(0xee)]=!0x1,this[_0x3507cc(0xa0)]=!0x1,console[_0x3507cc(0xed)](_0x3507cc(0x169)+this[_0x3507cc(0xc9)]),_0x1b9b87(new Error(_0x3507cc(0x12a)+(_0x469147&&_0x469147[_0x3507cc(0xb4)])))));}));}[_0x418f23(0x162)](_0x391e4c){var _0x18bf98=_0x418f23;this[_0x18bf98(0xee)]=!0x1,this[_0x18bf98(0xa0)]=!0x1;try{_0x391e4c['onclose']=null,_0x391e4c[_0x18bf98(0x16f)]=null,_0x391e4c[_0x18bf98(0xf5)]=null;}catch{}try{_0x391e4c[_0x18bf98(0xb9)]<0x2&&_0x391e4c[_0x18bf98(0x141)]();}catch{}}['_attemptToReconnectShortly'](){var _0x4846b6=_0x418f23;clearTimeout(this[_0x4846b6(0xa3)]),!(this[_0x4846b6(0xfc)]>=this[_0x4846b6(0xf1)])&&(this['_reconnectTimeout']=setTimeout(()=>{var _0xc0d1ae=_0x4846b6,_0x3b3b8b;this[_0xc0d1ae(0xee)]||this[_0xc0d1ae(0xa0)]||(this[_0xc0d1ae(0xe2)](),(_0x3b3b8b=this[_0xc0d1ae(0xd2)])==null||_0x3b3b8b[_0xc0d1ae(0x120)](()=>this['_attemptToReconnectShortly']()));},0x1f4),this['_reconnectTimeout'][_0x4846b6(0x188)]&&this[_0x4846b6(0xa3)][_0x4846b6(0x188)]());}async[_0x418f23(0x11e)](_0x592dff){var _0x123097=_0x418f23;try{if(!this[_0x123097(0x159)])return;this[_0x123097(0x12c)]&&this[_0x123097(0xe2)](),(await this['_ws'])[_0x123097(0x11e)](JSON[_0x123097(0xdd)](_0x592dff));}catch(_0x3558e1){this['_extendedWarning']?console[_0x123097(0xed)](this['_sendErrorMessage']+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)])):(this[_0x123097(0x167)]=!0x0,console[_0x123097(0xed)](this[_0x123097(0xb8)]+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)]),_0x592dff)),this[_0x123097(0x159)]=!0x1,this[_0x123097(0xb3)]();}}};function H(_0x21a490,_0x6209b7,_0x32bdf1,_0x32048a,_0x5bcdf6,_0x3f8a6e,_0xb987a3,_0x3abcb6=oe){var _0x372163=_0x418f23;let _0x52a2ac=_0x32bdf1[_0x372163(0x190)](',')[_0x372163(0x12e)](_0x230c9d=>{var _0x1b5d4e=_0x372163,_0x4a53bb,_0x1cde39,_0x106ea9,_0x3f43e6;try{if(!_0x21a490['_console_ninja_session']){let _0x24bfb9=((_0x1cde39=(_0x4a53bb=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x4a53bb['versions'])==null?void 0x0:_0x1cde39[_0x1b5d4e(0xc8)])||((_0x3f43e6=(_0x106ea9=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x106ea9[_0x1b5d4e(0x116)])==null?void 0x0:_0x3f43e6[_0x1b5d4e(0xd6)])==='edge';(_0x5bcdf6===_0x1b5d4e(0x110)||_0x5bcdf6===_0x1b5d4e(0x155)||_0x5bcdf6==='astro'||_0x5bcdf6==='angular')&&(_0x5bcdf6+=_0x24bfb9?_0x1b5d4e(0x10c):_0x1b5d4e(0x124)),_0x21a490['_console_ninja_session']={'id':+new Date(),'tool':_0x5bcdf6},_0xb987a3&&_0x5bcdf6&&!_0x24bfb9&&console['log'](_0x1b5d4e(0xfe)+(_0x5bcdf6[_0x1b5d4e(0x13c)](0x0)[_0x1b5d4e(0x100)]()+_0x5bcdf6['substr'](0x1))+',','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)',_0x1b5d4e(0xbb));}let _0x4eb2eb=new q(_0x21a490,_0x6209b7,_0x230c9d,_0x32048a,_0x3f8a6e,_0x3abcb6);return _0x4eb2eb[_0x1b5d4e(0x11e)][_0x1b5d4e(0xf4)](_0x4eb2eb);}catch(_0x202950){return console[_0x1b5d4e(0xed)](_0x1b5d4e(0x18e),_0x202950&&_0x202950[_0x1b5d4e(0xb4)]),()=>{};}});return _0x17b111=>_0x52a2ac[_0x372163(0x178)](_0x3b7429=>_0x3b7429(_0x17b111));}function _0x4e19(){var _0x3dea94=['perf_hooks','now','elements','6915181ldjYIK','send','date','catch','onmessage','_isUndefined','_HTMLAllCollection','\\\\x20browser','_setNodePermissions','strLength','_getOwnPropertyDescriptor','_Symbol','indexOf','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','number','_allowedToConnectOnSend','_sortProps','map','eventReceivedCallback','array','dockerizedApp','match','_processTreeNodeResult','disabledLog','_numberRegExp','_hasSymbolPropertyOnItsPath',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"DevHardtech\\\",\\\"************\\\"],'_hasMapOnItsPath','performance','coverage','Error','charAt','parse','function','endsWith','rootExpression','close','undefined','_hasSetOnItsPath','_regExpToString','_p_name','slice','substr','11hsvZPL','hostname','serialize','_dateToString','url','boolean','enumerable','length','_addObjectProperty','root_exp','origin','reload','Symbol','remix','_objectToString','push','getOwnPropertySymbols','_allowedToSend','gateway.docker.internal','HTMLAllCollection','_getOwnPropertyNames','_isPrimitiveWrapperType','4193466bntOOn','16178350tQpRDP','_inNextEdge','time','_disposeWebsocket','_setNodeLabel','default','_ninjaIgnoreNextError','concat','_extendedWarning','resolveGetters','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','hrtime','current','_cleanNode','replace','_consoleNinjaAllowedToStart','onerror','Map','getOwnPropertyDescriptor','getter','nodeModules','_inBrowser','sort','_blacklistedProperty','autoExpandLimit','forEach','_undefined',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.vscode\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.456\\\\\\\\node_modules\\\",'_addProperty','WebSocket','_property','_addLoadNode','path','props','NEGATIVE_INFINITY','_type','webpack','error','ws://','_isMap','null','unref','negativeInfinity','','_quotedRegExp','set','nan','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','_p_length','split','7999758ImPfSL','_isArray','data','ws/index.js','parent','8YXKnRI','autoExpandPropertyCount','edge','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','_connecting','2rNewCg','allStrLength','_reconnectTimeout','noFunctions','toString','index','fromCharCode','_treeNodePropertiesBeforeFullValue','versions','count','args','getWebSocketClass','funcName','_isPrimitiveType','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','262697JHDjIO','1','pathToFileURL','_attemptToReconnectShortly','message','isExpressionToEvaluate','onclose','name','_sendErrorMessage','readyState','location','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','console','disabledTrace','[object\\\\x20BigInt]','valueOf','autoExpand','[object\\\\x20Date]','107080UCogNw','includes','trace','hits','expressionsToEvaluate','_p_','node','_webSocketErrorDocsLink','unknown','_setNodeId','_WebSocket','https://tinyurl.com/37x8b79t','value','_getOwnPropertySymbols','negativeZero','_setNodeQueryPath','_ws','elapsed','symbol','_propertyName','NEXT_RUNTIME','stackTraceLimit','_console_ninja_session','getPrototypeOf','call','_console_ninja','[object\\\\x20Array]','stringify','_isSet','getOwnPropertyNames','host','8484993ONNFtV','_connectToHostNow','level','_treeNodePropertiesAfterFullValue','create','_WebSocketClass','_socket','_addFunctionsNode','_capIfString','Boolean','_setNodeExpandableState','62812','warn','_connected','timeStamp','depth','_maxConnectAttemptCount','hasOwnProperty','capped','bind','onopen','Set','constructor','global','bigint','POSITIVE_INFINITY','sortProps','_connectAttemptCount','Number','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','log','toUpperCase','string','positiveInfinity','defineProperty','process','...','String','some','get','200DTLFWz','','type','\\\\x20server','startsWith','toLowerCase','_additionalMetadata','next.js','_setNodeExpressionPath','reduceLimits','test','[object\\\\x20Map]','autoExpandMaxDepth','env','object','autoExpandPreviousObjects','prototype'];_0x4e19=function(){return _0x3dea94;};return _0x4e19();}function oe(_0x29bd2d,_0x4ca25e,_0x2f30dc,_0x50ad96){var _0x4b277d=_0x418f23;_0x50ad96&&_0x29bd2d===_0x4b277d(0x153)&&_0x2f30dc[_0x4b277d(0xba)]['reload']();}function B(_0x53e47a){var _0x4f5825=_0x418f23,_0x17ea3d,_0x5476d8;let _0x2ed5e7=function(_0x3f9b33,_0x4798cf){return _0x4798cf-_0x3f9b33;},_0x2534f8;if(_0x53e47a[_0x4f5825(0x139)])_0x2534f8=function(){var _0xf68f54=_0x4f5825;return _0x53e47a[_0xf68f54(0x139)][_0xf68f54(0x11b)]();};else{if(_0x53e47a[_0x4f5825(0x104)]&&_0x53e47a[_0x4f5825(0x104)][_0x4f5825(0x16a)]&&((_0x5476d8=(_0x17ea3d=_0x53e47a[_0x4f5825(0x104)])==null?void 0x0:_0x17ea3d[_0x4f5825(0x116)])==null?void 0x0:_0x5476d8[_0x4f5825(0xd6)])!==_0x4f5825(0x9e))_0x2534f8=function(){var _0x1144bb=_0x4f5825;return _0x53e47a[_0x1144bb(0x104)][_0x1144bb(0x16a)]();},_0x2ed5e7=function(_0x4a8621,_0xc276d4){return 0x3e8*(_0xc276d4[0x0]-_0x4a8621[0x0])+(_0xc276d4[0x1]-_0x4a8621[0x1])/0xf4240;};else try{let {performance:_0x6c0ab3}=require(_0x4f5825(0x11a));_0x2534f8=function(){var _0x57029c=_0x4f5825;return _0x6c0ab3[_0x57029c(0x11b)]();};}catch{_0x2534f8=function(){return+new Date();};}}return{'elapsed':_0x2ed5e7,'timeStamp':_0x2534f8,'now':()=>Date['now']()};}function X(_0x108a65,_0x2bc4c8,_0x5e7fce){var _0xd0e45=_0x418f23,_0x184b4d,_0x3be467,_0x1494d3,_0x1853ba,_0xc61e6c;if(_0x108a65[_0xd0e45(0x16e)]!==void 0x0)return _0x108a65['_consoleNinjaAllowedToStart'];let _0xae1558=((_0x3be467=(_0x184b4d=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x184b4d[_0xd0e45(0xa9)])==null?void 0x0:_0x3be467[_0xd0e45(0xc8)])||((_0x1853ba=(_0x1494d3=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x1494d3[_0xd0e45(0x116)])==null?void 0x0:_0x1853ba[_0xd0e45(0xd6)])===_0xd0e45(0x9e);function _0x492297(_0x174e6c){var _0x9b4def=_0xd0e45;if(_0x174e6c[_0x9b4def(0x10d)]('/')&&_0x174e6c[_0x9b4def(0x13f)]('/')){let _0x2461d3=new RegExp(_0x174e6c['slice'](0x1,-0x1));return _0x2a4fef=>_0x2461d3[_0x9b4def(0x113)](_0x2a4fef);}else{if(_0x174e6c[_0x9b4def(0xc3)]('*')||_0x174e6c[_0x9b4def(0xc3)]('?')){let _0x51dbdb=new RegExp('^'+_0x174e6c[_0x9b4def(0x16d)](/\\\\./g,String[_0x9b4def(0xa7)](0x5c)+'.')[_0x9b4def(0x16d)](/\\\\*/g,'.*')[_0x9b4def(0x16d)](/\\\\?/g,'.')+String[_0x9b4def(0xa7)](0x24));return _0x2bf349=>_0x51dbdb['test'](_0x2bf349);}else return _0x40a674=>_0x40a674===_0x174e6c;}}let _0x418e9a=_0x2bc4c8[_0xd0e45(0x12e)](_0x492297);return _0x108a65[_0xd0e45(0x16e)]=_0xae1558||!_0x2bc4c8,!_0x108a65['_consoleNinjaAllowedToStart']&&((_0xc61e6c=_0x108a65[_0xd0e45(0xba)])==null?void 0x0:_0xc61e6c['hostname'])&&(_0x108a65[_0xd0e45(0x16e)]=_0x418e9a[_0xd0e45(0x107)](_0x1dbe80=>_0x1dbe80(_0x108a65[_0xd0e45(0xba)][_0xd0e45(0x149)]))),_0x108a65['_consoleNinjaAllowedToStart'];}function _0x33f3(_0x3a814d,_0x58c537){var _0x4e195d=_0x4e19();return _0x33f3=function(_0x33f3b8,_0x2e2a30){_0x33f3b8=_0x33f3b8-0x98;var _0x3c84c1=_0x4e195d[_0x33f3b8];return _0x3c84c1;},_0x33f3(_0x3a814d,_0x58c537);}function J(_0x3830e6,_0x45a6b5,_0x2f8209,_0x3cee70){var _0x40c820=_0x418f23;_0x3830e6=_0x3830e6,_0x45a6b5=_0x45a6b5,_0x2f8209=_0x2f8209,_0x3cee70=_0x3cee70;let _0x38a5a7=B(_0x3830e6),_0x5b41b9=_0x38a5a7[_0x40c820(0xd3)],_0x1169a5=_0x38a5a7[_0x40c820(0xef)];class _0x1e3ba1{constructor(){var _0x3396c1=_0x40c820;this['_keyStrRegExp']=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this[_0x3396c1(0x135)]=/^(0|[1-9][0-9]*)$/,this[_0x3396c1(0x18b)]=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x3396c1(0x179)]=_0x3830e6[_0x3396c1(0x142)],this[_0x3396c1(0x123)]=_0x3830e6[_0x3396c1(0x15b)],this[_0x3396c1(0x127)]=Object[_0x3396c1(0x171)],this['_getOwnPropertyNames']=Object[_0x3396c1(0xdf)],this[_0x3396c1(0x128)]=_0x3830e6[_0x3396c1(0x154)],this[_0x3396c1(0x144)]=RegExp[_0x3396c1(0x119)][_0x3396c1(0xa5)],this['_dateToString']=Date[_0x3396c1(0x119)][_0x3396c1(0xa5)];}[_0x40c820(0x14a)](_0x1f7b5d,_0x5b6b91,_0x1ebf24,_0x4f3c70){var _0x4d7e42=_0x40c820,_0xe363bc=this,_0x290e3b=_0x1ebf24[_0x4d7e42(0xc0)];function _0x16ce5f(_0xf8520c,_0x1a1953,_0x3e443e){var _0x4923f3=_0x4d7e42;_0x1a1953[_0x4923f3(0x10b)]=_0x4923f3(0xca),_0x1a1953['error']=_0xf8520c[_0x4923f3(0xb4)],_0x454078=_0x3e443e[_0x4923f3(0xc8)]['current'],_0x3e443e['node'][_0x4923f3(0x16b)]=_0x1a1953,_0xe363bc['_treeNodePropertiesBeforeFullValue'](_0x1a1953,_0x3e443e);}let _0x1533a9;_0x3830e6[_0x4d7e42(0xbc)]&&(_0x1533a9=_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)],_0x1533a9&&(_0x3830e6['console'][_0x4d7e42(0x184)]=function(){}));try{try{_0x1ebf24[_0x4d7e42(0xe3)]++,_0x1ebf24['autoExpand']&&_0x1ebf24[_0x4d7e42(0x118)]['push'](_0x5b6b91);var _0x55a2c4,_0x5cbc7d,_0x10ebd6,_0x38ec49,_0x46d06f=[],_0x4ef003=[],_0x33c92e,_0xe8efc0=this[_0x4d7e42(0x182)](_0x5b6b91),_0x5b392f=_0xe8efc0===_0x4d7e42(0x130),_0x55d894=!0x1,_0x65caf4=_0xe8efc0===_0x4d7e42(0x13e),_0x512734=this['_isPrimitiveType'](_0xe8efc0),_0x3d6d36=this[_0x4d7e42(0x15d)](_0xe8efc0),_0x2d65b0=_0x512734||_0x3d6d36,_0x4b6f05={},_0x419e4c=0x0,_0x2bca20=!0x1,_0x454078,_0xed6526=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x1ebf24['depth']){if(_0x5b392f){if(_0x5cbc7d=_0x5b6b91['length'],_0x5cbc7d>_0x1ebf24[_0x4d7e42(0x11c)]){for(_0x10ebd6=0x0,_0x38ec49=_0x1ebf24[_0x4d7e42(0x11c)],_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003[_0x4d7e42(0x157)](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));_0x1f7b5d['cappedElements']=!0x0;}else{for(_0x10ebd6=0x0,_0x38ec49=_0x5cbc7d,_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));}_0x1ebf24[_0x4d7e42(0x9d)]+=_0x4ef003[_0x4d7e42(0x14f)];}if(!(_0xe8efc0==='null'||_0xe8efc0==='undefined')&&!_0x512734&&_0xe8efc0!==_0x4d7e42(0x106)&&_0xe8efc0!=='Buffer'&&_0xe8efc0!=='bigint'){var _0xfca776=_0x4f3c70['props']||_0x1ebf24[_0x4d7e42(0x180)];if(this['_isSet'](_0x5b6b91)?(_0x55a2c4=0x0,_0x5b6b91['forEach'](function(_0x1b3730){var _0x29b12d=_0x4d7e42;if(_0x419e4c++,_0x1ebf24['autoExpandPropertyCount']++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24[_0x29b12d(0xb5)]&&_0x1ebf24[_0x29b12d(0xc0)]&&_0x1ebf24[_0x29b12d(0x9d)]>_0x1ebf24[_0x29b12d(0x177)]){_0x2bca20=!0x0;return;}_0x4ef003['push'](_0xe363bc[_0x29b12d(0x17b)](_0x46d06f,_0x5b6b91,_0x29b12d(0xf6),_0x55a2c4++,_0x1ebf24,function(_0x383398){return function(){return _0x383398;};}(_0x1b3730)));})):this[_0x4d7e42(0x186)](_0x5b6b91)&&_0x5b6b91['forEach'](function(_0x4cd1d9,_0x42ee6b){var _0x3c460e=_0x4d7e42;if(_0x419e4c++,_0x1ebf24[_0x3c460e(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x3c460e(0xc0)]&&_0x1ebf24[_0x3c460e(0x9d)]>_0x1ebf24['autoExpandLimit']){_0x2bca20=!0x0;return;}var _0x2a4101=_0x42ee6b[_0x3c460e(0xa5)]();_0x2a4101[_0x3c460e(0x14f)]>0x64&&(_0x2a4101=_0x2a4101[_0x3c460e(0x146)](0x0,0x64)+_0x3c460e(0x105)),_0x4ef003[_0x3c460e(0x157)](_0xe363bc['_addProperty'](_0x46d06f,_0x5b6b91,_0x3c460e(0x170),_0x2a4101,_0x1ebf24,function(_0x1c45bc){return function(){return _0x1c45bc;};}(_0x4cd1d9)));}),!_0x55d894){try{for(_0x33c92e in _0x5b6b91)if(!(_0x5b392f&&_0xed6526['test'](_0x33c92e))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPropertyCount']>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x150)](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}catch{}if(_0x4b6f05[_0x4d7e42(0x18f)]=!0x0,_0x65caf4&&(_0x4b6f05[_0x4d7e42(0x145)]=!0x0),!_0x2bca20){var _0x469d20=[][_0x4d7e42(0x166)](this[_0x4d7e42(0x15c)](_0x5b6b91))[_0x4d7e42(0x166)](this[_0x4d7e42(0xcf)](_0x5b6b91));for(_0x55a2c4=0x0,_0x5cbc7d=_0x469d20[_0x4d7e42(0x14f)];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)if(_0x33c92e=_0x469d20[_0x55a2c4],!(_0x5b392f&&_0xed6526['test'](_0x33c92e[_0x4d7e42(0xa5)]()))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)&&!_0x4b6f05[_0x4d7e42(0xc7)+_0x33c92e[_0x4d7e42(0xa5)]()]){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24[_0x4d7e42(0xb5)]&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24[_0x4d7e42(0x9d)]>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc['_addObjectProperty'](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}}}}if(_0x1f7b5d['type']=_0xe8efc0,_0x2d65b0?(_0x1f7b5d['value']=_0x5b6b91[_0x4d7e42(0xbf)](),this['_capIfString'](_0xe8efc0,_0x1f7b5d,_0x1ebf24,_0x4f3c70)):_0xe8efc0===_0x4d7e42(0x11f)?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x14b)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='bigint'?_0x1f7b5d[_0x4d7e42(0xce)]=_0x5b6b91[_0x4d7e42(0xa5)]():_0xe8efc0==='RegExp'?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x144)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='symbol'&&this[_0x4d7e42(0x128)]?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x128)][_0x4d7e42(0x119)]['toString']['call'](_0x5b6b91):!_0x1ebf24[_0x4d7e42(0xf0)]&&!(_0xe8efc0===_0x4d7e42(0x187)||_0xe8efc0==='undefined')&&(delete _0x1f7b5d[_0x4d7e42(0xce)],_0x1f7b5d[_0x4d7e42(0xf3)]=!0x0),_0x2bca20&&(_0x1f7b5d['cappedProps']=!0x0),_0x454078=_0x1ebf24['node']['current'],_0x1ebf24[_0x4d7e42(0xc8)]['current']=_0x1f7b5d,this[_0x4d7e42(0xa8)](_0x1f7b5d,_0x1ebf24),_0x4ef003[_0x4d7e42(0x14f)]){for(_0x55a2c4=0x0,_0x5cbc7d=_0x4ef003['length'];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)_0x4ef003[_0x55a2c4](_0x55a2c4);}_0x46d06f['length']&&(_0x1f7b5d[_0x4d7e42(0x180)]=_0x46d06f);}catch(_0x54504a){_0x16ce5f(_0x54504a,_0x1f7b5d,_0x1ebf24);}this[_0x4d7e42(0x10f)](_0x5b6b91,_0x1f7b5d),this[_0x4d7e42(0xe4)](_0x1f7b5d,_0x1ebf24),_0x1ebf24[_0x4d7e42(0xc8)][_0x4d7e42(0x16b)]=_0x454078,_0x1ebf24['level']--,_0x1ebf24[_0x4d7e42(0xc0)]=_0x290e3b,_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPreviousObjects']['pop']();}finally{_0x1533a9&&(_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)]=_0x1533a9);}return _0x1f7b5d;}[_0x40c820(0xcf)](_0xd7ad14){var _0x474a44=_0x40c820;return Object[_0x474a44(0x158)]?Object[_0x474a44(0x158)](_0xd7ad14):[];}[_0x40c820(0xde)](_0x5b06ac){var _0x292c99=_0x40c820;return!!(_0x5b06ac&&_0x3830e6[_0x292c99(0xf6)]&&this[_0x292c99(0x156)](_0x5b06ac)==='[object\\\\x20Set]'&&_0x5b06ac[_0x292c99(0x178)]);}['_blacklistedProperty'](_0x10628d,_0x15c227,_0x5a4f15){var _0x152ffd=_0x40c820;return _0x5a4f15[_0x152ffd(0xa4)]?typeof _0x10628d[_0x15c227]==_0x152ffd(0x13e):!0x1;}[_0x40c820(0x182)](_0x13718c){var _0x2c19d1=_0x40c820,_0x225ae1='';return _0x225ae1=typeof _0x13718c,_0x225ae1==='object'?this[_0x2c19d1(0x156)](_0x13718c)==='[object\\\\x20Array]'?_0x225ae1=_0x2c19d1(0x130):this['_objectToString'](_0x13718c)===_0x2c19d1(0xc1)?_0x225ae1=_0x2c19d1(0x11f):this[_0x2c19d1(0x156)](_0x13718c)===_0x2c19d1(0xbe)?_0x225ae1=_0x2c19d1(0xf9):_0x13718c===null?_0x225ae1=_0x2c19d1(0x187):_0x13718c[_0x2c19d1(0xf7)]&&(_0x225ae1=_0x13718c['constructor'][_0x2c19d1(0xb7)]||_0x225ae1):_0x225ae1===_0x2c19d1(0x142)&&this[_0x2c19d1(0x123)]&&_0x13718c instanceof this['_HTMLAllCollection']&&(_0x225ae1=_0x2c19d1(0x15b)),_0x225ae1;}[_0x40c820(0x156)](_0x37617c){var _0xdf3907=_0x40c820;return Object[_0xdf3907(0x119)]['toString'][_0xdf3907(0xda)](_0x37617c);}[_0x40c820(0xae)](_0x26b95b){var _0x3b9373=_0x40c820;return _0x26b95b===_0x3b9373(0x14d)||_0x26b95b===_0x3b9373(0x101)||_0x26b95b===_0x3b9373(0x12b);}['_isPrimitiveWrapperType'](_0x150515){var _0x2539cd=_0x40c820;return _0x150515===_0x2539cd(0xea)||_0x150515==='String'||_0x150515===_0x2539cd(0xfd);}['_addProperty'](_0x1a647e,_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb){var _0x3c4649=this;return function(_0x5c2af7){var _0x865286=_0x33f3,_0x5ceb03=_0x985088[_0x865286(0xc8)][_0x865286(0x16b)],_0x14ad91=_0x985088[_0x865286(0xc8)]['index'],_0x10beb0=_0x985088[_0x865286(0xc8)][_0x865286(0x9b)];_0x985088['node'][_0x865286(0x9b)]=_0x5ceb03,_0x985088['node'][_0x865286(0xa6)]=typeof _0x551a3a==_0x865286(0x12b)?_0x551a3a:_0x5c2af7,_0x1a647e[_0x865286(0x157)](_0x3c4649[_0x865286(0x17d)](_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb)),_0x985088[_0x865286(0xc8)][_0x865286(0x9b)]=_0x10beb0,_0x985088['node']['index']=_0x14ad91;};}[_0x40c820(0x150)](_0x3e6c99,_0x96cdeb,_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b){var _0x102764=_0x40c820,_0xbcca65=this;return _0x96cdeb[_0x102764(0xc7)+_0x5e1ed6[_0x102764(0xa5)]()]=!0x0,function(_0x4f07e2){var _0x11a9a9=_0x102764,_0x41481f=_0x6bb8c1['node'][_0x11a9a9(0x16b)],_0x45aeeb=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)],_0x44cce6=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0x9b)];_0x6bb8c1['node']['parent']=_0x41481f,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x4f07e2,_0x3e6c99['push'](_0xbcca65[_0x11a9a9(0x17d)](_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b)),_0x6bb8c1['node']['parent']=_0x44cce6,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x45aeeb;};}['_property'](_0x5a954c,_0x11a196,_0x34292c,_0x53d319,_0x300135){var _0x3f13ca=_0x40c820,_0x350c39=this;_0x300135||(_0x300135=function(_0x26467a,_0x467a10){return _0x26467a[_0x467a10];});var _0x112124=_0x34292c[_0x3f13ca(0xa5)](),_0x42837e=_0x53d319[_0x3f13ca(0xc6)]||{},_0x265c6d=_0x53d319['depth'],_0x31debf=_0x53d319[_0x3f13ca(0xb5)];try{var _0x3c8586=this[_0x3f13ca(0x186)](_0x5a954c),_0x5579d4=_0x112124;_0x3c8586&&_0x5579d4[0x0]==='\\\\x27'&&(_0x5579d4=_0x5579d4[_0x3f13ca(0x147)](0x1,_0x5579d4[_0x3f13ca(0x14f)]-0x2));var _0x46f777=_0x53d319[_0x3f13ca(0xc6)]=_0x42837e[_0x3f13ca(0xc7)+_0x5579d4];_0x46f777&&(_0x53d319[_0x3f13ca(0xf0)]=_0x53d319[_0x3f13ca(0xf0)]+0x1),_0x53d319[_0x3f13ca(0xb5)]=!!_0x46f777;var _0x14534f=typeof _0x34292c=='symbol',_0x124dd3={'name':_0x14534f||_0x3c8586?_0x112124:this['_propertyName'](_0x112124)};if(_0x14534f&&(_0x124dd3[_0x3f13ca(0xd4)]=!0x0),!(_0x11a196===_0x3f13ca(0x130)||_0x11a196===_0x3f13ca(0x13b))){var _0x4ea27f=this[_0x3f13ca(0x127)](_0x5a954c,_0x34292c);if(_0x4ea27f&&(_0x4ea27f[_0x3f13ca(0x18c)]&&(_0x124dd3['setter']=!0x0),_0x4ea27f[_0x3f13ca(0x108)]&&!_0x46f777&&!_0x53d319['resolveGetters']))return _0x124dd3[_0x3f13ca(0x172)]=!0x0,this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x3214f9;try{_0x3214f9=_0x300135(_0x5a954c,_0x34292c);}catch(_0x3ef7eb){return _0x124dd3={'name':_0x112124,'type':_0x3f13ca(0xca),'error':_0x3ef7eb['message']},this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x4c3356=this[_0x3f13ca(0x182)](_0x3214f9),_0x18e3f8=this[_0x3f13ca(0xae)](_0x4c3356);if(_0x124dd3[_0x3f13ca(0x10b)]=_0x4c3356,_0x18e3f8)this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x7d7701=_0x3f13ca;_0x124dd3['value']=_0x3214f9[_0x7d7701(0xbf)](),!_0x46f777&&_0x350c39[_0x7d7701(0xe9)](_0x4c3356,_0x124dd3,_0x53d319,{});});else{var _0x275cea=_0x53d319[_0x3f13ca(0xc0)]&&_0x53d319['level']<_0x53d319['autoExpandMaxDepth']&&_0x53d319[_0x3f13ca(0x118)][_0x3f13ca(0x129)](_0x3214f9)<0x0&&_0x4c3356!==_0x3f13ca(0x13e)&&_0x53d319['autoExpandPropertyCount']<_0x53d319['autoExpandLimit'];_0x275cea||_0x53d319[_0x3f13ca(0xe3)]<_0x265c6d||_0x46f777?(this['serialize'](_0x124dd3,_0x3214f9,_0x53d319,_0x46f777||{}),this[_0x3f13ca(0x10f)](_0x3214f9,_0x124dd3)):this['_processTreeNodeResult'](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x2b8765=_0x3f13ca;_0x4c3356===_0x2b8765(0x187)||_0x4c3356===_0x2b8765(0x142)||(delete _0x124dd3[_0x2b8765(0xce)],_0x124dd3[_0x2b8765(0xf3)]=!0x0);});}return _0x124dd3;}finally{_0x53d319['expressionsToEvaluate']=_0x42837e,_0x53d319[_0x3f13ca(0xf0)]=_0x265c6d,_0x53d319['isExpressionToEvaluate']=_0x31debf;}}[_0x40c820(0xe9)](_0x3711dd,_0x3273d6,_0x30712a,_0x2bfd2c){var _0x22c791=_0x40c820,_0x3ed3d6=_0x2bfd2c['strLength']||_0x30712a[_0x22c791(0x126)];if((_0x3711dd==='string'||_0x3711dd==='String')&&_0x3273d6[_0x22c791(0xce)]){let _0xffdbb0=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x14f)];_0x30712a['allStrLength']+=_0xffdbb0,_0x30712a[_0x22c791(0xa2)]>_0x30712a['totalStrLength']?(_0x3273d6[_0x22c791(0xf3)]='',delete _0x3273d6[_0x22c791(0xce)]):_0xffdbb0>_0x3ed3d6&&(_0x3273d6[_0x22c791(0xf3)]=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x147)](0x0,_0x3ed3d6),delete _0x3273d6[_0x22c791(0xce)]);}}['_isMap'](_0x261c40){var _0xd2ec63=_0x40c820;return!!(_0x261c40&&_0x3830e6['Map']&&this[_0xd2ec63(0x156)](_0x261c40)===_0xd2ec63(0x114)&&_0x261c40[_0xd2ec63(0x178)]);}[_0x40c820(0xd5)](_0x24e250){var _0x49be73=_0x40c820;if(_0x24e250[_0x49be73(0x132)](/^\\\\d+$/))return _0x24e250;var _0x1d38d;try{_0x1d38d=JSON['stringify'](''+_0x24e250);}catch{_0x1d38d='\\\\x22'+this['_objectToString'](_0x24e250)+'\\\\x22';}return _0x1d38d['match'](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x1d38d=_0x1d38d[_0x49be73(0x147)](0x1,_0x1d38d[_0x49be73(0x14f)]-0x2):_0x1d38d=_0x1d38d[_0x49be73(0x16d)](/'/g,'\\\\x5c\\\\x27')[_0x49be73(0x16d)](/\\\\\\\\\\\"/g,'\\\\x22')[_0x49be73(0x16d)](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x1d38d;}[_0x40c820(0x133)](_0x1b7571,_0x8f439b,_0x2c2981,_0x188fd5){var _0xf97f10=_0x40c820;this[_0xf97f10(0xa8)](_0x1b7571,_0x8f439b),_0x188fd5&&_0x188fd5(),this[_0xf97f10(0x10f)](_0x2c2981,_0x1b7571),this['_treeNodePropertiesAfterFullValue'](_0x1b7571,_0x8f439b);}[_0x40c820(0xa8)](_0x231eb3,_0x5ea482){var _0x31f690=_0x40c820;this['_setNodeId'](_0x231eb3,_0x5ea482),this[_0x31f690(0xd1)](_0x231eb3,_0x5ea482),this[_0x31f690(0x111)](_0x231eb3,_0x5ea482),this[_0x31f690(0x125)](_0x231eb3,_0x5ea482);}[_0x40c820(0xcb)](_0x10d2e1,_0x3c8083){}[_0x40c820(0xd1)](_0x53d949,_0x188c67){}[_0x40c820(0x163)](_0x3f8259,_0x16e80a){}[_0x40c820(0x122)](_0x25a3a3){return _0x25a3a3===this['_undefined'];}[_0x40c820(0xe4)](_0x44987b,_0x4ed592){var _0x37fb3e=_0x40c820;this[_0x37fb3e(0x163)](_0x44987b,_0x4ed592),this[_0x37fb3e(0xeb)](_0x44987b),_0x4ed592[_0x37fb3e(0xfb)]&&this[_0x37fb3e(0x12d)](_0x44987b),this['_addFunctionsNode'](_0x44987b,_0x4ed592),this[_0x37fb3e(0x17e)](_0x44987b,_0x4ed592),this[_0x37fb3e(0x16c)](_0x44987b);}['_additionalMetadata'](_0x1c2784,_0x4c1dde){var _0x4f21c1=_0x40c820;try{_0x1c2784&&typeof _0x1c2784[_0x4f21c1(0x14f)]==_0x4f21c1(0x12b)&&(_0x4c1dde[_0x4f21c1(0x14f)]=_0x1c2784[_0x4f21c1(0x14f)]);}catch{}if(_0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x12b)||_0x4c1dde[_0x4f21c1(0x10b)]==='Number'){if(isNaN(_0x4c1dde[_0x4f21c1(0xce)]))_0x4c1dde[_0x4f21c1(0x18d)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];else switch(_0x4c1dde[_0x4f21c1(0xce)]){case Number[_0x4f21c1(0xfa)]:_0x4c1dde[_0x4f21c1(0x102)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case Number['NEGATIVE_INFINITY']:_0x4c1dde[_0x4f21c1(0x189)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case 0x0:this['_isNegativeZero'](_0x4c1dde[_0x4f21c1(0xce)])&&(_0x4c1dde[_0x4f21c1(0xd0)]=!0x0);break;}}else _0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x13e)&&typeof _0x1c2784['name']==_0x4f21c1(0x101)&&_0x1c2784[_0x4f21c1(0xb7)]&&_0x4c1dde['name']&&_0x1c2784[_0x4f21c1(0xb7)]!==_0x4c1dde[_0x4f21c1(0xb7)]&&(_0x4c1dde[_0x4f21c1(0xad)]=_0x1c2784[_0x4f21c1(0xb7)]);}['_isNegativeZero'](_0x289882){var _0x1b66c9=_0x40c820;return 0x1/_0x289882===Number[_0x1b66c9(0x181)];}['_sortProps'](_0x3992ee){var _0x3db550=_0x40c820;!_0x3992ee[_0x3db550(0x180)]||!_0x3992ee[_0x3db550(0x180)][_0x3db550(0x14f)]||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0x130)||_0x3992ee[_0x3db550(0x10b)]==='Map'||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0xf6)||_0x3992ee[_0x3db550(0x180)][_0x3db550(0x175)](function(_0x57a739,_0x31b40b){var _0x5dcaae=_0x3db550,_0x3d0d50=_0x57a739[_0x5dcaae(0xb7)][_0x5dcaae(0x10e)](),_0xd6d4fc=_0x31b40b[_0x5dcaae(0xb7)]['toLowerCase']();return _0x3d0d50<_0xd6d4fc?-0x1:_0x3d0d50>_0xd6d4fc?0x1:0x0;});}[_0x40c820(0xe8)](_0x12537a,_0x57f3dc){var _0x2884a4=_0x40c820;if(!(_0x57f3dc[_0x2884a4(0xa4)]||!_0x12537a['props']||!_0x12537a[_0x2884a4(0x180)][_0x2884a4(0x14f)])){for(var _0x53c006=[],_0x347d6e=[],_0x52e85a=0x0,_0x273297=_0x12537a['props']['length'];_0x52e85a<_0x273297;_0x52e85a++){var _0x1ee5b3=_0x12537a[_0x2884a4(0x180)][_0x52e85a];_0x1ee5b3[_0x2884a4(0x10b)]==='function'?_0x53c006[_0x2884a4(0x157)](_0x1ee5b3):_0x347d6e['push'](_0x1ee5b3);}if(!(!_0x347d6e[_0x2884a4(0x14f)]||_0x53c006[_0x2884a4(0x14f)]<=0x1)){_0x12537a[_0x2884a4(0x180)]=_0x347d6e;var _0x15f515={'functionsNode':!0x0,'props':_0x53c006};this[_0x2884a4(0xcb)](_0x15f515,_0x57f3dc),this[_0x2884a4(0x163)](_0x15f515,_0x57f3dc),this[_0x2884a4(0xeb)](_0x15f515),this[_0x2884a4(0x125)](_0x15f515,_0x57f3dc),_0x15f515['id']+='\\\\x20f',_0x12537a['props']['unshift'](_0x15f515);}}}['_addLoadNode'](_0x5bea6e,_0x14049e){}[_0x40c820(0xeb)](_0x199084){}[_0x40c820(0x98)](_0xf50c17){var _0x35cb98=_0x40c820;return Array['isArray'](_0xf50c17)||typeof _0xf50c17==_0x35cb98(0x117)&&this['_objectToString'](_0xf50c17)===_0x35cb98(0xdc);}[_0x40c820(0x125)](_0x3ea390,_0x54c209){}[_0x40c820(0x16c)](_0x25cdb9){var _0x1aa0a5=_0x40c820;delete _0x25cdb9[_0x1aa0a5(0x136)],delete _0x25cdb9[_0x1aa0a5(0x143)],delete _0x25cdb9[_0x1aa0a5(0x138)];}['_setNodeExpressionPath'](_0x17f351,_0x40c77e){}}let _0x459cb0=new _0x1e3ba1(),_0x218fe5={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x11fc4c={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x482c8e(_0x50675f,_0x2f7559,_0x19c481,_0x2c8a95,_0x245f16,_0x24484e){var _0x4c67af=_0x40c820;let _0x3ce9b8,_0x1af844;try{_0x1af844=_0x1169a5(),_0x3ce9b8=_0x2f8209[_0x2f7559],!_0x3ce9b8||_0x1af844-_0x3ce9b8['ts']>0x1f4&&_0x3ce9b8[_0x4c67af(0xaa)]&&_0x3ce9b8['time']/_0x3ce9b8[_0x4c67af(0xaa)]<0x64?(_0x2f8209[_0x2f7559]=_0x3ce9b8={'count':0x0,'time':0x0,'ts':_0x1af844},_0x2f8209[_0x4c67af(0xc5)]={}):_0x1af844-_0x2f8209[_0x4c67af(0xc5)]['ts']>0x32&&_0x2f8209['hits']['count']&&_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x161)]/_0x2f8209[_0x4c67af(0xc5)]['count']<0x64&&(_0x2f8209[_0x4c67af(0xc5)]={});let _0x157126=[],_0x1464d6=_0x3ce9b8[_0x4c67af(0x112)]||_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]?_0x11fc4c:_0x218fe5,_0x553948=_0x2ff7b4=>{var _0x3a1316=_0x4c67af;let _0x5d676c={};return _0x5d676c[_0x3a1316(0x180)]=_0x2ff7b4[_0x3a1316(0x180)],_0x5d676c['elements']=_0x2ff7b4['elements'],_0x5d676c[_0x3a1316(0x126)]=_0x2ff7b4[_0x3a1316(0x126)],_0x5d676c['totalStrLength']=_0x2ff7b4['totalStrLength'],_0x5d676c['autoExpandLimit']=_0x2ff7b4[_0x3a1316(0x177)],_0x5d676c[_0x3a1316(0x115)]=_0x2ff7b4['autoExpandMaxDepth'],_0x5d676c['sortProps']=!0x1,_0x5d676c['noFunctions']=!_0x45a6b5,_0x5d676c['depth']=0x1,_0x5d676c[_0x3a1316(0xe3)]=0x0,_0x5d676c['expId']='root_exp_id',_0x5d676c[_0x3a1316(0x140)]=_0x3a1316(0x151),_0x5d676c['autoExpand']=!0x0,_0x5d676c[_0x3a1316(0x118)]=[],_0x5d676c[_0x3a1316(0x9d)]=0x0,_0x5d676c[_0x3a1316(0x168)]=!0x0,_0x5d676c['allStrLength']=0x0,_0x5d676c[_0x3a1316(0xc8)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0x5d676c;};for(var _0x33de8f=0x0;_0x33de8f<_0x245f16[_0x4c67af(0x14f)];_0x33de8f++)_0x157126[_0x4c67af(0x157)](_0x459cb0[_0x4c67af(0x14a)]({'timeNode':_0x50675f===_0x4c67af(0x161)||void 0x0},_0x245f16[_0x33de8f],_0x553948(_0x1464d6),{}));if(_0x50675f==='trace'||_0x50675f===_0x4c67af(0x184)){let _0x1d9735=Error[_0x4c67af(0xd7)];try{Error[_0x4c67af(0xd7)]=0x1/0x0,_0x157126[_0x4c67af(0x157)](_0x459cb0['serialize']({'stackNode':!0x0},new Error()['stack'],_0x553948(_0x1464d6),{'strLength':0x1/0x0}));}finally{Error[_0x4c67af(0xd7)]=_0x1d9735;}}return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':_0x157126,'id':_0x2f7559,'context':_0x24484e}]};}catch(_0x2d5a77){return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':[{'type':_0x4c67af(0xca),'error':_0x2d5a77&&_0x2d5a77[_0x4c67af(0xb4)]}],'id':_0x2f7559,'context':_0x24484e}]};}finally{try{if(_0x3ce9b8&&_0x1af844){let _0xff386f=_0x1169a5();_0x3ce9b8[_0x4c67af(0xaa)]++,_0x3ce9b8['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x3ce9b8['ts']=_0xff386f,_0x2f8209[_0x4c67af(0xc5)]['count']++,_0x2f8209[_0x4c67af(0xc5)]['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x2f8209['hits']['ts']=_0xff386f,(_0x3ce9b8[_0x4c67af(0xaa)]>0x32||_0x3ce9b8[_0x4c67af(0x161)]>0x64)&&(_0x3ce9b8['reduceLimits']=!0x0),(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0xaa)]>0x3e8||_0x2f8209[_0x4c67af(0xc5)]['time']>0x12c)&&(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]=!0x0);}}catch{}}}return _0x482c8e;}((_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x296e29,_0x567fe9,_0x14adfa,_0x6b3989,_0x593945,_0x42f609)=>{var _0x543ef9=_0x418f23;if(_0x12a02f[_0x543ef9(0xdb)])return _0x12a02f[_0x543ef9(0xdb)];if(!X(_0x12a02f,_0x14adfa,_0x1164b7))return _0x12a02f[_0x543ef9(0xdb)]={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x12a02f[_0x543ef9(0xdb)];let _0x5a7d78=B(_0x12a02f),_0x236b4f=_0x5a7d78[_0x543ef9(0xd3)],_0x57b9d9=_0x5a7d78['timeStamp'],_0x163b61=_0x5a7d78[_0x543ef9(0x11b)],_0x384cd9={'hits':{},'ts':{}},_0x9c7997=J(_0x12a02f,_0x6b3989,_0x384cd9,_0x296e29),_0x3ffb36=_0xa1ec34=>{_0x384cd9['ts'][_0xa1ec34]=_0x57b9d9();},_0x4ce4d2=(_0x173258,_0x2e0c6d)=>{var _0x3b6e53=_0x543ef9;let _0x2b64c1=_0x384cd9['ts'][_0x2e0c6d];if(delete _0x384cd9['ts'][_0x2e0c6d],_0x2b64c1){let _0x557981=_0x236b4f(_0x2b64c1,_0x57b9d9());_0x593a90(_0x9c7997(_0x3b6e53(0x161),_0x173258,_0x163b61(),_0x50e896,[_0x557981],_0x2e0c6d));}},_0x46c5f3=_0x1f105e=>{var _0x1152c8=_0x543ef9,_0x4a2783;return _0x1164b7===_0x1152c8(0x110)&&_0x12a02f[_0x1152c8(0x152)]&&((_0x4a2783=_0x1f105e==null?void 0x0:_0x1f105e[_0x1152c8(0xab)])==null?void 0x0:_0x4a2783[_0x1152c8(0x14f)])&&(_0x1f105e[_0x1152c8(0xab)][0x0][_0x1152c8(0x152)]=_0x12a02f[_0x1152c8(0x152)]),_0x1f105e;};_0x12a02f[_0x543ef9(0xdb)]={'consoleLog':(_0x204f4b,_0x3e1804)=>{var _0x309615=_0x543ef9;_0x12a02f['console'][_0x309615(0xff)][_0x309615(0xb7)]!==_0x309615(0x134)&&_0x593a90(_0x9c7997(_0x309615(0xff),_0x204f4b,_0x163b61(),_0x50e896,_0x3e1804));},'consoleTrace':(_0x267a3f,_0x51c339)=>{var _0x4c4943=_0x543ef9,_0x33fd8c,_0x24e61f;_0x12a02f[_0x4c4943(0xbc)][_0x4c4943(0xff)]['name']!==_0x4c4943(0xbd)&&((_0x24e61f=(_0x33fd8c=_0x12a02f[_0x4c4943(0x104)])==null?void 0x0:_0x33fd8c['versions'])!=null&&_0x24e61f[_0x4c4943(0xc8)]&&(_0x12a02f[_0x4c4943(0x165)]=!0x0),_0x593a90(_0x46c5f3(_0x9c7997(_0x4c4943(0xc4),_0x267a3f,_0x163b61(),_0x50e896,_0x51c339))));},'consoleError':(_0xf7f1fc,_0x1384d7)=>{var _0x28d83c=_0x543ef9;_0x12a02f['_ninjaIgnoreNextError']=!0x0,_0x593a90(_0x46c5f3(_0x9c7997(_0x28d83c(0x184),_0xf7f1fc,_0x163b61(),_0x50e896,_0x1384d7)));},'consoleTime':_0x2ad865=>{_0x3ffb36(_0x2ad865);},'consoleTimeEnd':(_0x3c91cf,_0x308c8b)=>{_0x4ce4d2(_0x308c8b,_0x3c91cf);},'autoLog':(_0x4bbc9f,_0x3599a3)=>{var _0x598cfa=_0x543ef9;_0x593a90(_0x9c7997(_0x598cfa(0xff),_0x3599a3,_0x163b61(),_0x50e896,[_0x4bbc9f]));},'autoLogMany':(_0x158592,_0x29b77d)=>{var _0x425f64=_0x543ef9;_0x593a90(_0x9c7997(_0x425f64(0xff),_0x158592,_0x163b61(),_0x50e896,_0x29b77d));},'autoTrace':(_0x3f5f9d,_0xc378ab)=>{var _0x377a7d=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x377a7d(0xc4),_0xc378ab,_0x163b61(),_0x50e896,[_0x3f5f9d])));},'autoTraceMany':(_0x2c6f73,_0x35405b)=>{var _0x4f4e7f=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x4f4e7f(0xc4),_0x2c6f73,_0x163b61(),_0x50e896,_0x35405b)));},'autoTime':(_0x4915d6,_0xaaf0db,_0x4c1f1e)=>{_0x3ffb36(_0x4c1f1e);},'autoTimeEnd':(_0x397624,_0x436d57,_0x47b9b8)=>{_0x4ce4d2(_0x436d57,_0x47b9b8);},'coverage':_0x45a646=>{var _0x1e9860=_0x543ef9;_0x593a90({'method':_0x1e9860(0x13a),'version':_0x296e29,'args':[{'id':_0x45a646}]});}};let _0x593a90=H(_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x593945,_0x42f609),_0x50e896=_0x12a02f[_0x543ef9(0xd8)];return _0x12a02f[_0x543ef9(0xdb)];})(globalThis,'127.0.0.1',_0x418f23(0xec),_0x418f23(0x17a),_0x418f23(0x183),'1.0.0','1751470503250',_0x418f23(0x137),_0x418f23(0x18a),_0x418f23(0x10a),_0x418f23(0xb1));\");}catch(e){}};/* istanbul ignore next */function oo_oo(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleLog(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_tr(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleTrace(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_tx(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleError(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_ts(/**@type{any}**/v){try{oo_cm().consoleTime(v);}catch(e){} return v;};/* istanbul ignore next */function oo_te(/**@type{any}**/v, /**@type{any}**/i){try{oo_cm().consoleTimeEnd(v, i);}catch(e){} return v;};/*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/"]}, "metadata": {}, "sourceType": "module"}