<?php

namespace App\Core;

use Illuminate\Support\Facades\Validator;

class CustomResponse extends Validator
{
    static public function success($message = 'Petición exitosa', $data = null)
    {
        if ($data) {
            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => $data
            ]);
        } else {
            return response()->json([
                'success' => true,
                'message' => $message,
            ]);
        }
    }

    static public function failure($message = 'Ocurrió un error')
    {
        return response()->json([
            'success' => false,
            'message' => $message,
        ]);
    }
}