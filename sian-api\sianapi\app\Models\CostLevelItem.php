<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class CostLevelItem
 * 
 * @property int $cost_level_item_id
 * @property string $cost_level_item_name
 * @property string $alias
 * @property bool $status
 * @property bool $cost_level
 * @property bool $system
 * @property string|null $cost_center_ids
 * 
 * @property Collection|Combination[] $combinations
 *
 * @package App\Models
 */
class CostLevelItem extends Model
{
	protected $table = 'cost_level_item';
	protected $primaryKey = 'cost_level_item_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'cost_level' => 'bool',
		'system' => 'bool'
	];

	protected $fillable = [
		'cost_level_item_name',
		'alias',
		'status',
		'cost_level',
		'system',
		'cost_center_ids'
	];

	public function combinations()
	{
		return $this->hasMany(Combination::class, 'level5_id');
	}
}
