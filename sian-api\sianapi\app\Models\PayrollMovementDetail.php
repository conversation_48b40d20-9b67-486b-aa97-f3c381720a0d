<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class PayrollMovementDetail
 * 
 * @property int $movement_id
 * @property int $person_id
 * @property int $concept_id
 * @property float $amount
 * 
 * @property Concept $concept
 * @property PayrollMovementHeader $payroll_movement_header
 *
 * @package App\Models
 */
class PayrollMovementDetail extends Model
{
	protected $table = 'payroll_movement_detail';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'person_id' => 'int',
		'concept_id' => 'int',
		'amount' => 'float'
	];

	protected $fillable = [
		'amount'
	];

	public function concept()
	{
		return $this->belongsTo(Concept::class);
	}

	public function payroll_movement_header()
	{
		return $this->belongsTo(PayrollMovementHeader::class, 'movement_id')
					->where('payroll_movement_header.movement_id', '=', 'payroll_movement_detail.movement_id')
					->where('payroll_movement_header.person_id', '=', 'payroll_movement_detail.person_id');
	}
}
