<?php

namespace App\Http\Controllers\Api\V1\Hour;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;


class HourController extends Controller {
    /**
     *
     *
     * @return \Illuminate\Http\Response
     */

     public function index(Request $request) {
        date_default_timezone_set('America/Lima');
        $date = date('d-m-y H:i:s');
        $host = $request->getHost();

        return response()->json([
            'success' => true,
            'data' => [
                'host' => $host,
                'date' => $date
            ]
        ], 200);
    }

}
