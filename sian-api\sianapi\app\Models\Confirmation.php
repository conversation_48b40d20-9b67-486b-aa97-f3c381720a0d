<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Confirmation
 * 
 * @property int $confirm_id
 * @property int $movement_id
 * @property string $option_type
 * @property int $user_request
 * @property Carbon $request_date
 * @property string|null $state
 * @property int|null $user_confirm
 * @property Carbon|null $confirm_date
 * @property string|null $reason
 * @property string|null $response_reason
 * @property int|null $user_cancel
 * @property Carbon|null $cancel_date
 * 
 * @property Movement $movement
 * @property Person $person
 * @property Collection|Ability[] $abilities
 *
 * @package App\Models
 */
class Confirmation extends Model
{
	protected $table = 'confirmation';
	protected $primaryKey = 'confirm_id';
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'user_request' => 'int',
		'user_confirm' => 'int',
		'user_cancel' => 'int'
	];

	protected $dates = [
		'request_date',
		'confirm_date',
		'cancel_date'
	];

	protected $fillable = [
		'movement_id',
		'option_type',
		'user_request',
		'request_date',
		'state',
		'user_confirm',
		'confirm_date',
		'reason',
		'response_reason',
		'user_cancel',
		'cancel_date'
	];

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function person()
	{
		return $this->belongsTo(Person::class, 'user_request');
	}

	public function abilities()
	{
		return $this->hasMany(Ability::class, 'confirm_id');
	}
}
