<?php

namespace App\Http\Controllers\Api\V1\Commercial;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Tenant;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SalesDashboardController extends Controller
{
    public function getSales(Request $request)
    {
        try {
            // Ejecutar el stored procedure
            $results = DB::select('CALL sp_get_sales()');

            return response()->json([
                'success' => true,
                'data' => $results
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error interno del servidor',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getProgressGoals(Request $request)
    {
        try {
            // Capturar parámetros opcionales desde la request (pueden ser NULL)
            $year = $request->input('year');
            $month = $request->input('month');
            $business_unit_id = $request->input('business_unit_id');
            $store_id = $request->input('store_id');
            $date_from = $request->input('date_from');
            $date_to = $request->input('date_to');
            $date = $request->input('date');

            // Determinar el tipo de búsqueda
            $searchDate = null;
            if (!empty($date)) {
                // Búsqueda por día específico
                $searchDate = 0;
            } elseif (!empty($date_from) && !empty($date_to)) {
                // Búsqueda por rango de fechas
                $searchDate = 1;
            } elseif (!empty($month) && !empty($year)) {
                // Búsqueda por mes y año
                $searchDate = 2;
            }

            // Preparar los parámetros adecuadamente
            // Estas sentencias evitan errores de tipo
            $xsearchDate = $searchDate; // Puede ser un int o null
            $xdate = $date ?: '';
            $xstartDate = $date_from ?: '';
            $xendDate = $date_to ?: '';
            $xmonth = $month ?: null;  // IMPORTANTE: NULL para campos numéricos vacíos
            $xyear = $year ?: null;    // IMPORTANTE: NULL para campos numéricos vacíos
            $idsBusinessUnit = $business_unit_id ?: '';
            $idsStore = $store_id ?: '';

            // Usar DB::statement para ejecutar la consulta con los parámetros correctos
            $sql = "CALL sp_get_progress_of_goals(?, ?, ?, ?, ?, ?, ?, ?)";

            // Ejecutar el procedimiento almacenado con los parámetros ya procesados
            $results = DB::select($sql, [
                $xsearchDate,
                $xdate,
                $xstartDate,
                $xendDate,
                $xmonth,
                $xyear,
                $idsBusinessUnit,
                $idsStore
            ]);

            return response()->json([
                'code' => 200,
                'message' => 'Se procesó su petición.',
                'data' => $results,
                'legend_percentages' => [
                    'red' => 25,
                    'orange' => 26,
                    'yellow' => 51,
                    'green' => 80,
                    'last_updated' =>'11-03-2025'
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => 'Error interno del servidor: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
}
