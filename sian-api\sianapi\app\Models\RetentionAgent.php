<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class RetentionAgent
 * 
 * @property string $ruc
 * @property string $name
 * @property Carbon $from
 * @property string $resolution
 *
 * @package App\Models
 */
class RetentionAgent extends Model
{
	protected $table = 'retention_agent';
	protected $primaryKey = 'ruc';
	public $incrementing = false;
	public $timestamps = false;

	protected $dates = [
		'from'
	];

	protected $fillable = [
		'name',
		'from',
		'resolution'
	];
}
