<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Address
 * 
 * @property int $address_id
 * @property string $owner
 * @property int $owner_id
 * @property string $address
 * @property string $dept_code
 * @property string $prov_code
 * @property string $dist_code
 * @property string|null $reference
 * @property float|null $lat
 * @property float|null $lng
 * @property string|null $type
 * @property int $order
 * 
 * @property Geoloc $geoloc
 *
 * @package App\Models
 */
class Address extends Model
{
	protected $table = 'address';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'address_id' => 'int',
		'owner_id' => 'int',
		'lat' => 'float',
		'lng' => 'float',
		'order' => 'int'
	];

	protected $fillable = [
		'address_id',
		'reference',
		'lat',
		'lng',
		'type',
		'order'
	];

	public function geoloc()
	{
		return $this->belongsTo(Geoloc::class, 'dept_code')
					->where('geoloc.dept_code', '=', 'address.dept_code')
					->where('geoloc.prov_code', '=', 'address.prov_code')
					->where('geoloc.dist_code', '=', 'address.dist_code');
	}

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}
}
