<?php

namespace App\Http\Controllers\Api\V1\Accounting;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\AccountingDay;
use App\Models\AccountingYear;
use App\Models\Multitable;
use Illuminate\Support\Facades\Validator;

class AccountingFileController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getYearItems(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'accountingFileId' => 'required|int'
        ], [
            'accountingFileId.required' => 'El ID del libro contable es obligatorio.'
        ]);

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {
            $a_year = AccountingYear::select('year')
                //->accountingDays()
                ->where('accounting_file_id', $request->accountingFileId)
                //->where('opened', 1)
                ->groupBy('year')
                ->get();

            //$a_year =  AccountingYear::select('year')->addSelect([
            //    'accounting_day' => AccountingDay::whereColumn('opened', 1)
            //        ->groupBy('year')
            //])->get();

            $a_response = [
                'success' => true,
                'data' => [
                    'items' => $a_year
                ]
            ];
        }
        return response()->json($a_response);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getPeriodItems(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'accountingFileId' => 'required|int',
            'year' => 'required|int'
        ], [
            'accountingFileId.required' => 'El ID del libro contable es obligatorio.',
            'year.required' => 'El año es obligatorio.'
        ]);

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {
            $a_period = DB::table('multitable as M1')
                ->join("multitable as M2", "M2.multi_parent_id", "=", "M1.multi_id")
                ->join("accounting_period as AP", "AP.period", "=", "M2.value")
                ->where("AP.accounting_file_id", "=", $request->accountingFileId)
                ->where("AP.year", "=", $request->year)
                ->where("M1.value", "=", Multitable::MONTH_CODE)
                ->select("AP.period AS id", "M2.description AS name")
                ->get();

            $a_response = [
                'success' => true,
                'data' => [
                    'items' => $a_period
                ]
            ];
        }
        return response()->json($a_response);
    }
}
