<?php

namespace App\Http\Controllers\Api\V1\Accounting;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\Accounting\SimpleAccountResource;
use App\Models\Account;
use Illuminate\Support\Facades\Validator;

class AccountController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getItems(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'usable' => 'required|int',
            'accountInitial' => 'string'
        ], [
            'usable.required' => 'Indicador usable es obligatorio.'
        ]);

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {

            $a_accounts = Account::where("status", "=", 1)
                ->where("is_usable", "=", $request->usable)
                ->where("account_code", "like", $request->accountInitial . "%")
                ->get();

            $a_response = [
                'success' => true,
                'data' => [
                    'items' => SimpleAccountResource::collection($a_accounts) 
                ]
            ];
        }
        return response()->json($a_response);
    }
}
