<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class DocumentSerie
 * 
 * @property int $document_serie_id
 * @property string $document_code
 * @property string $document_serie
 * @property int $document_correlative
 * @property string $document_serie_name
 * @property int $print_type_id
 * @property bool $status
 * @property int $min_correlative
 * @property int $max_correlative
 * @property string|null $format
 * @property int $store_id
 * @property bool $store_default
 * @property bool $is_store_defaultable
 * 
 * @property Document $document
 * @property PrintType $print_type
 * @property Store $store
 *
 * @package App\Models
 */
class DocumentSerie extends Model
{
	protected $table = 'document_serie';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'document_serie_id' => 'int',
		'document_correlative' => 'int',
		'print_type_id' => 'int',
		'status' => 'bool',
		'min_correlative' => 'int',
		'max_correlative' => 'int',
		'store_id' => 'int',
		'store_default' => 'bool',
		'is_store_defaultable' => 'bool'
	];

	protected $fillable = [
		'document_serie_id',
		'document_correlative',
		'document_serie_name',
		'print_type_id',
		'status',
		'min_correlative',
		'max_correlative',
		'format',
		'store_id',
		'store_default',
		'is_store_defaultable'
	];

	public function document()
	{
		return $this->belongsTo(Document::class, 'document_code');
	}

	public function print_type()
	{
		return $this->belongsTo(PrintType::class);
	}

	public function store()
	{
		return $this->belongsTo(Store::class);
	}
}
