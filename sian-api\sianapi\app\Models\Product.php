<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Product
 *
 * @property int $product_id
 * @property string $product_name
 * @property string $alias
 * @property string $web_name
 * @property string $web_alias
 * @property string|null $description
 * @property int $expiration
 * @property bool $frontend
 * @property bool $outstanding
 * @property bool $advanced
 * @property bool $status
 * @property string $product_type
 * @property bool $perception_affected
 * @property bool $allow_decimals
 * @property bool $price_mode
 * @property bool $is_recent
 * @property bool $in_offer
 * @property int $active_promotions
 * @property bool $rating
 * @property bool $web_enabled
 * @property bool $skip_cost_validation
 * @property bool $print_dispatch_ticket
 * @property float $commercial_unit_stock
 * @property float $commercial_default_stock
 * @property string $commercial_mixed_stock
 * @property float $web_unit_stock
 * @property float $web_default_stock
 * @property string $web_mixed_stock
 * @property int|null $item_type_id
 * @property string $price_currency
 * @property bool $in_kiosk
 * @property float $uref_cost_pen
 * @property float $uref_cost_usd
 * @property int|null $parent_product_id
 *
 * @property-read Multitable|null $multitable
 * @property-read Combo|null $combo
 * @property-read Merchandise|null $merchandise
 * @property-read Collection|MovementSetting[] $movement_settings
 * @property-read Collection|Presentation[] $presentations
 * @property-read Service|null $service
 * @property-read Collection|ProductLink[] $productLinks
 * @property-read Product|null $parentProduct
 *
 * @package App\Models
 */
class Product extends Model {
    protected $table = 'product';
    protected $primaryKey = 'product_id';
    public $timestamps = false;

    protected $casts = [
        'expiration' => 'int',
        'frontend' => 'bool',
        'outstanding' => 'bool',
        'advanced' => 'bool',
        'status' => 'bool',
        'perception_affected' => 'bool',
        'allow_decimals' => 'bool',
        'price_mode' => 'bool',
        'is_recent' => 'bool',
        'in_offer' => 'bool',
        'rating' => 'bool',
        'web_enabled' => 'bool',
        'skip_cost_validation' => 'bool',
        'print_dispatch_ticket' => 'bool',
        'commercial_unit_stock' => 'float',
        'commercial_default_stock' => 'float',
        'web_unit_stock' => 'float',
        'web_default_stock' => 'float',
        'in_kiosk' => 'bool',
        'uref_cost_pen' => 'float',
        'uref_cost_usd' => 'float',
    ];

    protected $fillable = [
        'product_name',
        'alias',
        'web_name',
        'web_alias',
        'description',
        'expiration',
        'frontend',
        'outstanding',
        'advanced',
        'status',
        'product_type',
        'perception_affected',
        'allow_decimals',
        'price_mode',
        'is_recent',
        'in_offer',
        'active_promotions',
        'rating',
        'web_enabled',
        'skip_cost_validation',
        'print_dispatch_ticket',
        'commercial_unit_stock',
        'commercial_default_stock',
        'commercial_mixed_stock',
        'web_unit_stock',
        'web_default_stock',
        'web_mixed_stock',
        'item_type_id',
        'price_currency',
        'in_kiosk',
        'uref_cost_pen',
        'uref_cost_usd',
    ];
    public function multitable() {
        return $this->belongsTo(Multitable::class, 'item_type_id');
    }

    public function combo() {
        return $this->hasOne(Combo::class);
    }

    public function merchandise() {
        return $this->hasOne(Merchandise::class);
    }

    public function movement_settings() {
        return $this->hasMany(MovementSetting::class);
    }

    public function presentations() {
        return $this->hasMany(Presentation::class,'product_id','product_id');
    }

    public function service() {
        return $this->hasOne(Service::class);
    }

    public function productLinks() {
        return $this->hasMany(ProductLink::class, 'product_id', 'product_parent_id');
    }
}
