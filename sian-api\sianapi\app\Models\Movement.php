<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Movement
 * 
 * @property int $movement_id
 * @property string $movement_code
 * @property string $document_code
 * @property string $document_serie
 * @property int $document_correlative
 * @property int|null $issuer_id
 * @property string $operation_code
 * @property string $route
 * @property Carbon $register_date
 * @property Carbon $emission_date
 * @property Carbon $expiration_date
 * @property string|null $observation
 * @property bool $status
 * @property int $print_count
 * @property bool $locked
 * @property string $type
 * @property float $exchange_rate
 * @property string $currency
 * @property string $direction
 * @property int $person_id
 * @property int $aux_person_id
 * @property int $mask_person_id
 * @property int $store_id
 * @property string|null $digest_value
 * @property bool $sunat_sent
 * @property string $scope
 * @property int $link_count
 * @property bool $open_logs
 * @property int $node
 * @property int $business_unit_id
 * @property int|null $project_id
 * @property int|null $agreement_id
 * @property int|null $api_user_variant_id
 * @property int|null $web_user_id
 * @property int $attachment_count
 * @property int|null $person_in_charge_id
 * 
 * @property Project|null $project
 * @property Multitable|null $multitable
 * @property ApiUserVariant|null $api_user_variant
 * @property Person|null $person
 * @property BusinessUnit $business_unit
 * @property Document $document
 * @property Operation $operation
 * @property Scenario $scenario
 * @property Store $store
 * @property WebUser|null $web_user
 * @property Ability $ability
 * @property AccountingMovement $accounting_movement
 * @property AdvancedOption $advanced_option
 * @property AutomaticMovement $automatic_movement
 * @property Collection|Cashbox[] $cashboxes
 * @property Collection|CommercialCase[] $commercial_cases
 * @property CommercialMovement $commercial_movement
 * @property Collection|Confirmation[] $confirmations
 * @property DynamicMovement $dynamic_movement
 * @property Collection|Entry[] $entries
 * @property Collection|EntryGroup[] $entry_groups
 * @property ExtraInfo $extra_info
 * @property Collection|InventoryDetailResult[] $inventory_detail_results
 * @property Collection|Item[] $items
 * @property Collection|Kardex[] $kardexes
 * @property LoanMovement $loan_movement
 * @property MobilityMovement $mobility_movement
 * @property Collection|MovementLink[] $movement_links
 * @property Collection|MovementLog[] $movement_logs
 * @property MultipleMovement $multiple_movement
 * @property Collection|PaymentLink[] $payment_links
 * @property PayrollMovement $payroll_movement
 * @property Collection|PromotionStock[] $promotion_stocks
 * @property Collection|ScheduledEntry[] $scheduled_entries
 * @property Collection|SentMovement[] $sent_movements
 * @property Collection|Stock[] $stocks
 * @property Collection|TransformationResult[] $transformation_results
 * @property Collection|Warehouse[] $warehouses
 *
 * @package App\Models
 */
class Movement extends Model
{
	protected $table = 'movement';
	protected $primaryKey = 'movement_id';
	public $timestamps = false;

	protected $casts = [
		'document_correlative' => 'int',
		'issuer_id' => 'int',
		'status' => 'bool',
		'print_count' => 'int',
		'locked' => 'bool',
		'exchange_rate' => 'float',
		'person_id' => 'int',
		'aux_person_id' => 'int',
		'mask_person_id' => 'int',
		'store_id' => 'int',
		'sunat_sent' => 'bool',
		'link_count' => 'int',
		'open_logs' => 'bool',
		'node' => 'int',
		'business_unit_id' => 'int',
		'project_id' => 'int',
		'agreement_id' => 'int',
		'api_user_variant_id' => 'int',
		'web_user_id' => 'int',
		'attachment_count' => 'int',
		'person_in_charge_id' => 'int'
	];

	protected $dates = [
		'register_date',
		'emission_date',
		'expiration_date'
	];

	protected $fillable = [
		'movement_code',
		'document_code',
		'document_serie',
		'document_correlative',
		'issuer_id',
		'operation_code',
		'route',
		'register_date',
		'emission_date',
		'expiration_date',
		'observation',
		'status',
		'print_count',
		'locked',
		'type',
		'exchange_rate',
		'currency',
		'direction',
		'person_id',
		'aux_person_id',
		'mask_person_id',
		'store_id',
		'digest_value',
		'sunat_sent',
		'scope',
		'link_count',
		'open_logs',
		'node',
		'business_unit_id',
		'project_id',
		'agreement_id',
		'api_user_variant_id',
		'web_user_id',
		'attachment_count',
		'person_in_charge_id'
	];

	public function project()
	{
		return $this->belongsTo(Project::class);
	}

	public function multitable()
	{
		return $this->belongsTo(Multitable::class, 'agreement_id');
	}

	public function api_user_variant()
	{
		return $this->belongsTo(ApiUserVariant::class);
	}

	public function person()
	{
		return $this->belongsTo(Person::class, 'person_in_charge_id');
	}

	public function business_unit()
	{
		return $this->belongsTo(BusinessUnit::class);
	}

	public function document()
	{
		return $this->belongsTo(Document::class, 'document_code');
	}

	public function operation()
	{
		return $this->belongsTo(Operation::class, 'operation_code');
	}

	public function scenario()
	{
		return $this->belongsTo(Scenario::class, 'route');
	}

	public function store()
	{
		return $this->belongsTo(Store::class);
	}

	public function web_user()
	{
		return $this->belongsTo(WebUser::class);
	}

	public function ability()
	{
		return $this->hasOne(Ability::class);
	}

	public function accounting_movement()
	{
		return $this->hasOne(AccountingMovement::class);
	}

	public function advanced_option()
	{
		return $this->hasOne(AdvancedOption::class);
	}

	public function automatic_movement()
	{
		return $this->hasOne(AutomaticMovement::class);
	}

	public function cashboxes()
	{
		return $this->belongsToMany(Cashbox::class)
					->withPivot('total_pen', 'itf_pen', 'port_pen', 'desgravamen_pen', 'interest_pen', 'cash_round_pen', 'cash_round_income_pen', 'real_pen', 'cash_pen', 'total_usd', 'itf_usd', 'port_usd', 'desgravamen_usd', 'interest_usd', 'cash_round_usd', 'cash_round_income_usd', 'real_usd', 'cash_usd', 'type', 'reference_number', 'credit_card', 'calculate_cash_round', 'calculate_change', 'confirmed');
	}

	public function commercial_cases()
	{
		return $this->hasMany(CommercialCase::class);
	}

	public function commercial_movement()
	{
		return $this->hasOne(CommercialMovement::class);
	}

	public function confirmations()
	{
		return $this->hasMany(Confirmation::class);
	}

	public function dynamic_movement()
	{
		return $this->hasOne(DynamicMovement::class);
	}

	public function entries()
	{
		return $this->hasMany(Entry::class, 'reference_movement_id');
	}

	public function entry_groups()
	{
		return $this->hasMany(EntryGroup::class);
	}

	public function extra_info()
	{
		return $this->hasOne(ExtraInfo::class);
	}

	public function inventory_detail_results()
	{
		return $this->hasMany(InventoryDetailResult::class);
	}

	public function items()
	{
		return $this->hasMany(Item::class);
	}

	public function kardexes()
	{
		return $this->hasMany(Kardex::class);
	}

	public function loan_movement()
	{
		return $this->hasOne(LoanMovement::class);
	}

	public function mobility_movement()
	{
		return $this->hasOne(MobilityMovement::class);
	}

	public function movement_links()
	{
		return $this->hasMany(MovementLink::class, 'movement_parent_id');
	}

	public function movement_logs()
	{
		return $this->hasMany(MovementLog::class);
	}

	public function multiple_movement()
	{
		return $this->hasOne(MultipleMovement::class);
	}

	public function payment_links()
	{
		return $this->hasMany(PaymentLink::class);
	}

	public function payroll_movement()
	{
		return $this->hasOne(PayrollMovement::class);
	}

	public function promotion_stocks()
	{
		return $this->hasMany(PromotionStock::class);
	}

	public function scheduled_entries()
	{
		return $this->hasMany(ScheduledEntry::class, 'movement_parent_id');
	}

	public function sent_movements()
	{
		return $this->hasMany(SentMovement::class);
	}

	public function stocks()
	{
		return $this->hasMany(Stock::class);
	}

	public function transformation_results()
	{
		return $this->hasMany(TransformationResult::class);
	}

	public function warehouses()
	{
		return $this->belongsToMany(Warehouse::class, 'warehouse_movement')
					->withPivot('munit_quantity', 'munit_balance', 'bnet_pen', 'inet_pen', 'enet_pen', 'dnet_pen', 'net_pen', 'bnet_usd', 'inet_usd', 'enet_usd', 'dnet_usd', 'net_usd', 'allow_duplicate', 'transport_data', 'transport_company_id', 'transportist_id', 'fixed_asset_id', 'locker_request_id', 'inventory_id');
	}
}
