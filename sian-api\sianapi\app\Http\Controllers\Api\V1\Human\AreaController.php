<?php

namespace App\Http\Controllers\Api\V1\Human;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\Human\SimpleAreaResource;
use App\Models\Area;

class AreaController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getItems(Request $request)
    {
        $a_response = [
            'success' => true,
            'data' => [
                'items' => SimpleAreaResource::collection(Area::where("status", 1)->get())
            ]
        ];
        return response()->json($a_response);
    }
}
