<?php

namespace App\Http\Controllers\Api\V1\Recipe;

use App\Http\Controllers\Api\V1\Merchandise\MerchandiseController;
use App\Models\Fake\Currency;
use App\Models\GlobalVar;
use App\Models\Multitable;
use App\Models\Procedures\SpDefinePrices;
use App\Models\Procedures\SpGetSumPricesCost;
use App\Models\Procedures\SpGetProductPresentations;
use App\Models\Procedures\SpGetProportionRecipe;
use App\Models\ProductLink;
use App\Models\Recipe;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class RecipeController extends Controller {
    /**
     *
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        $validate = Validator::make($request->all(), [
            'page' => 'required|integer',
            'pageSize' => 'required|integer',
            'productName' => 'sometimes|string',
            'subline' => 'sometimes|string',
            'line' => 'sometimes|string',
            'type' => 'sometimes|string'
        ]);

        if ($validate->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validate->errors()
            ], 400);
        }


        return $this->getRecipes($request);
    }

    public function show($recipeID) {
        try {
            $recipe = Recipe::with(['product', 'createUser', 'updateUser', 'productLinks'])->where('status', '=', 1)
                ->findOrFail($recipeID);

            $presentations = [];
            $pricesProduct = SpGetProductPresentations::getAssociative(
                SpGetProductPresentations::MODE_COMBOBOX_WITH_PRICES,
                [$recipe->product_id],
                Currency::PEN,
                1
            );

            // SpGetProductPresentations::execute([$recipe->product_id]);

            $presentation = null;

            if ($pricesProduct && count($pricesProduct) > 0) {
                $presentationsResult = $pricesProduct[$recipe->product_id];
                foreach ($presentationsResult as $pres) {
                    $presentationItem = new \stdClass();
                    $presentationItem = $pres;
                    $presentationItem->mprice = floatVal($pres->mprice);
                    $presentationItem->aprice = floatVal($pres->aprice);
                    $presentationItem->wprice = floatVal($pres->wprice);
                    $presentationItem->imprice = floatVal($pres->imprice);
                    $presentationItem->iaprice = floatVal($pres->iaprice);
                    $presentationItem->iwprice = floatVal($pres->iwprice);

                    if ($recipe->equivalence === $presentationItem->equivalence) {
                        $presentation = $presentationItem;
                    }

                    $presentations[$presentationItem->equivalence] = $presentationItem;
                }
            }


            if (!$presentation) {
                $presentation = reset($presentations) ?? ["iaprice" => 0];
            }



            $product_links = ProductLink::with(['merchandise' => function ($query) {
                $query->select('product_id', 'subline_id', 'mark_id')->with([
                    'product' => function ($productQuery) {
                        $productQuery->select('product_id', 'product_name');
                    },
                    'subline' => function ($sublineQuery) {
                        $sublineQuery->select('subline_id', 'subline_name', 'line_id')->with(['line' => function ($queryLine) {
                            $queryLine->select('line_id', 'line_name');
                        }]);
                    },
                    'mark' => function ($markQuery) {
                        $markQuery->select('mark_id', 'mark_name');
                    }
                ]);
            }])
                ->where('product_parent_id', '=', $recipe->product_id)
                ->get(['product_link_id', 'equivalence', 'product_id', 'quantity', 'unit_cost', 'waste_percentage']);

            $ingredients = [];

            $ids = [];

            $queryResults = [];

            foreach ($product_links as $result) {
                Log::info(json_encode(['product_id' => $result->product_id, 'unit_cost' => $result->unit_cost]));
                $ids[] = $result->product_id;
                $product_link = new \stdClass();
                $product_link->product_link_id = $result->product_link_id;
                $product_link->productID = $result->product_id;
                $product_link->productName = $result->merchandise->product->product_name;
                $product_link->sublineName = $result->merchandise->subline->subline_name;
                $product_link->lineName = $result->merchandise->subline->line->line_name;
                $product_link->markName = $result->merchandise->mark->mark_name;
                $product_link->unitCost = $result->unit_cost;
                $product_link->presQuantity = $result->quantity;
                $product_link->tCost = $result->unit_cost * $result->quantity;
                $product_link->equivalence = $result->equivalence;
                $product_link->presentation = new \stdClass();
                $product_link->presentation->cost = 0;
                $product_link->presentation->icost = 0;
                $product_link->waste_percentage = $this->convertToInteger($result->waste_percentage);

                $product_link->is_sub_recipe = false;
                $sub_recipe = Recipe::where('product_id', '=', $result->product_id)->first();

                if (isset($sub_recipe)) {
                    $product_link->is_sub_recipe = true;
                    $product_link->sub_recipe_type = $sub_recipe->type;
                    $product_link->sub_recipe_id = $sub_recipe->recipe_id;
                }

                $queryResults[] = $product_link;
            }

            $productPresentations = SpGetProductPresentations::getAssociative(
                SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES,
                $ids,
                Currency::PEN,
                1
            );

            foreach ($queryResults as $link) {
                $link->presentations = $productPresentations[$link->productID];
                $equivalence = number_format($link->equivalence, 2, '.', ',');
                if ($link->unitCost > 0) {
                    $productPresentations[$link->productID][$equivalence]->cost = $link->unitCost;
                }
                $link->presentation = $productPresentations[$link->productID][$equivalence];
            }

            $data = [
                "initialFormData" => [
                    "selectedProduct" => [
                        "productName" => $recipe->product->product_name,
                        "productID" => $recipe->product_id,
                        "presentation" => $presentation,
                        "presentations" => $presentations,
                    ],
                    'presQuantity' => $recipe->pres_quantity,
                    'equivalence' => $recipe->equivalence,
                    "description" => $recipe->description,
                    "totalCost" => $recipe->total_material_cost,
                    "type" => $recipe->type
                ],
                "initialTableData" => $queryResults,
                "initialCost" => [
                    "totalMaterialCost" => $recipe->total_material_cost,
                    "marginErrorPercentage" => $recipe->margin_error_percentage,
                    "estimatedMaterialPercentage" => $recipe->estimated_material_percentage,
                    "taxPercentage" => $recipe->tax_percentage,
                    "estimatedPrice" => $recipe->estimated_price,
                    "price" => $recipe->price,
                    "realMaterialPercentage" => $recipe->real_material_percentage,
                ]

            ];

            $recipe['ingredients'] = $ingredients;

            return response()->json([
                'success' => true,
                'data' => $data,
            ]);

        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'Error retrieving recipe: ' . json_encode($ex->__tostring()),
            ], 500);
        }
    }


    public function getRecipeByProductID($productID) {
        try {
            $recipe = Recipe::with(['product'])
                ->where('status', '=', 1)
                ->where('recipe_id', '=', $productID)
                ->first();

            if (!$recipe) {
                return response()->json(['error' => 'Recipe not found'], 404);
            }

            $presentations = SpGetProductPresentations::getAssociative(
                SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES,
                $recipe->product_id,
                Currency::PEN_CAP,
                1
            );

            $recipe->presentations = $presentations[$recipe->product_id];

            return response()->json([
                'success' => true,
                'data' => $recipe,
            ]);

        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'Error retrieving recipe: ' . json_encode($ex->__tostring()),
            ], 500);
        }
    }


    public function oldStore(Request $request) {
        $validate = Validator::make($request->all(), [
            'product_id' => 'required|numeric',
            'description' => 'nullable|string',
            'equivalence' => 'required',
            'type' => 'required|string',
            'total_material_cost' => 'required|numeric',
            'margin_error_percentage' => 'required|numeric',
            'estimated_material_percentage' => 'required|numeric',
            'tax_percentage' => 'required|numeric',
            'estimated_price' => 'required|numeric',
            'price' => 'required|numeric',
            'real_material_percentage' => 'required|numeric',
            'create_user_id' => 'required|numeric',
            'product_links' => 'required|array',
            'update_price' => 'sometimes',
        ]);

        if ($validate->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validate->errors(),
            ], 400);
        }

        try {
            DB::beginTransaction();

            $recipe = null;
            $recipe = Recipe::where('product_id', $request->product_id)->first();
            $data = [
                'product_id' => $request->product_id,
                'description' => $request->description,
                'pres_quantity' => $request->pres_quantity,
                'equivalence' => $request->equivalence,
                'type' => $request->type,
                'total_material_cost' => $request->total_material_cost,
                'margin_error_percentage' => $request->margin_error_percentage,
                'estimated_material_percentage' => $request->estimated_material_percentage,
                'tax_percentage' => $request->tax_percentage,
                'estimated_price' => $request->estimated_price,
                'price' => $request->price,
                'real_material_percentage' => $request->real_material_percentage,
                'status' => true
            ];

            if ($recipe) {
                $recipe->update($data);
                $recipe->update_user_id = $request->create_user_id;
                $recipe->save();
            } else {
                $data['create_user_id'] = $request->create_user_id;
                $recipe = Recipe::create($data);
            }

            $body_data = $request->json()->all();
            $product_links = $body_data['product_links'];
            $saved_product_links = [];

            foreach ($product_links as $product_link) {
                $product_link = ProductLink::create([
                    'product_parent_id' => $product_link['product_parent_id'],
                    'product_id' => $product_link['product_id'],
                    'quantity' => $product_link['quantity'],
                    'equivalence' => $product_link['equivalence'],
                    'unit_cost' => $product_link['unit_cost'],
                    'waste_percentage' => $product_link['waste_percentage'],
                ]);

                $saved_product_links[] = $product_link;
            }

            if (count($saved_product_links) > 0) {
                $recipe['product_links'] = $saved_product_links;
            }

            $update_price = $body_data['update_price'];

            DB::commit();

            if ($update_price) {
                SpDefinePrices::execute($recipe->product_id, 1, $update_price['imprice'], $update_price['iaprice'], $update_price['iwprice']);
            }

            return response()->json([
                'success' => true,
                'data' => $recipe,
            ], 201);

        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'Error creating recipe: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function store(Request $request) {
        $validate = Validator::make($request->all(), [
            'product_id' => 'required|numeric',
            'description' => 'nullable|string',
            'equivalence' => 'required',
            'type' => 'required|string',
            'create_user_id' => 'required|numeric',
        ]);

        if ($validate->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validate->errors(),
            ], 400);
        }

        try {
            DB::beginTransaction();

            $recipe = null;
            $recipe = Recipe::where('product_id', $request->product_id)->first();
            $data = [
                'product_id' => $request->product_id,
                'description' => $request->description,
                'pres_quantity' => $request->pres_quantity,
                'equivalence' => $request->equivalence,
                'type' => $request->type,
                'status' => true,
                'initialized' => false,
            ];

            if ($recipe) {
                $data['update_user_id'] = $request->create_user_id;
                $recipe->update($data);
            } else {
                $data['create_user_id'] = $request->create_user_id;
                $recipe = Recipe::create($data);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $recipe,
            ], 201);

        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'Error creating recipe: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function updateRecipe(Request $request, $recipeID) {
        $validate = Validator::make($request->all(), [
            'update_user_id' => 'required|numeric',
        ]);

        if ($validate->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validate->errors(),
            ], 400);
        }

        try {
            $recipe = Recipe::findOrFail($recipeID);
            $recipe->update($request->all());

            return response()->json(['success' => true]);
        } catch (\Exception $ex) {
            Log::error('Error updating recipe: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error updating recipe: ' . $ex->getMessage(),
            ], 500);
        }

    }

    public function update(Request $request, $recipeID) {

        $validate = Validator::make($request->all(), [
            'update_user_id' => 'required|numeric',
            'update_price' => 'sometimes',
        ]);

        if ($validate->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validate->errors(),
            ], 400);
        }

        try {

            DB::beginTransaction();

            $recipe = Recipe::findOrFail($recipeID);

            $data = $request->json()->all();

            $recipe_changes = $data['recipe_changes'] ?? null;
            $links_changes = $data['links_changes'] ?? null;

            if ($recipe_changes) {
                $recipe->update($recipe_changes);
            }

            if ($links_changes) {
                $add_links = $links_changes['add'] ?? null;
                $update_links = $links_changes['updated'] ?? null;
                $delete_links = $links_changes['deleted'] ?? null;


                $added_links = [];
                $updated_links = [];

                if ($add_links && count($add_links) > 0) {
                    foreach ($add_links as $product_link) {
                        $product_link = ProductLink::create([
                            'product_parent_id' => $product_link['product_parent_id'],
                            'product_id' => $product_link['product_id'],
                            'quantity' => $product_link['quantity'],
                            'equivalence' => $product_link['equivalence'],
                            'unit_cost' => $product_link['unit_cost'],
                            'waste_percentage' => $product_link['waste_percentage'],
                        ]);
                        $added_links[] = $product_link;
                    }
                }

                if ($update_links && count($update_links) > 0) {
                    foreach ($update_links as $product_link) {
                        $product_link_model = ProductLink::findOrFail($product_link['product_link_id']);
                        $product_link_model->update($product_link);
                        $updated_links[] = $product_link_model;
                    }
                }

                if ($delete_links && count($delete_links) > 0) {
                    ProductLink::destroy($delete_links);
                }

            }
            $recipe->update(['initialized' => true]);
            DB::commit();

            $update_price = $data['update_price'];


            if ($update_price) {
                SpDefinePrices::execute($recipe->product_id, 1, $update_price['imprice'], $update_price['iaprice'], $update_price['iwprice']);
            }


            return response()->json([
                'success' => true,
                'data' => ['recipe' => $recipe, 'links' => [
                    'added_links' => $added_links ?? [],
                    'updated_links' => $updated_links ?? [],
                ]],
            ]);

        } catch (\Exception $ex) {
            Log::error('Error updating recipe: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error updating recipe: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function destroy($recipeID) {
        try {
            DB::beginTransaction();

            $recipe = Recipe::findOrFail($recipeID);
            $product_links = ProductLink::where('product_parent_id', $recipe->product_id)->get();

            foreach ($product_links as $product_link) {
                $product_link->delete();
            }

            $recipe->update([
                'status' => false,
                'total_material_cost' => 0,
                'margin_error_percentage' => 0,
                'estimated_material_percentage' => 0,
                'tax_percentage' => 0,
                'estimated_price' => 0,
                'price' => 0,
                'real_material_percentage' => 0,
                'initialized' => false,
            ]);
            $recipe->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => [
                    'message' => 'Receta eliminada Correctamente',
                ]
            ]);

        } catch (\Exception $ex) {
            Log::error('Error deleting recipe: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error deleting recipe: ' . $ex->getMessage(),
            ], 500);
        }
    }

    private function getRecipes(Request $request) {
        try {
            $page = intval($request->query('page'));
            $pageSize = intval($request->query('pageSize'));
            $startIndex = ($page - 1) * $pageSize;

            $query = DB::table('recipe as RE')
                ->join('merchandise as ME', 'ME.product_id', '=', 'RE.product_id')
                ->join('product as P', 'P.product_id', '=', 'ME.product_id')
                ->join('subline as S', 'S.subline_id', '=', 'ME.subline_id')
                ->join('line as L', 'L.line_id', '=', 'S.line_id')
                ->join('user as CU', 'CU.user_id', '=', 'RE.create_user_id')
                ->join('person as CP', 'CP.person_id', '=', 'CU.person_id')
                ->leftJoin('mark as MA', 'MA.mark_id', '=', 'ME.mark_id')
                ->leftJoin('user as UU', 'UU.user_id', '=', 'RE.update_user_id')
                ->leftJoin('person as UP', 'UP.person_id', '=', 'UU.person_id')
                ->where('P.status', '=', 1)
                ->where('RE.status', '=', 1);


            if ($request->has('productName')) {
                $productName = $request->input('productName');
                $query->where('P.product_name', 'like', "%$productName%");
            }

            if ($request->has('subline')) {
                $sublineString = $request->input('subline');
                $sublineArray = explode(',', $sublineString);
                $query->whereIn('S.subline_id', $sublineArray);
            }

            if ($request->has('line')) {
                $lineString = $request->input('line');
                $lineArray = explode(',', $lineString);
                $query->whereIn('L.line_id', $lineArray);
            }

            if ($request->has('startDateEmision') && $request->has('endDateEmision')) {
                $query->whereDate('RE.created_at', '>=', $request->input('startDateEmision'));
                $query->whereDate('RE.created_at', '<=', $request->input('endDateEmision'));
            }

            if ($request->has('type')) {
                $query->where('RE.type', '=', $request->input('type'));
            }


            if ($request->has('type')) {
                $query->where('RE.type', $request->input('type'));
            }

            $totalItems = $query->count();

            $queryResults = $query->select(
                'RE.recipe_id as recipeID',
                'ME.product_id as productID',
                'P.product_name as productName',
                'S.subline_name as sublineName',
                'L.line_name as lineName',
                'RE.created_at as createdAt',
                'RE.updated_at as updatedAt',
                DB::raw("REPLACE(CP.person_name, ',', ' ') as createUserName"),
                DB::raw("REPLACE(UP.person_name, ',', ' ') as updateUserName"),
                'RE.price as price',
                DB::raw("CASE WHEN P.item_type_id = " . Multitable::getMultitableByValue(Multitable::ITEM_TYPE_MERCHANDISE)->multi_id . " THEN true ELSE false END as isMerchandise"),
                'RE.type as type',
                'RE.initialized'
            )->orderBy('RE.recipe_id', 'desc')->offset($startIndex)->limit($pageSize)->get();

            $response = [
                'success' => true,
                'pagination' => [
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'totalRecords' => $totalItems,
                    'totalPages' => ceil($totalItems / $pageSize),
                ],
                'data' => $queryResults,
            ];
            return response()->json($response);

        } catch (\Exception $ex) {
            Log::error('Query error: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Query error: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function getRecipeVariables() {
        try {
            $recipeVariables = GlobalVar::select('margin_error_percentage', 'estimated_material_percentage', 'igv')->findOrFail(1);

            return response()->json([
                'success' => true,
                'data' => $recipeVariables,
            ]);

        } catch (\Exception $ex) {
            Log::error('Error retrieving recipe: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error retrieving recipe: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function getCostAnalysis(Request $request, $recipeID) {
        try {
            $recipe = Recipe::findOrFail($recipeID);

            $product_id = $recipe->product_id;
            $period = $request->query('period');
            $store_id = $request->query('store');

            list($startDate, $endDate, $daysInMonth, $month, $year) = $this->generateDateRange($period);

            $sp_get_proportion_recipe_results = SpGetProportionRecipe::execute($product_id, [$store_id], $startDate, $endDate, SpGetProportionRecipe::MODE_DATE_MANUAL);

            if (empty($sp_get_proportion_recipe_results) || $sp_get_proportion_recipe_results[0]->quantity === null || floatval($sp_get_proportion_recipe_results[0]->quantity) === 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'No se encontraron datos de proporción de receta'
                ], 404);
            }

            $sp_get_proportion_recipe = $sp_get_proportion_recipe_results[0];

            $sp_get_prices_cost_results = SpGetSumPricesCost::execute([$product_id], [$store_id], $startDate, $endDate);

            if (empty($sp_get_prices_cost_results) || $sp_get_prices_cost_results[0]->sale_quantity === null || $sp_get_prices_cost_results[0]->sale_quantity === 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'No se encontraron datos de costos y precios de la receta'
                ], 404);
            }

            $sp_get_prices_cost = $sp_get_prices_cost_results[0];

            $proportion_percentage = floatval($sp_get_proportion_recipe->proportion) ?? 0;
            $sale_quantity = floatval($sp_get_prices_cost->sale_quantity) ?? 1;
            $sum_sale_price = $sp_get_prices_cost->sum_sale_price ?? 0;
            $sale_price = $sum_sale_price / $sale_quantity;

            $productionCosts = $this->getProductionCostsByStore($store_id, $year, $month);

            if (empty($productionCosts)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No se encontraron datos de costos de producción'
                ], 404);
            }

            $production_cost = floatval($sp_get_prices_cost->sum_dispatch_cost) ?? 0;
            $unit_production_costs = $production_cost / $sale_quantity;
            $work_days = $productionCosts['work_days'];


            $global_var = GlobalVar::select('estimated_material_percentage')->findOrFail(1);


            $preparation_costs = [
                'concept_name' => 'Costo de Preparación',
                'total' => floatval($production_cost),
                'production_costs' => floatval($production_cost),
                'unit_production_costs' => $unit_production_costs,
                'percentage' => floatval($global_var->estimated_material_percentage) * 100,
                'concepts' => []
            ];

            $modifiedProductionCosts = [];
            $modifiedProductionCosts[] = $preparation_costs;
            $total_cost = $unit_production_costs;

            $mainConcepts = $productionCosts['concepts'];

            foreach ($mainConcepts as $productionCost) {
                $production_costs = (($productionCost['total'] / $work_days) * $daysInMonth) * $proportion_percentage;
                $production_cost = ($sale_quantity > 0) ? ($production_costs / $sale_quantity) : 0;
                $total_cost += $production_cost;

                $productionCost['production_costs'] = $production_costs;
                $productionCost['unit_production_costs'] = $production_cost;

                $modifiedProductionCosts[] = $productionCost;
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'recipe' => $recipeID,
                    'sale_price' => $sale_price,
                    'sale_quantity' => $sale_quantity,
                    'total_cost' => $total_cost,
                    'work_days' => $work_days,
                    'unit_preparation_costs' => $preparation_costs['unit_production_costs'],
                    'proportion_percentage' => $proportion_percentage,
                    'period_days' => $daysInMonth,
                    'costs' => $modifiedProductionCosts,
                ],
            ]);

        } catch (\Throwable $th) {
            Log::error('Error retrieving recipe: ' . $th->getMessage() . $th->getLine() . $th->getTraceAsString());
            return response()->json([
                'success' => false,
                'error' => 'Error retrieving recipe: ' . $th->getMessage() . $th->getLine() . $th->getTraceAsString()
            ], 500);
        }
    }

    public function getProductCost($startDate, $endDate, $productId, $storeId = null) {
        $query = DB::table('warehouse_merchandise as WM')
            ->select(
                DB::raw('SUM(I.unit_quantity) as total_quantity'),
                DB::raw('SUM(WM.net_pen) as total_net_pen')
            )
            ->join('item as I', 'I.item_id', '=', 'WM.item_id')
            ->join('movement as M', 'M.movement_id', '=', 'WM.movement_id')
            ->where('M.status', "=", 1)
            ->where('M.route', '=', 'warehouse/saleOut')
            ->where('I.product_id', '=', $productId)
            ->where('M.store_id', '=', $storeId)
            ->whereDate('M.emission_date', '>=', $startDate)
            ->whereDate('M.emission_date', '<=', $endDate)
            ->groupBy('I.product_name', 'I.product_id');

        return $query->get();
    }

    public function getProductGrossProfit($startDate, $endDate, $productIds, $storeId = null) {

        $query = DB::table('commercial_movement_product as CMP')
            ->select(
                DB::raw('SUM(I.unit_quantity) as total_quantity'),
                DB::raw('SUM(CMP.net_pen) as total_net_pen')
            )
            ->join('item as I', 'I.item_id', '=', 'CMP.item_id')
            ->join('movement as M', 'M.movement_id', '=', 'CMP.movement_id')
            ->where('M.status', "=", 1)
            ->where('M.route', 'commercial/saleBill')
            ->whereIn('I.product_id', $productIds)
            ->whereDate('M.emission_date', '>=', $startDate)
            ->whereDate('M.emission_date', '<=', $endDate);

        if ($storeId) {
            $query->where('M.store_id', $storeId);
        }


        $query->groupBy('I.product_name', 'I.product_id');

        return $query->get();

    }

    private static function generateDateRange($period) {
        list($month, $year) = explode('-', $period);
        $month = (int) $month;
        $year = (int) $year;
        $startDate = (new \DateTime())->setDate($year, $month, 1)->format('Y-m-d');

        $currentDate = new \DateTime();

        $endDate = '';
        $daysInMonth = '';

        if ($month == $currentDate->format('n') && $year == $currentDate->format('Y')) {
            $endDate = $currentDate->format('Y-m-d');
            $daysInMonth = $currentDate->format('j');
        } else {
            $endDate = (new \DateTime())->setDate($year, $month, 1)->modify('last day of this month')->format('Y-m-d');
            $daysInMonth = (new \DateTime())->setDate($year, $month, 1)->format('t');
        }

        return [$startDate, $endDate, $daysInMonth, $month, $year];
    }

    private function getRecipeProductIds() {
        $productIds = DB::table('recipe as R')
            ->select('R.product_id')
            ->get()
            ->pluck('product_id')
            ->toArray();

        return $productIds;
    }


    public function getProductionCostsByStore($store_id, $year, $month) {

        $workDays = DB::table('production_cost AS PC')
            ->select('PC.work_days')
            ->where('PC.month', $month)
            ->where('PC.year', $year)
            ->where('PC.store_id', $store_id)
            ->first();

        if (!$workDays) {
            return response()->json(['success' => false, 'error' => 'No hay costos de producción para la tienda en el mes y año especificados.'], 404);
        }

        $mainConcepts = DB::table('production_cost_concept AS PCC')
            ->join('production_cost_detail AS PCD', 'PCC.production_cost_concept_id', '=', 'PCD.production_cost_concept_id')
            ->join('production_cost AS PC', 'PC.production_cost_id', '=', 'PCD.production_cost_id')
            ->select(
                'PCC.production_cost_concept_id as concept_id',
                'PCC.concept_name as concept',
                'PCC.percentage',
                'PCD.amount'
            )
            ->whereNull('PCC.parent_concept_id')
            ->where('PC.month', $month)
            ->where('PC.year', $year)
            ->where('PC.store_id', $store_id)
            ->get();

        if (empty($mainConcepts)) {
            return response()->json(['success' => false, 'error' => 'No hay conceptos de costos de producción para la tienda en el mes y año especificados.'], 404);
        }

        $productionConcepts = [];

        foreach ($mainConcepts as $mainConcept) {
            $productionConcepts[] = $this->getChildProductionCostData($mainConcept);
        }

        return [
            'work_days' => $workDays->work_days,
            'concepts' => $productionConcepts,
        ];
    }


    function getChildProductionCostConcepts($parent_id) {
        return DB::table('production_cost_detail AS CD')
            ->join('production_cost_concept AS CC', 'CC.production_cost_concept_id', '=', 'CD.production_cost_concept_id')
            ->select(
                'CC.production_cost_concept_id AS concept_id',
                'CC.concept_name AS concept',
                'CD.amount AS amount',
                'CC.percentage'
            )
            ->where('CC.parent_concept_id', $parent_id)
            ->get();
    }

    function getChildProductionCostData($productionCost) {
        if (is_null($productionCost->amount)) {
            $total = 0;
            $childConcepts = $this->getChildProductionCostConcepts($productionCost->concept_id);

            $childConceptsData = [];
            foreach ($childConcepts as $childConcept) {
                $childData = $this->getChildProductionCostData($childConcept);
                $total += $childData['total'];
                $childConceptsData[] = $childData;
            }

            return [
                'concept_name' => $productionCost->concept,
                'total' => floatval($total),
                'percentage' => $productionCost->percentage,
                'concepts' => $childConceptsData,
            ];
        } else {
            return [
                'concept_name' => $productionCost->concept,
                'total' => floatval($productionCost->amount),
                'percentage' => $productionCost->percentage,
                'concepts' => [],
            ];
        }
    }

    function convertToInteger($decimal) {
        $entero = $decimal * 100;
        $enteroFormateado = number_format($entero, 6, '.', '');
        return $enteroFormateado;
    }



}
