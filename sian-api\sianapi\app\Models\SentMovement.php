<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class SentMovement
 * 
 * @property int $send_id
 * @property int $movement_id
 * @property string|null $reference
 * @property string $type
 * @property Carbon $send_date
 * @property int $status
 * @property string|null $status_code
 * @property string $ose
 * @property string|null $response
 * @property string|null $basename
 * @property int $order
 * @property bool $last
 * @property bool $code
 * 
 * @property Movement $movement
 *
 * @package App\Models
 */
class SentMovement extends Model
{
	protected $table = 'sent_movement';
	protected $primaryKey = 'send_id';
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'status' => 'int',
		'order' => 'int',
		'last' => 'bool',
		'code' => 'bool'
	];

	protected $dates = [
		'send_date'
	];

	protected $fillable = [
		'movement_id',
		'reference',
		'type',
		'send_date',
		'status',
		'status_code',
		'ose',
		'response',
		'basename',
		'order',
		'last',
		'code'
	];

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}
}
