<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Concept
 * 
 * @property int $concept_id
 * @property int $dictionary_id
 * @property string $type
 * @property string $field
 * @property string $short_name
 * @property string $concept_name
 * @property string|null $type_concept
 * @property string $type_formula
 * @property string|null $sentence
 * @property string|null $condition
 * @property string|null $true_sentence
 * @property string|null $false_sentence
 * @property bool $export_plame
 * @property int|null $plame_id
 * @property bool $is_printable
 * @property int $order
 * @property int $calc_order
 * 
 * @property Dictionary $dictionary
 * @property ScenarioField $scenario_field
 * @property Multitable|null $multitable
 * @property Collection|PayrollMovementDetail[] $payroll_movement_details
 *
 * @package App\Models
 */
class Concept extends Model
{
	protected $table = 'concept';
	protected $primaryKey = 'concept_id';
	public $timestamps = false;

	protected $casts = [
		'dictionary_id' => 'int',
		'export_plame' => 'bool',
		'plame_id' => 'int',
		'is_printable' => 'bool',
		'order' => 'int',
		'calc_order' => 'int'
	];

	protected $fillable = [
		'dictionary_id',
		'type',
		'field',
		'short_name',
		'concept_name',
		'type_concept',
		'type_formula',
		'sentence',
		'condition',
		'true_sentence',
		'false_sentence',
		'export_plame',
		'plame_id',
		'is_printable',
		'order',
		'calc_order'
	];

	public function dictionary()
	{
		return $this->belongsTo(Dictionary::class);
	}

	public function scenario_field()
	{
		return $this->belongsTo(ScenarioField::class, 'type')
					->where('scenario_field.type', '=', 'concept.type')
					->where('scenario_field.field', '=', 'concept.field');
	}

	public function multitable()
	{
		return $this->belongsTo(Multitable::class, 'plame_id');
	}

	public function payroll_movement_details()
	{
		return $this->hasMany(PayrollMovementDetail::class);
	}
}
