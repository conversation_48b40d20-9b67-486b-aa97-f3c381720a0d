<?php

namespace App\Http\Controllers\Api\V1\Logistic;

use Illuminate\Http\Request;
use App\Models\FixedAsset;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Resources\Logistic\SimpleMarkResource;
use App\Models\Mark;
use App\Models\Multitable;
use PhpParser\Parser\Multiple;

class MarkController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getItems(Request $request)
    {
        $a_response = [
            'success' => true,
            'data' => [
                'items' => SimpleMarkResource::collection(Mark::where("status", 1)->get())
            ]
        ];
        return response()->json($a_response);
    }
}
