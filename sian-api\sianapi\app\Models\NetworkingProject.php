<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class NetworkingProject
 * 
 * @property int $networking_project_id
 * @property string $networking_project_name
 * @property string|null $networking_project_description
 * @property int $networking_project_type_id
 * @property int $responsible_id
 * @property Carbon $start_date
 * @property Carbon $end_date
 * @property Carbon $estimated_date
 * @property Carbon|null $time_done
 * @property bool $status
 * @property int $business_partner_id
 * @property int $business_unit_id
 * 
 * @property Person $person
 * @property BusinessUnit $business_unit
 * @property NetworkingProjectType $networking_project_type
 * @property Collection|NetworkingActivity[] $networking_activities
 *
 * @package App\Models
 */
class NetworkingProject extends Model
{
	protected $table = 'networking_project';
	protected $primaryKey = 'networking_project_id';
	public $timestamps = false;

	protected $casts = [
		'networking_project_type_id' => 'int',
		'responsible_id' => 'int',
		'status' => 'bool',
		'business_partner_id' => 'int',
		'business_unit_id' => 'int'
	];

	protected $dates = [
		'start_date',
		'end_date',
		'estimated_date',
		'time_done'
	];

	protected $fillable = [
		'networking_project_name',
		'networking_project_description',
		'networking_project_type_id',
		'responsible_id',
		'start_date',
		'end_date',
		'estimated_date',
		'time_done',
		'status',
		'business_partner_id',
		'business_unit_id'
	];

	public function person()
	{
		return $this->belongsTo(Person::class, 'responsible_id');
	}

	public function business_unit()
	{
		return $this->belongsTo(BusinessUnit::class);
	}

	public function networking_project_type()
	{
		return $this->belongsTo(NetworkingProjectType::class);
	}

	public function networking_activities()
	{
		return $this->hasMany(NetworkingActivity::class);
	}
}
