<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ProductListItem
 * 
 * @property int $item_id
 * @property string $owner
 * @property int $owner_id
 * @property float $amount_pen
 * @property float $amount_usd
 * @property float $total_pen
 * @property float $total_usd
 * 
 * @property Item $item
 * @property ProductList $product_list
 *
 * @package App\Models
 */
class ProductListItem extends Model
{
	protected $table = 'product_list_item';
	protected $primaryKey = 'item_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'item_id' => 'int',
		'owner_id' => 'int',
		'amount_pen' => 'float',
		'amount_usd' => 'float',
		'total_pen' => 'float',
		'total_usd' => 'float'
	];

	protected $fillable = [
		'owner',
		'owner_id',
		'amount_pen',
		'amount_usd',
		'total_pen',
		'total_usd'
	];

	public function item()
	{
		return $this->belongsTo(Item::class);
	}

	public function product_list()
	{
		return $this->belongsTo(ProductList::class, 'owner')
					->where('product_list.owner', '=', 'product_list_item.owner')
					->where('product_list.owner_id', '=', 'product_list_item.owner_id');
	}
}
