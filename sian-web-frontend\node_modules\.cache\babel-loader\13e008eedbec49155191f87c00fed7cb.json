{"ast": null, "code": "var _jsxFileName = \"D:\\\\www\\\\SWF\\\\sian-web-frontend\\\\src\\\\views\\\\logistic\\\\reposition\\\\others\\\\RawMaterialRotationDetail.jsx\",\n    _s = $RefreshSig$(),\n    _s2 = $RefreshSig$(),\n    _s3 = $RefreshSig$();\n\nimport { Box, Typography } from '@mui/material';\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'store';\nimport { getRepositionDataByProduct, openRotationModal, setSelectedProduct } from 'store/slices/reposition/reposition';\nimport useLoading from 'hooks/useLoading';\nimport { BlockLoader } from 'ui-component/loaders/loaders';\nimport RightAlignedNumber from 'ui-component/grid/RightAlignedNumber';\nimport Grid from 'ui-component/grid/Grid';\nimport NestedGrid from 'ui-component/grid/NestedGrid';\nimport MainCard from 'ui-component/cards/MainCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst findDerivedProductRecursively = (derivedProducts, targetProductId) => {\n  if (!derivedProducts || !Array.isArray(derivedProducts)) return null;\n\n  for (const derived of derivedProducts) {\n    if (derived.product_id === targetProductId) {\n      return derived;\n    }\n\n    if (derived.derivedProducts && derived.derivedProducts.length > 0) {\n      const found = findDerivedProductRecursively(derived.derivedProducts, targetProductId);\n      if (found) return found;\n    }\n  }\n\n  return null;\n};\n\nconst findMainProductWithDerivedProduct = (data, targetProductId) => {\n  if (!data || !Array.isArray(data)) return null;\n\n  for (const item of data) {\n    const found = findDerivedProductRecursively(item.derivedProducts, targetProductId);\n    if (found) return item;\n  }\n\n  return null;\n};\n\nconst processAnalisys = (productData, storeData) => {\n  if (!productData || !productData.analisys || !Array.isArray(productData.analisys)) {\n    return [];\n  }\n\n  const listData = productData.analisys.map(item => {\n    const store = storeData.find(s => s.store_id === item.store_id);\n    return { ...item,\n      pk: `${item.store_id}_${productData.product_id}`,\n      store_name: store ? store.store_name : 'Tienda no encontrada'\n    };\n  });\n  listData.sort((a, b) => a.warehouse_id - b.warehouse_id);\n  return listData;\n};\n\nconst getDerivedProducts = productData => {\n  if (!productData || !productData.derivedProducts) return [];\n  return productData.derivedProducts.map((item, index) => ({ ...item,\n    pk: item.product_id,\n    globalIndex: index\n  }));\n};\n\nconst NestedCard = _ref => {\n  let {\n    children,\n    width = '50%'\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 1,\n      backgroundColor: 'white',\n      borderRadius: '1rem',\n      border: '1px solid #e0e0e0',\n      width\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n\n_c = NestedCard;\n\nconst DerivedProductAnalysis = _ref2 => {\n  _s();\n\n  let {\n    row,\n    columns\n  } = _ref2;\n  const productId = row[0];\n  const {\n    data\n  } = useSelector(state => state.reposition);\n  const {\n    data: storeData\n  } = useSelector(state => state.store);\n  const mainProductData = findMainProductWithDerivedProduct(data, productId);\n  const derivedProduct = findDerivedProductRecursively(mainProductData === null || mainProductData === void 0 ? void 0 : mainProductData.derivedProducts, productId);\n  const derivedAnalysis = derivedProduct !== null && derivedProduct !== void 0 && derivedProduct.analisys ? processAnalisys(derivedProduct, storeData) : null;\n\n  if (!derivedAnalysis || derivedAnalysis.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        textAlign: 'center',\n        color: 'text.secondary'\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: \"No hay an\\xE1lisis disponible para este producto derivado\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }, this);\n  }\n\n  return /*#__PURE__*/_jsxDEV(Grid, {\n    columns: columns,\n    data: derivedAnalysis,\n    options: {\n      search: false,\n      download: false,\n      print: false,\n      sort: false,\n      viewColumns: false,\n      filter: false,\n      pagination: false,\n      selectableRows: 'none',\n      toolbar: false,\n      elevation: 0\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 9\n  }, this);\n};\n\n_s(DerivedProductAnalysis, \"qeA+H7VVIvd47UjTttqhZeJWxQQ=\", false, function () {\n  return [useSelector, useSelector];\n});\n\n_c2 = DerivedProductAnalysis;\n\nconst DerivedProductNestedContent = _ref3 => {\n  let {\n    row,\n    data,\n    derivedAnalysisColumns,\n    simplifiedDerivedProductColumns\n  } = _ref3;\n  const productId = row[0];\n  const mainProductData = findMainProductWithDerivedProduct(data, productId);\n  const derivedProduct = findDerivedProductRecursively(mainProductData === null || mainProductData === void 0 ? void 0 : mainProductData.derivedProducts, productId);\n  const hasSubDerived = (derivedProduct === null || derivedProduct === void 0 ? void 0 : derivedProduct.derivedProducts) && derivedProduct.derivedProducts.length > 0;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      flexDirection: 'row',\n      gap: 2,\n      pb: 1,\n      px: 2,\n      justifyContent: 'center',\n      backgroundColor: '#f5f5f5',\n      borderRadius: '1rem',\n      my: 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: hasSubDerived ? '25%' : '40%'\n      },\n      children: /*#__PURE__*/_jsxDEV(MainCard, {\n        children: /*#__PURE__*/_jsxDEV(DerivedProductAnalysis, {\n          row: row,\n          columns: derivedAnalysisColumns\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 13\n    }, this), hasSubDerived && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '75%'\n      },\n      children: /*#__PURE__*/_jsxDEV(MainCard, {\n        children: /*#__PURE__*/_jsxDEV(SubDerivedProducts, {\n          row: row,\n          columns: simplifiedDerivedProductColumns,\n          analysisColumns: derivedAnalysisColumns\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 9\n  }, this);\n};\n\n_c3 = DerivedProductNestedContent;\n\nconst SubDerivedProducts = _ref4 => {\n  _s2();\n\n  let {\n    row,\n    columns,\n    analysisColumns\n  } = _ref4;\n  const productId = row[0];\n  const {\n    data\n  } = useSelector(state => state.reposition);\n  const mainProductData = findMainProductWithDerivedProduct(data, productId);\n  const derivedProduct = findDerivedProductRecursively(mainProductData === null || mainProductData === void 0 ? void 0 : mainProductData.derivedProducts, productId);\n  const rawSubDerivedProducts = (derivedProduct === null || derivedProduct === void 0 ? void 0 : derivedProduct.derivedProducts) || [];\n  const subDerivedProducts = rawSubDerivedProducts.map((item, index) => ({ ...item,\n    pk: item.product_id,\n    globalIndex: index\n  }));\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%',\n      height: 'fit-content'\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        '& .MuiTable-root': {\n          width: '100% !important',\n          tableLayout: 'fixed'\n        },\n        '& .MuiTableCell-root': {\n          padding: '4px 8px',\n          fontSize: '0.75rem'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(NestedGrid, {\n        columns: subDerivedProducts.length > 0 ? columns : [],\n        data: subDerivedProducts,\n        title: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontSize: '1rem',\n            fontWeight: 'bold'\n          },\n          children: \"Sub-Derivados\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 28\n        }, this),\n        RenderNestedContent: props => /*#__PURE__*/_jsxDEV(DerivedProductNestedContent, { ...props,\n          data: data,\n          derivedAnalysisColumns: analysisColumns,\n          simplifiedDerivedProductColumns: columns\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 25\n        }, this),\n        options: {\n          search: false,\n          download: false,\n          print: false,\n          sort: false,\n          viewColumns: false,\n          filter: false,\n          pagination: false,\n          selectableRows: 'none',\n          toolbar: false,\n          elevation: 0,\n          responsive: 'vertical'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 159,\n    columnNumber: 9\n  }, this);\n};\n\n_s2(SubDerivedProducts, \"/Hwr0EusckN6Yz4QGI98pPT/utc=\", false, function () {\n  return [useSelector];\n});\n\n_c4 = SubDerivedProducts;\n\nconst RawMaterialRotationDetail = _ref5 => {\n  _s3();\n\n  let {\n    row,\n    filters,\n    supplyAnalisys,\n    isFromProyection,\n    merchandiseFoodData\n  } = _ref5;\n  const dispatch = useDispatch();\n  const {\n    data\n  } = useSelector(state => state.reposition);\n  const {\n    data: storeData\n  } = useSelector(state => state.store);\n  const productData = isFromProyection ? merchandiseFoodData.find(item => item.product_id === row[0]) : data.find(item => item.product_id === row[0]);\n  const derivedProducts = getDerivedProducts(productData);\n  const displayProducts = derivedProducts.length > 0 ? derivedProducts : productData ? [{ ...productData,\n    pk: productData.product_id || productData.pk,\n    globalIndex: 0\n  }] : [];\n  const [repositionProduct, setRepositionProduct] = useState(supplyAnalisys || []);\n  const [isAsync] = useState(!supplyAnalisys);\n  const [loading, startLoading, endLoading] = useLoading(isAsync);\n\n  const openModal = () => dispatch(openRotationModal());\n\n  const setSelected = data => dispatch(setSelectedProduct(data));\n\n  const reload = () => {\n    if (isAsync) {\n      startLoading();\n      dispatch(getRepositionDataByProduct(row[0], { ...filters,\n        mode: isFromProyection ? 'Merc2' : filters.mode\n      }, storeData)).then(data => {\n        setRepositionProduct(data);\n        endLoading();\n      });\n    }\n  };\n\n  useEffect(() => {\n    reload();\n  }, []);\n\n  const getRowDataSafely = pk => {\n    return repositionProduct.find(item => item.pk === pk) || {};\n  };\n\n  const isRowDataAvailable = rowData => {\n    return rowData && !rowData.notAvailable;\n  }; // Supply columns for raw material analysis\n\n\n  const supplyColumns = [{\n    name: 'pk',\n    label: 'PK',\n    options: {\n      filter: false,\n      sort: false,\n      display: false\n    }\n  }, {\n    name: 'store_id',\n    label: 'ID',\n    options: {\n      filter: true,\n      sort: true,\n      display: false\n    }\n  }, {\n    name: 'store_name',\n    label: 'TIENDA',\n    options: {\n      filter: true,\n      sort: true\n    }\n  }, {\n    name: 'unit_quantity_proyected',\n    label: 'C.PROYECTADA',\n    options: {\n      filter: true,\n      sort: true,\n      display: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n        value: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 46\n      }, this)\n    }\n  }, {\n    name: 'purchase_stock',\n    label: 'STOCK',\n    options: {\n      filter: true,\n      sort: true,\n      display: true,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = getRowDataSafely(pk);\n\n        if (!isRowDataAvailable(rowData)) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 32\n          }, this);\n        }\n\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'end',\n            gap: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n            value: value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 25\n        }, this);\n      }\n    }\n  }, {\n    name: 'waste_info',\n    label: 'MERMA %',\n    options: {\n      filter: true,\n      sort: true,\n      setCellHeaderProps: () => ({\n        style: {\n          minWidth: '100px',\n          whiteSpace: 'nowrap'\n        }\n      }),\n      setCellProps: () => ({\n        style: {\n          minWidth: '100px',\n          whiteSpace: 'nowrap'\n        }\n      }),\n      customBodyRender: value => {\n        const wasteInfo = value;\n\n        if (!wasteInfo || !wasteInfo.waste_percentage_total) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              whiteSpace: 'nowrap',\n              textAlign: 'center'\n            },\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 32\n          }, this);\n        }\n\n        const percentage = parseFloat(wasteInfo.waste_percentage_total);\n        return /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            whiteSpace: 'nowrap',\n            textAlign: 'center'\n          },\n          children: [percentage.toFixed(2), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 28\n        }, this);\n      }\n    }\n  }];\n  const rawProductSupplyColumns = [{\n    name: 'pk',\n    label: 'PK',\n    options: {\n      filter: false,\n      sort: false,\n      display: false\n    }\n  }, {\n    name: 'store_id',\n    label: 'ID',\n    options: {\n      filter: true,\n      sort: true,\n      display: false\n    }\n  }, {\n    name: 'store_name',\n    label: 'TIENDA',\n    options: {\n      filter: true,\n      sort: true\n    }\n  }, {\n    name: 'purchase_stock',\n    label: 'STOCK',\n    options: {\n      filter: true,\n      sort: true,\n      display: true,\n      customBodyRender: (value, tableMeta) => {\n        const pk = tableMeta.rowData[0];\n        const rowData = getRowDataSafely(pk);\n\n        if (!isRowDataAvailable(rowData)) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"gray\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 32\n          }, this);\n        }\n\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'end',\n            gap: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n            value: value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 25\n        }, this);\n      }\n    }\n  }]; // Simplified columns for derived products (with waste info)\n\n  const simplifiedDerivedProductColumns = [{\n    name: 'product_id',\n    label: 'ID',\n    options: {\n      filter: true,\n      sort: true,\n      setCellHeaderProps: () => ({\n        style: {\n          width: '80px',\n          maxWidth: '80px'\n        }\n      }),\n      setCellProps: () => ({\n        style: {\n          width: '80px',\n          maxWidth: '80px'\n        }\n      })\n    }\n  }, {\n    name: 'product_name',\n    label: 'PRODUCTO',\n    options: {\n      filter: true,\n      sort: true,\n      setCellHeaderProps: () => ({\n        style: {\n          minWidth: '450px',\n          whiteSpace: 'nowrap'\n        }\n      }),\n      setCellProps: () => ({\n        style: {\n          minWidth: '450px',\n          whiteSpace: 'nowrap'\n        }\n      }),\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          whiteSpace: 'nowrap'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 21\n      }, this)\n    }\n  }, {\n    name: 'waste_info',\n    label: 'MERMA %',\n    options: {\n      filter: true,\n      sort: true,\n      setCellHeaderProps: () => ({\n        style: {\n          minWidth: '100px',\n          whiteSpace: 'nowrap'\n        }\n      }),\n      setCellProps: () => ({\n        style: {\n          minWidth: '100px',\n          whiteSpace: 'nowrap'\n        }\n      }),\n      customBodyRender: value => {\n        const wasteInfo = value;\n\n        if (!wasteInfo || !wasteInfo.waste_percentage_total) {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              whiteSpace: 'nowrap',\n              textAlign: 'center'\n            },\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 32\n          }, this);\n        }\n\n        const percentage = parseFloat(wasteInfo.waste_percentage_total);\n        return /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            whiteSpace: 'nowrap',\n            textAlign: 'center'\n          },\n          children: [percentage.toFixed(2), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 28\n        }, this);\n      }\n    }\n  }, {\n    name: 'unit_quantity_proyected',\n    label: 'C.PROYECTADA',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n        value: Math.random() * 100\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 46\n      }, this)\n    }\n  }, {\n    name: 'purchase_stock',\n    label: 'STOCK EN TIENDAS',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n        value: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 46\n      }, this)\n    }\n  }, {\n    name: 'supplying_stock',\n    label: 'S A.PRINCIPAL',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n        value: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 46\n      }, this)\n    }\n  }, {\n    name: 'measure_default',\n    label: 'PRES',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(Typography, {\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 46\n      }, this)\n    }\n  }, {\n    name: 'rep_pres_min',\n    label: '(NETO)',\n    options: {\n      filter: true,\n      sort: false,\n      customBodyRender: (value, tableMeta) => /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n        value: Math.random() * 100\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 57\n      }, this)\n    }\n  }, {\n    name: 'measure_name',\n    label: 'PRES MIN',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(Typography, {\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 46\n      }, this)\n    }\n  }]; // Simplified columns for non-derived products (without waste info)\n\n  const simplifiedNonDerivedProductColumns = [{\n    name: 'product_id',\n    label: 'ID',\n    options: {\n      filter: true,\n      sort: true,\n      setCellHeaderProps: () => ({\n        style: {\n          width: '80px',\n          maxWidth: '80px'\n        }\n      }),\n      setCellProps: () => ({\n        style: {\n          width: '80px',\n          maxWidth: '80px'\n        }\n      })\n    }\n  }, {\n    name: 'product_name',\n    label: 'PRODUCTO',\n    options: {\n      filter: true,\n      sort: true,\n      setCellHeaderProps: () => ({\n        style: {\n          minWidth: '450px',\n          whiteSpace: 'nowrap'\n        }\n      }),\n      setCellProps: () => ({\n        style: {\n          minWidth: '450px',\n          whiteSpace: 'nowrap'\n        }\n      }),\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          whiteSpace: 'nowrap'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 21\n      }, this)\n    }\n  }, {\n    name: 'unit_quantity_proyected',\n    label: 'C.PROYECTADA',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n        value: Math.random() * 100\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 46\n      }, this)\n    }\n  }, {\n    name: 'purchase_stock',\n    label: 'STOCK EN TIENDAS',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n        value: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 46\n      }, this)\n    }\n  }, {\n    name: 'supplying_stock',\n    label: 'S A.PRINCIPAL',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n        value: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 46\n      }, this)\n    }\n  }, {\n    name: 'measure_default',\n    label: 'PRES',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(Typography, {\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 554,\n        columnNumber: 46\n      }, this)\n    }\n  }, {\n    name: 'rep_pres_min',\n    label: '(NETO)',\n    options: {\n      filter: true,\n      sort: false,\n      customBodyRender: (value, tableMeta) => /*#__PURE__*/_jsxDEV(RightAlignedNumber, {\n        value: Math.random() * 100\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 57\n      }, this)\n    }\n  }, {\n    name: 'measure_name',\n    label: 'PRES MIN',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(Typography, {\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 572,\n        columnNumber: 46\n      }, this)\n    }\n  }]; // Analysis columns for derived products\n\n  const derivedAnalysisColumns = [{\n    name: 'pk',\n    label: 'PK',\n    options: {\n      filter: false,\n      sort: false,\n      display: false\n    }\n  }, {\n    name: 'store_name',\n    label: 'TIENDA',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          fontSize: '1.2rem',\n          fontWeight: 'bold'\n        },\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 46\n      }, this)\n    }\n  }, {\n    name: 'stock',\n    label: 'STOCK',\n    options: {\n      filter: true,\n      sort: true,\n      customBodyRender: value => /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          fontSize: '1.2rem',\n          fontWeight: 'medium',\n          textAlign: 'right'\n        },\n        children: parseFloat(value).toFixed(2)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 604,\n        columnNumber: 21\n      }, this)\n    }\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      gap: 1,\n      flexDirection: 'row',\n      width: '100%',\n      justifyContent: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(NestedCard, {\n      width: \"90%\",\n      sx: {\n        '& .MuiTable-root': {\n          width: '100% !important',\n          tableLayout: 'fixed'\n        },\n        '& .MuiTableCell-root': {\n          padding: '8px 16px'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(NestedGrid, {\n        columns: derivedProducts.length > 0 ? simplifiedDerivedProductColumns : simplifiedNonDerivedProductColumns,\n        data: displayProducts,\n        title: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontSize: '1.2rem',\n            fontWeight: 'bold'\n          },\n          children: \"Productos Derivados\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 629,\n          columnNumber: 28\n        }, this),\n        RenderNestedContent: props => /*#__PURE__*/_jsxDEV(DerivedProductNestedContent, { ...props,\n          data: data,\n          derivedAnalysisColumns: derivedAnalysisColumns,\n          simplifiedDerivedProductColumns: simplifiedDerivedProductColumns\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 25\n        }, this),\n        options: {\n          search: false,\n          download: false,\n          print: false,\n          sort: false,\n          viewColumns: true,\n          filter: false,\n          filterType: 'multiselect',\n          responsive: 'vertical',\n          fixedHeader: true,\n          fixedSelectColumn: true,\n          jumpToPage: false,\n          resizableColumns: false,\n          draggableColumns: {\n            enabled: true\n          },\n          serverSide: true,\n          selectableRows: 'none',\n          selectableRowsOnClick: false,\n          pagination: false,\n          confirmFilters: false,\n          rowHover: true,\n          toolbar: false\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 13\n    }, this), loading && /*#__PURE__*/_jsxDEV(BlockLoader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 665,\n      columnNumber: 25\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 613,\n    columnNumber: 9\n  }, this);\n};\n\n_s3(RawMaterialRotationDetail, \"NhEe9eFnpmjR3GHDYSjdoHw6CUw=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useLoading];\n});\n\n_c5 = RawMaterialRotationDetail;\nexport default RawMaterialRotationDetail;\n\nvar _c, _c2, _c3, _c4, _c5;\n\n$RefreshReg$(_c, \"NestedCard\");\n$RefreshReg$(_c2, \"DerivedProductAnalysis\");\n$RefreshReg$(_c3, \"DerivedProductNestedContent\");\n$RefreshReg$(_c4, \"SubDerivedProducts\");\n$RefreshReg$(_c5, \"RawMaterialRotationDetail\");", "map": {"version": 3, "sources": ["D:/www/SWF/sian-web-frontend/src/views/logistic/reposition/others/RawMaterialRotationDetail.jsx"], "names": ["Box", "Typography", "React", "useEffect", "useState", "useDispatch", "useSelector", "getRepositionDataByProduct", "openRotationModal", "setSelectedProduct", "useLoading", "<PERSON><PERSON><PERSON><PERSON>", "RightAlignedNumber", "Grid", "NestedGrid", "MainCard", "findDerivedProductRecursively", "derivedProducts", "targetProductId", "Array", "isArray", "derived", "product_id", "length", "found", "findMainProductWithDerivedProduct", "data", "item", "processAnalisys", "productData", "storeData", "analisys", "listData", "map", "store", "find", "s", "store_id", "pk", "store_name", "sort", "a", "b", "warehouse_id", "getDerivedProducts", "index", "globalIndex", "NestedCard", "children", "width", "p", "backgroundColor", "borderRadius", "border", "DerivedProductAnalysis", "row", "columns", "productId", "state", "reposition", "mainProductData", "derivedProduct", "derivedAnalysis", "textAlign", "color", "search", "download", "print", "viewColumns", "filter", "pagination", "selectableRows", "toolbar", "elevation", "DerivedProductNestedContent", "derivedAnalysisColumns", "simplifiedDerivedProductColumns", "hasSubDerived", "display", "flexDirection", "gap", "pb", "px", "justifyContent", "my", "SubDerivedProducts", "analysisColumns", "rawSubDerivedProducts", "subDerivedProducts", "height", "tableLayout", "padding", "fontSize", "fontWeight", "props", "responsive", "RawMaterialRotationDetail", "filters", "supplyAnalisys", "isFromProyection", "merchandiseFoodData", "dispatch", "displayProducts", "repositionProduct", "setRepositionProduct", "isAsync", "loading", "startLoading", "endLoading", "openModal", "setSelected", "reload", "mode", "then", "getRowDataSafely", "isRowDataAvailable", "rowData", "notAvailable", "supplyColumns", "name", "label", "options", "customBodyRender", "value", "tableMeta", "alignItems", "setCellHeaderProps", "style", "min<PERSON><PERSON><PERSON>", "whiteSpace", "setCellProps", "wasteInfo", "waste_percentage_total", "percentage", "parseFloat", "toFixed", "rawProductSupplyColumns", "max<PERSON><PERSON><PERSON>", "Math", "random", "simplifiedNonDerivedProductColumns", "filterType", "fixedHeader", "fixedSelectColumn", "jumpToPage", "resizableColumns", "draggableColumns", "enabled", "serverSide", "selectableRowsOnClick", "confirmFilters", "rowHover"], "mappings": ";;;;;AAAA,SAASA,GAAT,EAAcC,UAAd,QAAgC,eAAhC;AACA,OAAOC,KAAP,IAAgBC,SAAhB,EAA2BC,QAA3B,QAA2C,OAA3C;AACA,SAASC,WAAT,EAAsBC,WAAtB,QAAyC,OAAzC;AACA,SAASC,0BAAT,EAAqCC,iBAArC,EAAwDC,kBAAxD,QAAkF,oCAAlF;AACA,OAAOC,UAAP,MAAuB,kBAAvB;AACA,SAASC,WAAT,QAA4B,8BAA5B;AACA,OAAOC,kBAAP,MAA+B,sCAA/B;AACA,OAAOC,IAAP,MAAiB,wBAAjB;AACA,OAAOC,UAAP,MAAuB,8BAAvB;AACA,OAAOC,QAAP,MAAqB,6BAArB;;;AAEA,MAAMC,6BAA6B,GAAG,CAACC,eAAD,EAAkBC,eAAlB,KAAsC;AACxE,MAAI,CAACD,eAAD,IAAoB,CAACE,KAAK,CAACC,OAAN,CAAcH,eAAd,CAAzB,EAAyD,OAAO,IAAP;;AAEzD,OAAK,MAAMI,OAAX,IAAsBJ,eAAtB,EAAuC;AACnC,QAAII,OAAO,CAACC,UAAR,KAAuBJ,eAA3B,EAA4C;AACxC,aAAOG,OAAP;AACH;;AAED,QAAIA,OAAO,CAACJ,eAAR,IAA2BI,OAAO,CAACJ,eAAR,CAAwBM,MAAxB,GAAiC,CAAhE,EAAmE;AAC/D,YAAMC,KAAK,GAAGR,6BAA6B,CAACK,OAAO,CAACJ,eAAT,EAA0BC,eAA1B,CAA3C;AACA,UAAIM,KAAJ,EAAW,OAAOA,KAAP;AACd;AACJ;;AAED,SAAO,IAAP;AACH,CAfD;;AAiBA,MAAMC,iCAAiC,GAAG,CAACC,IAAD,EAAOR,eAAP,KAA2B;AACjE,MAAI,CAACQ,IAAD,IAAS,CAACP,KAAK,CAACC,OAAN,CAAcM,IAAd,CAAd,EAAmC,OAAO,IAAP;;AAEnC,OAAK,MAAMC,IAAX,IAAmBD,IAAnB,EAAyB;AACrB,UAAMF,KAAK,GAAGR,6BAA6B,CAACW,IAAI,CAACV,eAAN,EAAuBC,eAAvB,CAA3C;AACA,QAAIM,KAAJ,EAAW,OAAOG,IAAP;AACd;;AAED,SAAO,IAAP;AACH,CATD;;AAWA,MAAMC,eAAe,GAAG,CAACC,WAAD,EAAcC,SAAd,KAA4B;AAChD,MAAI,CAACD,WAAD,IAAgB,CAACA,WAAW,CAACE,QAA7B,IAAyC,CAACZ,KAAK,CAACC,OAAN,CAAcS,WAAW,CAACE,QAA1B,CAA9C,EAAmF;AAC/E,WAAO,EAAP;AACH;;AAED,QAAMC,QAAQ,GAAGH,WAAW,CAACE,QAAZ,CAAqBE,GAArB,CAA0BN,IAAD,IAAU;AAChD,UAAMO,KAAK,GAAGJ,SAAS,CAACK,IAAV,CAAgBC,CAAD,IAAOA,CAAC,CAACC,QAAF,KAAeV,IAAI,CAACU,QAA1C,CAAd;AACA,WAAO,EACH,GAAGV,IADA;AAEHW,MAAAA,EAAE,EAAG,GAAEX,IAAI,CAACU,QAAS,IAAGR,WAAW,CAACP,UAAW,EAF5C;AAGHiB,MAAAA,UAAU,EAAEL,KAAK,GAAGA,KAAK,CAACK,UAAT,GAAsB;AAHpC,KAAP;AAKH,GAPgB,CAAjB;AASAP,EAAAA,QAAQ,CAACQ,IAAT,CAAc,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACE,YAAF,GAAiBD,CAAC,CAACC,YAA3C;AAEA,SAAOX,QAAP;AACH,CAjBD;;AAmBA,MAAMY,kBAAkB,GAAIf,WAAD,IAAiB;AACxC,MAAI,CAACA,WAAD,IAAgB,CAACA,WAAW,CAACZ,eAAjC,EAAkD,OAAO,EAAP;AAElD,SAAOY,WAAW,CAACZ,eAAZ,CAA4BgB,GAA5B,CAAgC,CAACN,IAAD,EAAOkB,KAAP,MAAkB,EACrD,GAAGlB,IADkD;AAErDW,IAAAA,EAAE,EAAEX,IAAI,CAACL,UAF4C;AAGrDwB,IAAAA,WAAW,EAAED;AAHwC,GAAlB,CAAhC,CAAP;AAKH,CARD;;AAUA,MAAME,UAAU,GAAG;AAAA,MAAC;AAAEC,IAAAA,QAAF;AAAYC,IAAAA,KAAK,GAAG;AAApB,GAAD;AAAA,sBACf,QAAC,GAAD;AAAK,IAAA,EAAE,EAAE;AAAEC,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,eAAe,EAAE,OAAzB;AAAkCC,MAAAA,YAAY,EAAE,MAAhD;AAAwDC,MAAAA,MAAM,EAAE,mBAAhE;AAAqFJ,MAAAA;AAArF,KAAT;AAAA,cAAwGD;AAAxG;AAAA;AAAA;AAAA;AAAA,UADe;AAAA,CAAnB;;KAAMD,U;;AAIN,MAAMO,sBAAsB,GAAG,SAAsB;AAAA;;AAAA,MAArB;AAAEC,IAAAA,GAAF;AAAOC,IAAAA;AAAP,GAAqB;AACjD,QAAMC,SAAS,GAAGF,GAAG,CAAC,CAAD,CAArB;AACA,QAAM;AAAE7B,IAAAA;AAAF,MAAWpB,WAAW,CAAEoD,KAAD,IAAWA,KAAK,CAACC,UAAlB,CAA5B;AACA,QAAM;AAAEjC,IAAAA,IAAI,EAAEI;AAAR,MAAsBxB,WAAW,CAAEoD,KAAD,IAAWA,KAAK,CAACxB,KAAlB,CAAvC;AACA,QAAM0B,eAAe,GAAGnC,iCAAiC,CAACC,IAAD,EAAO+B,SAAP,CAAzD;AACA,QAAMI,cAAc,GAAG7C,6BAA6B,CAAC4C,eAAD,aAACA,eAAD,uBAACA,eAAe,CAAE3C,eAAlB,EAAmCwC,SAAnC,CAApD;AACA,QAAMK,eAAe,GAAGD,cAAc,SAAd,IAAAA,cAAc,WAAd,IAAAA,cAAc,CAAE9B,QAAhB,GAA2BH,eAAe,CAACiC,cAAD,EAAiB/B,SAAjB,CAA1C,GAAwE,IAAhG;;AAEA,MAAI,CAACgC,eAAD,IAAoBA,eAAe,CAACvC,MAAhB,KAA2B,CAAnD,EAAsD;AAClD,wBACI,QAAC,GAAD;AAAK,MAAA,EAAE,EAAE;AAAE2B,QAAAA,CAAC,EAAE,CAAL;AAAQa,QAAAA,SAAS,EAAE,QAAnB;AAA6BC,QAAAA,KAAK,EAAE;AAApC,OAAT;AAAA,6BACI,QAAC,UAAD;AAAY,QAAA,OAAO,EAAC,OAApB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,YADJ;AAKH;;AAED,sBACI,QAAC,IAAD;AACI,IAAA,OAAO,EAAER,OADb;AAEI,IAAA,IAAI,EAAEM,eAFV;AAGI,IAAA,OAAO,EAAE;AACLG,MAAAA,MAAM,EAAE,KADH;AAELC,MAAAA,QAAQ,EAAE,KAFL;AAGLC,MAAAA,KAAK,EAAE,KAHF;AAIL3B,MAAAA,IAAI,EAAE,KAJD;AAKL4B,MAAAA,WAAW,EAAE,KALR;AAMLC,MAAAA,MAAM,EAAE,KANH;AAOLC,MAAAA,UAAU,EAAE,KAPP;AAQLC,MAAAA,cAAc,EAAE,MARX;AASLC,MAAAA,OAAO,EAAE,KATJ;AAULC,MAAAA,SAAS,EAAE;AAVN;AAHb;AAAA;AAAA;AAAA;AAAA,UADJ;AAkBH,CAlCD;;GAAMnB,sB;UAEehD,W,EACWA,W;;;MAH1BgD,sB;;AAoCN,MAAMoB,2BAA2B,GAAG,SAA4E;AAAA,MAA3E;AAAEnB,IAAAA,GAAF;AAAO7B,IAAAA,IAAP;AAAaiD,IAAAA,sBAAb;AAAqCC,IAAAA;AAArC,GAA2E;AAC5G,QAAMnB,SAAS,GAAGF,GAAG,CAAC,CAAD,CAArB;AACA,QAAMK,eAAe,GAAGnC,iCAAiC,CAACC,IAAD,EAAO+B,SAAP,CAAzD;AACA,QAAMI,cAAc,GAAG7C,6BAA6B,CAAC4C,eAAD,aAACA,eAAD,uBAACA,eAAe,CAAE3C,eAAlB,EAAmCwC,SAAnC,CAApD;AACA,QAAMoB,aAAa,GAAG,CAAAhB,cAAc,SAAd,IAAAA,cAAc,WAAd,YAAAA,cAAc,CAAE5C,eAAhB,KAAmC4C,cAAc,CAAC5C,eAAf,CAA+BM,MAA/B,GAAwC,CAAjG;AAEA,sBACI,QAAC,GAAD;AACI,IAAA,EAAE,EAAE;AACAuD,MAAAA,OAAO,EAAE,MADT;AAEAC,MAAAA,aAAa,EAAE,KAFf;AAGAC,MAAAA,GAAG,EAAE,CAHL;AAIAC,MAAAA,EAAE,EAAE,CAJJ;AAKAC,MAAAA,EAAE,EAAE,CALJ;AAMAC,MAAAA,cAAc,EAAE,QANhB;AAOAhC,MAAAA,eAAe,EAAE,SAPjB;AAQAC,MAAAA,YAAY,EAAE,MARd;AASAgC,MAAAA,EAAE,EAAE;AATJ,KADR;AAAA,4BAaI,QAAC,GAAD;AAAK,MAAA,EAAE,EAAE;AAAEnC,QAAAA,KAAK,EAAE4B,aAAa,GAAG,KAAH,GAAW;AAAjC,OAAT;AAAA,6BACI,QAAC,QAAD;AAAA,+BACI,QAAC,sBAAD;AAAwB,UAAA,GAAG,EAAEtB,GAA7B;AAAkC,UAAA,OAAO,EAAEoB;AAA3C;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,YAbJ,EAkBKE,aAAa,iBACV,QAAC,GAAD;AAAK,MAAA,EAAE,EAAE;AAAE5B,QAAAA,KAAK,EAAE;AAAT,OAAT;AAAA,6BACI,QAAC,QAAD;AAAA,+BACI,QAAC,kBAAD;AAAoB,UAAA,GAAG,EAAEM,GAAzB;AAA8B,UAAA,OAAO,EAAEqB,+BAAvC;AAAwE,UAAA,eAAe,EAAED;AAAzF;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,YAnBR;AAAA;AAAA;AAAA;AAAA;AAAA,UADJ;AA4BH,CAlCD;;MAAMD,2B;;AAoCN,MAAMW,kBAAkB,GAAG,SAAuC;AAAA;;AAAA,MAAtC;AAAE9B,IAAAA,GAAF;AAAOC,IAAAA,OAAP;AAAgB8B,IAAAA;AAAhB,GAAsC;AAC9D,QAAM7B,SAAS,GAAGF,GAAG,CAAC,CAAD,CAArB;AACA,QAAM;AAAE7B,IAAAA;AAAF,MAAWpB,WAAW,CAAEoD,KAAD,IAAWA,KAAK,CAACC,UAAlB,CAA5B;AACA,QAAMC,eAAe,GAAGnC,iCAAiC,CAACC,IAAD,EAAO+B,SAAP,CAAzD;AACA,QAAMI,cAAc,GAAG7C,6BAA6B,CAAC4C,eAAD,aAACA,eAAD,uBAACA,eAAe,CAAE3C,eAAlB,EAAmCwC,SAAnC,CAApD;AACA,QAAM8B,qBAAqB,GAAG,CAAA1B,cAAc,SAAd,IAAAA,cAAc,WAAd,YAAAA,cAAc,CAAE5C,eAAhB,KAAmC,EAAjE;AAEA,QAAMuE,kBAAkB,GAAGD,qBAAqB,CAACtD,GAAtB,CAA0B,CAACN,IAAD,EAAOkB,KAAP,MAAkB,EACnE,GAAGlB,IADgE;AAEnEW,IAAAA,EAAE,EAAEX,IAAI,CAACL,UAF0D;AAGnEwB,IAAAA,WAAW,EAAED;AAHsD,GAAlB,CAA1B,CAA3B;AAMA,sBACI,QAAC,GAAD;AAAK,IAAA,EAAE,EAAE;AAAEI,MAAAA,KAAK,EAAE,MAAT;AAAiBwC,MAAAA,MAAM,EAAE;AAAzB,KAAT;AAAA,2BACI,QAAC,GAAD;AACI,MAAA,EAAE,EAAE;AACA,4BAAoB;AAChBxC,UAAAA,KAAK,EAAE,iBADS;AAEhByC,UAAAA,WAAW,EAAE;AAFG,SADpB;AAKA,gCAAwB;AACpBC,UAAAA,OAAO,EAAE,SADW;AAEpBC,UAAAA,QAAQ,EAAE;AAFU;AALxB,OADR;AAAA,6BAYI,QAAC,UAAD;AACI,QAAA,OAAO,EAAEJ,kBAAkB,CAACjE,MAAnB,GAA4B,CAA5B,GAAgCiC,OAAhC,GAA0C,EADvD;AAEI,QAAA,IAAI,EAAEgC,kBAFV;AAGI,QAAA,KAAK,eAAE,QAAC,UAAD;AAAY,UAAA,EAAE,EAAE;AAAEI,YAAAA,QAAQ,EAAE,MAAZ;AAAoBC,YAAAA,UAAU,EAAE;AAAhC,WAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAHX;AAII,QAAA,mBAAmB,EAAGC,KAAD,iBACjB,QAAC,2BAAD,OACQA,KADR;AAEI,UAAA,IAAI,EAAEpE,IAFV;AAGI,UAAA,sBAAsB,EAAE4D,eAH5B;AAII,UAAA,+BAA+B,EAAE9B;AAJrC;AAAA;AAAA;AAAA;AAAA,gBALR;AAYI,QAAA,OAAO,EAAE;AACLS,UAAAA,MAAM,EAAE,KADH;AAELC,UAAAA,QAAQ,EAAE,KAFL;AAGLC,UAAAA,KAAK,EAAE,KAHF;AAIL3B,UAAAA,IAAI,EAAE,KAJD;AAKL4B,UAAAA,WAAW,EAAE,KALR;AAMLC,UAAAA,MAAM,EAAE,KANH;AAOLC,UAAAA,UAAU,EAAE,KAPP;AAQLC,UAAAA,cAAc,EAAE,MARX;AASLC,UAAAA,OAAO,EAAE,KATJ;AAULC,UAAAA,SAAS,EAAE,CAVN;AAWLsB,UAAAA,UAAU,EAAE;AAXP;AAZb;AAAA;AAAA;AAAA;AAAA;AAZJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,UADJ;AA2CH,CAxDD;;IAAMV,kB;UAEe/E,W;;;MAFf+E,kB;;AA0DN,MAAMW,yBAAyB,GAAG,SAA6E;AAAA;;AAAA,MAA5E;AAAEzC,IAAAA,GAAF;AAAO0C,IAAAA,OAAP;AAAgBC,IAAAA,cAAhB;AAAgCC,IAAAA,gBAAhC;AAAkDC,IAAAA;AAAlD,GAA4E;AAC3G,QAAMC,QAAQ,GAAGhG,WAAW,EAA5B;AACA,QAAM;AAAEqB,IAAAA;AAAF,MAAWpB,WAAW,CAAEoD,KAAD,IAAWA,KAAK,CAACC,UAAlB,CAA5B;AACA,QAAM;AAAEjC,IAAAA,IAAI,EAAEI;AAAR,MAAsBxB,WAAW,CAAEoD,KAAD,IAAWA,KAAK,CAACxB,KAAlB,CAAvC;AAEA,QAAML,WAAW,GAAGsE,gBAAgB,GAC9BC,mBAAmB,CAACjE,IAApB,CAA0BR,IAAD,IAAUA,IAAI,CAACL,UAAL,KAAoBiC,GAAG,CAAC,CAAD,CAA1D,CAD8B,GAE9B7B,IAAI,CAACS,IAAL,CAAWR,IAAD,IAAUA,IAAI,CAACL,UAAL,KAAoBiC,GAAG,CAAC,CAAD,CAA3C,CAFN;AAIA,QAAMtC,eAAe,GAAG2B,kBAAkB,CAACf,WAAD,CAA1C;AAEA,QAAMyE,eAAe,GACjBrF,eAAe,CAACM,MAAhB,GAAyB,CAAzB,GACMN,eADN,GAEMY,WAAW,GACX,CACI,EACI,GAAGA,WADP;AAEIS,IAAAA,EAAE,EAAET,WAAW,CAACP,UAAZ,IAA0BO,WAAW,CAACS,EAF9C;AAGIQ,IAAAA,WAAW,EAAE;AAHjB,GADJ,CADW,GAQX,EAXV;AAaA,QAAM,CAACyD,iBAAD,EAAoBC,oBAApB,IAA4CpG,QAAQ,CAAC8F,cAAc,IAAI,EAAnB,CAA1D;AACA,QAAM,CAACO,OAAD,IAAYrG,QAAQ,CAAC,CAAC8F,cAAF,CAA1B;AACA,QAAM,CAACQ,OAAD,EAAUC,YAAV,EAAwBC,UAAxB,IAAsClG,UAAU,CAAC+F,OAAD,CAAtD;;AAEA,QAAMI,SAAS,GAAG,MAAMR,QAAQ,CAAC7F,iBAAiB,EAAlB,CAAhC;;AACA,QAAMsG,WAAW,GAAIpF,IAAD,IAAU2E,QAAQ,CAAC5F,kBAAkB,CAACiB,IAAD,CAAnB,CAAtC;;AAEA,QAAMqF,MAAM,GAAG,MAAM;AACjB,QAAIN,OAAJ,EAAa;AACTE,MAAAA,YAAY;AACZN,MAAAA,QAAQ,CAAC9F,0BAA0B,CAACgD,GAAG,CAAC,CAAD,CAAJ,EAAS,EAAE,GAAG0C,OAAL;AAAce,QAAAA,IAAI,EAAEb,gBAAgB,GAAG,OAAH,GAAaF,OAAO,CAACe;AAAzD,OAAT,EAA0ElF,SAA1E,CAA3B,CAAR,CAAyHmF,IAAzH,CACKvF,IAAD,IAAU;AACN8E,QAAAA,oBAAoB,CAAC9E,IAAD,CAApB;AACAkF,QAAAA,UAAU;AACb,OAJL;AAMH;AACJ,GAVD;;AAYAzG,EAAAA,SAAS,CAAC,MAAM;AACZ4G,IAAAA,MAAM;AACT,GAFQ,EAEN,EAFM,CAAT;;AAIA,QAAMG,gBAAgB,GAAI5E,EAAD,IAAQ;AAC7B,WAAOiE,iBAAiB,CAACpE,IAAlB,CAAwBR,IAAD,IAAUA,IAAI,CAACW,EAAL,KAAYA,EAA7C,KAAoD,EAA3D;AACH,GAFD;;AAIA,QAAM6E,kBAAkB,GAAIC,OAAD,IAAa;AACpC,WAAOA,OAAO,IAAI,CAACA,OAAO,CAACC,YAA3B;AACH,GAFD,CAnD2G,CAuD3G;;;AACA,QAAMC,aAAa,GAAG,CAClB;AACIC,IAAAA,IAAI,EAAE,IADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,KADH;AAEL7B,MAAAA,IAAI,EAAE,KAFD;AAGLsC,MAAAA,OAAO,EAAE;AAHJ;AAHb,GADkB,EAUlB;AACIyC,IAAAA,IAAI,EAAE,UADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLsC,MAAAA,OAAO,EAAE;AAHJ;AAHb,GAVkB,EAmBlB;AACIyC,IAAAA,IAAI,EAAE,YADV;AAEIC,IAAAA,KAAK,EAAE,QAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE;AAFD;AAHb,GAnBkB,EA2BlB;AACI+E,IAAAA,IAAI,EAAE,yBADV;AAEIC,IAAAA,KAAK,EAAE,cAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLsC,MAAAA,OAAO,EAAE,IAHJ;AAIL4C,MAAAA,gBAAgB,EAAGC,KAAD,iBAAW,QAAC,kBAAD;AAAoB,QAAA,KAAK,EAAEA;AAA3B;AAAA;AAAA;AAAA;AAAA;AAJxB;AAHb,GA3BkB,EAqClB;AACIJ,IAAAA,IAAI,EAAE,gBADV;AAEIC,IAAAA,KAAK,EAAE,OAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLsC,MAAAA,OAAO,EAAE,IAHJ;AAIL4C,MAAAA,gBAAgB,EAAE,CAACC,KAAD,EAAQC,SAAR,KAAsB;AACpC,cAAMtF,EAAE,GAAGsF,SAAS,CAACR,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGF,gBAAgB,CAAC5E,EAAD,CAAhC;;AACA,YAAI,CAAC6E,kBAAkB,CAACC,OAAD,CAAvB,EAAkC;AAC9B,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AACD,4BACI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAEtC,YAAAA,OAAO,EAAE,MAAX;AAAmB+C,YAAAA,UAAU,EAAE,QAA/B;AAAyC1C,YAAAA,cAAc,EAAE,KAAzD;AAAgEH,YAAAA,GAAG,EAAE;AAArE,WAAT;AAAA,iCACI,QAAC,kBAAD;AAAoB,YAAA,KAAK,EAAE2C;AAA3B;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,gBADJ;AAKH;AAfI;AAHb,GArCkB,EA0DlB;AACIJ,IAAAA,IAAI,EAAE,YADV;AAEIC,IAAAA,KAAK,EAAE,SAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLsF,MAAAA,kBAAkB,EAAE,OAAO;AAAEC,QAAAA,KAAK,EAAE;AAAEC,UAAAA,QAAQ,EAAE,OAAZ;AAAqBC,UAAAA,UAAU,EAAE;AAAjC;AAAT,OAAP,CAHf;AAILC,MAAAA,YAAY,EAAE,OAAO;AAAEH,QAAAA,KAAK,EAAE;AAAEC,UAAAA,QAAQ,EAAE,OAAZ;AAAqBC,UAAAA,UAAU,EAAE;AAAjC;AAAT,OAAP,CAJT;AAKLP,MAAAA,gBAAgB,EAAGC,KAAD,IAAW;AACzB,cAAMQ,SAAS,GAAGR,KAAlB;;AAEA,YAAI,CAACQ,SAAD,IAAc,CAACA,SAAS,CAACC,sBAA7B,EAAqD;AACjD,8BAAO,QAAC,UAAD;AAAY,YAAA,EAAE,EAAE;AAAEH,cAAAA,UAAU,EAAE,QAAd;AAAwBlE,cAAAA,SAAS,EAAE;AAAnC,aAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,cAAMsE,UAAU,GAAGC,UAAU,CAACH,SAAS,CAACC,sBAAX,CAA7B;AAEA,4BAAO,QAAC,UAAD;AAAY,UAAA,EAAE,EAAE;AAAEH,YAAAA,UAAU,EAAE,QAAd;AAAwBlE,YAAAA,SAAS,EAAE;AAAnC,WAAhB;AAAA,qBAAgEsE,UAAU,CAACE,OAAX,CAAmB,CAAnB,CAAhE;AAAA;AAAA;AAAA;AAAA;AAAA,gBAAP;AACH;AAfI;AAHb,GA1DkB,CAAtB;AAiFA,QAAMC,uBAAuB,GAAG,CAC5B;AACIjB,IAAAA,IAAI,EAAE,IADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,KADH;AAEL7B,MAAAA,IAAI,EAAE,KAFD;AAGLsC,MAAAA,OAAO,EAAE;AAHJ;AAHb,GAD4B,EAU5B;AACIyC,IAAAA,IAAI,EAAE,UADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLsC,MAAAA,OAAO,EAAE;AAHJ;AAHb,GAV4B,EAmB5B;AACIyC,IAAAA,IAAI,EAAE,YADV;AAEIC,IAAAA,KAAK,EAAE,QAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE;AAFD;AAHb,GAnB4B,EA2B5B;AACI+E,IAAAA,IAAI,EAAE,gBADV;AAEIC,IAAAA,KAAK,EAAE,OAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLsC,MAAAA,OAAO,EAAE,IAHJ;AAIL4C,MAAAA,gBAAgB,EAAE,CAACC,KAAD,EAAQC,SAAR,KAAsB;AACpC,cAAMtF,EAAE,GAAGsF,SAAS,CAACR,OAAV,CAAkB,CAAlB,CAAX;AACA,cAAMA,OAAO,GAAGF,gBAAgB,CAAC5E,EAAD,CAAhC;;AACA,YAAI,CAAC6E,kBAAkB,CAACC,OAAD,CAAvB,EAAkC;AAC9B,8BAAO,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,MAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AACD,4BACI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAEtC,YAAAA,OAAO,EAAE,MAAX;AAAmB+C,YAAAA,UAAU,EAAE,QAA/B;AAAyC1C,YAAAA,cAAc,EAAE,KAAzD;AAAgEH,YAAAA,GAAG,EAAE;AAArE,WAAT;AAAA,iCACI,QAAC,kBAAD;AAAoB,YAAA,KAAK,EAAE2C;AAA3B;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,gBADJ;AAKH;AAfI;AAHb,GA3B4B,CAAhC,CAzI2G,CA2L3G;;AACA,QAAM/C,+BAA+B,GAAG,CACpC;AACI2C,IAAAA,IAAI,EAAE,YADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLsF,MAAAA,kBAAkB,EAAE,OAAO;AAAEC,QAAAA,KAAK,EAAE;AAAE9E,UAAAA,KAAK,EAAE,MAAT;AAAiBwF,UAAAA,QAAQ,EAAE;AAA3B;AAAT,OAAP,CAHf;AAILP,MAAAA,YAAY,EAAE,OAAO;AAAEH,QAAAA,KAAK,EAAE;AAAE9E,UAAAA,KAAK,EAAE,MAAT;AAAiBwF,UAAAA,QAAQ,EAAE;AAA3B;AAAT,OAAP;AAJT;AAHb,GADoC,EAWpC;AACIlB,IAAAA,IAAI,EAAE,cADV;AAEIC,IAAAA,KAAK,EAAE,UAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLsF,MAAAA,kBAAkB,EAAE,OAAO;AAAEC,QAAAA,KAAK,EAAE;AAAEC,UAAAA,QAAQ,EAAE,OAAZ;AAAqBC,UAAAA,UAAU,EAAE;AAAjC;AAAT,OAAP,CAHf;AAILC,MAAAA,YAAY,EAAE,OAAO;AAAEH,QAAAA,KAAK,EAAE;AAAEC,UAAAA,QAAQ,EAAE,OAAZ;AAAqBC,UAAAA,UAAU,EAAE;AAAjC;AAAT,OAAP,CAJT;AAKLP,MAAAA,gBAAgB,EAAGC,KAAD,iBACd,QAAC,UAAD;AAAY,QAAA,EAAE,EAAE;AAAEM,UAAAA,UAAU,EAAE;AAAd,SAAhB;AAAA,+BACI;AAAA,oBAASN;AAAT;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AANC;AAHb,GAXoC,EA0BpC;AACIJ,IAAAA,IAAI,EAAE,YADV;AAEIC,IAAAA,KAAK,EAAE,SAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLsF,MAAAA,kBAAkB,EAAE,OAAO;AAAEC,QAAAA,KAAK,EAAE;AAAEC,UAAAA,QAAQ,EAAE,OAAZ;AAAqBC,UAAAA,UAAU,EAAE;AAAjC;AAAT,OAAP,CAHf;AAILC,MAAAA,YAAY,EAAE,OAAO;AAAEH,QAAAA,KAAK,EAAE;AAAEC,UAAAA,QAAQ,EAAE,OAAZ;AAAqBC,UAAAA,UAAU,EAAE;AAAjC;AAAT,OAAP,CAJT;AAKLP,MAAAA,gBAAgB,EAAGC,KAAD,IAAW;AACzB,cAAMQ,SAAS,GAAGR,KAAlB;;AAEA,YAAI,CAACQ,SAAD,IAAc,CAACA,SAAS,CAACC,sBAA7B,EAAqD;AACjD,8BAAO,QAAC,UAAD;AAAY,YAAA,EAAE,EAAE;AAAEH,cAAAA,UAAU,EAAE,QAAd;AAAwBlE,cAAAA,SAAS,EAAE;AAAnC,aAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAP;AACH;;AAED,cAAMsE,UAAU,GAAGC,UAAU,CAACH,SAAS,CAACC,sBAAX,CAA7B;AAEA,4BAAO,QAAC,UAAD;AAAY,UAAA,EAAE,EAAE;AAAEH,YAAAA,UAAU,EAAE,QAAd;AAAwBlE,YAAAA,SAAS,EAAE;AAAnC,WAAhB;AAAA,qBAAgEsE,UAAU,CAACE,OAAX,CAAmB,CAAnB,CAAhE;AAAA;AAAA;AAAA;AAAA;AAAA,gBAAP;AACH;AAfI;AAHb,GA1BoC,EA+CpC;AACIhB,IAAAA,IAAI,EAAE,yBADV;AAEIC,IAAAA,KAAK,EAAE,cAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLkF,MAAAA,gBAAgB,EAAGC,KAAD,iBAAW,QAAC,kBAAD;AAAoB,QAAA,KAAK,EAAEe,IAAI,CAACC,MAAL,KAAgB;AAA3C;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GA/CoC,EAwDpC;AACIpB,IAAAA,IAAI,EAAE,gBADV;AAEIC,IAAAA,KAAK,EAAE,kBAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLkF,MAAAA,gBAAgB,EAAGC,KAAD,iBAAW,QAAC,kBAAD;AAAoB,QAAA,KAAK,EAAEA;AAA3B;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GAxDoC,EAiEpC;AACIJ,IAAAA,IAAI,EAAE,iBADV;AAEIC,IAAAA,KAAK,EAAE,eAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLkF,MAAAA,gBAAgB,EAAGC,KAAD,iBAAW,QAAC,kBAAD;AAAoB,QAAA,KAAK,EAAEA;AAA3B;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GAjEoC,EA0EpC;AACIJ,IAAAA,IAAI,EAAE,iBADV;AAEIC,IAAAA,KAAK,EAAE,MAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLkF,MAAAA,gBAAgB,EAAGC,KAAD,iBAAW,QAAC,UAAD;AAAA,kBAAaA;AAAb;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GA1EoC,EAmFpC;AACIJ,IAAAA,IAAI,EAAE,cADV;AAEIC,IAAAA,KAAK,EAAE,QAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,KAFD;AAGLkF,MAAAA,gBAAgB,EAAE,CAACC,KAAD,EAAQC,SAAR,kBAAsB,QAAC,kBAAD;AAAoB,QAAA,KAAK,EAAEc,IAAI,CAACC,MAAL,KAAgB;AAA3C;AAAA;AAAA;AAAA;AAAA;AAHnC;AAHb,GAnFoC,EA4FpC;AACIpB,IAAAA,IAAI,EAAE,cADV;AAEIC,IAAAA,KAAK,EAAE,UAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLkF,MAAAA,gBAAgB,EAAGC,KAAD,iBAAW,QAAC,UAAD;AAAA,kBAAaA;AAAb;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GA5FoC,CAAxC,CA5L2G,CAmS3G;;AACA,QAAMiB,kCAAkC,GAAG,CACvC;AACIrB,IAAAA,IAAI,EAAE,YADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLsF,MAAAA,kBAAkB,EAAE,OAAO;AAAEC,QAAAA,KAAK,EAAE;AAAE9E,UAAAA,KAAK,EAAE,MAAT;AAAiBwF,UAAAA,QAAQ,EAAE;AAA3B;AAAT,OAAP,CAHf;AAILP,MAAAA,YAAY,EAAE,OAAO;AAAEH,QAAAA,KAAK,EAAE;AAAE9E,UAAAA,KAAK,EAAE,MAAT;AAAiBwF,UAAAA,QAAQ,EAAE;AAA3B;AAAT,OAAP;AAJT;AAHb,GADuC,EAWvC;AACIlB,IAAAA,IAAI,EAAE,cADV;AAEIC,IAAAA,KAAK,EAAE,UAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLsF,MAAAA,kBAAkB,EAAE,OAAO;AAAEC,QAAAA,KAAK,EAAE;AAAEC,UAAAA,QAAQ,EAAE,OAAZ;AAAqBC,UAAAA,UAAU,EAAE;AAAjC;AAAT,OAAP,CAHf;AAILC,MAAAA,YAAY,EAAE,OAAO;AAAEH,QAAAA,KAAK,EAAE;AAAEC,UAAAA,QAAQ,EAAE,OAAZ;AAAqBC,UAAAA,UAAU,EAAE;AAAjC;AAAT,OAAP,CAJT;AAKLP,MAAAA,gBAAgB,EAAGC,KAAD,iBACd,QAAC,UAAD;AAAY,QAAA,EAAE,EAAE;AAAEM,UAAAA,UAAU,EAAE;AAAd,SAAhB;AAAA,+BACI;AAAA,oBAASN;AAAT;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AANC;AAHb,GAXuC,EA0BvC;AACIJ,IAAAA,IAAI,EAAE,yBADV;AAEIC,IAAAA,KAAK,EAAE,cAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLkF,MAAAA,gBAAgB,EAAGC,KAAD,iBAAW,QAAC,kBAAD;AAAoB,QAAA,KAAK,EAAEe,IAAI,CAACC,MAAL,KAAgB;AAA3C;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GA1BuC,EAmCvC;AACIpB,IAAAA,IAAI,EAAE,gBADV;AAEIC,IAAAA,KAAK,EAAE,kBAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLkF,MAAAA,gBAAgB,EAAGC,KAAD,iBAAW,QAAC,kBAAD;AAAoB,QAAA,KAAK,EAAEA;AAA3B;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GAnCuC,EA4CvC;AACIJ,IAAAA,IAAI,EAAE,iBADV;AAEIC,IAAAA,KAAK,EAAE,eAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLkF,MAAAA,gBAAgB,EAAGC,KAAD,iBAAW,QAAC,kBAAD;AAAoB,QAAA,KAAK,EAAEA;AAA3B;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GA5CuC,EAqDvC;AACIJ,IAAAA,IAAI,EAAE,iBADV;AAEIC,IAAAA,KAAK,EAAE,MAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLkF,MAAAA,gBAAgB,EAAGC,KAAD,iBAAW,QAAC,UAAD;AAAA,kBAAaA;AAAb;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GArDuC,EA8DvC;AACIJ,IAAAA,IAAI,EAAE,cADV;AAEIC,IAAAA,KAAK,EAAE,QAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,KAFD;AAGLkF,MAAAA,gBAAgB,EAAE,CAACC,KAAD,EAAQC,SAAR,kBAAsB,QAAC,kBAAD;AAAoB,QAAA,KAAK,EAAEc,IAAI,CAACC,MAAL,KAAgB;AAA3C;AAAA;AAAA;AAAA;AAAA;AAHnC;AAHb,GA9DuC,EAuEvC;AACIpB,IAAAA,IAAI,EAAE,cADV;AAEIC,IAAAA,KAAK,EAAE,UAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLkF,MAAAA,gBAAgB,EAAGC,KAAD,iBAAW,QAAC,UAAD;AAAA,kBAAaA;AAAb;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GAvEuC,CAA3C,CApS2G,CAsX3G;;AACA,QAAMhD,sBAAsB,GAAG,CAC3B;AACI4C,IAAAA,IAAI,EAAE,IADV;AAEIC,IAAAA,KAAK,EAAE,IAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,KADH;AAEL7B,MAAAA,IAAI,EAAE,KAFD;AAGLsC,MAAAA,OAAO,EAAE;AAHJ;AAHb,GAD2B,EAU3B;AACIyC,IAAAA,IAAI,EAAE,YADV;AAEIC,IAAAA,KAAK,EAAE,QAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLkF,MAAAA,gBAAgB,EAAGC,KAAD,iBAAW,QAAC,UAAD;AAAY,QAAA,EAAE,EAAE;AAAE/B,UAAAA,QAAQ,EAAE,QAAZ;AAAsBC,UAAAA,UAAU,EAAE;AAAlC,SAAhB;AAAA,kBAA6D8B;AAA7D;AAAA;AAAA;AAAA;AAAA;AAHxB;AAHb,GAV2B,EAmB3B;AACIJ,IAAAA,IAAI,EAAE,OADV;AAEIC,IAAAA,KAAK,EAAE,OAFX;AAGIC,IAAAA,OAAO,EAAE;AACLpD,MAAAA,MAAM,EAAE,IADH;AAEL7B,MAAAA,IAAI,EAAE,IAFD;AAGLkF,MAAAA,gBAAgB,EAAGC,KAAD,iBACd,QAAC,UAAD;AAAY,QAAA,EAAE,EAAE;AAAE/B,UAAAA,QAAQ,EAAE,QAAZ;AAAsBC,UAAAA,UAAU,EAAE,QAAlC;AAA4C9B,UAAAA,SAAS,EAAE;AAAvD,SAAhB;AAAA,kBACKuE,UAAU,CAACX,KAAD,CAAV,CAAkBY,OAAlB,CAA0B,CAA1B;AADL;AAAA;AAAA;AAAA;AAAA;AAJC;AAHb,GAnB2B,CAA/B;AAkCA,sBACI,QAAC,GAAD;AAAK,IAAA,EAAE,EAAE;AAAEzD,MAAAA,OAAO,EAAE,MAAX;AAAmBE,MAAAA,GAAG,EAAE,CAAxB;AAA2BD,MAAAA,aAAa,EAAE,KAA1C;AAAiD9B,MAAAA,KAAK,EAAE,MAAxD;AAAgEkC,MAAAA,cAAc,EAAE;AAAhF,KAAT;AAAA,4BACI,QAAC,UAAD;AACI,MAAA,KAAK,EAAC,KADV;AAEI,MAAA,EAAE,EAAE;AACA,4BAAoB;AAChBlC,UAAAA,KAAK,EAAE,iBADS;AAEhByC,UAAAA,WAAW,EAAE;AAFG,SADpB;AAKA,gCAAwB;AACpBC,UAAAA,OAAO,EAAE;AADW;AALxB,OAFR;AAAA,6BAYI,QAAC,UAAD;AACI,QAAA,OAAO,EAAE1E,eAAe,CAACM,MAAhB,GAAyB,CAAzB,GAA6BqD,+BAA7B,GAA+DgE,kCAD5E;AAEI,QAAA,IAAI,EAAEtC,eAFV;AAGI,QAAA,KAAK,eAAE,QAAC,UAAD;AAAY,UAAA,EAAE,EAAE;AAAEV,YAAAA,QAAQ,EAAE,QAAZ;AAAsBC,YAAAA,UAAU,EAAE;AAAlC,WAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAHX;AAII,QAAA,mBAAmB,EAAGC,KAAD,iBACjB,QAAC,2BAAD,OACQA,KADR;AAEI,UAAA,IAAI,EAAEpE,IAFV;AAGI,UAAA,sBAAsB,EAAEiD,sBAH5B;AAII,UAAA,+BAA+B,EAAEC;AAJrC;AAAA;AAAA;AAAA;AAAA,gBALR;AAYI,QAAA,OAAO,EAAE;AACLX,UAAAA,MAAM,EAAE,KADH;AAELC,UAAAA,QAAQ,EAAE,KAFL;AAGLC,UAAAA,KAAK,EAAE,KAHF;AAIL3B,UAAAA,IAAI,EAAE,KAJD;AAKL4B,UAAAA,WAAW,EAAE,IALR;AAMLC,UAAAA,MAAM,EAAE,KANH;AAOLwE,UAAAA,UAAU,EAAE,aAPP;AAQL9C,UAAAA,UAAU,EAAE,UARP;AASL+C,UAAAA,WAAW,EAAE,IATR;AAULC,UAAAA,iBAAiB,EAAE,IAVd;AAWLC,UAAAA,UAAU,EAAE,KAXP;AAYLC,UAAAA,gBAAgB,EAAE,KAZb;AAaLC,UAAAA,gBAAgB,EAAE;AACdC,YAAAA,OAAO,EAAE;AADK,WAbb;AAgBLC,UAAAA,UAAU,EAAE,IAhBP;AAiBL7E,UAAAA,cAAc,EAAE,MAjBX;AAkBL8E,UAAAA,qBAAqB,EAAE,KAlBlB;AAmBL/E,UAAAA,UAAU,EAAE,KAnBP;AAoBLgF,UAAAA,cAAc,EAAE,KApBX;AAqBLC,UAAAA,QAAQ,EAAE,IArBL;AAsBL/E,UAAAA,OAAO,EAAE;AAtBJ;AAZb;AAAA;AAAA;AAAA;AAAA;AAZJ;AAAA;AAAA;AAAA;AAAA,YADJ,EAoDKkC,OAAO,iBAAI,QAAC,WAAD;AAAA;AAAA;AAAA;AAAA,YApDhB;AAAA;AAAA;AAAA;AAAA;AAAA,UADJ;AAwDH,CAjdD;;IAAMV,yB;UACe3F,W,EACAC,W,EACWA,W,EAuBgBI,U;;;MA1B1CsF,yB;AAmdN,eAAeA,yBAAf", "sourcesContent": ["import { Box, Typography } from '@mui/material';\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'store';\nimport { getRepositionDataByProduct, openRotationModal, setSelectedProduct } from 'store/slices/reposition/reposition';\nimport useLoading from 'hooks/useLoading';\nimport { BlockLoader } from 'ui-component/loaders/loaders';\nimport RightAlignedNumber from 'ui-component/grid/RightAlignedNumber';\nimport Grid from 'ui-component/grid/Grid';\nimport NestedGrid from 'ui-component/grid/NestedGrid';\nimport MainCard from 'ui-component/cards/MainCard';\n\nconst findDerivedProductRecursively = (derivedProducts, targetProductId) => {\n    if (!derivedProducts || !Array.isArray(derivedProducts)) return null;\n\n    for (const derived of derivedProducts) {\n        if (derived.product_id === targetProductId) {\n            return derived;\n        }\n\n        if (derived.derivedProducts && derived.derivedProducts.length > 0) {\n            const found = findDerivedProductRecursively(derived.derivedProducts, targetProductId);\n            if (found) return found;\n        }\n    }\n\n    return null;\n};\n\nconst findMainProductWithDerivedProduct = (data, targetProductId) => {\n    if (!data || !Array.isArray(data)) return null;\n\n    for (const item of data) {\n        const found = findDerivedProductRecursively(item.derivedProducts, targetProductId);\n        if (found) return item;\n    }\n\n    return null;\n};\n\nconst processAnalisys = (productData, storeData) => {\n    if (!productData || !productData.analisys || !Array.isArray(productData.analisys)) {\n        return [];\n    }\n\n    const listData = productData.analisys.map((item) => {\n        const store = storeData.find((s) => s.store_id === item.store_id);\n        return {\n            ...item,\n            pk: `${item.store_id}_${productData.product_id}`,\n            store_name: store ? store.store_name : 'Tienda no encontrada'\n        };\n    });\n\n    listData.sort((a, b) => a.warehouse_id - b.warehouse_id);\n\n    return listData;\n};\n\nconst getDerivedProducts = (productData) => {\n    if (!productData || !productData.derivedProducts) return [];\n\n    return productData.derivedProducts.map((item, index) => ({\n        ...item,\n        pk: item.product_id,\n        globalIndex: index\n    }));\n};\n\nconst NestedCard = ({ children, width = '50%' }) => (\n    <Box sx={{ p: 1, backgroundColor: 'white', borderRadius: '1rem', border: '1px solid #e0e0e0', width }}>{children}</Box>\n);\n\nconst DerivedProductAnalysis = ({ row, columns }) => {\n    const productId = row[0];\n    const { data } = useSelector((state) => state.reposition);\n    const { data: storeData } = useSelector((state) => state.store);\n    const mainProductData = findMainProductWithDerivedProduct(data, productId);\n    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);\n    const derivedAnalysis = derivedProduct?.analisys ? processAnalisys(derivedProduct, storeData) : null;\n\n    if (!derivedAnalysis || derivedAnalysis.length === 0) {\n        return (\n            <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>\n                <Typography variant=\"body2\">No hay análisis disponible para este producto derivado</Typography>\n            </Box>\n        );\n    }\n\n    return (\n        <Grid\n            columns={columns}\n            data={derivedAnalysis}\n            options={{\n                search: false,\n                download: false,\n                print: false,\n                sort: false,\n                viewColumns: false,\n                filter: false,\n                pagination: false,\n                selectableRows: 'none',\n                toolbar: false,\n                elevation: 0\n            }}\n        />\n    );\n};\n\nconst DerivedProductNestedContent = ({ row, data, derivedAnalysisColumns, simplifiedDerivedProductColumns }) => {\n    const productId = row[0];\n    const mainProductData = findMainProductWithDerivedProduct(data, productId);\n    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);\n    const hasSubDerived = derivedProduct?.derivedProducts && derivedProduct.derivedProducts.length > 0;\n\n    return (\n        <Box\n            sx={{\n                display: 'flex',\n                flexDirection: 'row',\n                gap: 2,\n                pb: 1,\n                px: 2,\n                justifyContent: 'center',\n                backgroundColor: '#f5f5f5',\n                borderRadius: '1rem',\n                my: 1\n            }}\n        >\n            <Box sx={{ width: hasSubDerived ? '25%' : '40%' }}>\n                <MainCard>\n                    <DerivedProductAnalysis row={row} columns={derivedAnalysisColumns} />\n                </MainCard>\n            </Box>\n            {hasSubDerived && (\n                <Box sx={{ width: '75%' }}>\n                    <MainCard>\n                        <SubDerivedProducts row={row} columns={simplifiedDerivedProductColumns} analysisColumns={derivedAnalysisColumns} />\n                    </MainCard>\n                </Box>\n            )}\n        </Box>\n    );\n};\n\nconst SubDerivedProducts = ({ row, columns, analysisColumns }) => {\n    const productId = row[0];\n    const { data } = useSelector((state) => state.reposition);\n    const mainProductData = findMainProductWithDerivedProduct(data, productId);\n    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);\n    const rawSubDerivedProducts = derivedProduct?.derivedProducts || [];\n\n    const subDerivedProducts = rawSubDerivedProducts.map((item, index) => ({\n        ...item,\n        pk: item.product_id,\n        globalIndex: index\n    }));\n\n    return (\n        <Box sx={{ width: '100%', height: 'fit-content' }}>\n            <Box\n                sx={{\n                    '& .MuiTable-root': {\n                        width: '100% !important',\n                        tableLayout: 'fixed'\n                    },\n                    '& .MuiTableCell-root': {\n                        padding: '4px 8px',\n                        fontSize: '0.75rem'\n                    }\n                }}\n            >\n                <NestedGrid\n                    columns={subDerivedProducts.length > 0 ? columns : []}\n                    data={subDerivedProducts}\n                    title={<Typography sx={{ fontSize: '1rem', fontWeight: 'bold' }}>Sub-Derivados</Typography>}\n                    RenderNestedContent={(props) => (\n                        <DerivedProductNestedContent\n                            {...props}\n                            data={data}\n                            derivedAnalysisColumns={analysisColumns}\n                            simplifiedDerivedProductColumns={columns}\n                        />\n                    )}\n                    options={{\n                        search: false,\n                        download: false,\n                        print: false,\n                        sort: false,\n                        viewColumns: false,\n                        filter: false,\n                        pagination: false,\n                        selectableRows: 'none',\n                        toolbar: false,\n                        elevation: 0,\n                        responsive: 'vertical'\n                    }}\n                />\n            </Box>\n        </Box>\n    );\n};\n\nconst RawMaterialRotationDetail = ({ row, filters, supplyAnalisys, isFromProyection, merchandiseFoodData }) => {\n    const dispatch = useDispatch();\n    const { data } = useSelector((state) => state.reposition);\n    const { data: storeData } = useSelector((state) => state.store);\n\n    const productData = isFromProyection\n        ? merchandiseFoodData.find((item) => item.product_id === row[0])\n        : data.find((item) => item.product_id === row[0]);\n\n    const derivedProducts = getDerivedProducts(productData);\n\n    const displayProducts =\n        derivedProducts.length > 0\n            ? derivedProducts\n            : productData\n            ? [\n                  {\n                      ...productData,\n                      pk: productData.product_id || productData.pk,\n                      globalIndex: 0\n                  }\n              ]\n            : [];\n\n    const [repositionProduct, setRepositionProduct] = useState(supplyAnalisys || []);\n    const [isAsync] = useState(!supplyAnalisys);\n    const [loading, startLoading, endLoading] = useLoading(isAsync);\n\n    const openModal = () => dispatch(openRotationModal());\n    const setSelected = (data) => dispatch(setSelectedProduct(data));\n\n    const reload = () => {\n        if (isAsync) {\n            startLoading();\n            dispatch(getRepositionDataByProduct(row[0], { ...filters, mode: isFromProyection ? 'Merc2' : filters.mode }, storeData)).then(\n                (data) => {\n                    setRepositionProduct(data);\n                    endLoading();\n                }\n            );\n        }\n    };\n\n    useEffect(() => {\n        reload();\n    }, []);\n\n    const getRowDataSafely = (pk) => {\n        return repositionProduct.find((item) => item.pk === pk) || {};\n    };\n\n    const isRowDataAvailable = (rowData) => {\n        return rowData && !rowData.notAvailable;\n    };\n\n    // Supply columns for raw material analysis\n    const supplyColumns = [\n        {\n            name: 'pk',\n            label: 'PK',\n            options: {\n                filter: false,\n                sort: false,\n                display: false\n            }\n        },\n        {\n            name: 'store_id',\n            label: 'ID',\n            options: {\n                filter: true,\n                sort: true,\n                display: false\n            }\n        },\n        {\n            name: 'store_name',\n            label: 'TIENDA',\n            options: {\n                filter: true,\n                sort: true\n            }\n        },\n        {\n            name: 'unit_quantity_proyected',\n            label: 'C.PROYECTADA',\n            options: {\n                filter: true,\n                sort: true,\n                display: true,\n                customBodyRender: (value) => <RightAlignedNumber value={value} />\n            }\n        },\n        {\n            name: 'purchase_stock',\n            label: 'STOCK',\n            options: {\n                filter: true,\n                sort: true,\n                display: true,\n                customBodyRender: (value, tableMeta) => {\n                    const pk = tableMeta.rowData[0];\n                    const rowData = getRowDataSafely(pk);\n                    if (!isRowDataAvailable(rowData)) {\n                        return <Typography color=\"gray\">-</Typography>;\n                    }\n                    return (\n                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end', gap: 1 }}>\n                            <RightAlignedNumber value={value} />\n                        </Box>\n                    );\n                }\n            }\n        },\n        {\n            name: 'waste_info',\n            label: 'MERMA %',\n            options: {\n                filter: true,\n                sort: true,\n                setCellHeaderProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),\n                setCellProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),\n                customBodyRender: (value) => {\n                    const wasteInfo = value;\n\n                    if (!wasteInfo || !wasteInfo.waste_percentage_total) {\n                        return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>-</Typography>;\n                    }\n\n                    const percentage = parseFloat(wasteInfo.waste_percentage_total);\n\n                    return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>{percentage.toFixed(2)}%</Typography>;\n                }\n            }\n        }\n    ];\n\n    const rawProductSupplyColumns = [\n        {\n            name: 'pk',\n            label: 'PK',\n            options: {\n                filter: false,\n                sort: false,\n                display: false\n            }\n        },\n        {\n            name: 'store_id',\n            label: 'ID',\n            options: {\n                filter: true,\n                sort: true,\n                display: false\n            }\n        },\n        {\n            name: 'store_name',\n            label: 'TIENDA',\n            options: {\n                filter: true,\n                sort: true\n            }\n        },\n        {\n            name: 'purchase_stock',\n            label: 'STOCK',\n            options: {\n                filter: true,\n                sort: true,\n                display: true,\n                customBodyRender: (value, tableMeta) => {\n                    const pk = tableMeta.rowData[0];\n                    const rowData = getRowDataSafely(pk);\n                    if (!isRowDataAvailable(rowData)) {\n                        return <Typography color=\"gray\">-</Typography>;\n                    }\n                    return (\n                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end', gap: 1 }}>\n                            <RightAlignedNumber value={value} />\n                        </Box>\n                    );\n                }\n            }\n        }\n    ];\n\n    // Simplified columns for derived products (with waste info)\n    const simplifiedDerivedProductColumns = [\n        {\n            name: 'product_id',\n            label: 'ID',\n            options: {\n                filter: true,\n                sort: true,\n                setCellHeaderProps: () => ({ style: { width: '80px', maxWidth: '80px' } }),\n                setCellProps: () => ({ style: { width: '80px', maxWidth: '80px' } })\n            }\n        },\n        {\n            name: 'product_name',\n            label: 'PRODUCTO',\n            options: {\n                filter: true,\n                sort: true,\n                setCellHeaderProps: () => ({ style: { minWidth: '450px', whiteSpace: 'nowrap' } }),\n                setCellProps: () => ({ style: { minWidth: '450px', whiteSpace: 'nowrap' } }),\n                customBodyRender: (value) => (\n                    <Typography sx={{ whiteSpace: 'nowrap' }}>\n                        <strong>{value}</strong>\n                    </Typography>\n                )\n            }\n        },\n        {\n            name: 'waste_info',\n            label: 'MERMA %',\n            options: {\n                filter: true,\n                sort: true,\n                setCellHeaderProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),\n                setCellProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),\n                customBodyRender: (value) => {\n                    const wasteInfo = value;\n\n                    if (!wasteInfo || !wasteInfo.waste_percentage_total) {\n                        return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>-</Typography>;\n                    }\n\n                    const percentage = parseFloat(wasteInfo.waste_percentage_total);\n\n                    return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>{percentage.toFixed(2)}%</Typography>;\n                }\n            }\n        },\n        {\n            name: 'unit_quantity_proyected',\n            label: 'C.PROYECTADA',\n            options: {\n                filter: true,\n                sort: true,\n                customBodyRender: (value) => <RightAlignedNumber value={Math.random() * 100} />\n            }\n        },\n        {\n            name: 'purchase_stock',\n            label: 'STOCK EN TIENDAS',\n            options: {\n                filter: true,\n                sort: true,\n                customBodyRender: (value) => <RightAlignedNumber value={value} />\n            }\n        },\n        {\n            name: 'supplying_stock',\n            label: 'S A.PRINCIPAL',\n            options: {\n                filter: true,\n                sort: true,\n                customBodyRender: (value) => <RightAlignedNumber value={value} />\n            }\n        },\n        {\n            name: 'measure_default',\n            label: 'PRES',\n            options: {\n                filter: true,\n                sort: true,\n                customBodyRender: (value) => <Typography>{value}</Typography>\n            }\n        },\n        {\n            name: 'rep_pres_min',\n            label: '(NETO)',\n            options: {\n                filter: true,\n                sort: false,\n                customBodyRender: (value, tableMeta) => <RightAlignedNumber value={Math.random() * 100} />\n            }\n        },\n        {\n            name: 'measure_name',\n            label: 'PRES MIN',\n            options: {\n                filter: true,\n                sort: true,\n                customBodyRender: (value) => <Typography>{value}</Typography>\n            }\n        }\n    ];\n\n    // Simplified columns for non-derived products (without waste info)\n    const simplifiedNonDerivedProductColumns = [\n        {\n            name: 'product_id',\n            label: 'ID',\n            options: {\n                filter: true,\n                sort: true,\n                setCellHeaderProps: () => ({ style: { width: '80px', maxWidth: '80px' } }),\n                setCellProps: () => ({ style: { width: '80px', maxWidth: '80px' } })\n            }\n        },\n        {\n            name: 'product_name',\n            label: 'PRODUCTO',\n            options: {\n                filter: true,\n                sort: true,\n                setCellHeaderProps: () => ({ style: { minWidth: '450px', whiteSpace: 'nowrap' } }),\n                setCellProps: () => ({ style: { minWidth: '450px', whiteSpace: 'nowrap' } }),\n                customBodyRender: (value) => (\n                    <Typography sx={{ whiteSpace: 'nowrap' }}>\n                        <strong>{value}</strong>\n                    </Typography>\n                )\n            }\n        },\n        {\n            name: 'unit_quantity_proyected',\n            label: 'C.PROYECTADA',\n            options: {\n                filter: true,\n                sort: true,\n                customBodyRender: (value) => <RightAlignedNumber value={Math.random() * 100} />\n            }\n        },\n        {\n            name: 'purchase_stock',\n            label: 'STOCK EN TIENDAS',\n            options: {\n                filter: true,\n                sort: true,\n                customBodyRender: (value) => <RightAlignedNumber value={value} />\n            }\n        },\n        {\n            name: 'supplying_stock',\n            label: 'S A.PRINCIPAL',\n            options: {\n                filter: true,\n                sort: true,\n                customBodyRender: (value) => <RightAlignedNumber value={value} />\n            }\n        },\n        {\n            name: 'measure_default',\n            label: 'PRES',\n            options: {\n                filter: true,\n                sort: true,\n                customBodyRender: (value) => <Typography>{value}</Typography>\n            }\n        },\n        {\n            name: 'rep_pres_min',\n            label: '(NETO)',\n            options: {\n                filter: true,\n                sort: false,\n                customBodyRender: (value, tableMeta) => <RightAlignedNumber value={Math.random() * 100} />\n            }\n        },\n        {\n            name: 'measure_name',\n            label: 'PRES MIN',\n            options: {\n                filter: true,\n                sort: true,\n                customBodyRender: (value) => <Typography>{value}</Typography>\n            }\n        }\n    ];\n\n    // Analysis columns for derived products\n    const derivedAnalysisColumns = [\n        {\n            name: 'pk',\n            label: 'PK',\n            options: {\n                filter: false,\n                sort: false,\n                display: false\n            }\n        },\n        {\n            name: 'store_name',\n            label: 'TIENDA',\n            options: {\n                filter: true,\n                sort: true,\n                customBodyRender: (value) => <Typography sx={{ fontSize: '1.2rem', fontWeight: 'bold' }}>{value}</Typography>\n            }\n        },\n        {\n            name: 'stock',\n            label: 'STOCK',\n            options: {\n                filter: true,\n                sort: true,\n                customBodyRender: (value) => (\n                    <Typography sx={{ fontSize: '1.2rem', fontWeight: 'medium', textAlign: 'right' }}>\n                        {parseFloat(value).toFixed(2)}\n                    </Typography>\n                )\n            }\n        }\n    ];\n\n    return (\n        <Box sx={{ display: 'flex', gap: 1, flexDirection: 'row', width: '100%', justifyContent: 'center' }}>\n            <NestedCard\n                width=\"90%\"\n                sx={{\n                    '& .MuiTable-root': {\n                        width: '100% !important',\n                        tableLayout: 'fixed'\n                    },\n                    '& .MuiTableCell-root': {\n                        padding: '8px 16px'\n                    }\n                }}\n            >\n                <NestedGrid\n                    columns={derivedProducts.length > 0 ? simplifiedDerivedProductColumns : simplifiedNonDerivedProductColumns}\n                    data={displayProducts}\n                    title={<Typography sx={{ fontSize: '1.2rem', fontWeight: 'bold' }}>Productos Derivados</Typography>}\n                    RenderNestedContent={(props) => (\n                        <DerivedProductNestedContent\n                            {...props}\n                            data={data}\n                            derivedAnalysisColumns={derivedAnalysisColumns}\n                            simplifiedDerivedProductColumns={simplifiedDerivedProductColumns}\n                        />\n                    )}\n                    options={{\n                        search: false,\n                        download: false,\n                        print: false,\n                        sort: false,\n                        viewColumns: true,\n                        filter: false,\n                        filterType: 'multiselect',\n                        responsive: 'vertical',\n                        fixedHeader: true,\n                        fixedSelectColumn: true,\n                        jumpToPage: false,\n                        resizableColumns: false,\n                        draggableColumns: {\n                            enabled: true\n                        },\n                        serverSide: true,\n                        selectableRows: 'none',\n                        selectableRowsOnClick: false,\n                        pagination: false,\n                        confirmFilters: false,\n                        rowHover: true,\n                        toolbar: false\n                    }}\n                />\n            </NestedCard>\n\n            {loading && <BlockLoader />}\n        </Box>\n    );\n};\n\nexport default RawMaterialRotationDetail;\n"]}, "metadata": {}, "sourceType": "module"}