<?php

namespace App\Http\Controllers\Api\V1\SupplyStocks;

use App\Models\Procedures\SpReportsSupplyStocks;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class SupplyStocksController extends Controller {
    /**
     *
     *
     * @return \Illuminate\Http\Response
     */
    public function report(Request $request) {
        $validate = Validator::make($request->all(), [
            'page' => 'required|string',
            'pageSize' => 'required|integer',
            'mode' => 'required|string',
            'warehouse' => 'required|string'
        ]);

        if ($validate->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validate->errors()
            ], 400);
        }

        $s_divisionIds = "";
        $s_lineIds = "";
        $s_sublineIds = "";
        $s_productIds = "";
        $s_product_name = "";
        $s_sort = "";
        $s_order = "";
        $s_domain = "";
        $i_with_stock = 0;


        if ($request->has('division')) {
            $s_divisionIds = $request->input('division');
        }

        if ($request->has('line')) {
            $s_lineIds = $request->input('line');
        }

        if ($request->has('subline')) {
            $s_sublineIds = $request->input('subline');
        }

        if ($request->has('productIds')) {
            $s_productIds = $request->input('productIds');
        }

        if ($request->has('productName')) {
            $s_product_name = $request->input('productName');
        }

        if ($request->has('domain')) {
            $s_domain = $request->input('domain');
        }

        if ($request->has('sort')) {
            $s_sort = $request->input('sort');
        }

        if ($request->has('order')) {
            $s_order = $request->input('order');
        }

        if ($request->has('includeNoStock')) {
            $i_with_stock = intval($request->input('includeNoStock'));
        }

        $data = SpReportsSupplyStocks::execute(
            $request->input('mode'),
            $request->input('warehouse'),
            $s_divisionIds,
            $s_lineIds,
            $s_sublineIds,
            $s_product_name,
            $s_productIds,
            $i_with_stock,
            $s_domain,
            $s_sort,
            $s_order,
            $request->input('page'),
            $request->input('pageSize'),
        );
        Log::info(json_encode(['response' => $data]));
        // $data['success'] = true;

        return response()->json($data);
    }

}
