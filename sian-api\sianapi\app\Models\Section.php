<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Section
 * 
 * @property int $section_id
 * @property string $model
 * @property bool $status
 * @property string $secret
 * @property int $subcategory_id
 * 
 * @property Subcategory $subcategory
 *
 * @package App\Models
 */
class Section extends Model
{
	protected $table = 'section';
	protected $primaryKey = 'section_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'subcategory_id' => 'int'
	];

	protected $hidden = [
		'secret'
	];

	protected $fillable = [
		'model',
		'status',
		'secret',
		'subcategory_id'
	];

	public function subcategory()
	{
		return $this->belongsTo(Subcategory::class);
	}
}
