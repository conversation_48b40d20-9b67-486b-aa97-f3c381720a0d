<?php

namespace App\Models\Procedures;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class SpGetSumPricesCost {
    const MODE_SUM = 0;
    const MODE_UNIT = 1;

    public static function execute($product_ids = [], $store_ids = [], $startDate = '', $endDate = '', $currency = '') {
        $stringProductIds = implode(',', $product_ids);
        $stringStoreIds = implode(',', $store_ids);

        $sql = "CALL sp_get_sum_prices_cost(?, ?, ?, ?, ?)";
        $parameters = [
            $stringProductIds,
            $stringStoreIds,
            $startDate,
            $endDate,
            $currency,
        ];

        return DB::select($sql, $parameters);
    }
}
