<?php

namespace App\Http\Controllers\Api\V1\Warehouse;

use App\Models\Warehouse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Models\Subline;
use Illuminate\Support\Facades\Log;


class WarehouseController extends Controller {
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getItems(Request $request) {
        try {

            $query = Warehouse::where("status", 1);

            if ($request->has('keyword')) {
                $keyword = $request->input('keyword');
                $query->where('warehouse_name', 'like', '%' . $keyword . '%');
            }

            if ($request->has('warehouse')) {
                $warehouseInput = $request->input('warehouse');
                $arrayWarehouse = explode(",", $warehouseInput);
                $query->whereNotIn("warehouse_id", $arrayWarehouse);
            }

            if ($request->has('limit')) {
                $limit = $request->input('limit');
                $query->limit($limit);
            } else {
                $query->limit(10);
            }


            $data = $query->get();

            return response()->json([
                'success' => true,
                'data' => $data,

            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }
}
