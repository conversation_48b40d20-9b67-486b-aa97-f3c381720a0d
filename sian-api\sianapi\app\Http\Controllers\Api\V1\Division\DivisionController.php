<?php

namespace App\Http\Controllers\Api\V1\Division;

use App\Models\Division;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;



class DivisionController extends Controller {
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getItems(Request $request) {
        try {

            $divisionFoodId = Division::where('status', 1)
                ->where('division_name', '=', Division::DIVISION_FOOD_NAME)
                ->value('division_id');

            $query = Division::where("status", 1)->where('division_id','!=',$divisionFoodId);

            if ($request->has('keyword')) {
                $keyword = $request->input('keyword');
                $query->where('division_name', 'like', '%' . $keyword . '%');
            }

            if ($request->has('division')) {
                $divisionSelected = $request->input('division');
                $arrayDivision = explode(",", $divisionSelected);
                $query->whereNotIn("division_id", $arrayDivision);
            }

            if ($request->has('limit')) {
                $limit = $request->input('limit');
                $query->limit($limit);
            } else {
                $query->limit(10);
            }


            $data = $query->get();

            return response()->json([
                'success' => true,
                'data' => $data,

            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }
}
