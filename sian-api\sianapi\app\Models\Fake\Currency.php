<?php

namespace App\Models\Fake;

class Currency {
    // CONST
    const PEN = 'pen';
    const USD = 'usd';

    const PEN_CAP = 'PEN';
    const USD_CAP = 'USD';
    //
    const PEN_NAME = 'Soles';
    const USD_NAME = 'Dólares';
    //
    const PEN_SYMBOL = 'S/';
    const USD_SYMBOL = '$';

    // VAR
    public $currency;

    public function __construct($currency) {
        $this->currency = $currency;
    }

    public static function getListData() {
        return [
            self::PEN => self::PEN_NAME . ' ' . self::PEN_SYMBOL,
            self::USD => self::USD_NAME . ' ' . self::USD_SYMBOL,
        ];
    }

    public static function getSymbol($currency) {
        switch ($currency) {
            case self::PEN:
                return self::PEN_SYMBOL;
            default:
                return self::USD_SYMBOL;
        }
    }

    public static function getName($currency) {
        switch ($currency) {
            case self::PEN:
                return self::PEN_NAME;
            case self::USD:
                return self::USD_NAME;
            default:
                return null;
        }
    }

    public static function getSQLCase($field) {
        return "CASE {$field}
            WHEN '" . self::PEN . "' THEN '" . self::PEN_NAME . "'
            WHEN '" . self::USD . "' THEN '" . self::USD_NAME . "'
            ELSE NULL
        END";
    }

    public static function getSymbolSQLCase($field) {
        return "CASE {$field}
            WHEN '" . self::PEN . "' THEN '" . self::PEN_SYMBOL . "'
            WHEN '" . self::USD . "' THEN '" . self::USD_SYMBOL . "'
            ELSE NULL
        END";
    }

    public function toString() {
        return self::getName($this->currency);
    }

    public static function format($value, $fixed = 2) {
        return number_format($value, $fixed, '.', ',');
    }

    public static function amountToLetters($amount, $currency = self::PEN) {
        $xarray = [
            0 => "CERO",
            1 => "UN", "DOS", "TRES", "CUATRO", "CINCO", "SEIS", "SIETE", "OCHO", "NUEVE",
            "DIEZ", "ONCE", "DOCE", "TRECE", "CATORCE", "QUINCE", "DIECISEIS", "DIECISIETE", "DIECIOCHO", "DIECINUEVE",
            "VEINTI", 30 => "TREINTA", 40 => "CUARENTA", 50 => "CINCUENTA", 60 => "SESENTA", 70 => "SETENTA", 80 => "OCHENTA", 90 => "NOVENTA",
            100 => "CIENTO", 200 => "DOSCIENTOS", 300 => "TRESCIENTOS", 400 => "CUATROCIENTOS", 500 => "QUINIENTOS", 600 => "SEISCIENTOS", 700 => "SETECIENTOS", 800 => "OCHOCIENTOS", 900 => "NOVECIENTOS"
        ];
        $amount = trim($amount);
        $xlength = strlen($amount);
        $xpos_punto = strpos($amount, ".");
        $xaux_int = $amount;
        $xdecimales = "00";
        $currency = strtoupper(self::getName($currency));
        if ($xpos_punto !== false) {
            if ($xpos_punto == 0) {
                $amount = "0" . $amount;
                $xpos_punto = strpos($amount, ".");
            }
            $xaux_int = substr($amount, 0, $xpos_punto);
            $xdecimales = substr($amount . "00", $xpos_punto + 1, 2);
        }

        $XAUX = str_pad($xaux_int, 18, " ", STR_PAD_LEFT);
        $xcadena = "";
        for ($xz = 0; $xz < 3; $xz++) {
            $xaux = substr($XAUX, $xz * 6, 6);
            $xi = 0;
            $xlimite = 6;
            $xexit = true;
            while ($xexit) {
                if ($xi == $xlimite) {
                    break;
                }

                $x3digitos = ($xlimite - $xi) * -1;
                $xaux = substr($xaux, $x3digitos, abs($x3digitos));
                for ($xy = 1; $xy < 4; $xy++) {
                    switch ($xy) {
                        case 1:
                            if (substr($xaux, 0, 3) < 100) {
                            } else {
                                $key = (int) substr($xaux, 0, 3);
                                if (array_key_exists($key, $xarray)) {
                                    $xseek = $xarray[$key];
                                    $xsub = self::suffix($xaux);
                                    if (substr($xaux, 0, 3) == 100)
                                        $xcadena = " " . $xcadena . " CIEN " . $xsub;
                                    else
                                        $xcadena = " " . $xcadena . " " . $xseek . " " . $xsub;
                                    $xy = 3;
                                } else {
                                    $key = (int) substr($xaux, 0, 1) * 100;
                                    $xseek = $xarray[$key];
                                    $xcadena = " " . $xcadena . " " . $xseek;
                                }
                            }
                            break;
                        case 2:
                            if (substr($xaux, 1, 2) < 10) {
                            } else {
                                $key = (int) substr($xaux, 1, 2);
                                if (array_key_exists($key, $xarray)) {
                                    $xseek = $xarray[$key];
                                    $xsub = self::suffix($xaux);
                                    if (substr($xaux, 1, 2) == 20)
                                        $xcadena = " " . $xcadena . " VEINTE " . $xsub;
                                    else
                                        $xcadena = " " . $xcadena . " " . $xseek . " " . $xsub;
                                    $xy = 3;
                                } else {
                                    $key = (int) substr($xaux, 1, 1) * 10;
                                    $xseek = $xarray[$key];
                                    if (20 == substr($xaux, 1, 1) * 10)
                                        $xcadena = " " . $xcadena . " " . $xseek;
                                    else
                                        $xcadena = " " . $xcadena . " " . $xseek . " Y ";
                                }
                            }
                            break;
                        case 3:
                            if (substr($xaux, 2, 1) < 1) {
                            } else {
                                $key = (int) substr($xaux, 2, 1);
                                $xseek = $xarray[$key];
                                $xsub = self::suffix($xaux);
                                $xcadena = " " . $xcadena . " " . $xseek . " " . $xsub;
                            }
                            break;
                    }
                }
                $xi = $xi + 3;
            }

            if (substr(trim($xcadena), -5, 5) == "ILLON")
                $xcadena .= " DE";

            if (substr(trim($xcadena), -7, 7) == "ILLONES")
                $xcadena .= " DE";

            if (trim($xaux) != "") {
                switch ($xz) {
                    case 0:
                        if (trim(substr($XAUX, $xz * 6, 6)) == "1")
                            $xcadena .= "UN BILLON ";
                        else
                            $xcadena .= " BILLONES ";
                        break;
                    case 1:
                        if (trim(substr($XAUX, $xz * 6, 6)) == "1")
                            $xcadena .= "UN MILLON ";
                        else
                            $xcadena .= " MILLONES ";
                        break;
                    case 2:
                        if ($amount < 1) {
                            $xcadena = "CERO CON $xdecimales/100 $currency";
                        }
                        if ($amount >= 1 && $amount < 2) {
                            $xcadena = "UN CON $xdecimales/100 $currency";
                        }
                        if ($amount >= 2) {
                            $xcadena .= " CON $xdecimales/100 $currency";
                        }
                        break;
                }
            }

            $xcadena = str_replace("VEINTI ", "VEINTI", $xcadena);
            $xcadena = str_replace("  ", " ", $xcadena);
            $xcadena = str_replace("UN UN", "UN", $xcadena);
            $xcadena = str_replace("  ", " ", $xcadena);
            $xcadena = str_replace("BILLON DE MILLONES", "BILLON DE", $xcadena);
            $xcadena = str_replace("BILLONES DE MILLONES", "BILLONES DE", $xcadena);
            $xcadena = str_replace("DE UN", "UN", $xcadena);
        }
        return trim($xcadena);
    }

    private static function suffix($xx) {
        $xx = trim($xx);
        $xstrlen = strlen($xx);
        if ($xstrlen == 1 || $xstrlen == 2 || $xstrlen == 3)
            $xsub = "";

        if ($xstrlen == 4 || $xstrlen == 5 || $xstrlen == 6)
            $xsub = "MIL";

        return $xsub;
    }

    public static function exchange($sourceAmount, $sourceCurrency, $exchangeRate, $fixed = 2) {
        switch ($sourceCurrency) {
            case self::PEN:
                return [
                    'pen' => $sourceAmount,
                    'usd' => round($sourceAmount / $exchangeRate, $fixed)
                ];

            case self::USD:
                return [
                    'pen' => round($sourceAmount * $exchangeRate, $fixed),
                    'usd' => $sourceAmount
                ];
            default:
                throw new \Exception('Moneda inválida');
        }
    }

    public static function isValidCash($amount) {
        $oneDecimal = floor($amount * 10) / 10;
        return $amount === $oneDecimal;
    }
}
