<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

/**
 * Class Payment Schedule
 *
 * @property int $payment_schedule_date_id
 * @property int $payment_schedule_detail_id
 * @property Carbon $date
 * @property float $amount
 * @property string $state
 * @property int|null $entry_group_id
 * @property int $movement_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property int $cashbox_id
 *
 * @package App\Models
 */
class PaymentScheduleDate extends Model {

    const STATE_OPEN = "OPEN";
    const STATE_CLOSED = "CLOSED";
    const STATE_CANCELED = "CANCELED";

    protected $table = 'payment_schedule_date';

    protected $primaryKey = 'payment_schedule_date_id';

    protected $fillable = [
        'payment_schedule_detail_id',
        'date',
        'amount',
        'state',
        'cashbox_id'
    ];

    public $timestamps = false;

    public function paymentScheduleDetail() {
        return $this->belongsTo(PaymentScheduleDetail::class, 'payment_schedule_detail_id');
    }

    public static function updateEntryGroup(){

        $affectedIds = DB::table('payment_schedule_detail as PSD')
        ->leftJoin('payment_schedule_date as PSDT', function ($join) {
            $join->on('PSD.payment_schedule_detail_id', '=', 'PSDT.payment_schedule_detail_id')
                 ->whereIn('PSDT.state', ['OPEN', 'CLOSED']);
        })
        ->groupBy('PSD.payment_schedule_detail_id')
        ->havingRaw('COUNT(PSDT.payment_schedule_date_id) = 0')
        ->pluck('PSD.payment_schedule_detail_id');

        DB::table('payment_schedule_detail')
            ->whereIn('payment_schedule_detail_id', $affectedIds)
            ->update(['entry_group_id' => null]);
        }

}
