<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use App\Models\FixedAsset as ModelsFixedAsset;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;

/**
 * Class FixedAssetMovement
 * 
 * @property int $fixed_asset_movement_id
 * @property int $fixed_asset_id
 * 
 * @property string $code
 * @property string $description
 * @property string $account_code
 * @property int|null $mark_id
 * @property string|null $model
 * @property string|null $serie
 * @property Carbon $buy_date
 * @property bool $status
 * @property float $historical_depreciation

 * @property Movement|null $movement
 * @property BusinessUnit $business_unit
 * @property Area|null $area
 * @property Person|null $responsible
 * @property FixedAsset $fixed_asset
 * @package App\Models
 */
class FixedAssetMovement extends Model
{
    use HasFactory, Notifiable;

    protected $table = 'fixed_asset_movement';
    protected $primaryKey = 'fixed_asset_movement_id';
    public $timestamps = false;

    const MOVEMENT_TYPE_OPENING = 'Opening';
    const MOVEMENT_TYPE_PURCHASE = 'Purchase';
    const MOVEMENT_TYPE_UPGRADE = 'Upgrade';
    const MOVEMENT_TYPE_LOW = 'Low';
    const MOVEMENT_TYPE_DEPRECIATION = 'Depreciation';
    const MOVEMENT_TYPE_ADJUSTMENT = 'Adjustment';
    const MOVEMENT_TYPE_TRANSFER = 'Transfer';

    const MOVEMENT_TYPE_OPENING_LABEL = 'Apertura';
    const MOVEMENT_TYPE_PURCHASE_LABEL = 'Compra';
    const MOVEMENT_TYPE_UPGRADE_LABEL = 'Mejora';
    const MOVEMENT_TYPE_LOW_LABEL = 'Baja';
    const MOVEMENT_TYPE_DEPRECIATION_LABEL = 'Depreciación';
    const MOVEMENT_TYPE_ADJUSTMENT_LABEL = 'Ajuste';
    const MOVEMENT_TYPE_TRANSFER_LABEL = 'Traspaso';

    protected $casts = [
        'fixed_asset_movement_id' => 'int',
        'fixed_asset_id' => 'int',
        'movement_date' => 'date',
        'movement_type' => 'string',
        'business_unit_id' => 'int',
        'area_id' => 'int',
        'responsible_id' => 'int',
        'movement_id' => 'int',
        'ubication' => 'string',
        'last' => 'int',
        'change_value' => 'int',
        'increase' => 'float',
        'decrease' => 'float',
        'historical_depreciation' => 'float',
        'historical_increase' => 'float',
        'historical_decrease' => 'float',
        'actual_value' => 'float',
        'observation' => 'string'
    ];

    protected $dates = [
        'movement_date',
    ];

    protected $fillable = [
        'fixed_asset_id',
        'movement_date',
        'movement_type',
        'business_unit_id',
        'area_id',
        'responsible_id',
        'movement_id',
        'ubication',
        'last',
        'change_value',
        'increase',
        'decrease',
        'historical_increase',
        'historical_decrease',
        'historical_depreciation',
        'actual_value',
        'observation'
    ];

    public function fixedAsset()
    {
        return $this->belongsTo(FixedAsset::class, 'fixed_asset_id', 'fixed_asset_id');
    }

    public function businessUnit()
    {
        return $this->belongsTo(BusinessUnit::class, 'business_unit_id', 'business_unit_id');
    }

    public function responsible()
    {
        return $this->belongsTo(Person::class, 'responsible_id', 'person_id');
    }

    public function area()
    {
        return $this->belongsTo(Area::class, 'area_id', 'area_id');
    }

    public function id()
    {
        return $this->fixed_asset_movement_id;
    }

    public static function getManualMovementTypeItems()
    {
        return [
            Self::MOVEMENT_TYPE_OPENING => [
                'label' => Self::MOVEMENT_TYPE_OPENING_LABEL,
                'changeValue' => 1
            ],
            Self::MOVEMENT_TYPE_TRANSFER => [
                'label' => Self::MOVEMENT_TYPE_TRANSFER_LABEL,
                'changeValue' => 0
            ]
        ];
    }

    public function getMovDateAttribute()
	{
		return $this->movement_date->format('d/m/Y');
	}

    public static function getMovementTypeItems()
    {
        return [
            Self::MOVEMENT_TYPE_OPENING => [
                'label' => Self::MOVEMENT_TYPE_OPENING_LABEL,
                'changeValue' => 1
            ],
            Self::MOVEMENT_TYPE_TRANSFER => [
                'label' => Self::MOVEMENT_TYPE_TRANSFER_LABEL,
                'changeValue' => 0
            ],
            Self::MOVEMENT_TYPE_DEPRECIATION => [
                'label' => Self::MOVEMENT_TYPE_DEPRECIATION_LABEL,
                'changeValue' => 1
            ],
            Self::MOVEMENT_TYPE_PURCHASE => [
                'label' => Self::MOVEMENT_TYPE_PURCHASE_LABEL,
                'changeValue' => 1
            ],
            Self::MOVEMENT_TYPE_UPGRADE => [
                'label' => Self::MOVEMENT_TYPE_UPGRADE_LABEL,
                'changeValue' => 1
            ],
            Self::MOVEMENT_TYPE_LOW => [
                'label' => Self::MOVEMENT_TYPE_LOW_LABEL,
                'changeValue' => 1
            ]
        ];
    }
}
