<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Transaction
 * 
 * @property int $transaction_id
 * @property string $detail
 * @property Carbon $register_date
 * 
 * @property Collection|TransactionDetail[] $transaction_details
 *
 * @package App\Models
 */
class Transaction extends Model
{
	protected $table = 'transaction';
	protected $primaryKey = 'transaction_id';
	public $timestamps = false;

	protected $dates = [
		'register_date'
	];

	protected $fillable = [
		'detail',
		'register_date'
	];

	public function transaction_details()
	{
		return $this->hasMany(TransactionDetail::class);
	}
}
