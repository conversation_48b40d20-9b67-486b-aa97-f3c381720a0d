<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class KardexItem
 * 
 * @property int $kardex_item_id
 * @property int $item_id
 * @property int $warehouse_id
 * @property string $direction
 * @property bool $priority
 * @property float $unit_cost
 * @property float $unit_net
 * 
 * @property Item $item
 * @property Warehouse $warehouse
 *
 * @package App\Models
 */
class KardexItem extends Model
{
	protected $table = 'kardex_item';
	protected $primaryKey = 'kardex_item_id';
	public $timestamps = false;

	protected $casts = [
		'item_id' => 'int',
		'warehouse_id' => 'int',
		'priority' => 'bool',
		'unit_cost' => 'float',
		'unit_net' => 'float'
	];

	protected $fillable = [
		'item_id',
		'warehouse_id',
		'direction',
		'priority',
		'unit_cost',
		'unit_net'
	];

	public function item()
	{
		return $this->belongsTo(Item::class);
	}

	public function warehouse()
	{
		return $this->belongsTo(Warehouse::class);
	}
}
