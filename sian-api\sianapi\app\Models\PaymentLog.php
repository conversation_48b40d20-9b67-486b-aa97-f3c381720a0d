<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PaymentLog
 * 
 * @property int $id
 * @property string|null $type
 * @property string|null $gateway
 * @property string|null $interface
 * @property string|null $action
 * @property bool|null $success
 * @property string|null $txn_id
 * @property Carbon|null $date
 * @property string|null $items
 * @property string|null $custom
 * @property string|null $sent_data
 * @property string|null $recieved_data
 * @property Carbon|null $created_date
 * @property int|null $userCreate
 * @property Carbon|null $creationDate
 * @property int|null $userLastChange
 * @property Carbon|null $lastChangeDate
 * @property float|null $amount
 * @property float|null $normalAmount
 * @property string|null $creditCardType
 * @property string|null $creditCardNumber
 * @property int|null $datecard
 * @property string|null $firstName
 * @property string|null $lastName
 * @property string|null $city
 * @property string|null $address
 * @property string|null $country
 * @property string|null $zip
 * @property string|null $email
 * @property string|null $phone
 * @property float|null $tc
 * @property string|null $paymentType
 * @property string|null $checkstatus
 * @property string|null $token
 * @property string|null $payerId
 * @property string|null $currencyCode
 * @property string|null $ip
 * @property string|null $error_message
 * @property bool|null $canceled
 *
 * @package App\Models
 */
class PaymentLog extends Model
{
	protected $table = 'payment_logs';
	public $timestamps = false;

	protected $casts = [
		'success' => 'bool',
		'userCreate' => 'int',
		'userLastChange' => 'int',
		'amount' => 'float',
		'normalAmount' => 'float',
		'datecard' => 'int',
		'tc' => 'float',
		'canceled' => 'bool'
	];

	protected $dates = [
		'date',
		'created_date',
		'creationDate',
		'lastChangeDate'
	];

	protected $hidden = [
		'token'
	];

	protected $fillable = [
		'type',
		'gateway',
		'interface',
		'action',
		'success',
		'txn_id',
		'date',
		'items',
		'custom',
		'sent_data',
		'recieved_data',
		'created_date',
		'userCreate',
		'creationDate',
		'userLastChange',
		'lastChangeDate',
		'amount',
		'normalAmount',
		'creditCardType',
		'creditCardNumber',
		'datecard',
		'firstName',
		'lastName',
		'city',
		'address',
		'country',
		'zip',
		'email',
		'phone',
		'tc',
		'paymentType',
		'checkstatus',
		'token',
		'payerId',
		'currencyCode',
		'ip',
		'error_message',
		'canceled'
	];
}
