<?php

namespace App\Http\Resources\Logistic;

use Illuminate\Http\Resources\Json\JsonResource;

class SimpleMarkResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->mark_id,
            'name' => $this->mark_name,
            'alias' => $this->alias
        ];
    }
}
