<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;


/**
 * Class Payment Schedule
 *
 * @property int $payment_schedule_id
 * @property string $description
 * @property Carbon $emission_date
 * @property int $person_id
 * @property Carbon $updated_at
 * @property Carbon $start_register_date
 *
 * @package App\Models
 */

class PaymentSchedule extends Model
{
    protected $table = 'payment_schedule';

    protected $primaryKey = 'payment_schedule_id';

    protected $fillable = [
        'description',
        'person_id',
        'start_register_date',
    ];

    protected $casts = [
        'start_register_date' => 'datetime',
    ];

    public $timestamps = true;
    const CREATED_AT = null;

    public function person()
    {
        return $this->belongsTo(Person::class, 'person_id');
    }
}
