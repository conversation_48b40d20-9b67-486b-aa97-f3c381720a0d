<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class BankAccount
 * 
 * @property string $owner
 * @property int $owner_id
 * @property int $entity_multi_id
 * @property string $type
 * @property string $currency
 * @property string $account_number
 * @property string|null $icc
 * @property int $order
 * 
 * @property Multitable $multitable
 *
 * @package App\Models
 */
class BankAccount extends Model
{
	protected $table = 'bank_account';
	public $incrementing = false;
	public $timestamps = false;

	const TYPE_DETRACTION = 'Detracción';

	protected $casts = [
		'owner_id' => 'int',
		'entity_multi_id' => 'int',
		'order' => 'int'
	];

	protected $fillable = [
		'entity_multi_id',
		'currency',
		'icc',
		'order'
	];

	public function multitable()
	{
		return $this->belongsTo(Multitable::class, 'entity_multi_id');
	}

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}
}
