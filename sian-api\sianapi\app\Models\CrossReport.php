<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class CrossReport
 * 
 * @property int $action_id
 * @property string|null $column_owner
 * @property string|null $row_owner
 * @property string|null $column_sub_owner
 * @property string|null $row_sub_owner
 * @property int $owner_count
 * 
 * @property Action $action
 *
 * @package App\Models
 */
class CrossReport extends Model
{
	protected $table = 'cross_report';
	protected $primaryKey = 'action_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'action_id' => 'int',
		'owner_count' => 'int'
	];

	protected $fillable = [
		'column_owner',
		'row_owner',
		'column_sub_owner',
		'row_sub_owner',
		'owner_count'
	];

	public function action()
	{
		return $this->belongsTo(Action::class, 'action_id', 'action_id');
	}
}
