<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Entry
 * 
 * @property int $entry_id
 * @property int $movement_id
 * @property int $entry_number
 * @property string $column
 * @property string $account_code
 * @property string $owner
 * @property int $owner_id
 * @property int|null $reference_movement_id
 * @property int $fee_number
 * @property bool $status
 * @property string|null $detail
 * @property float $exchange_rate
 * @property Carbon $expiration_date
 * @property float $amount_pen
 * @property float $amount_usd
 * @property float $ibalance_pen
 * @property float $ibalance_usd
 * @property float $balance_pen
 * @property float $balance_usd
 * @property float $ret_balance_pen
 * @property float $ret_balance_usd
 * @property bool $scheduled
 * @property int|null $scheduled_entry_id
 * @property Carbon|null $scheduled_date
 * @property string|null $scheduled_currency
 * @property float|null $scheduled_amount_pen
 * @property float|null $scheduled_amount_usd
 * @property string|null $dynamic_account
 * @property string $account_group
 * @property int $days_overdue
 * @property bool $expired
 * @property int $entry_link_count
 * @property string|null $field
 * @property bool $destiny
 * @property bool $to_collect
 * @property bool $to_pay
 * @property bool $to_do
 * @property int $order
 * @property bool $is_paid
 * @property bool $is_payable
 * @property bool $is_redeemable
 * @property bool $is_applicable
 * @property bool $is_schedulable
 * @property bool $has_parent
 * @property int|null $combination_id
 * 
 * @property Account|null $account
 * @property AccountingMovement $accounting_movement
 * @property Combination|null $combination
 * @property Movement|null $movement
 * @property ScheduledEntry|null $scheduled_entry
 * @property Collection|EntryLink[] $entry_links
 * @property Collection|ScheduledEntry[] $scheduled_entries
 *
 * @package App\Models
 */
class Entry extends Model
{
	protected $table = 'entry';
	protected $primaryKey = 'entry_id';
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'entry_number' => 'int',
		'owner_id' => 'int',
		'reference_movement_id' => 'int',
		'fee_number' => 'int',
		'status' => 'bool',
		'exchange_rate' => 'float',
		'amount_pen' => 'float',
		'amount_usd' => 'float',
		'ibalance_pen' => 'float',
		'ibalance_usd' => 'float',
		'balance_pen' => 'float',
		'balance_usd' => 'float',
		'ret_balance_pen' => 'float',
		'ret_balance_usd' => 'float',
		'scheduled' => 'bool',
		'scheduled_entry_id' => 'int',
		'scheduled_amount_pen' => 'float',
		'scheduled_amount_usd' => 'float',
		'days_overdue' => 'int',
		'expired' => 'bool',
		'entry_link_count' => 'int',
		'destiny' => 'bool',
		'to_collect' => 'bool',
		'to_pay' => 'bool',
		'to_do' => 'bool',
		'order' => 'int',
		'is_paid' => 'bool',
		'is_payable' => 'bool',
		'is_redeemable' => 'bool',
		'is_applicable' => 'bool',
		'is_schedulable' => 'bool',
		'has_parent' => 'bool',
		'combination_id' => 'int'
	];

	protected $dates = [
		'expiration_date',
		'scheduled_date'
	];

	protected $fillable = [
		'movement_id',
		'entry_number',
		'column',
		'account_code',
		'owner',
		'owner_id',
		'reference_movement_id',
		'fee_number',
		'status',
		'detail',
		'exchange_rate',
		'expiration_date',
		'amount_pen',
		'amount_usd',
		'ibalance_pen',
		'ibalance_usd',
		'balance_pen',
		'balance_usd',
		'ret_balance_pen',
		'ret_balance_usd',
		'scheduled',
		'scheduled_entry_id',
		'scheduled_date',
		'scheduled_currency',
		'scheduled_amount_pen',
		'scheduled_amount_usd',
		'dynamic_account',
		'account_group',
		'days_overdue',
		'expired',
		'entry_link_count',
		'field',
		'destiny',
		'to_collect',
		'to_pay',
		'to_do',
		'order',
		'is_paid',
		'is_payable',
		'is_redeemable',
		'is_applicable',
		'is_schedulable',
		'has_parent',
		'combination_id'
	];

	public function account()
	{
		return $this->belongsTo(Account::class, 'dynamic_account');
	}

	public function accounting_movement()
	{
		return $this->belongsTo(AccountingMovement::class, 'movement_id');
	}

	public function combination()
	{
		return $this->belongsTo(Combination::class);
	}

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class, 'reference_movement_id');
	}

	public function scheduled_entry()
	{
		return $this->belongsTo(ScheduledEntry::class);
	}

	public function entry_links()
	{
		return $this->hasMany(EntryLink::class, 'entry_parent_id');
	}

	public function scheduled_entries()
	{
		return $this->hasMany(ScheduledEntry::class, 'entry_parent_id');
	}
}
