<?php

namespace App\Http\Controllers\Api\V1\Subline;

use App\Models\Division;
use App\Models\Line;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Models\Subline;
use Illuminate\Support\Facades\Log;


class SublineController extends Controller {
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getItems(Request $request) {
        try {

            $divisionFoodId = Division::where('status', 1)
                ->where('division_name', '=', Division::DIVISION_FOOD_NAME)
                ->value('division_id');

            $lineServicesId = Line::where('status', 1)
                ->where('line_name', '=', Line::LINE_NAME_SERVICES)
                ->value('line_id');


            $query = Subline::where('subline.status', 1)
                ->join('line', 'subline.line_id', '=', 'line.line_id')
                ->where('line.line_id', '!=', $lineServicesId);

            if ($request->input('filter') && $request->input('filter') === Division::DIVISION_FOOD_NAME) {
                $query->where('line.division_id', '=', $divisionFoodId);
            } else {
                $query->where('line.division_id', '!=', $divisionFoodId);
            }

            if ($request->has('keyword')) {
                $keyword = $request->input('keyword');
                $query->where('subline.subline_name', 'like', '%' . $keyword . '%');
            }

            if ($request->has('line')) {
                $line = $request->input('line');
                $arrayLine = explode(",", $line);
                $query->whereIn("subline.line_id", $arrayLine);
            }

            if ($request->has('subline')) {
                $sublineSelected = $request->input('subline');
                $arraySubline = explode(",", $sublineSelected);
                $query->whereNotIn("subline.subline_id", $arraySubline);
            }

            if ($request->has('limit')) {
                $limit = $request->input('limit');
                $query->limit($limit);
            } else {
                $query->limit(10);
            }


            $data = $query->get();

            return response()->json([
                'success' => true,
                'data' => $data,

            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }
}
