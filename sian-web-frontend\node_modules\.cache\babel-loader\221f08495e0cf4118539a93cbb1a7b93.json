{"ast": null, "code": "var _jsxFileName = \"D:\\\\www\\\\SWF\\\\sian-web-frontend\\\\src\\\\views\\\\commercial\\\\salesDashboard\\\\components\\\\StoresTab.jsx\",\n    _s = $RefreshSig$();\n\n/* eslint-disable */\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Box, Button, ButtonGroup, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography, ToggleButton, ToggleButtonGroup, Menu, MenuItem, TextField, IconButton, Autocomplete, CircularProgress, Backdrop, Tooltip as MuiTooltip, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';\nimport { Pie<PERSON>hart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Tooltip, Legend } from 'recharts';\nimport axios from 'axios';\nimport { getSalesDashboardPromise, getSalesDashboardGoalsPromise } from 'services/salesDashboard';\nimport { ParticipacionesSection } from './stores/ParticipacionesSection';\nimport { ParticipacionesTitle } from './stores/ParticipacionesTitle';\nimport { StyledTableCell } from './stores/StyledTableCell';\nimport { StyledTableRow } from './stores/StyledTableRow';\nimport { TotalRow } from './stores/TotalRow';\nimport { DivisionRow } from './stores/DivisionRow';\nimport { LineDetailsTable } from './stores/LineDetailsTable';\nimport { ChartsContainer } from './stores/ChartsContainer';\nimport { ChartContainer } from './stores/ChartContainer';\nimport { FiltersWrapper } from './stores/FiltersWrapper';\nimport { ChartTitle } from './stores/ChartTitle';\nimport { FilterGroup } from './stores/FilterGroup';\nimport { FilterChip } from './stores/FilterChip';\nimport { CompactDatePicker } from './stores/CompactDatePicker';\nimport { TimeSelect } from './stores/TimeSelect';\nimport { StoreSelect } from './stores/StoreSelect';\nimport { HeatMapLegend } from './stores/HeatMapLegend';\nimport { getHeatMapColor, getHeatMapColors } from './stores/functions';\nimport { COLORS } from './stores/colors';\nimport { LocalizationProvider } from '@mui/x-date-pickers';\nimport { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\nimport { es } from 'date-fns/locale';\nimport FilterListIcon from '@mui/icons-material/FilterList';\nimport DownloadIcon from '@mui/icons-material/Download';\nimport ExpandMoreIcon from '@mui/icons-material/ExpandMore';\nimport UnfoldMoreIcon from '@mui/icons-material/UnfoldMore';\nimport UnfoldLessIcon from '@mui/icons-material/UnfoldLess';\nimport * as XLSX from 'xlsx';\nimport AIChat from './AIChat';\nimport { format } from 'date-fns';\nimport CalendarViewDayIcon from '@mui/icons-material/CalendarViewDay';\nimport CalendarViewWeekIcon from '@mui/icons-material/CalendarViewWeek';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { createElement as _createElement } from \"react\";\n\nconst StoresTab = () => {\n  _s();\n\n  const [salesData, setSalesData] = useState([]);\n  const [goalsData, setGoalsData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedStore, setSelectedStore] = useState('all');\n  const [selectedLineNames, setSelectedLineNames] = useState([]);\n  const [selectedRealLineNames, setSelectedRealLineNames] = useState([]);\n  const [dateRange, setDateRange] = useState([null, null]);\n  const [startTime, setStartTime] = useState('00:00');\n  const [endTime, setEndTime] = useState('23:59');\n  const [availableDates, setAvailableDates] = useState([]);\n  const [apiMode, setApiMode] = useState(0);\n  const [apiModeGoals, setApiModeGoals] = useState(0);\n  const [viewMode, setViewMode] = useState('daily');\n  const [filterAnchorEl, setFilterAnchorEl] = useState(null);\n  const [availableLineNames, setAvailableLineNames] = useState([]);\n  const [availableRealLineNames, setAvailableRealLineNames] = useState([]); // Estados para acordeones\n\n  const [expandedDivisions, setExpandedDivisions] = useState({});\n  const [expandedLines, setExpandedLines] = useState({}); // Referencia para almacenar la estructura de datos procesada sin recalcular\n\n  const processedDataRef = useRef(null);\n  const hourChartRef = useRef(null);\n  const divisionChartRef = useRef(null);\n  const storeChartRef = useRef(null);\n  const dayChartRef = useRef(null);\n  useEffect(() => {\n    const fetchGoalsData = async () => {\n      setLoading(true);\n\n      try {\n        let response;\n\n        if (apiModeGoals === 0) {\n          response = await getSalesDashboardGoalsPromise();\n          setGoalsData(response.data);\n        }\n      } catch (error) {\n        /* eslint-disable */\n        console.error(...oo_tx(`300546921_104_16_104_66_11`, 'Error fetching goals data:', error));\n\n        if (apiModeGoals === 0) {\n          setApiModeGoals(1);\n        }\n      }\n    };\n\n    fetchGoalsData();\n  }, [apiModeGoals]);\n  useEffect(() => {\n    const fetchSalesData = async () => {\n      try {\n        let response;\n        let arrayData = [];\n\n        if (apiMode === 0) {\n          response = await getSalesDashboardPromise();\n          /* eslint-disable */\n\n          console.log(...oo_oo(`300546921_121_20_121_81_4`, 'Sales data from Api Enviroment:', response.data));\n          arrayData = response.data;\n          setSalesData(response.data);\n        } else {\n          response = await axios.get('https://supermercadosmia.siansystem.com/admin/apiSian/pbi/sales');\n          /* eslint-disable */\n\n          console.log(...oo_oo(`300546921_126_20_126_76_4`, 'Sales data from Prod:', response.data.data));\n          arrayData = response.data.data;\n          setSalesData(response.data.data);\n        } // Procesar fechas únicas\n\n\n        const dates = [...new Set(arrayData.map(sale => sale.emission_date))].sort();\n        setAvailableDates(dates); // Procesar divisiones únicas\n\n        const lines = [...new Set(arrayData.map(sale => sale.division_name))].sort();\n        setAvailableLineNames(lines); // Procesar líneas reales únicas\n\n        const realLines = [...new Set(arrayData.map(sale => sale.line_name))].sort();\n        setAvailableRealLineNames(realLines); // Establecer fecha inicial y final\n\n        const today = new Date();\n        today.setHours(12, 0, 0, 0);\n        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\n        firstDayOfMonth.setHours(12, 0, 0, 0);\n        setDateRange([firstDayOfMonth, today]);\n      } catch (error) {\n        /* eslint-disable */\n        console.error(...oo_tx(`300546921_149_16_149_66_11`, 'Error fetching sales data:', error));\n\n        if (apiMode === 0) {\n          setApiMode(1);\n        }\n      } finally {\n        setTimeout(() => {\n          setLoading(false);\n        }, 500);\n      }\n    };\n\n    if (goalsData.length > 0) {\n      fetchSalesData();\n    }\n  }, [apiMode, goalsData]);\n\n  const handleFilterClick = event => {\n    setFilterAnchorEl(event.currentTarget);\n  };\n\n  const handleFilterClose = () => {\n    setFilterAnchorEl(null);\n  };\n\n  const handleDateRangePreset = preset => {\n    const today = new Date();\n    let start = new Date();\n    let end = new Date();\n\n    switch (preset) {\n      case 'today':\n        start = today;\n        end = today;\n        break;\n\n      case 'yesterday':\n        start.setDate(today.getDate() - 1);\n        end = start;\n        break;\n\n      case 'last7':\n        start.setDate(today.getDate() - 7);\n        end = today;\n        break;\n\n      case 'last30':\n        start.setDate(today.getDate() - 30);\n        end = today;\n        break;\n\n      default:\n        break;\n    }\n\n    setDateRange([start, end]);\n    handleFilterClose();\n  };\n\n  const handleTimeChange = (type, event) => {\n    const newValue = event.target.value;\n\n    if (type === 'start') {\n      setStartTime(newValue);\n    } else {\n      setEndTime(newValue);\n    } // Convertir la hora para el filtrado\n\n\n    const [hours, minutes] = newValue.split(':').map(Number);\n    const timeValue = hours + minutes / 60; // Actualizar el filtrado aquí\n  };\n\n  const handleLineNameChange = (event, newValue) => {\n    setSelectedLineNames(newValue);\n  };\n\n  const handleRealLineNameChange = (event, newValue) => {\n    setSelectedRealLineNames(newValue);\n  };\n\n  const getFilteredData = () => {\n    if (!dateRange[0] || !dateRange[1]) return [];\n    return salesData.filter(sale => {\n      // Convertir la fecha de emisión a fecha local\n      const saleDate = new Date(sale.emission_date + 'T00:00:00');\n      const saleHour = new Date(sale.register_date).getHours(); // Crear fechas de inicio y fin del día\n\n      const startDate = new Date(dateRange[0]);\n      startDate.setHours(0, 0, 0, 0);\n      const endDate = new Date(dateRange[1]);\n      endDate.setHours(23, 59, 59, 999);\n      const storeMatch = selectedStore === 'all' || sale.store_name === selectedStore;\n      const lineMatch = selectedLineNames.length === 0 || selectedLineNames.includes(sale.division_name);\n      const realLineMatch = selectedRealLineNames.length === 0 || selectedRealLineNames.includes(sale.line_name);\n      const timeMatch = saleHour >= parseInt(startTime.split(':')[0]) && saleHour <= parseInt(endTime.split(':')[0]);\n      const dateMatch = saleDate >= startDate && saleDate <= endDate;\n      return storeMatch && dateMatch && timeMatch && lineMatch && realLineMatch;\n    });\n  };\n\n  const getFilteredDataWithoutStore = () => {\n    if (!dateRange[0] || !dateRange[1]) return [];\n    return salesData.filter(sale => {\n      // Convertir la fecha de emisión a fecha local\n      const saleDate = new Date(sale.emission_date + 'T00:00:00');\n      const saleHour = new Date(sale.register_date).getHours(); // Crear fechas de inicio y fin del día\n\n      const startDate = new Date(dateRange[0]);\n      startDate.setHours(0, 0, 0, 0);\n      const endDate = new Date(dateRange[1]);\n      endDate.setHours(23, 59, 59, 999);\n      const lineMatch = selectedLineNames.length === 0 || selectedLineNames.includes(sale.division_name);\n      const realLineMatch = selectedRealLineNames.length === 0 || selectedRealLineNames.includes(sale.line_name);\n      const timeMatch = saleHour >= parseInt(startTime.split(':')[0]) && saleHour <= parseInt(endTime.split(':')[0]);\n      const dateMatch = saleDate >= startDate && saleDate <= endDate;\n      return dateMatch && timeMatch && lineMatch && realLineMatch;\n    });\n  }; // Función auxiliar para obtener el número de semana\n\n\n  const getWeekNumber = date => {\n    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);\n    const pastDaysOfYear = (date - firstDayOfYear) / 86400000;\n    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);\n  };\n\n  const processData = () => {\n    const filteredData = getFilteredData();\n    const allFilteredData = getFilteredDataWithoutStore();\n    const salesByStore = {};\n    const allSalesByStore = {};\n    const salesByLine = {};\n    const salesByRealLine = {};\n    const salesByHour = {};\n    const salesByDay = {};\n    const salesByWeek = {}; // Nuevo objeto para mapear ventas por tienda y línea\n\n    const salesByStoreAndLine = {};\n    let totalSales = 0; // Obtener la fecha actual en formato YYYY-MM-DD\n\n    const today = new Date().toISOString().split('T')[0]; // Función para obtener número de semana ISO\n\n    const getISOWeekNumber = date => {\n      const d = new Date(date);\n      d.setHours(0, 0, 0, 0); // Jueves de la semana actual\n\n      d.setDate(d.getDate() + 4 - (d.getDay() || 7)); // Primer día del año\n\n      const yearStart = new Date(d.getFullYear(), 0, 1); // Calcular semana ISO\n\n      return Math.ceil(((d - yearStart) / 86400000 + 1) / 7);\n    }; // Procesar datos filtrados para las métricas principales\n\n\n    filteredData.forEach(sale => {\n      const saleDate = sale.emission_date;\n      const saleTotal = parseFloat(sale.total);\n      totalSales += saleTotal; // Procesar ventas por línea\n\n      if (!salesByLine[sale.division_name]) {\n        salesByLine[sale.division_name] = {\n          total: 0,\n          realLines: {}\n        };\n      }\n\n      salesByLine[sale.division_name].total += saleTotal;\n\n      if (!salesByLine[sale.division_name].realLines[sale.line_name]) {\n        salesByLine[sale.division_name].realLines[sale.line_name] = {\n          total: 0,\n          sublines: {}\n        };\n      }\n\n      salesByLine[sale.division_name].realLines[sale.line_name].total += saleTotal; // Agregar sublineas\n\n      if (!salesByLine[sale.division_name].realLines[sale.line_name].sublines[sale.subline_name]) {\n        salesByLine[sale.division_name].realLines[sale.line_name].sublines[sale.subline_name] = 0;\n      }\n\n      salesByLine[sale.division_name].realLines[sale.line_name].sublines[sale.subline_name] += saleTotal; // Crear estructura para ventas por tienda y línea\n\n      if (!salesByStoreAndLine[sale.store_name]) {\n        salesByStoreAndLine[sale.store_name] = {};\n      }\n\n      if (!salesByStoreAndLine[sale.store_name][sale.division_name]) {\n        salesByStoreAndLine[sale.store_name][sale.division_name] = 0;\n      }\n\n      salesByStoreAndLine[sale.store_name][sale.division_name] += saleTotal;\n\n      if (!salesByStore[sale.store_name]) {\n        salesByStore[sale.store_name] = {\n          name: sale.store_name,\n          total: 0,\n          todaySales: 0\n        };\n      }\n\n      salesByStore[sale.store_name].total += saleTotal;\n\n      if (saleDate === today) {\n        salesByStore[sale.store_name].todaySales += saleTotal;\n      }\n\n      const hour = new Date(sale.register_date).getHours();\n      const hourKey = `${hour.toString().padStart(2, '0')}:00`;\n\n      if (!salesByHour[hourKey]) {\n        salesByHour[hourKey] = 0;\n      }\n\n      salesByHour[hourKey] += saleTotal; // Agregar ventas por día - Normalizamos el formato de fecha\n\n      const formattedDate = saleDate.split('T')[0]; // Aseguramos formato YYYY-MM-DD\n\n      if (!salesByDay[formattedDate]) {\n        salesByDay[formattedDate] = 0;\n      }\n\n      salesByDay[formattedDate] += saleTotal; // Calcular la semana de la venta usando el estándar ISO 8601\n\n      const dateParts = formattedDate.split('-');\n      const year = parseInt(dateParts[0]);\n      const month = parseInt(dateParts[1]) - 1; // Meses en JS son 0-11\n\n      const day = parseInt(dateParts[2]); // Crear fecha con hora fija para evitar problemas de zona horaria\n\n      const date = new Date(year, month, day, 12, 0, 0);\n      const weekNumber = getISOWeekNumber(date);\n      const yearWeek = `${year}-W${weekNumber.toString().padStart(2, '0')}`;\n\n      if (!salesByWeek[yearWeek]) {\n        salesByWeek[yearWeek] = 0;\n      }\n\n      salesByWeek[yearWeek] += saleTotal;\n    }); // Procesar datos sin filtro de tienda para el gráfico\n\n    allFilteredData.forEach(sale => {\n      const saleDate = sale.emission_date;\n      const saleTotal = parseFloat(sale.total);\n\n      if (!allSalesByStore[sale.store_name]) {\n        allSalesByStore[sale.store_name] = {\n          name: sale.store_name,\n          total: 0,\n          todaySales: 0\n        };\n      }\n\n      allSalesByStore[sale.store_name].total += saleTotal;\n\n      if (saleDate === today) {\n        allSalesByStore[sale.store_name].todaySales += saleTotal;\n      }\n    }); // Convertir y ordenar las Línea\n\n    const sortedLines = Object.entries(salesByLine).map(_ref => {\n      let [name, data] = _ref;\n      return {\n        name,\n        value: data.total,\n        percentage: data.total / totalSales * 100,\n        realLines: Object.entries(data.realLines).map(_ref2 => {\n          let [realName, lineData] = _ref2;\n          return {\n            name: realName,\n            value: lineData.total,\n            percentage: lineData.total / totalSales * 100,\n            sublines: Object.entries(lineData.sublines).map(_ref3 => {\n              let [sublineName, value] = _ref3;\n              return {\n                name: sublineName,\n                value,\n                percentage: value / totalSales * 100\n              };\n            }).sort((a, b) => b.value - a.value)\n          };\n        }).sort((a, b) => b.value - a.value)\n      };\n    }).sort((a, b) => b.value - a.value); // Crear estructura detallada de tiendas con sus ventas por línea\n\n    const storesArray = Object.values(salesByStore).map(store => {\n      const storeGoals = goalsData.find(g => g.store_name === store.name) || {};\n      const storeData = {\n        name: store.name,\n        total: store.total,\n        todaySales: store.todaySales,\n        monthlyGoal: parseFloat(storeGoals.period_goal),\n        dailyGoal: parseFloat(storeGoals.today_goal),\n        monthlyProgress: (store.total / parseFloat(storeGoals.period_goal) * 100).toFixed(2),\n        dailyProgress: (store.todaySales / parseFloat(storeGoals.today_goal) * 100).toFixed(2)\n      }; // Agregar ventas por línea para esta tienda\n\n      const storeLineData = salesByStoreAndLine[store.name] || {}; // Agregar propiedades por cada línea directamente al objeto tienda\n\n      sortedLines.forEach(lineInfo => {\n        const lineName = lineInfo.name;\n        storeData[lineName] = storeLineData[lineName] || 0;\n      });\n      return storeData;\n    });\n    const allStoresArray = Object.values(allSalesByStore).map(store => {\n      const storeGoals = goalsData.find(g => g.store_name === store.name) || {};\n      return {\n        name: store.name,\n        total: store.total,\n        todaySales: store.todaySales,\n        monthlyGoal: parseFloat(storeGoals.period_goal),\n        dailyGoal: parseFloat(storeGoals.today_goal),\n        monthlyProgress: (store.total / parseFloat(storeGoals.period_goal) * 100).toFixed(2),\n        dailyProgress: (store.todaySales / parseFloat(storeGoals.today_goal) * 100).toFixed(2)\n      };\n    }); // Crear estructura para ventas totales por línea\n\n    const linesSummary = Object.keys(salesByLine).map(lineName => {\n      let total = 0;\n      Object.values(salesByStoreAndLine).forEach(storeLines => {\n        total += storeLines[lineName] || 0;\n      });\n      return {\n        name: lineName,\n        value: total\n      };\n    });\n    return {\n      stores: storesArray,\n      allStores: allStoresArray,\n      lines: sortedLines.slice(0, 4),\n      allLines: sortedLines,\n      linesByStore: salesByStoreAndLine,\n      linesSummary: linesSummary,\n      hours: Object.entries(salesByHour).map(_ref4 => {\n        let [hour, value] = _ref4;\n        return {\n          hour,\n          value,\n          formattedValue: formatCurrency(value)\n        };\n      }).sort((a, b) => {\n        const hourA = parseInt(a.hour.split(':')[0]);\n        const hourB = parseInt(a.hour.split(':')[0]);\n        return hourA - hourB;\n      }),\n      days: Object.entries(salesByDay).map(_ref5 => {\n        let [day, value] = _ref5;\n        return {\n          day,\n          value,\n          formattedValue: formatCurrency(value)\n        };\n      }).sort((a, b) => {\n        // Ordenar por fecha usando componentes individuales para evitar problemas de timezone\n        const [yearA, monthA, dayA] = a.day.split('-').map(num => parseInt(num));\n        const [yearB, monthB, dayB] = b.day.split('-').map(num => parseInt(num));\n        if (yearA !== yearB) return yearA - yearB;\n        if (monthA !== monthB) return monthA - monthB;\n        return dayA - dayB;\n      }),\n      weeks: Object.entries(salesByWeek).map(_ref6 => {\n        let [yearWeek, value] = _ref6;\n        const [year, weekPart] = yearWeek.split('-W');\n        const weekNum = parseInt(weekPart); // Calculamos la fecha del primer día de la semana (lunes)\n\n        const firstDayOfWeek = new Date(parseInt(year), 0, 1); // Ajustamos al lunes\n\n        const dayOfWeek = firstDayOfWeek.getDay() || 7;\n        firstDayOfWeek.setDate(firstDayOfWeek.getDate() + (dayOfWeek <= 4 ? 1 - dayOfWeek : 8 - dayOfWeek)); // Calculamos el último día (domingo)\n\n        const lastDayOfWeek = new Date(firstDayOfWeek);\n        lastDayOfWeek.setDate(firstDayOfWeek.getDate() + 6); // Formatos de fecha\n\n        const formatDate = d => {\n          return d.toISOString().split('T')[0];\n        };\n\n        return {\n          week: weekNum.toString(),\n          year: year,\n          yearWeek: yearWeek,\n          value,\n          firstDay: formatDate(firstDayOfWeek),\n          lastDay: formatDate(lastDayOfWeek),\n          formattedValue: formatCurrency(value)\n        };\n      }).sort((a, b) => {\n        if (a.year !== b.year) return parseInt(a.year) - parseInt(b.year);\n        return parseInt(a.week) - parseInt(b.week);\n      })\n    };\n  };\n\n  const getProcessedData = () => {\n    if (!processedDataRef.current) {\n      processedDataRef.current = processData();\n    }\n\n    return processedDataRef.current;\n  }; // Función optimizada para detectar cambios que requieren recalcular los datos\n\n\n  useEffect(() => {\n    // Cuando cambian los datos o filtros, invalidamos la caché\n    processedDataRef.current = null;\n  }, [salesData, selectedStore, selectedLineNames, selectedRealLineNames, dateRange, startTime, endTime]);\n\n  const formatCurrency = value => {\n    return new Intl.NumberFormat('es-PE', {\n      style: 'currency',\n      currency: 'PEN',\n      minimumFractionDigits: 2\n    }).format(value);\n  };\n\n  const formatDate = dateStr => {\n    const date = new Date(dateStr + 'T00:00:00');\n    const day = String(date.getDate()).padStart(2, '0');\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    return `${day}/${month}`;\n  };\n\n  const CustomTooltip = _ref7 => {\n    let {\n      active,\n      payload,\n      label\n    } = _ref7;\n\n    if (active && payload && payload.length) {\n      var _payload$0$payload$pe;\n\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          backgroundColor: 'white',\n          padding: '10px',\n          border: '1px solid #ccc',\n          borderRadius: '4px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          children: payload[0].name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          children: formatCurrency(payload[0].value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"primary\",\n          children: [(_payload$0$payload$pe = payload[0].payload.percentage) === null || _payload$0$payload$pe === void 0 ? void 0 : _payload$0$payload$pe.toFixed(2), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 596,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 584,\n        columnNumber: 17\n      }, this);\n    }\n\n    return null;\n  };\n\n  const downloadChart = (chartRef, title) => {\n    if (chartRef.current) {\n      const svgElement = chartRef.current.container.children[0];\n      const svgData = new XMLSerializer().serializeToString(svgElement);\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image(); // Configurar el tamaño del canvas basado en el SVG\n\n      const boundingBox = svgElement.getBoundingClientRect();\n      canvas.width = boundingBox.width;\n      canvas.height = boundingBox.height;\n\n      img.onload = () => {\n        // Fondo blanco\n        ctx.fillStyle = '#FFFFFF';\n        ctx.fillRect(0, 0, canvas.width, canvas.height); // Dibujar el gráfico\n\n        ctx.drawImage(img, 0, 0); // Convertir a PNG y descargar\n\n        const pngFile = canvas.toDataURL('image/png');\n        const downloadLink = document.createElement('a');\n        downloadLink.download = `${title.replace(/\\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.png`;\n        downloadLink.href = pngFile;\n        downloadLink.click();\n      };\n\n      img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));\n    }\n  };\n\n  const exportLinesSummaryToExcel = () => {\n    if (!getProcessedData().stores || !getProcessedData().allLines) return;\n    const workbook = XLSX.utils.book_new(); // Preparar los encabezados\n\n    const headers = ['Tiendas', ...getProcessedData().allLines.map(line => line.name), 'Total']; // Preparar los datos de las tiendas\n\n    const storeData = getProcessedData().stores.map(store => {\n      const row = {\n        Tiendas: store.name\n      }; // Añadir cada valor de línea para esta tienda\n\n      getProcessedData().allLines.forEach(line => {\n        row[line.name] = store[line.name] || 0;\n      }); // Añadir el total de la tienda\n\n      row['Total'] = store.total || 0;\n      return row;\n    }); // Añadir fila de totales\n\n    const totalRow = {\n      Tiendas: 'Total General'\n    }; // Añadir totales por línea\n\n    getProcessedData().allLines.forEach(line => {\n      totalRow[line.name] = line.value || 0;\n    }); // Añadir total general\n\n    totalRow['Total'] = getProcessedData().stores.reduce((sum, store) => sum + (store.total || 0), 0); // Combinar los datos\n\n    const exportData = [...storeData, totalRow]; // Crear hoja de trabajo\n\n    const worksheet = XLSX.utils.json_to_sheet(exportData, {\n      header: headers\n    }); // Formatear las celdas como moneda y ajustar el ancho de las columnas\n\n    const range = XLSX.utils.decode_range(worksheet['!ref']); // Establecer anchos de columna para asegurar que los valores sean visibles\n\n    const columnWidths = [];\n\n    for (let i = 0; i <= range.e.c; i++) {\n      columnWidths[i] = {\n        width: 15\n      }; // Ancho estándar para todas las columnas\n    }\n\n    worksheet['!cols'] = columnWidths; // Aplicar formato de moneda\n\n    for (let C = 1; C <= range.e.c; C++) {\n      for (let R = 1; R <= range.e.r + 1; R++) {\n        const cellAddress = XLSX.utils.encode_cell({\n          r: R,\n          c: C\n        });\n\n        if (worksheet[cellAddress] && typeof worksheet[cellAddress].v === 'number') {\n          worksheet[cellAddress].z = '\"S/\"#,##0.00';\n        }\n      }\n    }\n\n    XLSX.utils.book_append_sheet(workbook, worksheet, 'Ventas_Por_Tienda_Linea');\n    XLSX.writeFile(workbook, 'ventas_por_tienda_linea.xlsx');\n  };\n\n  const exportLinesTableToExcel = () => {\n    const data = [];\n    getProcessedData().allLines.forEach(line => {\n      // Add division row\n      data.push({\n        División: line.name,\n        Línea: '',\n        Sublínea: '',\n        'Total S/.': line.value,\n        Porcentaje: `${line.percentage.toFixed(2)}%`\n      }); // Add real lines with their sublines\n\n      line.realLines.forEach(realLine => {\n        // Add line row\n        data.push({\n          División: line.name,\n          Línea: realLine.name,\n          Sublínea: '',\n          'Total S/.': realLine.value,\n          Porcentaje: `${realLine.percentage.toFixed(2)}%`\n        }); // Add sublines\n\n        realLine.sublines.forEach(subline => {\n          data.push({\n            División: line.name,\n            Línea: realLine.name,\n            Sublínea: subline.name,\n            'Total S/.': subline.value,\n            Porcentaje: `${subline.percentage.toFixed(2)}%`\n          });\n        });\n      }); // Add division total\n\n      data.push({\n        División: `Total ${line.name}`,\n        Línea: '',\n        Sublínea: '',\n        'Total S/.': line.value,\n        Porcentaje: `${line.percentage.toFixed(2)}%`\n      });\n    }); // Add grand total\n\n    data.push({\n      División: 'Total General',\n      Línea: '',\n      Sublínea: '',\n      'Total S/.': getProcessedData().allLines.reduce((sum, line) => sum + line.value, 0),\n      Porcentaje: '100.00%'\n    });\n    const ws = XLSX.utils.json_to_sheet(data);\n    const wb = XLSX.utils.book_new();\n    XLSX.utils.book_append_sheet(wb, ws, 'Ventas por División');\n    XLSX.writeFile(wb, 'ventas_por_division.xlsx');\n  };\n\n  const isMonday = dateStr => {\n    // Asegurarnos de que la fecha esté en formato YYYY-MM-DD\n    const [year, month, day] = dateStr.split('-').map(num => parseInt(num, 10)); // En JavaScript, los meses van de 0-11, por eso restamos 1 al mes\n\n    const date = new Date(year, month - 1, day);\n    return date.getDay() === 1; // 0 es domingo, 1 es lunes\n  };\n\n  const isSunday = dateStr => {\n    // Asegurarnos de que la fecha esté en formato YYYY-MM-DD\n    const [year, month, day] = dateStr.split('-').map(num => parseInt(num, 10)); // En JavaScript, los meses van de 0-11, por eso restamos 1 al mes\n\n    const date = new Date(year, month - 1, day);\n    return date.getDay() === 0; // 0 es domingo\n  };\n\n  const getMixedViewData = () => {\n    const days = [...getProcessedData().days];\n    const mixedData = [];\n    let i = 0;\n\n    while (i < days.length) {\n      // Si el día actual es lunes y hay suficientes días para una semana completa\n      // y el último día es domingo (semana completa)\n      if (isMonday(days[i].day) && i + 6 < days.length && isSunday(days[i + 6].day)) {\n        // Crear una entrada semanal completa\n        const weekStartDate = days[i].day;\n        const weekEndDate = days[i + 6].day;\n        const weekTotal = days.slice(i, i + 7).reduce((sum, day) => sum + day.value, 0);\n        mixedData.push({\n          type: 'complete_week',\n          day: `${weekStartDate} - ${weekEndDate}`,\n          value: weekTotal,\n          formattedValue: formatCurrency(weekTotal)\n        }); // Avanzar al siguiente día después de la semana\n\n        i += 7;\n      } else {\n        // Buscar días consecutivos que no forman una semana completa\n        let j = i; // Avanzar hasta encontrar el inicio de una semana completa o el final del array\n\n        while (j < days.length && !(isMonday(days[j].day) && j + 6 < days.length && isSunday(days[j + 6].day))) {\n          j++;\n        } // Si encontramos al menos un día para agrupar\n\n\n        if (j > i) {\n          const incompleteWeekStartDate = days[i].day;\n          const incompleteWeekEndDate = days[j - 1].day;\n          const incompleteWeekTotal = days.slice(i, j).reduce((sum, day) => sum + day.value, 0);\n          mixedData.push({\n            type: 'incomplete_week',\n            day: `${incompleteWeekStartDate} - ${incompleteWeekEndDate}`,\n            value: incompleteWeekTotal,\n            formattedValue: formatCurrency(incompleteWeekTotal)\n          }); // Avanzar después del grupo de días\n\n          i = j;\n        } else {\n          // Este caso no debería ocurrir, pero lo mantenemos por seguridad\n          i++;\n        }\n      }\n    }\n\n    return mixedData;\n  };\n\n  const handleViewChange = (event, newView) => {\n    if (newView !== null) {\n      setViewMode(newView);\n    }\n  };\n\n  const handleStoreClick = storeName => {\n    setSelectedStore(storeName);\n  }; // Simplificamos las funciones para mejor manejo\n\n\n  const toggleAllLines = (divisionName, e) => {\n    // Detener la propagación para evitar clic en acordeón\n    if (e) {\n      e.stopPropagation();\n      e.preventDefault();\n    } // Obtenemos la lista de líneas una sola vez\n\n\n    const processedData = getProcessedData();\n    const division = processedData.allLines.find(line => line.name === divisionName);\n    if (!division || !division.realLines || division.realLines.length === 0) return; // Verificamos si al menos una línea está abierta\n\n    const lineKeys = division.realLines.map(realLine => `${divisionName}-${realLine.name}`);\n    const anyLineOpen = lineKeys.some(key => expandedLines[key]); // Creamos un nuevo objeto de estado directamente con los valores deseados\n\n    const newExpandedLines = { ...expandedLines\n    };\n    lineKeys.forEach(key => {\n      newExpandedLines[key] = !anyLineOpen;\n    }); // Si vamos a expandir, aseguramos que la división esté abierta\n\n    if (!anyLineOpen) {\n      setExpandedDivisions(prev => ({ ...prev,\n        [divisionName]: true\n      }));\n    } // Actualización única del estado\n\n\n    setExpandedLines(newExpandedLines);\n  }; // Manejador para expandir/colapsar la división principal\n\n\n  const handleDivisionExpand = (divisionName, isExpanded) => {\n    setExpandedDivisions(prev => ({ ...prev,\n      [divisionName]: isExpanded\n    })); // Si se está colapsando la división, cerramos todas sus líneas\n\n    if (!isExpanded) {\n      const processedData = getProcessedData();\n      const division = processedData.allLines.find(line => line.name === divisionName);\n\n      if (division && division.realLines) {\n        const newExpandedLines = { ...expandedLines\n        };\n        division.realLines.forEach(realLine => {\n          const lineKey = `${divisionName}-${realLine.name}`;\n          newExpandedLines[lineKey] = false;\n        });\n        setExpandedLines(newExpandedLines);\n      }\n    }\n  }; // Manejador para la expansión individual de líneas\n\n\n  const handleLineExpand = (lineKey, isExpanded) => {\n    setExpandedLines(prev => ({ ...prev,\n      [lineKey]: isExpanded\n    }));\n  }; // Función optimizada para verificar si alguna línea está abierta\n\n\n  const isAnyLineExpanded = divisionName => {\n    const processedData = getProcessedData();\n    const division = processedData.allLines.find(line => line.name === divisionName);\n    if (!division || !division.realLines) return false; // Verificamos más eficientemente usando some() en lugar de un bucle\n\n    return division.realLines.some(realLine => expandedLines[`${divisionName}-${realLine.name}`] === true);\n  };\n\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Backdrop, {\n      sx: {\n        color: '#fff',\n        zIndex: theme => theme.zIndex.drawer + 1,\n        backgroundColor: 'rgba(255, 255, 255, 0.8)'\n      },\n      open: loading,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 60,\n          thickness: 4,\n          sx: {\n            color: '#b3256e'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 947,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            color: '#b3256e',\n            fontWeight: 'bold'\n          },\n          children: \"Cargando datos...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 954,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 939,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 931,\n      columnNumber: 13\n    }, this);\n  } // Calculamos los datos procesados una sola vez\n\n\n  const processedData = getProcessedData();\n  const stores = [...new Set(salesData.map(sale => sale.store_name))];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      padding: '20px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(ParticipacionesSection, {\n      children: [/*#__PURE__*/_jsxDEV(ParticipacionesTitle, {\n        children: \"Filtros\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 975,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(FiltersWrapper, {\n        children: [/*#__PURE__*/_jsxDEV(FilterGroup, {\n          children: [/*#__PURE__*/_jsxDEV(LocalizationProvider, {\n            dateAdapter: AdapterDateFns,\n            locale: es,\n            children: [/*#__PURE__*/_jsxDEV(CompactDatePicker, {\n              label: \"Fecha inicial\",\n              value: dateRange[0],\n              onChange: newValue => {\n                // Prevenir valores nulos o inválidos\n                if (newValue && !isNaN(newValue.getTime())) {\n                  const adjusted = new Date(newValue);\n                  adjusted.setHours(12, 0, 0, 0);\n                  setDateRange([adjusted, dateRange[1]]);\n                }\n              },\n              renderInput: params => /*#__PURE__*/_jsxDEV(TextField, { ...params,\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 990,\n                columnNumber: 58\n              }, this),\n              inputFormat: \"dd/MM/yyyy\",\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  '&.Mui-focused fieldset': {\n                    borderColor: '#b3256e'\n                  }\n                },\n                '& .MuiInputLabel-root.Mui-focused': {\n                  color: '#b3256e'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 979,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(CompactDatePicker, {\n              label: \"Fecha final\",\n              value: dateRange[1],\n              onChange: newValue => {\n                // Prevenir valores nulos o inválidos\n                if (newValue && !isNaN(newValue.getTime())) {\n                  const adjusted = new Date(newValue);\n                  adjusted.setHours(12, 0, 0, 0);\n                  setDateRange([dateRange[0], adjusted]);\n                }\n              },\n              renderInput: params => /*#__PURE__*/_jsxDEV(TextField, { ...params,\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1014,\n                columnNumber: 58\n              }, this),\n              inputFormat: \"dd/MM/yyyy\",\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  '&.Mui-focused fieldset': {\n                    borderColor: '#b3256e'\n                  }\n                },\n                '& .MuiInputLabel-root.Mui-focused': {\n                  color: '#b3256e'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1003,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 978,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleFilterClick,\n            sx: {\n              border: '1px solid #e0e0e0',\n              borderRadius: '18px',\n              padding: '8px'\n            },\n            children: /*#__PURE__*/_jsxDEV(FilterListIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1028,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 977,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(FilterGroup, {\n          children: [/*#__PURE__*/_jsxDEV(TimeSelect, {\n            type: \"time\",\n            value: startTime,\n            onChange: e => handleTimeChange('start', e),\n            size: \"small\",\n            InputLabelProps: {\n              shrink: true\n            },\n            label: \"Desde\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1042,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TimeSelect, {\n            type: \"time\",\n            value: endTime,\n            onChange: e => handleTimeChange('end', e),\n            size: \"small\",\n            InputLabelProps: {\n              shrink: true\n            },\n            label: \"Hasta\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1050,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1041,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(FilterGroup, {\n          sx: {\n            flex: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n            multiple: true,\n            size: \"small\",\n            value: selectedLineNames,\n            onChange: handleLineNameChange,\n            options: availableLineNames,\n            renderInput: params => /*#__PURE__*/_jsxDEV(TextField, { ...params,\n              placeholder: \"Divisiones\",\n              size: \"small\",\n              sx: {\n                minWidth: 200,\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: '18px',\n                  height: '36px'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1068,\n              columnNumber: 33\n            }, this),\n            renderTags: (selected, getTagProps) => selected.map((option, index) => /*#__PURE__*/_createElement(FilterChip, { ...getTagProps({\n                index\n              }),\n              key: option,\n              label: option,\n              selected: true,\n              onDelete: getTagProps({\n                index\n              }).onDelete,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1083,\n                columnNumber: 37\n              }\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1061,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1060,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(FilterGroup, {\n          sx: {\n            flex: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n            multiple: true,\n            size: \"small\",\n            value: selectedRealLineNames,\n            onChange: handleRealLineNameChange,\n            options: availableRealLineNames,\n            renderInput: params => /*#__PURE__*/_jsxDEV(TextField, { ...params,\n              placeholder: \"L\\xEDneas\",\n              size: \"small\",\n              sx: {\n                minWidth: 200,\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: '18px',\n                  height: '36px'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 33\n            }, this),\n            renderTags: (selected, getTagProps) => selected.map((option, index) => /*#__PURE__*/_createElement(FilterChip, { ...getTagProps({\n                index\n              }),\n              key: option,\n              label: option,\n              selected: true,\n              onDelete: getTagProps({\n                index\n              }).onDelete,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1118,\n                columnNumber: 37\n              }\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1096,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1095,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 976,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        anchorEl: filterAnchorEl,\n        open: Boolean(filterAnchorEl),\n        onClose: handleFilterClose,\n        PaperProps: {\n          sx: {\n            mt: 1,\n            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n            borderRadius: '8px',\n            '& .MuiMenuItem-root': {\n              fontSize: '0.875rem',\n              padding: '8px 16px',\n              '&:hover': {\n                backgroundColor: '#b3256e10'\n              }\n            }\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => handleDateRangePreset('today'),\n          children: \"Hoy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1150,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => handleDateRangePreset('yesterday'),\n          children: \"Ayer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1151,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => handleDateRangePreset('last7'),\n          children: \"\\xDAltimos 7 d\\xEDas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1152,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => handleDateRangePreset('last30'),\n          children: \"\\xDAltimos 30 d\\xEDas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1153,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1131,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(StoreSelect, {\n        value: selectedStore,\n        exclusive: true,\n        onChange: (event, newStore) => setSelectedStore(newStore || 'all'),\n        \"aria-label\": \"store selection\",\n        children: [/*#__PURE__*/_jsxDEV(ToggleButton, {\n          value: \"all\",\n          children: \"Todas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1162,\n          columnNumber: 21\n        }, this), stores.map(store => /*#__PURE__*/_jsxDEV(ToggleButton, {\n          value: store,\n          children: store\n        }, store, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1164,\n          columnNumber: 25\n        }, this)), /*#__PURE__*/_jsxDEV(AIChat, {\n          salesAdvanceData: processedData.stores,\n          salesHoursData: processedData.hours,\n          salesDivisionData: processedData.lines,\n          salesStoreData: processedData.allStores,\n          salesLinesData: processedData.allLines\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1168,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1156,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 974,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ParticipacionesSection, {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Ventas Por Divisi\\xF3n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1179,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          marginBottom: '20px',\n          borderRadius: '8px',\n          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(StyledTableCell, {\n                className: \"header\",\n                children: \"Tiendas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1187,\n                columnNumber: 33\n              }, this), processedData.allLines && processedData.allLines.map(line => /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                className: \"header\",\n                align: \"right\",\n                children: line.name\n              }, line.name, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1190,\n                columnNumber: 41\n              }, this)), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                className: \"header\",\n                align: \"right\",\n                children: \"Total\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1194,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1186,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1185,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: [processedData.stores && processedData.stores.map(store => /*#__PURE__*/_jsxDEV(StyledTableRow, {\n              children: [/*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: store.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1203,\n                columnNumber: 41\n              }, this), processedData.allLines && processedData.allLines.map(line => /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: formatCurrency(store[line.name] || 0)\n              }, `${store.name}-${line.name}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1206,\n                columnNumber: 49\n              }, this)), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: formatCurrency(store.total || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1210,\n                columnNumber: 41\n              }, this)]\n            }, store.name, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1202,\n              columnNumber: 37\n            }, this)), /*#__PURE__*/_jsxDEV(TotalRow, {\n              children: [/*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Total General\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1214,\n                columnNumber: 33\n              }, this), processedData.allLines && processedData.allLines.map(line => /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: formatCurrency(line.value || 0)\n              }, `total-${line.name}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1217,\n                columnNumber: 41\n              }, this)), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: formatCurrency(processedData.stores ? processedData.stores.reduce((sum, store) => sum + (store.total || 0), 0) : 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1221,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1213,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1199,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1184,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1180,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(MuiTooltip, {\n          title: \"Exportar a Excel\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: exportLinesSummaryToExcel,\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1233,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1232,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1231,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1230,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1178,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ParticipacionesSection, {\n      children: [/*#__PURE__*/_jsxDEV(ParticipacionesTitle, {\n        children: \"Participaciones\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1240,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(ChartContainer, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            mb: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(ChartTitle, {\n            children: [\"Participaci\\xF3n de Ventas por \", viewMode === 'daily' ? 'Día' : 'Semana/Día']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1244,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(ToggleButtonGroup, {\n              value: viewMode,\n              exclusive: true,\n              onChange: handleViewChange,\n              size: \"small\",\n              sx: {\n                mr: 2,\n                '& .MuiToggleButton-root': {\n                  '&.Mui-selected': {\n                    backgroundColor: '#b94b84',\n                    color: 'white',\n                    '&:hover': {\n                      backgroundColor: '#a03c71'\n                    }\n                  }\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(ToggleButton, {\n                value: \"daily\",\n                \"aria-label\": \"vista diaria\",\n                children: /*#__PURE__*/_jsxDEV(MuiTooltip, {\n                  title: \"Vista Diaria\",\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      fontSize: '12px',\n                      fontWeight: 'medium',\n                      color: viewMode === 'daily' ? 'white' : 'inherit'\n                    },\n                    children: \"D\\xEDas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1266,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1265,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1264,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(ToggleButton, {\n                value: \"mixed\",\n                \"aria-label\": \"vista mixta\",\n                children: /*#__PURE__*/_jsxDEV(MuiTooltip, {\n                  title: \"Vista Mixta (Semanas Completas e Incompletas)\",\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      fontSize: '12px',\n                      fontWeight: 'medium',\n                      color: viewMode === 'mixed' ? 'white' : 'inherit'\n                    },\n                    children: \"Semanas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1279,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1278,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1277,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1246,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(MuiTooltip, {\n              title: \"Descargar gr\\xE1fico\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => downloadChart(dayChartRef, viewMode === 'daily' ? 'Participación_Ventas_por_Día' : 'Participación_Ventas_Mixta'),\n                children: /*#__PURE__*/_jsxDEV(DownloadIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1301,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1292,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1291,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1245,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1243,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: 400,\n          children: /*#__PURE__*/_jsxDEV(BarChart, {\n            ref: dayChartRef,\n            data: viewMode === 'daily' ? processedData.days : getMixedViewData(),\n            barSize: 60,\n            maxBarSize: 60,\n            margin: {\n              top: 30,\n              right: 30,\n              left: 20,\n              bottom: 5\n            },\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1314,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              dataKey: \"day\",\n              tickFormatter: value => {\n                // Si estamos en modo mixto y el valor contiene un guion, es un rango de semana\n                if (viewMode === 'mixed' && value.includes(' - ')) {\n                  // Extraer las fechas de inicio y fin\n                  const [startDate, endDate] = value.split(' - '); // Formatear ambas fechas\n\n                  return `${formatDate(startDate)} - ${formatDate(endDate)}`;\n                } // Para días individuales, usar el formatDate normal\n\n\n                return formatDate(value);\n              },\n              angle: -45,\n              textAnchor: \"end\",\n              height: 80\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1315,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n              tickFormatter: value => `S/ ${(value / 1000).toFixed(1)}K`,\n              tick: {\n                fontSize: 11\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1332,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              formatter: (value, name) => [formatCurrency(value), 'Venta'],\n              labelFormatter: label => {\n                if (viewMode === 'day') {\n                  try {\n                    // Verificar si el label es un formato de fecha válido\n                    if (typeof label === 'string' && label.includes('-') && label.split('-').length === 3) {\n                      const parts = label.split('-');\n                      const year = parseInt(parts[0]);\n                      const month = parseInt(parts[1]) - 1;\n                      const day = parseInt(parts[2]);\n\n                      if (!isNaN(year) && !isNaN(month) && !isNaN(day)) {\n                        const date = new Date(year, month, day, 12, 0, 0);\n                        return format(date, 'yyyy-MM-dd');\n                      }\n                    }\n\n                    return String(label);\n                  } catch (error) {\n                    /* eslint-disable */\n                    console.error(...oo_tx(`300546921_1352_44_1352_90_11`, 'Error formatting date:', error));\n                    return String(label);\n                  }\n                } else {\n                  try {\n                    // Validar que el número de semana esté en rango (1-53)\n                    const weekNum = parseInt(label);\n\n                    if (isNaN(weekNum) || weekNum < 1 || weekNum > 53) {\n                      return `Semana ${label}`;\n                    }\n\n                    const today = new Date();\n                    const year = today.getFullYear(); // Calcula el primer día del año\n\n                    const firstDayOfYear = new Date(year, 0, 1); // Ajusta al primer día de la semana (lunes=1, domingo=0)\n\n                    const dayOfWeek = firstDayOfYear.getDay() || 7;\n                    const firstWeekday = new Date(firstDayOfYear);\n                    firstWeekday.setDate(firstDayOfYear.getDate() + (dayOfWeek <= 4 ? 1 - dayOfWeek : 8 - dayOfWeek)); // Calcula la primera fecha de la semana solicitada\n\n                    const firstDate = new Date(firstWeekday);\n                    firstDate.setDate(firstWeekday.getDate() + (weekNum - 1) * 7); // Calcula el último día (domingo)\n\n                    const lastDate = new Date(firstDate);\n                    lastDate.setDate(firstDate.getDate() + 6);\n                    return `Semana ${label}: ${format(firstDate, 'dd/MM')} - ${format(lastDate, 'dd/MM')}`;\n                  } catch (error) {\n                    /* eslint-disable */\n                    console.error(...oo_tx(`300546921_1386_44_1386_90_11`, 'Error formatting week:', error));\n                    return `Semana ${label}`;\n                  }\n                }\n              },\n              contentStyle: {\n                fontSize: 12\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1333,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Bar, {\n              dataKey: \"value\",\n              fill: \"#b3256e\",\n              label: {\n                position: 'top',\n                formatter: (value, entry, index) => {\n                  // Calcula la suma total\n                  const totalSum = (viewMode === 'day' ? processedData.days : processedData.weeks).reduce((acc, curr) => acc + curr.value, 0); // Calcula el porcentaje sin redondear para uso interno\n\n                  const exactPercentage = value / totalSum * 100; // Para evitar que los porcentajes sumen más de 100%, ajusta el último elemento\n\n                  const isLastItem = index === (viewMode === 'day' ? processedData.days.length - 1 : processedData.weeks.length - 1);\n\n                  if (isLastItem) {\n                    // Suma de todos los porcentajes excepto el último\n                    const otherPercentages = (viewMode === 'day' ? processedData.days : processedData.weeks).slice(0, -1).reduce((acc, curr) => acc + Math.round(curr.value / totalSum * 1000) / 10, 0); // El último porcentaje es el complemento para llegar a 100%\n\n                    return `${(100 - otherPercentages).toFixed(1)}%`;\n                  } // Para los demás elementos, redondea normalmente\n\n\n                  return `${Math.round(exactPercentage * 10) / 10}%`;\n                },\n                fontSize: 11\n              },\n              children: (viewMode === 'daily' ? processedData.days : getMixedViewData()).map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                fill: entry.type === 'complete_week' ? '#4a148c' : '#b3256e'\n              }, `cell-${index}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1429,\n                columnNumber: 37\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1393,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1307,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1306,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1242,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(ChartContainer, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            mb: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(ChartTitle, {\n            children: \"Participaci\\xF3n de Ventas por Hora\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1438,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(MuiTooltip, {\n            title: \"Descargar gr\\xE1fico\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => downloadChart(hourChartRef, 'Participación_Ventas_por_Hora'),\n              children: /*#__PURE__*/_jsxDEV(DownloadIcon, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1441,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1440,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1439,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1437,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: 300,\n          children: /*#__PURE__*/_jsxDEV(BarChart, {\n            ref: hourChartRef,\n            data: processedData.hours.map(hour => {\n              const total = processedData.hours.reduce((acc, curr) => acc + curr.value, 0);\n              const percentage = hour.value / total * 100;\n              return { ...hour,\n                percentage,\n                fillColor: getHeatMapColor(hour.value, total)\n              };\n            }),\n            layout: \"vertical\",\n            margin: {\n              top: 5,\n              right: 30,\n              left: 40,\n              bottom: 5\n            },\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\",\n              horizontal: false\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1460,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              type: \"number\",\n              tickFormatter: value => `S/ ${(value / 1000).toFixed(1)}K`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1461,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n              dataKey: \"hour\",\n              type: \"category\",\n              width: 60,\n              tick: {\n                fill: '#666'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1462,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              formatter: (value, name, props) => {\n                const percentage = value / processedData.hours.reduce((acc, curr) => acc + curr.value, 0) * 100;\n                return [`${formatCurrency(value)} (${percentage.toFixed(1)}%)`, 'Venta'];\n              },\n              labelFormatter: label => `Hora: ${label}`,\n              contentStyle: {\n                backgroundColor: '#fff',\n                border: '1px solid #e0e0e0',\n                borderRadius: '4px',\n                padding: '8px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1463,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Bar, {\n              dataKey: \"value\",\n              name: \"Ventas\",\n              fill: \"#b3256e\",\n              background: {\n                fill: '#f5f5f5'\n              },\n              label: {\n                position: 'right',\n                content: _ref8 => {\n                  let {\n                    value\n                  } = _ref8;\n                  return `S/ ${(value / 1000).toFixed(1)}K`;\n                },\n                fontSize: 11\n              },\n              children: processedData.hours.map((entry, index) => {\n                const total = processedData.hours.reduce((acc, curr) => acc + curr.value, 0);\n                return /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: getHeatMapColor(entry.value, total)\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1489,\n                  columnNumber: 44\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1476,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1446,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1445,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(HeatMapLegend, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: '#666',\n              fontWeight: 'bold'\n            },\n            children: \"<2%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1495,\n            columnNumber: 25\n          }, this), getHeatMapColors().map((color, index) => /*#__PURE__*/_jsxDEV(Box, {\n            className: \"color-box\",\n            sx: {\n              backgroundColor: color\n            }\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1499,\n            columnNumber: 29\n          }, this)), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: '#666',\n              fontWeight: 'bold'\n            },\n            children: \">8%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1501,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1494,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1436,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(ChartsContainer, {\n        children: [/*#__PURE__*/_jsxDEV(ChartContainer, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(ChartTitle, {\n              children: \"Participaci\\xF3n de Ventas por Divisi\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1510,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(MuiTooltip, {\n              title: \"Descargar gr\\xE1fico\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => downloadChart(divisionChartRef, 'Participación_Ventas_por_División'),\n                children: /*#__PURE__*/_jsxDEV(DownloadIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1516,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1512,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1511,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1509,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              ref: divisionChartRef,\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: processedData.lines,\n                dataKey: \"value\",\n                nameKey: \"name\",\n                cx: \"50%\",\n                cy: \"50%\",\n                outerRadius: 80,\n                label: _ref9 => {\n                  let {\n                    cx,\n                    cy,\n                    midAngle,\n                    innerRadius,\n                    outerRadius,\n                    value,\n                    percentage\n                  } = _ref9;\n                  const RADIAN = Math.PI / 180;\n                  const radius = outerRadius + 25;\n                  const x = cx + radius * Math.cos(-midAngle * RADIAN);\n                  const y = cy + radius * Math.sin(-midAngle * RADIAN);\n                  return /*#__PURE__*/_jsxDEV(\"g\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"text\", {\n                      x: x,\n                      y: y,\n                      fill: \"#666\",\n                      textAnchor: x > cx ? 'start' : 'end',\n                      dominantBaseline: \"central\",\n                      children: `${percentage === null || percentage === void 0 ? void 0 : percentage.toFixed(1)}%`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1536,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                      x: x,\n                      y: y + 15,\n                      fill: \"#666\",\n                      textAnchor: x > cx ? 'start' : 'end',\n                      dominantBaseline: \"central\",\n                      style: {\n                        fontSize: '0.8em'\n                      },\n                      children: formatCurrency(value)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1545,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1535,\n                    columnNumber: 45\n                  }, this);\n                },\n                children: processedData.lines.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: COLORS[index % COLORS.length]\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1560,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1522,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1563,\n                  columnNumber: 51\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1563,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {\n                verticalAlign: \"bottom\",\n                height: 36,\n                formatter: (value, entry) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: '#666',\n                    marginRight: '10px'\n                  },\n                  children: value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1567,\n                  columnNumber: 66\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1564,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1521,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1520,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1508,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ChartContainer, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(ChartTitle, {\n              children: \"Participaci\\xF3n de Ventas por Tienda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1575,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(MuiTooltip, {\n              title: \"Descargar gr\\xE1fico\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => downloadChart(storeChartRef, 'Participación_Ventas_por_Tienda'),\n                children: /*#__PURE__*/_jsxDEV(DownloadIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1578,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1577,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1576,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1574,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 400,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              ref: storeChartRef,\n              data: processedData.allStores,\n              barSize: 60,\n              maxBarSize: 60,\n              margin: {\n                top: 30,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1590,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"name\",\n                tickFormatter: value => {\n                  return value.split(' ').map(word => word.charAt(0)).join('').toUpperCase();\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1591,\n                columnNumber: 33\n              }, this), ' ', /*#__PURE__*/_jsxDEV(YAxis, {\n                tickFormatter: value => `S/ ${(value / 1000).toFixed(1)}K`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1601,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                formatter: (value, name) => [formatCurrency(value), 'Venta'],\n                labelFormatter: label => `${label}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1602,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"total\",\n                fill: \"#b3256e\",\n                label: {\n                  position: 'top',\n                  formatter: value => {\n                    const totalSum = processedData.allStores.reduce((acc, curr) => acc + curr.total, 0);\n                    return `${(value / totalSum * 100).toFixed(1)}%`;\n                  }\n                },\n                children: processedData.allStores.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: \"#b3256e\",\n                  opacity: selectedStore === 'all' || selectedStore === entry.name ? 1 : 0.3,\n                  cursor: \"pointer\",\n                  onClick: () => handleStoreClick(entry.name)\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1618,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1606,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1583,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1582,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1573,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1507,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(ChartTitle, {\n          children: \"Detalle de Ventas por Divisi\\xF3n, L\\xEDneas y Subl\\xEDneas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1632,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 1\n          },\n          children: [processedData.allLines.map(line => /*#__PURE__*/_jsxDEV(Accordion, {\n            expanded: expandedDivisions[line.name] || false,\n            onChange: (_, isExpanded) => handleDivisionExpand(line.name, isExpanded),\n            children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n              expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {\n                sx: {\n                  color: 'white'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1641,\n                columnNumber: 49\n              }, this),\n              \"aria-controls\": `${line.name}-content`,\n              id: `${line.name}-header`,\n              sx: {\n                bgcolor: '#9e187d',\n                color: 'white',\n                '&.Mui-expanded': {\n                  minHeight: '48px'\n                },\n                '& .MuiAccordionSummary-content.Mui-expanded': {\n                  margin: '12px 0'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  width: '100%',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    fontWeight: \"bold\",\n                    color: \"white\",\n                    children: line.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1657,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1656,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"white\",\n                  children: [formatCurrency(line.value), \" (\", line.percentage.toFixed(2), \"%)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1661,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1655,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1640,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2,\n                    mb: 2,\n                    p: 1,\n                    borderBottom: '1px solid rgba(158, 24, 125, 0.1)'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    component: \"span\",\n                    onClick: e => {\n                      if (!isAnyLineExpanded(line.name)) {\n                        toggleAllLines(line.name, e);\n                      }\n                    },\n                    sx: {\n                      color: '#2D58E4FF',\n                      cursor: 'pointer',\n                      textDecoration: 'underline'\n                    },\n                    children: \"Expandir todo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1678,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    component: \"span\",\n                    onClick: e => {\n                      if (isAnyLineExpanded(line.name)) {\n                        toggleAllLines(line.name, e);\n                      }\n                    },\n                    sx: {\n                      color: '#2D58E4FF',\n                      cursor: 'pointer',\n                      textDecoration: 'underline'\n                    },\n                    children: \"Contraer todo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1693,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1668,\n                  columnNumber: 41\n                }, this), line.realLines.map(realLine => {\n                  const lineKey = `${line.name}-${realLine.name}`;\n                  const isExpanded = expandedLines[lineKey] || false;\n                  return /*#__PURE__*/_jsxDEV(Accordion, {\n                    expanded: isExpanded,\n                    onChange: (_, isLineExpanded) => handleLineExpand(lineKey, isLineExpanded),\n                    children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n                      expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {\n                        sx: {\n                          color: 'white'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1720,\n                        columnNumber: 69\n                      }, this),\n                      \"aria-controls\": `${lineKey}-content`,\n                      id: `${lineKey}-header`,\n                      sx: {\n                        bgcolor: '#8A848AFF',\n                        color: 'white',\n                        '&.Mui-expanded': {\n                          minHeight: '48px'\n                        },\n                        '& .MuiAccordionSummary-content.Mui-expanded': {\n                          margin: '12px 0'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          justifyContent: 'space-between',\n                          width: '100%',\n                          alignItems: 'center'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          fontWeight: \"bold\",\n                          color: \"white\",\n                          children: realLine.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1742,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"white\",\n                          children: [formatCurrency(realLine.value), \" (\", realLine.percentage.toFixed(2), \"%)\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1745,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1734,\n                        columnNumber: 57\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1719,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n                      children: /*#__PURE__*/_jsxDEV(TableContainer, {\n                        component: Paper,\n                        sx: {\n                          mb: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Table, {\n                          size: \"small\",\n                          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                            children: /*#__PURE__*/_jsxDEV(TableRow, {\n                              children: [/*#__PURE__*/_jsxDEV(StyledTableCell, {\n                                children: \"Subl\\xEDnea\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1755,\n                                columnNumber: 73\n                              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                                align: \"right\",\n                                children: \"Total S/.\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1756,\n                                columnNumber: 73\n                              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                                align: \"right\",\n                                children: \"Porcentaje\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1757,\n                                columnNumber: 73\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1754,\n                              columnNumber: 69\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1753,\n                            columnNumber: 65\n                          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                            children: realLine.sublines.map(subline => /*#__PURE__*/_jsxDEV(StyledTableRow, {\n                              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                                children: subline.name\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1765,\n                                columnNumber: 77\n                              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                                align: \"right\",\n                                children: formatCurrency(subline.value)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1766,\n                                columnNumber: 77\n                              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                                align: \"right\",\n                                children: [subline.percentage.toFixed(2), \"%\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1769,\n                                columnNumber: 77\n                              }, this)]\n                            }, `${line.name}-${realLine.name}-${subline.name}`, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1762,\n                              columnNumber: 73\n                            }, this))\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1760,\n                            columnNumber: 65\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1752,\n                          columnNumber: 61\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1751,\n                        columnNumber: 57\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1750,\n                      columnNumber: 53\n                    }, this)]\n                  }, lineKey, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1714,\n                    columnNumber: 49\n                  }, this);\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1667,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1666,\n              columnNumber: 33\n            }, this)]\n          }, line.name, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1635,\n            columnNumber: 29\n          }, this)), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2,\n              p: 1,\n              bgcolor: '#f5f5f5',\n              borderRadius: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                fontWeight: 'bold'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                children: \"Total General\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1787,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  gap: 4\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  children: formatCurrency(processedData.allLines.reduce((sum, line) => sum + line.value, 0))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1789,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  children: \"100.00%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1792,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1788,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1786,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1785,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1633,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            mb: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(MuiTooltip, {\n            title: \"Exportar a Excel\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: exportLinesTableToExcel,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1800,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1799,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1798,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1797,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1631,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1239,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 973,\n    columnNumber: 9\n  }, this);\n};\n\n_s(StoresTab, \"DVkQpeW7o2WN0OP19q+5iUZ0WlE=\");\n\n_c = StoresTab;\nexport default StoresTab;\n/* istanbul ignore next */\n\n/* c8 ignore start */\n\n/* eslint-disable */\n\n;\n\nfunction oo_cm() {\n  try {\n    return (0, eval)(\"globalThis._console_ninja\") || (0, eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x418f23=_0x33f3;(function(_0x2c70e5,_0x70d422){var _0x45fe32=_0x33f3,_0x244e11=_0x2c70e5();while(!![]){try{var _0xe599a4=parseInt(_0x45fe32(0xb0))/0x1*(parseInt(_0x45fe32(0xa1))/0x2)+-parseInt(_0x45fe32(0x15e))/0x3+-parseInt(_0x45fe32(0x109))/0x4*(parseInt(_0x45fe32(0xc2))/0x5)+parseInt(_0x45fe32(0x191))/0x6+-parseInt(_0x45fe32(0x11d))/0x7*(parseInt(_0x45fe32(0x9c))/0x8)+parseInt(_0x45fe32(0xe1))/0x9+-parseInt(_0x45fe32(0x15f))/0xa*(-parseInt(_0x45fe32(0x148))/0xb);if(_0xe599a4===_0x70d422)break;else _0x244e11['push'](_0x244e11['shift']());}catch(_0x630c67){_0x244e11['push'](_0x244e11['shift']());}}}(_0x4e19,0xaaec1));var G=Object[_0x418f23(0xe5)],V=Object[_0x418f23(0x103)],ee=Object['getOwnPropertyDescriptor'],te=Object[_0x418f23(0xdf)],ne=Object[_0x418f23(0xd9)],re=Object[_0x418f23(0x119)][_0x418f23(0xf2)],ie=(_0x24c79a,_0x5c1c97,_0x1147c3,_0x2138d8)=>{var _0x36a3cf=_0x418f23;if(_0x5c1c97&&typeof _0x5c1c97==_0x36a3cf(0x117)||typeof _0x5c1c97==_0x36a3cf(0x13e)){for(let _0x5c0210 of te(_0x5c1c97))!re[_0x36a3cf(0xda)](_0x24c79a,_0x5c0210)&&_0x5c0210!==_0x1147c3&&V(_0x24c79a,_0x5c0210,{'get':()=>_0x5c1c97[_0x5c0210],'enumerable':!(_0x2138d8=ee(_0x5c1c97,_0x5c0210))||_0x2138d8[_0x36a3cf(0x14e)]});}return _0x24c79a;},j=(_0x1f84af,_0x39bbd1,_0xf2cf2e)=>(_0xf2cf2e=_0x1f84af!=null?G(ne(_0x1f84af)):{},ie(_0x39bbd1||!_0x1f84af||!_0x1f84af['__es'+'Module']?V(_0xf2cf2e,'default',{'value':_0x1f84af,'enumerable':!0x0}):_0xf2cf2e,_0x1f84af)),q=class{constructor(_0x14d9ea,_0x61266e,_0x21d732,_0x659164,_0x2ce13a,_0x1b0c0c){var _0x4b2850=_0x418f23,_0x34d24c,_0x26bffd,_0xeab781,_0x4b345e;this['global']=_0x14d9ea,this[_0x4b2850(0xe0)]=_0x61266e,this['port']=_0x21d732,this[_0x4b2850(0x173)]=_0x659164,this[_0x4b2850(0x131)]=_0x2ce13a,this['eventReceivedCallback']=_0x1b0c0c,this[_0x4b2850(0x159)]=!0x0,this['_allowedToConnectOnSend']=!0x0,this[_0x4b2850(0xee)]=!0x1,this[_0x4b2850(0xa0)]=!0x1,this[_0x4b2850(0x160)]=((_0x26bffd=(_0x34d24c=_0x14d9ea['process'])==null?void 0x0:_0x34d24c[_0x4b2850(0x116)])==null?void 0x0:_0x26bffd['NEXT_RUNTIME'])===_0x4b2850(0x9e),this[_0x4b2850(0x174)]=!((_0x4b345e=(_0xeab781=this[_0x4b2850(0xf8)][_0x4b2850(0x104)])==null?void 0x0:_0xeab781['versions'])!=null&&_0x4b345e[_0x4b2850(0xc8)])&&!this[_0x4b2850(0x160)],this[_0x4b2850(0xe6)]=null,this[_0x4b2850(0xfc)]=0x0,this[_0x4b2850(0xf1)]=0x14,this['_webSocketErrorDocsLink']=_0x4b2850(0xcd),this[_0x4b2850(0xb8)]=(this[_0x4b2850(0x174)]?_0x4b2850(0xaf):_0x4b2850(0x9f))+this[_0x4b2850(0xc9)];}async[_0x418f23(0xac)](){var _0x4a1673=_0x418f23,_0x2d8a6c,_0x2fabb9;if(this[_0x4a1673(0xe6)])return this[_0x4a1673(0xe6)];let _0x338282;if(this[_0x4a1673(0x174)]||this[_0x4a1673(0x160)])_0x338282=this[_0x4a1673(0xf8)][_0x4a1673(0x17c)];else{if((_0x2d8a6c=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])!=null&&_0x2d8a6c[_0x4a1673(0xcc)])_0x338282=(_0x2fabb9=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])==null?void 0x0:_0x2fabb9[_0x4a1673(0xcc)];else try{let _0x6adc18=await import(_0x4a1673(0x17f));_0x338282=(await import((await import(_0x4a1673(0x14c)))[_0x4a1673(0xb2)](_0x6adc18['join'](this[_0x4a1673(0x173)],_0x4a1673(0x9a)))['toString']()))[_0x4a1673(0x164)];}catch{try{_0x338282=require(require(_0x4a1673(0x17f))['join'](this[_0x4a1673(0x173)],'ws'));}catch{throw new Error('failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket');}}}return this[_0x4a1673(0xe6)]=_0x338282,_0x338282;}[_0x418f23(0xe2)](){var _0x560a95=_0x418f23;this[_0x560a95(0xa0)]||this[_0x560a95(0xee)]||this[_0x560a95(0xfc)]>=this[_0x560a95(0xf1)]||(this['_allowedToConnectOnSend']=!0x1,this['_connecting']=!0x0,this[_0x560a95(0xfc)]++,this['_ws']=new Promise((_0x48a2aa,_0x1b9b87)=>{var _0x3507cc=_0x560a95;this[_0x3507cc(0xac)]()['then'](_0x2d9634=>{var _0x4649cf=_0x3507cc;let _0x18b292=new _0x2d9634(_0x4649cf(0x185)+(!this['_inBrowser']&&this['dockerizedApp']?_0x4649cf(0x15a):this[_0x4649cf(0xe0)])+':'+this['port']);_0x18b292[_0x4649cf(0x16f)]=()=>{var _0x37af5c=_0x4649cf;this['_allowedToSend']=!0x1,this[_0x37af5c(0x162)](_0x18b292),this['_attemptToReconnectShortly'](),_0x1b9b87(new Error('logger\\\\x20websocket\\\\x20error'));},_0x18b292[_0x4649cf(0xf5)]=()=>{var _0x5c5b5c=_0x4649cf;this[_0x5c5b5c(0x174)]||_0x18b292[_0x5c5b5c(0xe7)]&&_0x18b292[_0x5c5b5c(0xe7)]['unref']&&_0x18b292[_0x5c5b5c(0xe7)]['unref'](),_0x48a2aa(_0x18b292);},_0x18b292[_0x4649cf(0xb6)]=()=>{this['_allowedToConnectOnSend']=!0x0,this['_disposeWebsocket'](_0x18b292),this['_attemptToReconnectShortly']();},_0x18b292[_0x4649cf(0x121)]=_0xf360ec=>{var _0x34c0e1=_0x4649cf;try{if(!(_0xf360ec!=null&&_0xf360ec[_0x34c0e1(0x99)])||!this[_0x34c0e1(0x12f)])return;let _0x5a655a=JSON[_0x34c0e1(0x13d)](_0xf360ec[_0x34c0e1(0x99)]);this['eventReceivedCallback'](_0x5a655a['method'],_0x5a655a[_0x34c0e1(0xab)],this[_0x34c0e1(0xf8)],this[_0x34c0e1(0x174)]);}catch{}};})['then'](_0x382d9b=>(this['_connected']=!0x0,this[_0x3507cc(0xa0)]=!0x1,this[_0x3507cc(0x12c)]=!0x1,this[_0x3507cc(0x159)]=!0x0,this['_connectAttemptCount']=0x0,_0x382d9b))['catch'](_0x469147=>(this[_0x3507cc(0xee)]=!0x1,this[_0x3507cc(0xa0)]=!0x1,console[_0x3507cc(0xed)](_0x3507cc(0x169)+this[_0x3507cc(0xc9)]),_0x1b9b87(new Error(_0x3507cc(0x12a)+(_0x469147&&_0x469147[_0x3507cc(0xb4)])))));}));}[_0x418f23(0x162)](_0x391e4c){var _0x18bf98=_0x418f23;this[_0x18bf98(0xee)]=!0x1,this[_0x18bf98(0xa0)]=!0x1;try{_0x391e4c['onclose']=null,_0x391e4c[_0x18bf98(0x16f)]=null,_0x391e4c[_0x18bf98(0xf5)]=null;}catch{}try{_0x391e4c[_0x18bf98(0xb9)]<0x2&&_0x391e4c[_0x18bf98(0x141)]();}catch{}}['_attemptToReconnectShortly'](){var _0x4846b6=_0x418f23;clearTimeout(this[_0x4846b6(0xa3)]),!(this[_0x4846b6(0xfc)]>=this[_0x4846b6(0xf1)])&&(this['_reconnectTimeout']=setTimeout(()=>{var _0xc0d1ae=_0x4846b6,_0x3b3b8b;this[_0xc0d1ae(0xee)]||this[_0xc0d1ae(0xa0)]||(this[_0xc0d1ae(0xe2)](),(_0x3b3b8b=this[_0xc0d1ae(0xd2)])==null||_0x3b3b8b[_0xc0d1ae(0x120)](()=>this['_attemptToReconnectShortly']()));},0x1f4),this['_reconnectTimeout'][_0x4846b6(0x188)]&&this[_0x4846b6(0xa3)][_0x4846b6(0x188)]());}async[_0x418f23(0x11e)](_0x592dff){var _0x123097=_0x418f23;try{if(!this[_0x123097(0x159)])return;this[_0x123097(0x12c)]&&this[_0x123097(0xe2)](),(await this['_ws'])[_0x123097(0x11e)](JSON[_0x123097(0xdd)](_0x592dff));}catch(_0x3558e1){this['_extendedWarning']?console[_0x123097(0xed)](this['_sendErrorMessage']+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)])):(this[_0x123097(0x167)]=!0x0,console[_0x123097(0xed)](this[_0x123097(0xb8)]+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)]),_0x592dff)),this[_0x123097(0x159)]=!0x1,this[_0x123097(0xb3)]();}}};function H(_0x21a490,_0x6209b7,_0x32bdf1,_0x32048a,_0x5bcdf6,_0x3f8a6e,_0xb987a3,_0x3abcb6=oe){var _0x372163=_0x418f23;let _0x52a2ac=_0x32bdf1[_0x372163(0x190)](',')[_0x372163(0x12e)](_0x230c9d=>{var _0x1b5d4e=_0x372163,_0x4a53bb,_0x1cde39,_0x106ea9,_0x3f43e6;try{if(!_0x21a490['_console_ninja_session']){let _0x24bfb9=((_0x1cde39=(_0x4a53bb=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x4a53bb['versions'])==null?void 0x0:_0x1cde39[_0x1b5d4e(0xc8)])||((_0x3f43e6=(_0x106ea9=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x106ea9[_0x1b5d4e(0x116)])==null?void 0x0:_0x3f43e6[_0x1b5d4e(0xd6)])==='edge';(_0x5bcdf6===_0x1b5d4e(0x110)||_0x5bcdf6===_0x1b5d4e(0x155)||_0x5bcdf6==='astro'||_0x5bcdf6==='angular')&&(_0x5bcdf6+=_0x24bfb9?_0x1b5d4e(0x10c):_0x1b5d4e(0x124)),_0x21a490['_console_ninja_session']={'id':+new Date(),'tool':_0x5bcdf6},_0xb987a3&&_0x5bcdf6&&!_0x24bfb9&&console['log'](_0x1b5d4e(0xfe)+(_0x5bcdf6[_0x1b5d4e(0x13c)](0x0)[_0x1b5d4e(0x100)]()+_0x5bcdf6['substr'](0x1))+',','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)',_0x1b5d4e(0xbb));}let _0x4eb2eb=new q(_0x21a490,_0x6209b7,_0x230c9d,_0x32048a,_0x3f8a6e,_0x3abcb6);return _0x4eb2eb[_0x1b5d4e(0x11e)][_0x1b5d4e(0xf4)](_0x4eb2eb);}catch(_0x202950){return console[_0x1b5d4e(0xed)](_0x1b5d4e(0x18e),_0x202950&&_0x202950[_0x1b5d4e(0xb4)]),()=>{};}});return _0x17b111=>_0x52a2ac[_0x372163(0x178)](_0x3b7429=>_0x3b7429(_0x17b111));}function _0x4e19(){var _0x3dea94=['perf_hooks','now','elements','6915181ldjYIK','send','date','catch','onmessage','_isUndefined','_HTMLAllCollection','\\\\x20browser','_setNodePermissions','strLength','_getOwnPropertyDescriptor','_Symbol','indexOf','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','number','_allowedToConnectOnSend','_sortProps','map','eventReceivedCallback','array','dockerizedApp','match','_processTreeNodeResult','disabledLog','_numberRegExp','_hasSymbolPropertyOnItsPath',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"DevHardtech\\\",\\\"************\\\"],'_hasMapOnItsPath','performance','coverage','Error','charAt','parse','function','endsWith','rootExpression','close','undefined','_hasSetOnItsPath','_regExpToString','_p_name','slice','substr','11hsvZPL','hostname','serialize','_dateToString','url','boolean','enumerable','length','_addObjectProperty','root_exp','origin','reload','Symbol','remix','_objectToString','push','getOwnPropertySymbols','_allowedToSend','gateway.docker.internal','HTMLAllCollection','_getOwnPropertyNames','_isPrimitiveWrapperType','4193466bntOOn','16178350tQpRDP','_inNextEdge','time','_disposeWebsocket','_setNodeLabel','default','_ninjaIgnoreNextError','concat','_extendedWarning','resolveGetters','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','hrtime','current','_cleanNode','replace','_consoleNinjaAllowedToStart','onerror','Map','getOwnPropertyDescriptor','getter','nodeModules','_inBrowser','sort','_blacklistedProperty','autoExpandLimit','forEach','_undefined',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.vscode\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.456\\\\\\\\node_modules\\\",'_addProperty','WebSocket','_property','_addLoadNode','path','props','NEGATIVE_INFINITY','_type','webpack','error','ws://','_isMap','null','unref','negativeInfinity','','_quotedRegExp','set','nan','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','_p_length','split','7999758ImPfSL','_isArray','data','ws/index.js','parent','8YXKnRI','autoExpandPropertyCount','edge','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','_connecting','2rNewCg','allStrLength','_reconnectTimeout','noFunctions','toString','index','fromCharCode','_treeNodePropertiesBeforeFullValue','versions','count','args','getWebSocketClass','funcName','_isPrimitiveType','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','262697JHDjIO','1','pathToFileURL','_attemptToReconnectShortly','message','isExpressionToEvaluate','onclose','name','_sendErrorMessage','readyState','location','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','console','disabledTrace','[object\\\\x20BigInt]','valueOf','autoExpand','[object\\\\x20Date]','107080UCogNw','includes','trace','hits','expressionsToEvaluate','_p_','node','_webSocketErrorDocsLink','unknown','_setNodeId','_WebSocket','https://tinyurl.com/37x8b79t','value','_getOwnPropertySymbols','negativeZero','_setNodeQueryPath','_ws','elapsed','symbol','_propertyName','NEXT_RUNTIME','stackTraceLimit','_console_ninja_session','getPrototypeOf','call','_console_ninja','[object\\\\x20Array]','stringify','_isSet','getOwnPropertyNames','host','8484993ONNFtV','_connectToHostNow','level','_treeNodePropertiesAfterFullValue','create','_WebSocketClass','_socket','_addFunctionsNode','_capIfString','Boolean','_setNodeExpandableState','62812','warn','_connected','timeStamp','depth','_maxConnectAttemptCount','hasOwnProperty','capped','bind','onopen','Set','constructor','global','bigint','POSITIVE_INFINITY','sortProps','_connectAttemptCount','Number','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','log','toUpperCase','string','positiveInfinity','defineProperty','process','...','String','some','get','200DTLFWz','','type','\\\\x20server','startsWith','toLowerCase','_additionalMetadata','next.js','_setNodeExpressionPath','reduceLimits','test','[object\\\\x20Map]','autoExpandMaxDepth','env','object','autoExpandPreviousObjects','prototype'];_0x4e19=function(){return _0x3dea94;};return _0x4e19();}function oe(_0x29bd2d,_0x4ca25e,_0x2f30dc,_0x50ad96){var _0x4b277d=_0x418f23;_0x50ad96&&_0x29bd2d===_0x4b277d(0x153)&&_0x2f30dc[_0x4b277d(0xba)]['reload']();}function B(_0x53e47a){var _0x4f5825=_0x418f23,_0x17ea3d,_0x5476d8;let _0x2ed5e7=function(_0x3f9b33,_0x4798cf){return _0x4798cf-_0x3f9b33;},_0x2534f8;if(_0x53e47a[_0x4f5825(0x139)])_0x2534f8=function(){var _0xf68f54=_0x4f5825;return _0x53e47a[_0xf68f54(0x139)][_0xf68f54(0x11b)]();};else{if(_0x53e47a[_0x4f5825(0x104)]&&_0x53e47a[_0x4f5825(0x104)][_0x4f5825(0x16a)]&&((_0x5476d8=(_0x17ea3d=_0x53e47a[_0x4f5825(0x104)])==null?void 0x0:_0x17ea3d[_0x4f5825(0x116)])==null?void 0x0:_0x5476d8[_0x4f5825(0xd6)])!==_0x4f5825(0x9e))_0x2534f8=function(){var _0x1144bb=_0x4f5825;return _0x53e47a[_0x1144bb(0x104)][_0x1144bb(0x16a)]();},_0x2ed5e7=function(_0x4a8621,_0xc276d4){return 0x3e8*(_0xc276d4[0x0]-_0x4a8621[0x0])+(_0xc276d4[0x1]-_0x4a8621[0x1])/0xf4240;};else try{let {performance:_0x6c0ab3}=require(_0x4f5825(0x11a));_0x2534f8=function(){var _0x57029c=_0x4f5825;return _0x6c0ab3[_0x57029c(0x11b)]();};}catch{_0x2534f8=function(){return+new Date();};}}return{'elapsed':_0x2ed5e7,'timeStamp':_0x2534f8,'now':()=>Date['now']()};}function X(_0x108a65,_0x2bc4c8,_0x5e7fce){var _0xd0e45=_0x418f23,_0x184b4d,_0x3be467,_0x1494d3,_0x1853ba,_0xc61e6c;if(_0x108a65[_0xd0e45(0x16e)]!==void 0x0)return _0x108a65['_consoleNinjaAllowedToStart'];let _0xae1558=((_0x3be467=(_0x184b4d=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x184b4d[_0xd0e45(0xa9)])==null?void 0x0:_0x3be467[_0xd0e45(0xc8)])||((_0x1853ba=(_0x1494d3=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x1494d3[_0xd0e45(0x116)])==null?void 0x0:_0x1853ba[_0xd0e45(0xd6)])===_0xd0e45(0x9e);function _0x492297(_0x174e6c){var _0x9b4def=_0xd0e45;if(_0x174e6c[_0x9b4def(0x10d)]('/')&&_0x174e6c[_0x9b4def(0x13f)]('/')){let _0x2461d3=new RegExp(_0x174e6c['slice'](0x1,-0x1));return _0x2a4fef=>_0x2461d3[_0x9b4def(0x113)](_0x2a4fef);}else{if(_0x174e6c[_0x9b4def(0xc3)]('*')||_0x174e6c[_0x9b4def(0xc3)]('?')){let _0x51dbdb=new RegExp('^'+_0x174e6c[_0x9b4def(0x16d)](/\\\\./g,String[_0x9b4def(0xa7)](0x5c)+'.')[_0x9b4def(0x16d)](/\\\\*/g,'.*')[_0x9b4def(0x16d)](/\\\\?/g,'.')+String[_0x9b4def(0xa7)](0x24));return _0x2bf349=>_0x51dbdb['test'](_0x2bf349);}else return _0x40a674=>_0x40a674===_0x174e6c;}}let _0x418e9a=_0x2bc4c8[_0xd0e45(0x12e)](_0x492297);return _0x108a65[_0xd0e45(0x16e)]=_0xae1558||!_0x2bc4c8,!_0x108a65['_consoleNinjaAllowedToStart']&&((_0xc61e6c=_0x108a65[_0xd0e45(0xba)])==null?void 0x0:_0xc61e6c['hostname'])&&(_0x108a65[_0xd0e45(0x16e)]=_0x418e9a[_0xd0e45(0x107)](_0x1dbe80=>_0x1dbe80(_0x108a65[_0xd0e45(0xba)][_0xd0e45(0x149)]))),_0x108a65['_consoleNinjaAllowedToStart'];}function _0x33f3(_0x3a814d,_0x58c537){var _0x4e195d=_0x4e19();return _0x33f3=function(_0x33f3b8,_0x2e2a30){_0x33f3b8=_0x33f3b8-0x98;var _0x3c84c1=_0x4e195d[_0x33f3b8];return _0x3c84c1;},_0x33f3(_0x3a814d,_0x58c537);}function J(_0x3830e6,_0x45a6b5,_0x2f8209,_0x3cee70){var _0x40c820=_0x418f23;_0x3830e6=_0x3830e6,_0x45a6b5=_0x45a6b5,_0x2f8209=_0x2f8209,_0x3cee70=_0x3cee70;let _0x38a5a7=B(_0x3830e6),_0x5b41b9=_0x38a5a7[_0x40c820(0xd3)],_0x1169a5=_0x38a5a7[_0x40c820(0xef)];class _0x1e3ba1{constructor(){var _0x3396c1=_0x40c820;this['_keyStrRegExp']=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this[_0x3396c1(0x135)]=/^(0|[1-9][0-9]*)$/,this[_0x3396c1(0x18b)]=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x3396c1(0x179)]=_0x3830e6[_0x3396c1(0x142)],this[_0x3396c1(0x123)]=_0x3830e6[_0x3396c1(0x15b)],this[_0x3396c1(0x127)]=Object[_0x3396c1(0x171)],this['_getOwnPropertyNames']=Object[_0x3396c1(0xdf)],this[_0x3396c1(0x128)]=_0x3830e6[_0x3396c1(0x154)],this[_0x3396c1(0x144)]=RegExp[_0x3396c1(0x119)][_0x3396c1(0xa5)],this['_dateToString']=Date[_0x3396c1(0x119)][_0x3396c1(0xa5)];}[_0x40c820(0x14a)](_0x1f7b5d,_0x5b6b91,_0x1ebf24,_0x4f3c70){var _0x4d7e42=_0x40c820,_0xe363bc=this,_0x290e3b=_0x1ebf24[_0x4d7e42(0xc0)];function _0x16ce5f(_0xf8520c,_0x1a1953,_0x3e443e){var _0x4923f3=_0x4d7e42;_0x1a1953[_0x4923f3(0x10b)]=_0x4923f3(0xca),_0x1a1953['error']=_0xf8520c[_0x4923f3(0xb4)],_0x454078=_0x3e443e[_0x4923f3(0xc8)]['current'],_0x3e443e['node'][_0x4923f3(0x16b)]=_0x1a1953,_0xe363bc['_treeNodePropertiesBeforeFullValue'](_0x1a1953,_0x3e443e);}let _0x1533a9;_0x3830e6[_0x4d7e42(0xbc)]&&(_0x1533a9=_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)],_0x1533a9&&(_0x3830e6['console'][_0x4d7e42(0x184)]=function(){}));try{try{_0x1ebf24[_0x4d7e42(0xe3)]++,_0x1ebf24['autoExpand']&&_0x1ebf24[_0x4d7e42(0x118)]['push'](_0x5b6b91);var _0x55a2c4,_0x5cbc7d,_0x10ebd6,_0x38ec49,_0x46d06f=[],_0x4ef003=[],_0x33c92e,_0xe8efc0=this[_0x4d7e42(0x182)](_0x5b6b91),_0x5b392f=_0xe8efc0===_0x4d7e42(0x130),_0x55d894=!0x1,_0x65caf4=_0xe8efc0===_0x4d7e42(0x13e),_0x512734=this['_isPrimitiveType'](_0xe8efc0),_0x3d6d36=this[_0x4d7e42(0x15d)](_0xe8efc0),_0x2d65b0=_0x512734||_0x3d6d36,_0x4b6f05={},_0x419e4c=0x0,_0x2bca20=!0x1,_0x454078,_0xed6526=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x1ebf24['depth']){if(_0x5b392f){if(_0x5cbc7d=_0x5b6b91['length'],_0x5cbc7d>_0x1ebf24[_0x4d7e42(0x11c)]){for(_0x10ebd6=0x0,_0x38ec49=_0x1ebf24[_0x4d7e42(0x11c)],_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003[_0x4d7e42(0x157)](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));_0x1f7b5d['cappedElements']=!0x0;}else{for(_0x10ebd6=0x0,_0x38ec49=_0x5cbc7d,_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));}_0x1ebf24[_0x4d7e42(0x9d)]+=_0x4ef003[_0x4d7e42(0x14f)];}if(!(_0xe8efc0==='null'||_0xe8efc0==='undefined')&&!_0x512734&&_0xe8efc0!==_0x4d7e42(0x106)&&_0xe8efc0!=='Buffer'&&_0xe8efc0!=='bigint'){var _0xfca776=_0x4f3c70['props']||_0x1ebf24[_0x4d7e42(0x180)];if(this['_isSet'](_0x5b6b91)?(_0x55a2c4=0x0,_0x5b6b91['forEach'](function(_0x1b3730){var _0x29b12d=_0x4d7e42;if(_0x419e4c++,_0x1ebf24['autoExpandPropertyCount']++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24[_0x29b12d(0xb5)]&&_0x1ebf24[_0x29b12d(0xc0)]&&_0x1ebf24[_0x29b12d(0x9d)]>_0x1ebf24[_0x29b12d(0x177)]){_0x2bca20=!0x0;return;}_0x4ef003['push'](_0xe363bc[_0x29b12d(0x17b)](_0x46d06f,_0x5b6b91,_0x29b12d(0xf6),_0x55a2c4++,_0x1ebf24,function(_0x383398){return function(){return _0x383398;};}(_0x1b3730)));})):this[_0x4d7e42(0x186)](_0x5b6b91)&&_0x5b6b91['forEach'](function(_0x4cd1d9,_0x42ee6b){var _0x3c460e=_0x4d7e42;if(_0x419e4c++,_0x1ebf24[_0x3c460e(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x3c460e(0xc0)]&&_0x1ebf24[_0x3c460e(0x9d)]>_0x1ebf24['autoExpandLimit']){_0x2bca20=!0x0;return;}var _0x2a4101=_0x42ee6b[_0x3c460e(0xa5)]();_0x2a4101[_0x3c460e(0x14f)]>0x64&&(_0x2a4101=_0x2a4101[_0x3c460e(0x146)](0x0,0x64)+_0x3c460e(0x105)),_0x4ef003[_0x3c460e(0x157)](_0xe363bc['_addProperty'](_0x46d06f,_0x5b6b91,_0x3c460e(0x170),_0x2a4101,_0x1ebf24,function(_0x1c45bc){return function(){return _0x1c45bc;};}(_0x4cd1d9)));}),!_0x55d894){try{for(_0x33c92e in _0x5b6b91)if(!(_0x5b392f&&_0xed6526['test'](_0x33c92e))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPropertyCount']>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x150)](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}catch{}if(_0x4b6f05[_0x4d7e42(0x18f)]=!0x0,_0x65caf4&&(_0x4b6f05[_0x4d7e42(0x145)]=!0x0),!_0x2bca20){var _0x469d20=[][_0x4d7e42(0x166)](this[_0x4d7e42(0x15c)](_0x5b6b91))[_0x4d7e42(0x166)](this[_0x4d7e42(0xcf)](_0x5b6b91));for(_0x55a2c4=0x0,_0x5cbc7d=_0x469d20[_0x4d7e42(0x14f)];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)if(_0x33c92e=_0x469d20[_0x55a2c4],!(_0x5b392f&&_0xed6526['test'](_0x33c92e[_0x4d7e42(0xa5)]()))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)&&!_0x4b6f05[_0x4d7e42(0xc7)+_0x33c92e[_0x4d7e42(0xa5)]()]){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24[_0x4d7e42(0xb5)]&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24[_0x4d7e42(0x9d)]>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc['_addObjectProperty'](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}}}}if(_0x1f7b5d['type']=_0xe8efc0,_0x2d65b0?(_0x1f7b5d['value']=_0x5b6b91[_0x4d7e42(0xbf)](),this['_capIfString'](_0xe8efc0,_0x1f7b5d,_0x1ebf24,_0x4f3c70)):_0xe8efc0===_0x4d7e42(0x11f)?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x14b)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='bigint'?_0x1f7b5d[_0x4d7e42(0xce)]=_0x5b6b91[_0x4d7e42(0xa5)]():_0xe8efc0==='RegExp'?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x144)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='symbol'&&this[_0x4d7e42(0x128)]?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x128)][_0x4d7e42(0x119)]['toString']['call'](_0x5b6b91):!_0x1ebf24[_0x4d7e42(0xf0)]&&!(_0xe8efc0===_0x4d7e42(0x187)||_0xe8efc0==='undefined')&&(delete _0x1f7b5d[_0x4d7e42(0xce)],_0x1f7b5d[_0x4d7e42(0xf3)]=!0x0),_0x2bca20&&(_0x1f7b5d['cappedProps']=!0x0),_0x454078=_0x1ebf24['node']['current'],_0x1ebf24[_0x4d7e42(0xc8)]['current']=_0x1f7b5d,this[_0x4d7e42(0xa8)](_0x1f7b5d,_0x1ebf24),_0x4ef003[_0x4d7e42(0x14f)]){for(_0x55a2c4=0x0,_0x5cbc7d=_0x4ef003['length'];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)_0x4ef003[_0x55a2c4](_0x55a2c4);}_0x46d06f['length']&&(_0x1f7b5d[_0x4d7e42(0x180)]=_0x46d06f);}catch(_0x54504a){_0x16ce5f(_0x54504a,_0x1f7b5d,_0x1ebf24);}this[_0x4d7e42(0x10f)](_0x5b6b91,_0x1f7b5d),this[_0x4d7e42(0xe4)](_0x1f7b5d,_0x1ebf24),_0x1ebf24[_0x4d7e42(0xc8)][_0x4d7e42(0x16b)]=_0x454078,_0x1ebf24['level']--,_0x1ebf24[_0x4d7e42(0xc0)]=_0x290e3b,_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPreviousObjects']['pop']();}finally{_0x1533a9&&(_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)]=_0x1533a9);}return _0x1f7b5d;}[_0x40c820(0xcf)](_0xd7ad14){var _0x474a44=_0x40c820;return Object[_0x474a44(0x158)]?Object[_0x474a44(0x158)](_0xd7ad14):[];}[_0x40c820(0xde)](_0x5b06ac){var _0x292c99=_0x40c820;return!!(_0x5b06ac&&_0x3830e6[_0x292c99(0xf6)]&&this[_0x292c99(0x156)](_0x5b06ac)==='[object\\\\x20Set]'&&_0x5b06ac[_0x292c99(0x178)]);}['_blacklistedProperty'](_0x10628d,_0x15c227,_0x5a4f15){var _0x152ffd=_0x40c820;return _0x5a4f15[_0x152ffd(0xa4)]?typeof _0x10628d[_0x15c227]==_0x152ffd(0x13e):!0x1;}[_0x40c820(0x182)](_0x13718c){var _0x2c19d1=_0x40c820,_0x225ae1='';return _0x225ae1=typeof _0x13718c,_0x225ae1==='object'?this[_0x2c19d1(0x156)](_0x13718c)==='[object\\\\x20Array]'?_0x225ae1=_0x2c19d1(0x130):this['_objectToString'](_0x13718c)===_0x2c19d1(0xc1)?_0x225ae1=_0x2c19d1(0x11f):this[_0x2c19d1(0x156)](_0x13718c)===_0x2c19d1(0xbe)?_0x225ae1=_0x2c19d1(0xf9):_0x13718c===null?_0x225ae1=_0x2c19d1(0x187):_0x13718c[_0x2c19d1(0xf7)]&&(_0x225ae1=_0x13718c['constructor'][_0x2c19d1(0xb7)]||_0x225ae1):_0x225ae1===_0x2c19d1(0x142)&&this[_0x2c19d1(0x123)]&&_0x13718c instanceof this['_HTMLAllCollection']&&(_0x225ae1=_0x2c19d1(0x15b)),_0x225ae1;}[_0x40c820(0x156)](_0x37617c){var _0xdf3907=_0x40c820;return Object[_0xdf3907(0x119)]['toString'][_0xdf3907(0xda)](_0x37617c);}[_0x40c820(0xae)](_0x26b95b){var _0x3b9373=_0x40c820;return _0x26b95b===_0x3b9373(0x14d)||_0x26b95b===_0x3b9373(0x101)||_0x26b95b===_0x3b9373(0x12b);}['_isPrimitiveWrapperType'](_0x150515){var _0x2539cd=_0x40c820;return _0x150515===_0x2539cd(0xea)||_0x150515==='String'||_0x150515===_0x2539cd(0xfd);}['_addProperty'](_0x1a647e,_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb){var _0x3c4649=this;return function(_0x5c2af7){var _0x865286=_0x33f3,_0x5ceb03=_0x985088[_0x865286(0xc8)][_0x865286(0x16b)],_0x14ad91=_0x985088[_0x865286(0xc8)]['index'],_0x10beb0=_0x985088[_0x865286(0xc8)][_0x865286(0x9b)];_0x985088['node'][_0x865286(0x9b)]=_0x5ceb03,_0x985088['node'][_0x865286(0xa6)]=typeof _0x551a3a==_0x865286(0x12b)?_0x551a3a:_0x5c2af7,_0x1a647e[_0x865286(0x157)](_0x3c4649[_0x865286(0x17d)](_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb)),_0x985088[_0x865286(0xc8)][_0x865286(0x9b)]=_0x10beb0,_0x985088['node']['index']=_0x14ad91;};}[_0x40c820(0x150)](_0x3e6c99,_0x96cdeb,_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b){var _0x102764=_0x40c820,_0xbcca65=this;return _0x96cdeb[_0x102764(0xc7)+_0x5e1ed6[_0x102764(0xa5)]()]=!0x0,function(_0x4f07e2){var _0x11a9a9=_0x102764,_0x41481f=_0x6bb8c1['node'][_0x11a9a9(0x16b)],_0x45aeeb=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)],_0x44cce6=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0x9b)];_0x6bb8c1['node']['parent']=_0x41481f,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x4f07e2,_0x3e6c99['push'](_0xbcca65[_0x11a9a9(0x17d)](_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b)),_0x6bb8c1['node']['parent']=_0x44cce6,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x45aeeb;};}['_property'](_0x5a954c,_0x11a196,_0x34292c,_0x53d319,_0x300135){var _0x3f13ca=_0x40c820,_0x350c39=this;_0x300135||(_0x300135=function(_0x26467a,_0x467a10){return _0x26467a[_0x467a10];});var _0x112124=_0x34292c[_0x3f13ca(0xa5)](),_0x42837e=_0x53d319[_0x3f13ca(0xc6)]||{},_0x265c6d=_0x53d319['depth'],_0x31debf=_0x53d319[_0x3f13ca(0xb5)];try{var _0x3c8586=this[_0x3f13ca(0x186)](_0x5a954c),_0x5579d4=_0x112124;_0x3c8586&&_0x5579d4[0x0]==='\\\\x27'&&(_0x5579d4=_0x5579d4[_0x3f13ca(0x147)](0x1,_0x5579d4[_0x3f13ca(0x14f)]-0x2));var _0x46f777=_0x53d319[_0x3f13ca(0xc6)]=_0x42837e[_0x3f13ca(0xc7)+_0x5579d4];_0x46f777&&(_0x53d319[_0x3f13ca(0xf0)]=_0x53d319[_0x3f13ca(0xf0)]+0x1),_0x53d319[_0x3f13ca(0xb5)]=!!_0x46f777;var _0x14534f=typeof _0x34292c=='symbol',_0x124dd3={'name':_0x14534f||_0x3c8586?_0x112124:this['_propertyName'](_0x112124)};if(_0x14534f&&(_0x124dd3[_0x3f13ca(0xd4)]=!0x0),!(_0x11a196===_0x3f13ca(0x130)||_0x11a196===_0x3f13ca(0x13b))){var _0x4ea27f=this[_0x3f13ca(0x127)](_0x5a954c,_0x34292c);if(_0x4ea27f&&(_0x4ea27f[_0x3f13ca(0x18c)]&&(_0x124dd3['setter']=!0x0),_0x4ea27f[_0x3f13ca(0x108)]&&!_0x46f777&&!_0x53d319['resolveGetters']))return _0x124dd3[_0x3f13ca(0x172)]=!0x0,this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x3214f9;try{_0x3214f9=_0x300135(_0x5a954c,_0x34292c);}catch(_0x3ef7eb){return _0x124dd3={'name':_0x112124,'type':_0x3f13ca(0xca),'error':_0x3ef7eb['message']},this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x4c3356=this[_0x3f13ca(0x182)](_0x3214f9),_0x18e3f8=this[_0x3f13ca(0xae)](_0x4c3356);if(_0x124dd3[_0x3f13ca(0x10b)]=_0x4c3356,_0x18e3f8)this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x7d7701=_0x3f13ca;_0x124dd3['value']=_0x3214f9[_0x7d7701(0xbf)](),!_0x46f777&&_0x350c39[_0x7d7701(0xe9)](_0x4c3356,_0x124dd3,_0x53d319,{});});else{var _0x275cea=_0x53d319[_0x3f13ca(0xc0)]&&_0x53d319['level']<_0x53d319['autoExpandMaxDepth']&&_0x53d319[_0x3f13ca(0x118)][_0x3f13ca(0x129)](_0x3214f9)<0x0&&_0x4c3356!==_0x3f13ca(0x13e)&&_0x53d319['autoExpandPropertyCount']<_0x53d319['autoExpandLimit'];_0x275cea||_0x53d319[_0x3f13ca(0xe3)]<_0x265c6d||_0x46f777?(this['serialize'](_0x124dd3,_0x3214f9,_0x53d319,_0x46f777||{}),this[_0x3f13ca(0x10f)](_0x3214f9,_0x124dd3)):this['_processTreeNodeResult'](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x2b8765=_0x3f13ca;_0x4c3356===_0x2b8765(0x187)||_0x4c3356===_0x2b8765(0x142)||(delete _0x124dd3[_0x2b8765(0xce)],_0x124dd3[_0x2b8765(0xf3)]=!0x0);});}return _0x124dd3;}finally{_0x53d319['expressionsToEvaluate']=_0x42837e,_0x53d319[_0x3f13ca(0xf0)]=_0x265c6d,_0x53d319['isExpressionToEvaluate']=_0x31debf;}}[_0x40c820(0xe9)](_0x3711dd,_0x3273d6,_0x30712a,_0x2bfd2c){var _0x22c791=_0x40c820,_0x3ed3d6=_0x2bfd2c['strLength']||_0x30712a[_0x22c791(0x126)];if((_0x3711dd==='string'||_0x3711dd==='String')&&_0x3273d6[_0x22c791(0xce)]){let _0xffdbb0=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x14f)];_0x30712a['allStrLength']+=_0xffdbb0,_0x30712a[_0x22c791(0xa2)]>_0x30712a['totalStrLength']?(_0x3273d6[_0x22c791(0xf3)]='',delete _0x3273d6[_0x22c791(0xce)]):_0xffdbb0>_0x3ed3d6&&(_0x3273d6[_0x22c791(0xf3)]=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x147)](0x0,_0x3ed3d6),delete _0x3273d6[_0x22c791(0xce)]);}}['_isMap'](_0x261c40){var _0xd2ec63=_0x40c820;return!!(_0x261c40&&_0x3830e6['Map']&&this[_0xd2ec63(0x156)](_0x261c40)===_0xd2ec63(0x114)&&_0x261c40[_0xd2ec63(0x178)]);}[_0x40c820(0xd5)](_0x24e250){var _0x49be73=_0x40c820;if(_0x24e250[_0x49be73(0x132)](/^\\\\d+$/))return _0x24e250;var _0x1d38d;try{_0x1d38d=JSON['stringify'](''+_0x24e250);}catch{_0x1d38d='\\\\x22'+this['_objectToString'](_0x24e250)+'\\\\x22';}return _0x1d38d['match'](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x1d38d=_0x1d38d[_0x49be73(0x147)](0x1,_0x1d38d[_0x49be73(0x14f)]-0x2):_0x1d38d=_0x1d38d[_0x49be73(0x16d)](/'/g,'\\\\x5c\\\\x27')[_0x49be73(0x16d)](/\\\\\\\\\\\"/g,'\\\\x22')[_0x49be73(0x16d)](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x1d38d;}[_0x40c820(0x133)](_0x1b7571,_0x8f439b,_0x2c2981,_0x188fd5){var _0xf97f10=_0x40c820;this[_0xf97f10(0xa8)](_0x1b7571,_0x8f439b),_0x188fd5&&_0x188fd5(),this[_0xf97f10(0x10f)](_0x2c2981,_0x1b7571),this['_treeNodePropertiesAfterFullValue'](_0x1b7571,_0x8f439b);}[_0x40c820(0xa8)](_0x231eb3,_0x5ea482){var _0x31f690=_0x40c820;this['_setNodeId'](_0x231eb3,_0x5ea482),this[_0x31f690(0xd1)](_0x231eb3,_0x5ea482),this[_0x31f690(0x111)](_0x231eb3,_0x5ea482),this[_0x31f690(0x125)](_0x231eb3,_0x5ea482);}[_0x40c820(0xcb)](_0x10d2e1,_0x3c8083){}[_0x40c820(0xd1)](_0x53d949,_0x188c67){}[_0x40c820(0x163)](_0x3f8259,_0x16e80a){}[_0x40c820(0x122)](_0x25a3a3){return _0x25a3a3===this['_undefined'];}[_0x40c820(0xe4)](_0x44987b,_0x4ed592){var _0x37fb3e=_0x40c820;this[_0x37fb3e(0x163)](_0x44987b,_0x4ed592),this[_0x37fb3e(0xeb)](_0x44987b),_0x4ed592[_0x37fb3e(0xfb)]&&this[_0x37fb3e(0x12d)](_0x44987b),this['_addFunctionsNode'](_0x44987b,_0x4ed592),this[_0x37fb3e(0x17e)](_0x44987b,_0x4ed592),this[_0x37fb3e(0x16c)](_0x44987b);}['_additionalMetadata'](_0x1c2784,_0x4c1dde){var _0x4f21c1=_0x40c820;try{_0x1c2784&&typeof _0x1c2784[_0x4f21c1(0x14f)]==_0x4f21c1(0x12b)&&(_0x4c1dde[_0x4f21c1(0x14f)]=_0x1c2784[_0x4f21c1(0x14f)]);}catch{}if(_0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x12b)||_0x4c1dde[_0x4f21c1(0x10b)]==='Number'){if(isNaN(_0x4c1dde[_0x4f21c1(0xce)]))_0x4c1dde[_0x4f21c1(0x18d)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];else switch(_0x4c1dde[_0x4f21c1(0xce)]){case Number[_0x4f21c1(0xfa)]:_0x4c1dde[_0x4f21c1(0x102)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case Number['NEGATIVE_INFINITY']:_0x4c1dde[_0x4f21c1(0x189)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case 0x0:this['_isNegativeZero'](_0x4c1dde[_0x4f21c1(0xce)])&&(_0x4c1dde[_0x4f21c1(0xd0)]=!0x0);break;}}else _0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x13e)&&typeof _0x1c2784['name']==_0x4f21c1(0x101)&&_0x1c2784[_0x4f21c1(0xb7)]&&_0x4c1dde['name']&&_0x1c2784[_0x4f21c1(0xb7)]!==_0x4c1dde[_0x4f21c1(0xb7)]&&(_0x4c1dde[_0x4f21c1(0xad)]=_0x1c2784[_0x4f21c1(0xb7)]);}['_isNegativeZero'](_0x289882){var _0x1b66c9=_0x40c820;return 0x1/_0x289882===Number[_0x1b66c9(0x181)];}['_sortProps'](_0x3992ee){var _0x3db550=_0x40c820;!_0x3992ee[_0x3db550(0x180)]||!_0x3992ee[_0x3db550(0x180)][_0x3db550(0x14f)]||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0x130)||_0x3992ee[_0x3db550(0x10b)]==='Map'||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0xf6)||_0x3992ee[_0x3db550(0x180)][_0x3db550(0x175)](function(_0x57a739,_0x31b40b){var _0x5dcaae=_0x3db550,_0x3d0d50=_0x57a739[_0x5dcaae(0xb7)][_0x5dcaae(0x10e)](),_0xd6d4fc=_0x31b40b[_0x5dcaae(0xb7)]['toLowerCase']();return _0x3d0d50<_0xd6d4fc?-0x1:_0x3d0d50>_0xd6d4fc?0x1:0x0;});}[_0x40c820(0xe8)](_0x12537a,_0x57f3dc){var _0x2884a4=_0x40c820;if(!(_0x57f3dc[_0x2884a4(0xa4)]||!_0x12537a['props']||!_0x12537a[_0x2884a4(0x180)][_0x2884a4(0x14f)])){for(var _0x53c006=[],_0x347d6e=[],_0x52e85a=0x0,_0x273297=_0x12537a['props']['length'];_0x52e85a<_0x273297;_0x52e85a++){var _0x1ee5b3=_0x12537a[_0x2884a4(0x180)][_0x52e85a];_0x1ee5b3[_0x2884a4(0x10b)]==='function'?_0x53c006[_0x2884a4(0x157)](_0x1ee5b3):_0x347d6e['push'](_0x1ee5b3);}if(!(!_0x347d6e[_0x2884a4(0x14f)]||_0x53c006[_0x2884a4(0x14f)]<=0x1)){_0x12537a[_0x2884a4(0x180)]=_0x347d6e;var _0x15f515={'functionsNode':!0x0,'props':_0x53c006};this[_0x2884a4(0xcb)](_0x15f515,_0x57f3dc),this[_0x2884a4(0x163)](_0x15f515,_0x57f3dc),this[_0x2884a4(0xeb)](_0x15f515),this[_0x2884a4(0x125)](_0x15f515,_0x57f3dc),_0x15f515['id']+='\\\\x20f',_0x12537a['props']['unshift'](_0x15f515);}}}['_addLoadNode'](_0x5bea6e,_0x14049e){}[_0x40c820(0xeb)](_0x199084){}[_0x40c820(0x98)](_0xf50c17){var _0x35cb98=_0x40c820;return Array['isArray'](_0xf50c17)||typeof _0xf50c17==_0x35cb98(0x117)&&this['_objectToString'](_0xf50c17)===_0x35cb98(0xdc);}[_0x40c820(0x125)](_0x3ea390,_0x54c209){}[_0x40c820(0x16c)](_0x25cdb9){var _0x1aa0a5=_0x40c820;delete _0x25cdb9[_0x1aa0a5(0x136)],delete _0x25cdb9[_0x1aa0a5(0x143)],delete _0x25cdb9[_0x1aa0a5(0x138)];}['_setNodeExpressionPath'](_0x17f351,_0x40c77e){}}let _0x459cb0=new _0x1e3ba1(),_0x218fe5={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x11fc4c={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x482c8e(_0x50675f,_0x2f7559,_0x19c481,_0x2c8a95,_0x245f16,_0x24484e){var _0x4c67af=_0x40c820;let _0x3ce9b8,_0x1af844;try{_0x1af844=_0x1169a5(),_0x3ce9b8=_0x2f8209[_0x2f7559],!_0x3ce9b8||_0x1af844-_0x3ce9b8['ts']>0x1f4&&_0x3ce9b8[_0x4c67af(0xaa)]&&_0x3ce9b8['time']/_0x3ce9b8[_0x4c67af(0xaa)]<0x64?(_0x2f8209[_0x2f7559]=_0x3ce9b8={'count':0x0,'time':0x0,'ts':_0x1af844},_0x2f8209[_0x4c67af(0xc5)]={}):_0x1af844-_0x2f8209[_0x4c67af(0xc5)]['ts']>0x32&&_0x2f8209['hits']['count']&&_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x161)]/_0x2f8209[_0x4c67af(0xc5)]['count']<0x64&&(_0x2f8209[_0x4c67af(0xc5)]={});let _0x157126=[],_0x1464d6=_0x3ce9b8[_0x4c67af(0x112)]||_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]?_0x11fc4c:_0x218fe5,_0x553948=_0x2ff7b4=>{var _0x3a1316=_0x4c67af;let _0x5d676c={};return _0x5d676c[_0x3a1316(0x180)]=_0x2ff7b4[_0x3a1316(0x180)],_0x5d676c['elements']=_0x2ff7b4['elements'],_0x5d676c[_0x3a1316(0x126)]=_0x2ff7b4[_0x3a1316(0x126)],_0x5d676c['totalStrLength']=_0x2ff7b4['totalStrLength'],_0x5d676c['autoExpandLimit']=_0x2ff7b4[_0x3a1316(0x177)],_0x5d676c[_0x3a1316(0x115)]=_0x2ff7b4['autoExpandMaxDepth'],_0x5d676c['sortProps']=!0x1,_0x5d676c['noFunctions']=!_0x45a6b5,_0x5d676c['depth']=0x1,_0x5d676c[_0x3a1316(0xe3)]=0x0,_0x5d676c['expId']='root_exp_id',_0x5d676c[_0x3a1316(0x140)]=_0x3a1316(0x151),_0x5d676c['autoExpand']=!0x0,_0x5d676c[_0x3a1316(0x118)]=[],_0x5d676c[_0x3a1316(0x9d)]=0x0,_0x5d676c[_0x3a1316(0x168)]=!0x0,_0x5d676c['allStrLength']=0x0,_0x5d676c[_0x3a1316(0xc8)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0x5d676c;};for(var _0x33de8f=0x0;_0x33de8f<_0x245f16[_0x4c67af(0x14f)];_0x33de8f++)_0x157126[_0x4c67af(0x157)](_0x459cb0[_0x4c67af(0x14a)]({'timeNode':_0x50675f===_0x4c67af(0x161)||void 0x0},_0x245f16[_0x33de8f],_0x553948(_0x1464d6),{}));if(_0x50675f==='trace'||_0x50675f===_0x4c67af(0x184)){let _0x1d9735=Error[_0x4c67af(0xd7)];try{Error[_0x4c67af(0xd7)]=0x1/0x0,_0x157126[_0x4c67af(0x157)](_0x459cb0['serialize']({'stackNode':!0x0},new Error()['stack'],_0x553948(_0x1464d6),{'strLength':0x1/0x0}));}finally{Error[_0x4c67af(0xd7)]=_0x1d9735;}}return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':_0x157126,'id':_0x2f7559,'context':_0x24484e}]};}catch(_0x2d5a77){return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':[{'type':_0x4c67af(0xca),'error':_0x2d5a77&&_0x2d5a77[_0x4c67af(0xb4)]}],'id':_0x2f7559,'context':_0x24484e}]};}finally{try{if(_0x3ce9b8&&_0x1af844){let _0xff386f=_0x1169a5();_0x3ce9b8[_0x4c67af(0xaa)]++,_0x3ce9b8['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x3ce9b8['ts']=_0xff386f,_0x2f8209[_0x4c67af(0xc5)]['count']++,_0x2f8209[_0x4c67af(0xc5)]['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x2f8209['hits']['ts']=_0xff386f,(_0x3ce9b8[_0x4c67af(0xaa)]>0x32||_0x3ce9b8[_0x4c67af(0x161)]>0x64)&&(_0x3ce9b8['reduceLimits']=!0x0),(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0xaa)]>0x3e8||_0x2f8209[_0x4c67af(0xc5)]['time']>0x12c)&&(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]=!0x0);}}catch{}}}return _0x482c8e;}((_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x296e29,_0x567fe9,_0x14adfa,_0x6b3989,_0x593945,_0x42f609)=>{var _0x543ef9=_0x418f23;if(_0x12a02f[_0x543ef9(0xdb)])return _0x12a02f[_0x543ef9(0xdb)];if(!X(_0x12a02f,_0x14adfa,_0x1164b7))return _0x12a02f[_0x543ef9(0xdb)]={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x12a02f[_0x543ef9(0xdb)];let _0x5a7d78=B(_0x12a02f),_0x236b4f=_0x5a7d78[_0x543ef9(0xd3)],_0x57b9d9=_0x5a7d78['timeStamp'],_0x163b61=_0x5a7d78[_0x543ef9(0x11b)],_0x384cd9={'hits':{},'ts':{}},_0x9c7997=J(_0x12a02f,_0x6b3989,_0x384cd9,_0x296e29),_0x3ffb36=_0xa1ec34=>{_0x384cd9['ts'][_0xa1ec34]=_0x57b9d9();},_0x4ce4d2=(_0x173258,_0x2e0c6d)=>{var _0x3b6e53=_0x543ef9;let _0x2b64c1=_0x384cd9['ts'][_0x2e0c6d];if(delete _0x384cd9['ts'][_0x2e0c6d],_0x2b64c1){let _0x557981=_0x236b4f(_0x2b64c1,_0x57b9d9());_0x593a90(_0x9c7997(_0x3b6e53(0x161),_0x173258,_0x163b61(),_0x50e896,[_0x557981],_0x2e0c6d));}},_0x46c5f3=_0x1f105e=>{var _0x1152c8=_0x543ef9,_0x4a2783;return _0x1164b7===_0x1152c8(0x110)&&_0x12a02f[_0x1152c8(0x152)]&&((_0x4a2783=_0x1f105e==null?void 0x0:_0x1f105e[_0x1152c8(0xab)])==null?void 0x0:_0x4a2783[_0x1152c8(0x14f)])&&(_0x1f105e[_0x1152c8(0xab)][0x0][_0x1152c8(0x152)]=_0x12a02f[_0x1152c8(0x152)]),_0x1f105e;};_0x12a02f[_0x543ef9(0xdb)]={'consoleLog':(_0x204f4b,_0x3e1804)=>{var _0x309615=_0x543ef9;_0x12a02f['console'][_0x309615(0xff)][_0x309615(0xb7)]!==_0x309615(0x134)&&_0x593a90(_0x9c7997(_0x309615(0xff),_0x204f4b,_0x163b61(),_0x50e896,_0x3e1804));},'consoleTrace':(_0x267a3f,_0x51c339)=>{var _0x4c4943=_0x543ef9,_0x33fd8c,_0x24e61f;_0x12a02f[_0x4c4943(0xbc)][_0x4c4943(0xff)]['name']!==_0x4c4943(0xbd)&&((_0x24e61f=(_0x33fd8c=_0x12a02f[_0x4c4943(0x104)])==null?void 0x0:_0x33fd8c['versions'])!=null&&_0x24e61f[_0x4c4943(0xc8)]&&(_0x12a02f[_0x4c4943(0x165)]=!0x0),_0x593a90(_0x46c5f3(_0x9c7997(_0x4c4943(0xc4),_0x267a3f,_0x163b61(),_0x50e896,_0x51c339))));},'consoleError':(_0xf7f1fc,_0x1384d7)=>{var _0x28d83c=_0x543ef9;_0x12a02f['_ninjaIgnoreNextError']=!0x0,_0x593a90(_0x46c5f3(_0x9c7997(_0x28d83c(0x184),_0xf7f1fc,_0x163b61(),_0x50e896,_0x1384d7)));},'consoleTime':_0x2ad865=>{_0x3ffb36(_0x2ad865);},'consoleTimeEnd':(_0x3c91cf,_0x308c8b)=>{_0x4ce4d2(_0x308c8b,_0x3c91cf);},'autoLog':(_0x4bbc9f,_0x3599a3)=>{var _0x598cfa=_0x543ef9;_0x593a90(_0x9c7997(_0x598cfa(0xff),_0x3599a3,_0x163b61(),_0x50e896,[_0x4bbc9f]));},'autoLogMany':(_0x158592,_0x29b77d)=>{var _0x425f64=_0x543ef9;_0x593a90(_0x9c7997(_0x425f64(0xff),_0x158592,_0x163b61(),_0x50e896,_0x29b77d));},'autoTrace':(_0x3f5f9d,_0xc378ab)=>{var _0x377a7d=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x377a7d(0xc4),_0xc378ab,_0x163b61(),_0x50e896,[_0x3f5f9d])));},'autoTraceMany':(_0x2c6f73,_0x35405b)=>{var _0x4f4e7f=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x4f4e7f(0xc4),_0x2c6f73,_0x163b61(),_0x50e896,_0x35405b)));},'autoTime':(_0x4915d6,_0xaaf0db,_0x4c1f1e)=>{_0x3ffb36(_0x4c1f1e);},'autoTimeEnd':(_0x397624,_0x436d57,_0x47b9b8)=>{_0x4ce4d2(_0x436d57,_0x47b9b8);},'coverage':_0x45a646=>{var _0x1e9860=_0x543ef9;_0x593a90({'method':_0x1e9860(0x13a),'version':_0x296e29,'args':[{'id':_0x45a646}]});}};let _0x593a90=H(_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x593945,_0x42f609),_0x50e896=_0x12a02f[_0x543ef9(0xd8)];return _0x12a02f[_0x543ef9(0xdb)];})(globalThis,'127.0.0.1',_0x418f23(0xec),_0x418f23(0x17a),_0x418f23(0x183),'1.0.0','1751470503250',_0x418f23(0x137),_0x418f23(0x18a),_0x418f23(0x10a),_0x418f23(0xb1));\");\n  } catch (e) {}\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_oo(\n/**@type{any}**/\ni) {\n  for (var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    v[_key - 1] = arguments[_key];\n  }\n\n  try {\n    oo_cm().consoleLog(i, v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_tr(\n/**@type{any}**/\ni) {\n  for (var _len2 = arguments.length, v = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    v[_key2 - 1] = arguments[_key2];\n  }\n\n  try {\n    oo_cm().consoleTrace(i, v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_tx(\n/**@type{any}**/\ni) {\n  for (var _len3 = arguments.length, v = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n    v[_key3 - 1] = arguments[_key3];\n  }\n\n  try {\n    oo_cm().consoleError(i, v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_ts(\n/**@type{any}**/\nv) {\n  try {\n    oo_cm().consoleTime(v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_te(\n/**@type{any}**/\nv,\n/**@type{any}**/\ni) {\n  try {\n    oo_cm().consoleTimeEnd(v, i);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/\n\nvar _c;\n\n$RefreshReg$(_c, \"StoresTab\");", "map": {"version": 3, "sources": ["D:/www/SWF/sian-web-frontend/src/views/commercial/salesDashboard/components/StoresTab.jsx"], "names": ["React", "useState", "useEffect", "useRef", "Box", "<PERSON><PERSON>", "ButtonGroup", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Typography", "ToggleButton", "ToggleButtonGroup", "<PERSON><PERSON>", "MenuItem", "TextField", "IconButton", "Autocomplete", "CircularProgress", "Backdrop", "<PERSON><PERSON><PERSON>", "MuiTooltip", "Accordion", "AccordionSummary", "AccordionDetails", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "ResponsiveContainer", "Legend", "axios", "getSalesDashboardPromise", "getSalesDashboardGoalsPromise", "ParticipacionesSection", "ParticipacionesTitle", "StyledTableCell", "StyledTableRow", "TotalRow", "DivisionRow", "LineDetailsTable", "ChartsContainer", "ChartContainer", "FiltersWrapper", "ChartTitle", "FilterGroup", "FilterChip", "CompactDatePicker", "TimeSelect", "StoreSelect", "HeatMapLegend", "getHeatMapColor", "getHeatMapColors", "COLORS", "LocalizationProvider", "AdapterDateFns", "es", "FilterListIcon", "DownloadIcon", "ExpandMoreIcon", "UnfoldMoreIcon", "UnfoldLessIcon", "XLSX", "AIChat", "format", "CalendarViewDayIcon", "CalendarViewWeekIcon", "StoresTab", "salesData", "setSalesData", "goalsData", "setGoalsData", "loading", "setLoading", "selectedStore", "setSelectedStore", "selectedLineNames", "setSelectedLineNames", "selectedRealLineNames", "setSelectedRealLineNames", "date<PERSON><PERSON><PERSON>", "setDateRange", "startTime", "setStartTime", "endTime", "setEndTime", "availableDates", "setAvailableDates", "apiMode", "setApiMode", "apiModeGoals", "setApiModeGoals", "viewMode", "setViewMode", "filterAnchorEl", "setFilterAnchorEl", "availableLineNames", "setAvailableLineNames", "availableRealLineNames", "setAvailableRealLineNames", "expandedDivisions", "setExpandedDivisions", "expandedLines", "setExpandedLines", "processedDataRef", "hourChartRef", "divisionChartRef", "storeChartRef", "dayChart<PERSON>ef", "fetchGoalsData", "response", "data", "error", "console", "oo_tx", "fetchSalesData", "arrayData", "log", "oo_oo", "get", "dates", "Set", "map", "sale", "emission_date", "sort", "lines", "division_name", "realLines", "line_name", "today", "Date", "setHours", "firstDayOfMonth", "getFullYear", "getMonth", "setTimeout", "length", "handleFilterClick", "event", "currentTarget", "handleFilterClose", "handleDateRangePreset", "preset", "start", "end", "setDate", "getDate", "handleTimeChange", "type", "newValue", "target", "value", "hours", "minutes", "split", "Number", "timeValue", "handleLineNameChange", "handleRealLineNameChange", "getFilteredData", "filter", "saleDate", "saleHour", "register_date", "getHours", "startDate", "endDate", "storeMatch", "store_name", "lineMatch", "includes", "realLineMatch", "timeMatch", "parseInt", "dateMatch", "getFilteredDataWithoutStore", "getWeekNumber", "date", "firstDayOfYear", "pastDaysOfYear", "Math", "ceil", "getDay", "processData", "filteredData", "allFilteredData", "salesByStore", "allSalesByStore", "salesByLine", "salesByRealLine", "salesByHour", "salesByDay", "salesByWeek", "salesByStoreAndLine", "totalSales", "toISOString", "getISOWeekNumber", "d", "yearStart", "for<PERSON>ach", "saleTotal", "parseFloat", "total", "sublines", "subline_name", "name", "todaySales", "hour", "<PERSON><PERSON><PERSON>", "toString", "padStart", "formattedDate", "dateParts", "year", "month", "day", "weekNumber", "yearWeek", "sortedLines", "Object", "entries", "percentage", "realName", "lineData", "sublineName", "a", "b", "storesArray", "values", "store", "storeGoals", "find", "g", "storeData", "monthlyGoal", "period_goal", "dailyGoal", "today_goal", "monthlyProgress", "toFixed", "dailyProgress", "storeLineData", "lineInfo", "lineName", "allStoresArray", "linesSummary", "keys", "storeLines", "stores", "allStores", "slice", "allLines", "linesByStore", "formattedValue", "formatCurrency", "hourA", "hourB", "days", "yearA", "monthA", "dayA", "num", "yearB", "monthB", "dayB", "weeks", "weekPart", "weekNum", "firstDayOfWeek", "dayOfWeek", "lastDayOfWeek", "formatDate", "week", "firstDay", "lastDay", "getProcessedData", "current", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "dateStr", "String", "CustomTooltip", "active", "payload", "label", "backgroundColor", "padding", "border", "borderRadius", "downloadChart", "chartRef", "title", "svgElement", "container", "children", "svgData", "XMLSerializer", "serializeToString", "canvas", "document", "createElement", "ctx", "getContext", "img", "Image", "boundingBox", "getBoundingClientRect", "width", "height", "onload", "fillStyle", "fillRect", "drawImage", "pngFile", "toDataURL", "downloadLink", "download", "replace", "href", "click", "src", "btoa", "unescape", "encodeURIComponent", "exportLinesSummaryToExcel", "workbook", "utils", "book_new", "headers", "line", "row", "Tiendas", "totalRow", "reduce", "sum", "exportData", "worksheet", "json_to_sheet", "header", "range", "decode_range", "columnWidths", "i", "e", "c", "C", "R", "r", "cellAddress", "encode_cell", "v", "z", "book_append_sheet", "writeFile", "exportLinesTableToExcel", "push", "División", "Lín<PERSON>", "Sublínea", "Po<PERSON>entaj<PERSON>", "realLine", "subline", "ws", "wb", "isMonday", "is<PERSON><PERSON><PERSON>", "getMixedViewData", "mixedData", "weekStartDate", "weekEndDate", "weekTotal", "j", "incompleteWeekStartDate", "incompleteWeekEndDate", "incompleteWeekTotal", "handleViewChange", "newView", "handleStoreClick", "storeName", "toggleAllLines", "divisionName", "stopPropagation", "preventDefault", "processedData", "division", "lineKeys", "anyLineOpen", "some", "key", "newExpandedLines", "prev", "handleDivisionExpand", "isExpanded", "lineKey", "handleLineExpand", "isAnyLineExpanded", "color", "zIndex", "theme", "drawer", "display", "flexDirection", "alignItems", "gap", "fontWeight", "isNaN", "getTime", "adjusted", "params", "borderColor", "shrink", "flex", "min<PERSON><PERSON><PERSON>", "selected", "getTagProps", "option", "index", "onDelete", "Boolean", "sx", "mt", "boxShadow", "fontSize", "newStore", "marginBottom", "justifyContent", "mb", "mr", "top", "right", "left", "bottom", "parts", "firstWeekday", "firstDate", "lastDate", "position", "formatter", "entry", "totalSum", "acc", "curr", "exactPercentage", "isLastItem", "otherPercentages", "round", "fillColor", "fill", "props", "content", "cx", "cy", "midAngle", "innerRadius", "outerRadius", "RADIAN", "PI", "radius", "x", "cos", "y", "sin", "marginRight", "word", "char<PERSON>t", "join", "toUpperCase", "_", "bgcolor", "minHeight", "margin", "p", "borderBottom", "cursor", "textDecoration", "isLineExpanded", "oo_cm", "eval", "consoleLog", "oo_tr", "consoleTrace", "consoleError", "oo_ts", "consoleTime", "oo_te", "consoleTimeEnd"], "mappings": ";;;AAAA;AACA,OAAOA,KAAP,IAAgBC,QAAhB,EAA0BC,SAA1B,EAAqCC,MAArC,QAAmD,OAAnD;AACA,SACIC,GADJ,EAEIC,MAFJ,EAGIC,WAHJ,EAIIC,KAJJ,EAKIC,SALJ,EAMIC,SANJ,EAOIC,cAPJ,EAQIC,SARJ,EASIC,QATJ,EAUIC,KAVJ,EAWIC,UAXJ,EAYIC,YAZJ,EAaIC,iBAbJ,EAcIC,IAdJ,EAeIC,QAfJ,EAgBIC,SAhBJ,EAiBIC,UAjBJ,EAkBIC,YAlBJ,EAmBIC,gBAnBJ,EAoBIC,QApBJ,EAqBIC,OAAO,IAAIC,UArBf,EAsBIC,SAtBJ,EAuBIC,gBAvBJ,EAwBIC,gBAxBJ,QAyBO,eAzBP;AA0BA,SAASC,QAAT,EAAmBC,GAAnB,EAAwBC,IAAxB,EAA8BC,QAA9B,EAAwCC,GAAxC,EAA6CC,KAA7C,EAAoDC,KAApD,EAA2DC,aAA3D,EAA0EC,mBAA1E,EAA+Fb,OAA/F,EAAwGc,MAAxG,QAAsH,UAAtH;AACA,OAAOC,KAAP,MAAkB,OAAlB;AACA,SAASC,wBAAT,EAAmCC,6BAAnC,QAAwE,yBAAxE;AACA,SAASC,sBAAT,QAAuC,iCAAvC;AACA,SAASC,oBAAT,QAAqC,+BAArC;AACA,SAASC,eAAT,QAAgC,0BAAhC;AACA,SAASC,cAAT,QAA+B,yBAA/B;AACA,SAASC,QAAT,QAAyB,mBAAzB;AACA,SAASC,WAAT,QAA4B,sBAA5B;AACA,SAASC,gBAAT,QAAiC,2BAAjC;AACA,SAASC,eAAT,QAAgC,0BAAhC;AACA,SAASC,cAAT,QAA+B,yBAA/B;AACA,SAASC,cAAT,QAA+B,yBAA/B;AACA,SAASC,UAAT,QAA2B,qBAA3B;AACA,SAASC,WAAT,QAA4B,sBAA5B;AACA,SAASC,UAAT,QAA2B,qBAA3B;AACA,SAASC,iBAAT,QAAkC,4BAAlC;AACA,SAASC,UAAT,QAA2B,qBAA3B;AACA,SAASC,WAAT,QAA4B,sBAA5B;AACA,SAASC,aAAT,QAA8B,wBAA9B;AACA,SAASC,eAAT,EAA0BC,gBAA1B,QAAkD,oBAAlD;AACA,SAASC,MAAT,QAAuB,iBAAvB;AACA,SAASC,oBAAT,QAAqC,qBAArC;AACA,SAASC,cAAT,QAA+B,oCAA/B;AACA,SAASC,EAAT,QAAmB,iBAAnB;AACA,OAAOC,cAAP,MAA2B,gCAA3B;AACA,OAAOC,YAAP,MAAyB,8BAAzB;AACA,OAAOC,cAAP,MAA2B,gCAA3B;AACA,OAAOC,cAAP,MAA2B,gCAA3B;AACA,OAAOC,cAAP,MAA2B,gCAA3B;AACA,OAAO,KAAKC,IAAZ,MAAsB,MAAtB;AACA,OAAOC,MAAP,MAAmB,UAAnB;AACA,SAASC,MAAT,QAAuB,UAAvB;AACA,OAAOC,mBAAP,MAAgC,qCAAhC;AACA,OAAOC,oBAAP,MAAiC,sCAAjC;;;;AAEA,MAAMC,SAAS,GAAG,MAAM;AAAA;;AACpB,QAAM,CAACC,SAAD,EAAYC,YAAZ,IAA4B5E,QAAQ,CAAC,EAAD,CAA1C;AACA,QAAM,CAAC6E,SAAD,EAAYC,YAAZ,IAA4B9E,QAAQ,CAAC,EAAD,CAA1C;AACA,QAAM,CAAC+E,OAAD,EAAUC,UAAV,IAAwBhF,QAAQ,CAAC,IAAD,CAAtC;AACA,QAAM,CAACiF,aAAD,EAAgBC,gBAAhB,IAAoClF,QAAQ,CAAC,KAAD,CAAlD;AACA,QAAM,CAACmF,iBAAD,EAAoBC,oBAApB,IAA4CpF,QAAQ,CAAC,EAAD,CAA1D;AACA,QAAM,CAACqF,qBAAD,EAAwBC,wBAAxB,IAAoDtF,QAAQ,CAAC,EAAD,CAAlE;AACA,QAAM,CAACuF,SAAD,EAAYC,YAAZ,IAA4BxF,QAAQ,CAAC,CAAC,IAAD,EAAO,IAAP,CAAD,CAA1C;AACA,QAAM,CAACyF,SAAD,EAAYC,YAAZ,IAA4B1F,QAAQ,CAAC,OAAD,CAA1C;AACA,QAAM,CAAC2F,OAAD,EAAUC,UAAV,IAAwB5F,QAAQ,CAAC,OAAD,CAAtC;AACA,QAAM,CAAC6F,cAAD,EAAiBC,iBAAjB,IAAsC9F,QAAQ,CAAC,EAAD,CAApD;AACA,QAAM,CAAC+F,OAAD,EAAUC,UAAV,IAAwBhG,QAAQ,CAAC,CAAD,CAAtC;AACA,QAAM,CAACiG,YAAD,EAAeC,eAAf,IAAkClG,QAAQ,CAAC,CAAD,CAAhD;AACA,QAAM,CAACmG,QAAD,EAAWC,WAAX,IAA0BpG,QAAQ,CAAC,OAAD,CAAxC;AACA,QAAM,CAACqG,cAAD,EAAiBC,iBAAjB,IAAsCtG,QAAQ,CAAC,IAAD,CAApD;AACA,QAAM,CAACuG,kBAAD,EAAqBC,qBAArB,IAA8CxG,QAAQ,CAAC,EAAD,CAA5D;AACA,QAAM,CAACyG,sBAAD,EAAyBC,yBAAzB,IAAsD1G,QAAQ,CAAC,EAAD,CAApE,CAhBoB,CAkBpB;;AACA,QAAM,CAAC2G,iBAAD,EAAoBC,oBAApB,IAA4C5G,QAAQ,CAAC,EAAD,CAA1D;AACA,QAAM,CAAC6G,aAAD,EAAgBC,gBAAhB,IAAoC9G,QAAQ,CAAC,EAAD,CAAlD,CApBoB,CAqBpB;;AACA,QAAM+G,gBAAgB,GAAG7G,MAAM,CAAC,IAAD,CAA/B;AAEA,QAAM8G,YAAY,GAAG9G,MAAM,CAAC,IAAD,CAA3B;AACA,QAAM+G,gBAAgB,GAAG/G,MAAM,CAAC,IAAD,CAA/B;AACA,QAAMgH,aAAa,GAAGhH,MAAM,CAAC,IAAD,CAA5B;AACA,QAAMiH,WAAW,GAAGjH,MAAM,CAAC,IAAD,CAA1B;AAEAD,EAAAA,SAAS,CAAC,MAAM;AACZ,UAAMmH,cAAc,GAAG,YAAY;AAC/BpC,MAAAA,UAAU,CAAC,IAAD,CAAV;;AACA,UAAI;AACA,YAAIqC,QAAJ;;AACA,YAAIpB,YAAY,KAAK,CAArB,EAAwB;AACpBoB,UAAAA,QAAQ,GAAG,MAAM7E,6BAA6B,EAA9C;AACAsC,UAAAA,YAAY,CAACuC,QAAQ,CAACC,IAAV,CAAZ;AACH;AACJ,OAND,CAME,OAAOC,KAAP,EAAc;AACZ;AAAoBC,QAAAA,OAAO,CAACD,KAAR,CAAc,GAAGE,KAAK,CAAE,4BAAF,EAA8B,4BAA9B,EAA4DF,KAA5D,CAAtB;;AACpB,YAAItB,YAAY,KAAK,CAArB,EAAwB;AACpBC,UAAAA,eAAe,CAAC,CAAD,CAAf;AACH;AACJ;AACJ,KAdD;;AAgBAkB,IAAAA,cAAc;AACjB,GAlBQ,EAkBN,CAACnB,YAAD,CAlBM,CAAT;AAoBAhG,EAAAA,SAAS,CAAC,MAAM;AACZ,UAAMyH,cAAc,GAAG,YAAY;AAC/B,UAAI;AACA,YAAIL,QAAJ;AACA,YAAIM,SAAS,GAAG,EAAhB;;AACA,YAAI5B,OAAO,KAAK,CAAhB,EAAmB;AACfsB,UAAAA,QAAQ,GAAG,MAAM9E,wBAAwB,EAAzC;AACA;;AAAoBiF,UAAAA,OAAO,CAACI,GAAR,CAAY,GAAGC,KAAK,CAAE,2BAAF,EAA6B,iCAA7B,EAAgER,QAAQ,CAACC,IAAzE,CAApB;AACpBK,UAAAA,SAAS,GAAGN,QAAQ,CAACC,IAArB;AACA1C,UAAAA,YAAY,CAACyC,QAAQ,CAACC,IAAV,CAAZ;AACH,SALD,MAKO;AACHD,UAAAA,QAAQ,GAAG,MAAM/E,KAAK,CAACwF,GAAN,CAAU,iEAAV,CAAjB;AACA;;AAAoBN,UAAAA,OAAO,CAACI,GAAR,CAAY,GAAGC,KAAK,CAAE,2BAAF,EAA6B,uBAA7B,EAAsDR,QAAQ,CAACC,IAAT,CAAcA,IAApE,CAApB;AACpBK,UAAAA,SAAS,GAAGN,QAAQ,CAACC,IAAT,CAAcA,IAA1B;AACA1C,UAAAA,YAAY,CAACyC,QAAQ,CAACC,IAAT,CAAcA,IAAf,CAAZ;AACH,SAbD,CAcA;;;AACA,cAAMS,KAAK,GAAG,CAAC,GAAG,IAAIC,GAAJ,CAAQL,SAAS,CAACM,GAAV,CAAeC,IAAD,IAAUA,IAAI,CAACC,aAA7B,CAAR,CAAJ,EAA0DC,IAA1D,EAAd;AACAtC,QAAAA,iBAAiB,CAACiC,KAAD,CAAjB,CAhBA,CAkBA;;AACA,cAAMM,KAAK,GAAG,CAAC,GAAG,IAAIL,GAAJ,CAAQL,SAAS,CAACM,GAAV,CAAeC,IAAD,IAAUA,IAAI,CAACI,aAA7B,CAAR,CAAJ,EAA0DF,IAA1D,EAAd;AACA5B,QAAAA,qBAAqB,CAAC6B,KAAD,CAArB,CApBA,CAsBA;;AACA,cAAME,SAAS,GAAG,CAAC,GAAG,IAAIP,GAAJ,CAAQL,SAAS,CAACM,GAAV,CAAeC,IAAD,IAAUA,IAAI,CAACM,SAA7B,CAAR,CAAJ,EAAsDJ,IAAtD,EAAlB;AACA1B,QAAAA,yBAAyB,CAAC6B,SAAD,CAAzB,CAxBA,CA0BA;;AACA,cAAME,KAAK,GAAG,IAAIC,IAAJ,EAAd;AACAD,QAAAA,KAAK,CAACE,QAAN,CAAe,EAAf,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB;AACA,cAAMC,eAAe,GAAG,IAAIF,IAAJ,CAASD,KAAK,CAACI,WAAN,EAAT,EAA8BJ,KAAK,CAACK,QAAN,EAA9B,EAAgD,CAAhD,CAAxB;AACAF,QAAAA,eAAe,CAACD,QAAhB,CAAyB,EAAzB,EAA6B,CAA7B,EAAgC,CAAhC,EAAmC,CAAnC;AACAnD,QAAAA,YAAY,CAAC,CAACoD,eAAD,EAAkBH,KAAlB,CAAD,CAAZ;AACH,OAhCD,CAgCE,OAAOlB,KAAP,EAAc;AACZ;AAAoBC,QAAAA,OAAO,CAACD,KAAR,CAAc,GAAGE,KAAK,CAAE,4BAAF,EAA8B,4BAA9B,EAA4DF,KAA5D,CAAtB;;AACpB,YAAIxB,OAAO,KAAK,CAAhB,EAAmB;AACfC,UAAAA,UAAU,CAAC,CAAD,CAAV;AACH;AACJ,OArCD,SAqCU;AACN+C,QAAAA,UAAU,CAAC,MAAM;AACb/D,UAAAA,UAAU,CAAC,KAAD,CAAV;AACH,SAFS,EAEP,GAFO,CAAV;AAGH;AACJ,KA3CD;;AA6CA,QAAIH,SAAS,CAACmE,MAAV,GAAmB,CAAvB,EAA0B;AACtBtB,MAAAA,cAAc;AACjB;AACJ,GAjDQ,EAiDN,CAAC3B,OAAD,EAAUlB,SAAV,CAjDM,CAAT;;AAmDA,QAAMoE,iBAAiB,GAAIC,KAAD,IAAW;AACjC5C,IAAAA,iBAAiB,CAAC4C,KAAK,CAACC,aAAP,CAAjB;AACH,GAFD;;AAIA,QAAMC,iBAAiB,GAAG,MAAM;AAC5B9C,IAAAA,iBAAiB,CAAC,IAAD,CAAjB;AACH,GAFD;;AAIA,QAAM+C,qBAAqB,GAAIC,MAAD,IAAY;AACtC,UAAMb,KAAK,GAAG,IAAIC,IAAJ,EAAd;AACA,QAAIa,KAAK,GAAG,IAAIb,IAAJ,EAAZ;AACA,QAAIc,GAAG,GAAG,IAAId,IAAJ,EAAV;;AAEA,YAAQY,MAAR;AACI,WAAK,OAAL;AACIC,QAAAA,KAAK,GAAGd,KAAR;AACAe,QAAAA,GAAG,GAAGf,KAAN;AACA;;AACJ,WAAK,WAAL;AACIc,QAAAA,KAAK,CAACE,OAAN,CAAchB,KAAK,CAACiB,OAAN,KAAkB,CAAhC;AACAF,QAAAA,GAAG,GAAGD,KAAN;AACA;;AACJ,WAAK,OAAL;AACIA,QAAAA,KAAK,CAACE,OAAN,CAAchB,KAAK,CAACiB,OAAN,KAAkB,CAAhC;AACAF,QAAAA,GAAG,GAAGf,KAAN;AACA;;AACJ,WAAK,QAAL;AACIc,QAAAA,KAAK,CAACE,OAAN,CAAchB,KAAK,CAACiB,OAAN,KAAkB,EAAhC;AACAF,QAAAA,GAAG,GAAGf,KAAN;AACA;;AACJ;AACI;AAlBR;;AAqBAjD,IAAAA,YAAY,CAAC,CAAC+D,KAAD,EAAQC,GAAR,CAAD,CAAZ;AACAJ,IAAAA,iBAAiB;AACpB,GA5BD;;AA8BA,QAAMO,gBAAgB,GAAG,CAACC,IAAD,EAAOV,KAAP,KAAiB;AACtC,UAAMW,QAAQ,GAAGX,KAAK,CAACY,MAAN,CAAaC,KAA9B;;AACA,QAAIH,IAAI,KAAK,OAAb,EAAsB;AAClBlE,MAAAA,YAAY,CAACmE,QAAD,CAAZ;AACH,KAFD,MAEO;AACHjE,MAAAA,UAAU,CAACiE,QAAD,CAAV;AACH,KANqC,CAQtC;;;AACA,UAAM,CAACG,KAAD,EAAQC,OAAR,IAAmBJ,QAAQ,CAACK,KAAT,CAAe,GAAf,EAAoBjC,GAApB,CAAwBkC,MAAxB,CAAzB;AACA,UAAMC,SAAS,GAAGJ,KAAK,GAAGC,OAAO,GAAG,EAApC,CAVsC,CAYtC;AACH,GAbD;;AAeA,QAAMI,oBAAoB,GAAG,CAACnB,KAAD,EAAQW,QAAR,KAAqB;AAC9CzE,IAAAA,oBAAoB,CAACyE,QAAD,CAApB;AACH,GAFD;;AAIA,QAAMS,wBAAwB,GAAG,CAACpB,KAAD,EAAQW,QAAR,KAAqB;AAClDvE,IAAAA,wBAAwB,CAACuE,QAAD,CAAxB;AACH,GAFD;;AAIA,QAAMU,eAAe,GAAG,MAAM;AAC1B,QAAI,CAAChF,SAAS,CAAC,CAAD,CAAV,IAAiB,CAACA,SAAS,CAAC,CAAD,CAA/B,EAAoC,OAAO,EAAP;AAEpC,WAAOZ,SAAS,CAAC6F,MAAV,CAAkBtC,IAAD,IAAU;AAC9B;AACA,YAAMuC,QAAQ,GAAG,IAAI/B,IAAJ,CAASR,IAAI,CAACC,aAAL,GAAqB,WAA9B,CAAjB;AACA,YAAMuC,QAAQ,GAAG,IAAIhC,IAAJ,CAASR,IAAI,CAACyC,aAAd,EAA6BC,QAA7B,EAAjB,CAH8B,CAK9B;;AACA,YAAMC,SAAS,GAAG,IAAInC,IAAJ,CAASnD,SAAS,CAAC,CAAD,CAAlB,CAAlB;AACAsF,MAAAA,SAAS,CAAClC,QAAV,CAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B;AAEA,YAAMmC,OAAO,GAAG,IAAIpC,IAAJ,CAASnD,SAAS,CAAC,CAAD,CAAlB,CAAhB;AACAuF,MAAAA,OAAO,CAACnC,QAAR,CAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB,EAA6B,GAA7B;AAEA,YAAMoC,UAAU,GAAG9F,aAAa,KAAK,KAAlB,IAA2BiD,IAAI,CAAC8C,UAAL,KAAoB/F,aAAlE;AACA,YAAMgG,SAAS,GAAG9F,iBAAiB,CAAC6D,MAAlB,KAA6B,CAA7B,IAAkC7D,iBAAiB,CAAC+F,QAAlB,CAA2BhD,IAAI,CAACI,aAAhC,CAApD;AACA,YAAM6C,aAAa,GAAG9F,qBAAqB,CAAC2D,MAAtB,KAAiC,CAAjC,IAAsC3D,qBAAqB,CAAC6F,QAAtB,CAA+BhD,IAAI,CAACM,SAApC,CAA5D;AACA,YAAM4C,SAAS,GAAGV,QAAQ,IAAIW,QAAQ,CAAC5F,SAAS,CAACyE,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAD,CAApB,IAAiDQ,QAAQ,IAAIW,QAAQ,CAAC1F,OAAO,CAACuE,KAAR,CAAc,GAAd,EAAmB,CAAnB,CAAD,CAAvF;AACA,YAAMoB,SAAS,GAAGb,QAAQ,IAAII,SAAZ,IAAyBJ,QAAQ,IAAIK,OAAvD;AAEA,aAAOC,UAAU,IAAIO,SAAd,IAA2BF,SAA3B,IAAwCH,SAAxC,IAAqDE,aAA5D;AACH,KAnBM,CAAP;AAoBH,GAvBD;;AAyBA,QAAMI,2BAA2B,GAAG,MAAM;AACtC,QAAI,CAAChG,SAAS,CAAC,CAAD,CAAV,IAAiB,CAACA,SAAS,CAAC,CAAD,CAA/B,EAAoC,OAAO,EAAP;AAEpC,WAAOZ,SAAS,CAAC6F,MAAV,CAAkBtC,IAAD,IAAU;AAC9B;AACA,YAAMuC,QAAQ,GAAG,IAAI/B,IAAJ,CAASR,IAAI,CAACC,aAAL,GAAqB,WAA9B,CAAjB;AACA,YAAMuC,QAAQ,GAAG,IAAIhC,IAAJ,CAASR,IAAI,CAACyC,aAAd,EAA6BC,QAA7B,EAAjB,CAH8B,CAK9B;;AACA,YAAMC,SAAS,GAAG,IAAInC,IAAJ,CAASnD,SAAS,CAAC,CAAD,CAAlB,CAAlB;AACAsF,MAAAA,SAAS,CAAClC,QAAV,CAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B;AAEA,YAAMmC,OAAO,GAAG,IAAIpC,IAAJ,CAASnD,SAAS,CAAC,CAAD,CAAlB,CAAhB;AACAuF,MAAAA,OAAO,CAACnC,QAAR,CAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB,EAA6B,GAA7B;AAEA,YAAMsC,SAAS,GAAG9F,iBAAiB,CAAC6D,MAAlB,KAA6B,CAA7B,IAAkC7D,iBAAiB,CAAC+F,QAAlB,CAA2BhD,IAAI,CAACI,aAAhC,CAApD;AACA,YAAM6C,aAAa,GAAG9F,qBAAqB,CAAC2D,MAAtB,KAAiC,CAAjC,IAAsC3D,qBAAqB,CAAC6F,QAAtB,CAA+BhD,IAAI,CAACM,SAApC,CAA5D;AACA,YAAM4C,SAAS,GAAGV,QAAQ,IAAIW,QAAQ,CAAC5F,SAAS,CAACyE,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAD,CAApB,IAAiDQ,QAAQ,IAAIW,QAAQ,CAAC1F,OAAO,CAACuE,KAAR,CAAc,GAAd,EAAmB,CAAnB,CAAD,CAAvF;AACA,YAAMoB,SAAS,GAAGb,QAAQ,IAAII,SAAZ,IAAyBJ,QAAQ,IAAIK,OAAvD;AAEA,aAAOQ,SAAS,IAAIF,SAAb,IAA0BH,SAA1B,IAAuCE,aAA9C;AACH,KAlBM,CAAP;AAmBH,GAtBD,CA1LoB,CAkNpB;;;AACA,QAAMK,aAAa,GAAIC,IAAD,IAAU;AAC5B,UAAMC,cAAc,GAAG,IAAIhD,IAAJ,CAAS+C,IAAI,CAAC5C,WAAL,EAAT,EAA6B,CAA7B,EAAgC,CAAhC,CAAvB;AACA,UAAM8C,cAAc,GAAG,CAACF,IAAI,GAAGC,cAAR,IAA0B,QAAjD;AACA,WAAOE,IAAI,CAACC,IAAL,CAAU,CAACF,cAAc,GAAGD,cAAc,CAACI,MAAf,EAAjB,GAA2C,CAA5C,IAAiD,CAA3D,CAAP;AACH,GAJD;;AAMA,QAAMC,WAAW,GAAG,MAAM;AACtB,UAAMC,YAAY,GAAGzB,eAAe,EAApC;AACA,UAAM0B,eAAe,GAAGV,2BAA2B,EAAnD;AACA,UAAMW,YAAY,GAAG,EAArB;AACA,UAAMC,eAAe,GAAG,EAAxB;AACA,UAAMC,WAAW,GAAG,EAApB;AACA,UAAMC,eAAe,GAAG,EAAxB;AACA,UAAMC,WAAW,GAAG,EAApB;AACA,UAAMC,UAAU,GAAG,EAAnB;AACA,UAAMC,WAAW,GAAG,EAApB,CATsB,CAUtB;;AACA,UAAMC,mBAAmB,GAAG,EAA5B;AACA,QAAIC,UAAU,GAAG,CAAjB,CAZsB,CActB;;AACA,UAAMjE,KAAK,GAAG,IAAIC,IAAJ,GAAWiE,WAAX,GAAyBzC,KAAzB,CAA+B,GAA/B,EAAoC,CAApC,CAAd,CAfsB,CAiBtB;;AACA,UAAM0C,gBAAgB,GAAInB,IAAD,IAAU;AAC/B,YAAMoB,CAAC,GAAG,IAAInE,IAAJ,CAAS+C,IAAT,CAAV;AACAoB,MAAAA,CAAC,CAAClE,QAAF,CAAW,CAAX,EAAc,CAAd,EAAiB,CAAjB,EAAoB,CAApB,EAF+B,CAG/B;;AACAkE,MAAAA,CAAC,CAACpD,OAAF,CAAUoD,CAAC,CAACnD,OAAF,KAAc,CAAd,IAAmBmD,CAAC,CAACf,MAAF,MAAc,CAAjC,CAAV,EAJ+B,CAK/B;;AACA,YAAMgB,SAAS,GAAG,IAAIpE,IAAJ,CAASmE,CAAC,CAAChE,WAAF,EAAT,EAA0B,CAA1B,EAA6B,CAA7B,CAAlB,CAN+B,CAO/B;;AACA,aAAO+C,IAAI,CAACC,IAAL,CAAU,CAAC,CAACgB,CAAC,GAAGC,SAAL,IAAkB,QAAlB,GAA6B,CAA9B,IAAmC,CAA7C,CAAP;AACH,KATD,CAlBsB,CA6BtB;;;AACAd,IAAAA,YAAY,CAACe,OAAb,CAAsB7E,IAAD,IAAU;AAC3B,YAAMuC,QAAQ,GAAGvC,IAAI,CAACC,aAAtB;AACA,YAAM6E,SAAS,GAAGC,UAAU,CAAC/E,IAAI,CAACgF,KAAN,CAA5B;AACAR,MAAAA,UAAU,IAAIM,SAAd,CAH2B,CAK3B;;AACA,UAAI,CAACZ,WAAW,CAAClE,IAAI,CAACI,aAAN,CAAhB,EAAsC;AAClC8D,QAAAA,WAAW,CAAClE,IAAI,CAACI,aAAN,CAAX,GAAkC;AAC9B4E,UAAAA,KAAK,EAAE,CADuB;AAE9B3E,UAAAA,SAAS,EAAE;AAFmB,SAAlC;AAIH;;AACD6D,MAAAA,WAAW,CAAClE,IAAI,CAACI,aAAN,CAAX,CAAgC4E,KAAhC,IAAyCF,SAAzC;;AAEA,UAAI,CAACZ,WAAW,CAAClE,IAAI,CAACI,aAAN,CAAX,CAAgCC,SAAhC,CAA0CL,IAAI,CAACM,SAA/C,CAAL,EAAgE;AAC5D4D,QAAAA,WAAW,CAAClE,IAAI,CAACI,aAAN,CAAX,CAAgCC,SAAhC,CAA0CL,IAAI,CAACM,SAA/C,IAA4D;AACxD0E,UAAAA,KAAK,EAAE,CADiD;AAExDC,UAAAA,QAAQ,EAAE;AAF8C,SAA5D;AAIH;;AACDf,MAAAA,WAAW,CAAClE,IAAI,CAACI,aAAN,CAAX,CAAgCC,SAAhC,CAA0CL,IAAI,CAACM,SAA/C,EAA0D0E,KAA1D,IAAmEF,SAAnE,CApB2B,CAsB3B;;AACA,UAAI,CAACZ,WAAW,CAAClE,IAAI,CAACI,aAAN,CAAX,CAAgCC,SAAhC,CAA0CL,IAAI,CAACM,SAA/C,EAA0D2E,QAA1D,CAAmEjF,IAAI,CAACkF,YAAxE,CAAL,EAA4F;AACxFhB,QAAAA,WAAW,CAAClE,IAAI,CAACI,aAAN,CAAX,CAAgCC,SAAhC,CAA0CL,IAAI,CAACM,SAA/C,EAA0D2E,QAA1D,CAAmEjF,IAAI,CAACkF,YAAxE,IAAwF,CAAxF;AACH;;AACDhB,MAAAA,WAAW,CAAClE,IAAI,CAACI,aAAN,CAAX,CAAgCC,SAAhC,CAA0CL,IAAI,CAACM,SAA/C,EAA0D2E,QAA1D,CAAmEjF,IAAI,CAACkF,YAAxE,KAAyFJ,SAAzF,CA1B2B,CA4B3B;;AACA,UAAI,CAACP,mBAAmB,CAACvE,IAAI,CAAC8C,UAAN,CAAxB,EAA2C;AACvCyB,QAAAA,mBAAmB,CAACvE,IAAI,CAAC8C,UAAN,CAAnB,GAAuC,EAAvC;AACH;;AACD,UAAI,CAACyB,mBAAmB,CAACvE,IAAI,CAAC8C,UAAN,CAAnB,CAAqC9C,IAAI,CAACI,aAA1C,CAAL,EAA+D;AAC3DmE,QAAAA,mBAAmB,CAACvE,IAAI,CAAC8C,UAAN,CAAnB,CAAqC9C,IAAI,CAACI,aAA1C,IAA2D,CAA3D;AACH;;AACDmE,MAAAA,mBAAmB,CAACvE,IAAI,CAAC8C,UAAN,CAAnB,CAAqC9C,IAAI,CAACI,aAA1C,KAA4D0E,SAA5D;;AAEA,UAAI,CAACd,YAAY,CAAChE,IAAI,CAAC8C,UAAN,CAAjB,EAAoC;AAChCkB,QAAAA,YAAY,CAAChE,IAAI,CAAC8C,UAAN,CAAZ,GAAgC;AAC5BqC,UAAAA,IAAI,EAAEnF,IAAI,CAAC8C,UADiB;AAE5BkC,UAAAA,KAAK,EAAE,CAFqB;AAG5BI,UAAAA,UAAU,EAAE;AAHgB,SAAhC;AAKH;;AACDpB,MAAAA,YAAY,CAAChE,IAAI,CAAC8C,UAAN,CAAZ,CAA8BkC,KAA9B,IAAuCF,SAAvC;;AAEA,UAAIvC,QAAQ,KAAKhC,KAAjB,EAAwB;AACpByD,QAAAA,YAAY,CAAChE,IAAI,CAAC8C,UAAN,CAAZ,CAA8BsC,UAA9B,IAA4CN,SAA5C;AACH;;AAED,YAAMO,IAAI,GAAG,IAAI7E,IAAJ,CAASR,IAAI,CAACyC,aAAd,EAA6BC,QAA7B,EAAb;AACA,YAAM4C,OAAO,GAAI,GAAED,IAAI,CAACE,QAAL,GAAgBC,QAAhB,CAAyB,CAAzB,EAA4B,GAA5B,CAAiC,KAApD;;AACA,UAAI,CAACpB,WAAW,CAACkB,OAAD,CAAhB,EAA2B;AACvBlB,QAAAA,WAAW,CAACkB,OAAD,CAAX,GAAuB,CAAvB;AACH;;AACDlB,MAAAA,WAAW,CAACkB,OAAD,CAAX,IAAwBR,SAAxB,CAvD2B,CAyD3B;;AACA,YAAMW,aAAa,GAAGlD,QAAQ,CAACP,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAtB,CA1D2B,CA0DmB;;AAC9C,UAAI,CAACqC,UAAU,CAACoB,aAAD,CAAf,EAAgC;AAC5BpB,QAAAA,UAAU,CAACoB,aAAD,CAAV,GAA4B,CAA5B;AACH;;AACDpB,MAAAA,UAAU,CAACoB,aAAD,CAAV,IAA6BX,SAA7B,CA9D2B,CAgE3B;;AACA,YAAMY,SAAS,GAAGD,aAAa,CAACzD,KAAd,CAAoB,GAApB,CAAlB;AACA,YAAM2D,IAAI,GAAGxC,QAAQ,CAACuC,SAAS,CAAC,CAAD,CAAV,CAArB;AACA,YAAME,KAAK,GAAGzC,QAAQ,CAACuC,SAAS,CAAC,CAAD,CAAV,CAAR,GAAyB,CAAvC,CAnE2B,CAmEe;;AAC1C,YAAMG,GAAG,GAAG1C,QAAQ,CAACuC,SAAS,CAAC,CAAD,CAAV,CAApB,CApE2B,CAsE3B;;AACA,YAAMnC,IAAI,GAAG,IAAI/C,IAAJ,CAASmF,IAAT,EAAeC,KAAf,EAAsBC,GAAtB,EAA2B,EAA3B,EAA+B,CAA/B,EAAkC,CAAlC,CAAb;AACA,YAAMC,UAAU,GAAGpB,gBAAgB,CAACnB,IAAD,CAAnC;AACA,YAAMwC,QAAQ,GAAI,GAAEJ,IAAK,KAAIG,UAAU,CAACP,QAAX,GAAsBC,QAAtB,CAA+B,CAA/B,EAAkC,GAAlC,CAAuC,EAApE;;AAEA,UAAI,CAAClB,WAAW,CAACyB,QAAD,CAAhB,EAA4B;AACxBzB,QAAAA,WAAW,CAACyB,QAAD,CAAX,GAAwB,CAAxB;AACH;;AACDzB,MAAAA,WAAW,CAACyB,QAAD,CAAX,IAAyBjB,SAAzB;AACH,KA/ED,EA9BsB,CA+GtB;;AACAf,IAAAA,eAAe,CAACc,OAAhB,CAAyB7E,IAAD,IAAU;AAC9B,YAAMuC,QAAQ,GAAGvC,IAAI,CAACC,aAAtB;AACA,YAAM6E,SAAS,GAAGC,UAAU,CAAC/E,IAAI,CAACgF,KAAN,CAA5B;;AAEA,UAAI,CAACf,eAAe,CAACjE,IAAI,CAAC8C,UAAN,CAApB,EAAuC;AACnCmB,QAAAA,eAAe,CAACjE,IAAI,CAAC8C,UAAN,CAAf,GAAmC;AAC/BqC,UAAAA,IAAI,EAAEnF,IAAI,CAAC8C,UADoB;AAE/BkC,UAAAA,KAAK,EAAE,CAFwB;AAG/BI,UAAAA,UAAU,EAAE;AAHmB,SAAnC;AAKH;;AACDnB,MAAAA,eAAe,CAACjE,IAAI,CAAC8C,UAAN,CAAf,CAAiCkC,KAAjC,IAA0CF,SAA1C;;AAEA,UAAIvC,QAAQ,KAAKhC,KAAjB,EAAwB;AACpB0D,QAAAA,eAAe,CAACjE,IAAI,CAAC8C,UAAN,CAAf,CAAiCsC,UAAjC,IAA+CN,SAA/C;AACH;AACJ,KAhBD,EAhHsB,CAkItB;;AACA,UAAMkB,WAAW,GAAGC,MAAM,CAACC,OAAP,CAAehC,WAAf,EACfnE,GADe,CACX;AAAA,UAAC,CAACoF,IAAD,EAAO/F,IAAP,CAAD;AAAA,aAAmB;AACpB+F,QAAAA,IADoB;AAEpBtD,QAAAA,KAAK,EAAEzC,IAAI,CAAC4F,KAFQ;AAGpBmB,QAAAA,UAAU,EAAG/G,IAAI,CAAC4F,KAAL,GAAaR,UAAd,GAA4B,GAHpB;AAIpBnE,QAAAA,SAAS,EAAE4F,MAAM,CAACC,OAAP,CAAe9G,IAAI,CAACiB,SAApB,EACNN,GADM,CACF;AAAA,cAAC,CAACqG,QAAD,EAAWC,QAAX,CAAD;AAAA,iBAA2B;AAC5BlB,YAAAA,IAAI,EAAEiB,QADsB;AAE5BvE,YAAAA,KAAK,EAAEwE,QAAQ,CAACrB,KAFY;AAG5BmB,YAAAA,UAAU,EAAGE,QAAQ,CAACrB,KAAT,GAAiBR,UAAlB,GAAgC,GAHhB;AAI5BS,YAAAA,QAAQ,EAAEgB,MAAM,CAACC,OAAP,CAAeG,QAAQ,CAACpB,QAAxB,EACLlF,GADK,CACD;AAAA,kBAAC,CAACuG,WAAD,EAAczE,KAAd,CAAD;AAAA,qBAA2B;AAC5BsD,gBAAAA,IAAI,EAAEmB,WADsB;AAE5BzE,gBAAAA,KAF4B;AAG5BsE,gBAAAA,UAAU,EAAGtE,KAAK,GAAG2C,UAAT,GAAuB;AAHP,eAA3B;AAAA,aADC,EAMLtE,IANK,CAMA,CAACqG,CAAD,EAAIC,CAAJ,KAAUA,CAAC,CAAC3E,KAAF,GAAU0E,CAAC,CAAC1E,KANtB;AAJkB,WAA3B;AAAA,SADE,EAaN3B,IAbM,CAaD,CAACqG,CAAD,EAAIC,CAAJ,KAAUA,CAAC,CAAC3E,KAAF,GAAU0E,CAAC,CAAC1E,KAbrB;AAJS,OAAnB;AAAA,KADW,EAoBf3B,IApBe,CAoBV,CAACqG,CAAD,EAAIC,CAAJ,KAAUA,CAAC,CAAC3E,KAAF,GAAU0E,CAAC,CAAC1E,KApBZ,CAApB,CAnIsB,CAyJtB;;AACA,UAAM4E,WAAW,GAAGR,MAAM,CAACS,MAAP,CAAc1C,YAAd,EAA4BjE,GAA5B,CAAiC4G,KAAD,IAAW;AAC3D,YAAMC,UAAU,GAAGjK,SAAS,CAACkK,IAAV,CAAgBC,CAAD,IAAOA,CAAC,CAAChE,UAAF,KAAiB6D,KAAK,CAACxB,IAA7C,KAAsD,EAAzE;AACA,YAAM4B,SAAS,GAAG;AACd5B,QAAAA,IAAI,EAAEwB,KAAK,CAACxB,IADE;AAEdH,QAAAA,KAAK,EAAE2B,KAAK,CAAC3B,KAFC;AAGdI,QAAAA,UAAU,EAAEuB,KAAK,CAACvB,UAHJ;AAId4B,QAAAA,WAAW,EAAEjC,UAAU,CAAC6B,UAAU,CAACK,WAAZ,CAJT;AAKdC,QAAAA,SAAS,EAAEnC,UAAU,CAAC6B,UAAU,CAACO,UAAZ,CALP;AAMdC,QAAAA,eAAe,EAAE,CAAET,KAAK,CAAC3B,KAAN,GAAcD,UAAU,CAAC6B,UAAU,CAACK,WAAZ,CAAzB,GAAqD,GAAtD,EAA2DI,OAA3D,CAAmE,CAAnE,CANH;AAOdC,QAAAA,aAAa,EAAE,CAAEX,KAAK,CAACvB,UAAN,GAAmBL,UAAU,CAAC6B,UAAU,CAACO,UAAZ,CAA9B,GAAyD,GAA1D,EAA+DE,OAA/D,CAAuE,CAAvE;AAPD,OAAlB,CAF2D,CAY3D;;AACA,YAAME,aAAa,GAAGhD,mBAAmB,CAACoC,KAAK,CAACxB,IAAP,CAAnB,IAAmC,EAAzD,CAb2D,CAc3D;;AACAa,MAAAA,WAAW,CAACnB,OAAZ,CAAqB2C,QAAD,IAAc;AAC9B,cAAMC,QAAQ,GAAGD,QAAQ,CAACrC,IAA1B;AACA4B,QAAAA,SAAS,CAACU,QAAD,CAAT,GAAsBF,aAAa,CAACE,QAAD,CAAb,IAA2B,CAAjD;AACH,OAHD;AAKA,aAAOV,SAAP;AACH,KArBmB,CAApB;AAuBA,UAAMW,cAAc,GAAGzB,MAAM,CAACS,MAAP,CAAczC,eAAd,EAA+BlE,GAA/B,CAAoC4G,KAAD,IAAW;AACjE,YAAMC,UAAU,GAAGjK,SAAS,CAACkK,IAAV,CAAgBC,CAAD,IAAOA,CAAC,CAAChE,UAAF,KAAiB6D,KAAK,CAACxB,IAA7C,KAAsD,EAAzE;AACA,aAAO;AACHA,QAAAA,IAAI,EAAEwB,KAAK,CAACxB,IADT;AAEHH,QAAAA,KAAK,EAAE2B,KAAK,CAAC3B,KAFV;AAGHI,QAAAA,UAAU,EAAEuB,KAAK,CAACvB,UAHf;AAIH4B,QAAAA,WAAW,EAAEjC,UAAU,CAAC6B,UAAU,CAACK,WAAZ,CAJpB;AAKHC,QAAAA,SAAS,EAAEnC,UAAU,CAAC6B,UAAU,CAACO,UAAZ,CALlB;AAMHC,QAAAA,eAAe,EAAE,CAAET,KAAK,CAAC3B,KAAN,GAAcD,UAAU,CAAC6B,UAAU,CAACK,WAAZ,CAAzB,GAAqD,GAAtD,EAA2DI,OAA3D,CAAmE,CAAnE,CANd;AAOHC,QAAAA,aAAa,EAAE,CAAEX,KAAK,CAACvB,UAAN,GAAmBL,UAAU,CAAC6B,UAAU,CAACO,UAAZ,CAA9B,GAAyD,GAA1D,EAA+DE,OAA/D,CAAuE,CAAvE;AAPZ,OAAP;AASH,KAXsB,CAAvB,CAjLsB,CA8LtB;;AACA,UAAMM,YAAY,GAAG1B,MAAM,CAAC2B,IAAP,CAAY1D,WAAZ,EAAyBnE,GAAzB,CAA8B0H,QAAD,IAAc;AAC5D,UAAIzC,KAAK,GAAG,CAAZ;AACAiB,MAAAA,MAAM,CAACS,MAAP,CAAcnC,mBAAd,EAAmCM,OAAnC,CAA4CgD,UAAD,IAAgB;AACvD7C,QAAAA,KAAK,IAAI6C,UAAU,CAACJ,QAAD,CAAV,IAAwB,CAAjC;AACH,OAFD;AAGA,aAAO;AACHtC,QAAAA,IAAI,EAAEsC,QADH;AAEH5F,QAAAA,KAAK,EAAEmD;AAFJ,OAAP;AAIH,KAToB,CAArB;AAUA,WAAO;AACH8C,MAAAA,MAAM,EAAErB,WADL;AAEHsB,MAAAA,SAAS,EAAEL,cAFR;AAGHvH,MAAAA,KAAK,EAAE6F,WAAW,CAACgC,KAAZ,CAAkB,CAAlB,EAAqB,CAArB,CAHJ;AAIHC,MAAAA,QAAQ,EAAEjC,WAJP;AAKHkC,MAAAA,YAAY,EAAE3D,mBALX;AAMHoD,MAAAA,YAAY,EAAEA,YANX;AAOH7F,MAAAA,KAAK,EAAEmE,MAAM,CAACC,OAAP,CAAe9B,WAAf,EACFrE,GADE,CACE;AAAA,YAAC,CAACsF,IAAD,EAAOxD,KAAP,CAAD;AAAA,eAAoB;AACrBwD,UAAAA,IADqB;AAErBxD,UAAAA,KAFqB;AAGrBsG,UAAAA,cAAc,EAAEC,cAAc,CAACvG,KAAD;AAHT,SAApB;AAAA,OADF,EAMF3B,IANE,CAMG,CAACqG,CAAD,EAAIC,CAAJ,KAAU;AACZ,cAAM6B,KAAK,GAAGlF,QAAQ,CAACoD,CAAC,CAAClB,IAAF,CAAOrD,KAAP,CAAa,GAAb,EAAkB,CAAlB,CAAD,CAAtB;AACA,cAAMsG,KAAK,GAAGnF,QAAQ,CAACoD,CAAC,CAAClB,IAAF,CAAOrD,KAAP,CAAa,GAAb,EAAkB,CAAlB,CAAD,CAAtB;AACA,eAAOqG,KAAK,GAAGC,KAAf;AACH,OAVE,CAPJ;AAkBHC,MAAAA,IAAI,EAAEtC,MAAM,CAACC,OAAP,CAAe7B,UAAf,EACDtE,GADC,CACG;AAAA,YAAC,CAAC8F,GAAD,EAAMhE,KAAN,CAAD;AAAA,eAAmB;AACpBgE,UAAAA,GADoB;AAEpBhE,UAAAA,KAFoB;AAGpBsG,UAAAA,cAAc,EAAEC,cAAc,CAACvG,KAAD;AAHV,SAAnB;AAAA,OADH,EAMD3B,IANC,CAMI,CAACqG,CAAD,EAAIC,CAAJ,KAAU;AACZ;AACA,cAAM,CAACgC,KAAD,EAAQC,MAAR,EAAgBC,IAAhB,IAAwBnC,CAAC,CAACV,GAAF,CAAM7D,KAAN,CAAY,GAAZ,EAAiBjC,GAAjB,CAAsB4I,GAAD,IAASxF,QAAQ,CAACwF,GAAD,CAAtC,CAA9B;AACA,cAAM,CAACC,KAAD,EAAQC,MAAR,EAAgBC,IAAhB,IAAwBtC,CAAC,CAACX,GAAF,CAAM7D,KAAN,CAAY,GAAZ,EAAiBjC,GAAjB,CAAsB4I,GAAD,IAASxF,QAAQ,CAACwF,GAAD,CAAtC,CAA9B;AAEA,YAAIH,KAAK,KAAKI,KAAd,EAAqB,OAAOJ,KAAK,GAAGI,KAAf;AACrB,YAAIH,MAAM,KAAKI,MAAf,EAAuB,OAAOJ,MAAM,GAAGI,MAAhB;AACvB,eAAOH,IAAI,GAAGI,IAAd;AACH,OAdC,CAlBH;AAiCHC,MAAAA,KAAK,EAAE9C,MAAM,CAACC,OAAP,CAAe5B,WAAf,EACFvE,GADE,CACE,SAAuB;AAAA,YAAtB,CAACgG,QAAD,EAAWlE,KAAX,CAAsB;AACxB,cAAM,CAAC8D,IAAD,EAAOqD,QAAP,IAAmBjD,QAAQ,CAAC/D,KAAT,CAAe,IAAf,CAAzB;AACA,cAAMiH,OAAO,GAAG9F,QAAQ,CAAC6F,QAAD,CAAxB,CAFwB,CAIxB;;AACA,cAAME,cAAc,GAAG,IAAI1I,IAAJ,CAAS2C,QAAQ,CAACwC,IAAD,CAAjB,EAAyB,CAAzB,EAA4B,CAA5B,CAAvB,CALwB,CAMxB;;AACA,cAAMwD,SAAS,GAAGD,cAAc,CAACtF,MAAf,MAA2B,CAA7C;AACAsF,QAAAA,cAAc,CAAC3H,OAAf,CAAuB2H,cAAc,CAAC1H,OAAf,MAA4B2H,SAAS,IAAI,CAAb,GAAiB,IAAIA,SAArB,GAAiC,IAAIA,SAAjE,CAAvB,EARwB,CAUxB;;AACA,cAAMC,aAAa,GAAG,IAAI5I,IAAJ,CAAS0I,cAAT,CAAtB;AACAE,QAAAA,aAAa,CAAC7H,OAAd,CAAsB2H,cAAc,CAAC1H,OAAf,KAA2B,CAAjD,EAZwB,CAcxB;;AACA,cAAM6H,UAAU,GAAI1E,CAAD,IAAO;AACtB,iBAAOA,CAAC,CAACF,WAAF,GAAgBzC,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAP;AACH,SAFD;;AAIA,eAAO;AACHsH,UAAAA,IAAI,EAAEL,OAAO,CAAC1D,QAAR,EADH;AAEHI,UAAAA,IAAI,EAAEA,IAFH;AAGHI,UAAAA,QAAQ,EAAEA,QAHP;AAIHlE,UAAAA,KAJG;AAKH0H,UAAAA,QAAQ,EAAEF,UAAU,CAACH,cAAD,CALjB;AAMHM,UAAAA,OAAO,EAAEH,UAAU,CAACD,aAAD,CANhB;AAOHjB,UAAAA,cAAc,EAAEC,cAAc,CAACvG,KAAD;AAP3B,SAAP;AASH,OA7BE,EA8BF3B,IA9BE,CA8BG,CAACqG,CAAD,EAAIC,CAAJ,KAAU;AACZ,YAAID,CAAC,CAACZ,IAAF,KAAWa,CAAC,CAACb,IAAjB,EAAuB,OAAOxC,QAAQ,CAACoD,CAAC,CAACZ,IAAH,CAAR,GAAmBxC,QAAQ,CAACqD,CAAC,CAACb,IAAH,CAAlC;AACvB,eAAOxC,QAAQ,CAACoD,CAAC,CAAC+C,IAAH,CAAR,GAAmBnG,QAAQ,CAACqD,CAAC,CAAC8C,IAAH,CAAlC;AACH,OAjCE;AAjCJ,KAAP;AAoEH,GA7QD;;AA+QA,QAAMG,gBAAgB,GAAG,MAAM;AAC3B,QAAI,CAAC5K,gBAAgB,CAAC6K,OAAtB,EAA+B;AAC3B7K,MAAAA,gBAAgB,CAAC6K,OAAjB,GAA2B7F,WAAW,EAAtC;AACH;;AACD,WAAOhF,gBAAgB,CAAC6K,OAAxB;AACH,GALD,CAxeoB,CA+epB;;;AACA3R,EAAAA,SAAS,CAAC,MAAM;AACZ;AACA8G,IAAAA,gBAAgB,CAAC6K,OAAjB,GAA2B,IAA3B;AACH,GAHQ,EAGN,CAACjN,SAAD,EAAYM,aAAZ,EAA2BE,iBAA3B,EAA8CE,qBAA9C,EAAqEE,SAArE,EAAgFE,SAAhF,EAA2FE,OAA3F,CAHM,CAAT;;AAKA,QAAM2K,cAAc,GAAIvG,KAAD,IAAW;AAC9B,WAAO,IAAI8H,IAAI,CAACC,YAAT,CAAsB,OAAtB,EAA+B;AAClCC,MAAAA,KAAK,EAAE,UAD2B;AAElCC,MAAAA,QAAQ,EAAE,KAFwB;AAGlCC,MAAAA,qBAAqB,EAAE;AAHW,KAA/B,EAIJ1N,MAJI,CAIGwF,KAJH,CAAP;AAKH,GAND;;AAQA,QAAMwH,UAAU,GAAIW,OAAD,IAAa;AAC5B,UAAMzG,IAAI,GAAG,IAAI/C,IAAJ,CAASwJ,OAAO,GAAG,WAAnB,CAAb;AACA,UAAMnE,GAAG,GAAGoE,MAAM,CAAC1G,IAAI,CAAC/B,OAAL,EAAD,CAAN,CAAuBgE,QAAvB,CAAgC,CAAhC,EAAmC,GAAnC,CAAZ;AACA,UAAMI,KAAK,GAAGqE,MAAM,CAAC1G,IAAI,CAAC3C,QAAL,KAAkB,CAAnB,CAAN,CAA4B4E,QAA5B,CAAqC,CAArC,EAAwC,GAAxC,CAAd;AACA,WAAQ,GAAEK,GAAI,IAAGD,KAAM,EAAvB;AACH,GALD;;AAOA,QAAMsE,aAAa,GAAG,SAAgC;AAAA,QAA/B;AAAEC,MAAAA,MAAF;AAAUC,MAAAA,OAAV;AAAmBC,MAAAA;AAAnB,KAA+B;;AAClD,QAAIF,MAAM,IAAIC,OAAV,IAAqBA,OAAO,CAACtJ,MAAjC,EAAyC;AAAA;;AACrC,0BACI,QAAC,GAAD;AACI,QAAA,EAAE,EAAE;AACAwJ,UAAAA,eAAe,EAAE,OADjB;AAEAC,UAAAA,OAAO,EAAE,MAFT;AAGAC,UAAAA,MAAM,EAAE,gBAHR;AAIAC,UAAAA,YAAY,EAAE;AAJd,SADR;AAAA,gCAQI,QAAC,UAAD;AAAY,UAAA,OAAO,EAAC,WAApB;AAAA,oBAAiCL,OAAO,CAAC,CAAD,CAAP,CAAWjF;AAA5C;AAAA;AAAA;AAAA;AAAA,gBARJ,eASI,QAAC,UAAD;AAAY,UAAA,OAAO,EAAC,OAApB;AAA4B,UAAA,KAAK,EAAC,eAAlC;AAAA,oBACKiD,cAAc,CAACgC,OAAO,CAAC,CAAD,CAAP,CAAWvI,KAAZ;AADnB;AAAA;AAAA;AAAA;AAAA,gBATJ,eAYI,QAAC,UAAD;AAAY,UAAA,OAAO,EAAC,OAApB;AAA4B,UAAA,KAAK,EAAC,SAAlC;AAAA,8CACKuI,OAAO,CAAC,CAAD,CAAP,CAAWA,OAAX,CAAmBjE,UADxB,0DACK,sBAA+BkB,OAA/B,CAAuC,CAAvC,CADL;AAAA;AAAA;AAAA;AAAA;AAAA,gBAZJ;AAAA;AAAA;AAAA;AAAA;AAAA,cADJ;AAkBH;;AACD,WAAO,IAAP;AACH,GAtBD;;AAwBA,QAAMqD,aAAa,GAAG,CAACC,QAAD,EAAWC,KAAX,KAAqB;AACvC,QAAID,QAAQ,CAACjB,OAAb,EAAsB;AAClB,YAAMmB,UAAU,GAAGF,QAAQ,CAACjB,OAAT,CAAiBoB,SAAjB,CAA2BC,QAA3B,CAAoC,CAApC,CAAnB;AACA,YAAMC,OAAO,GAAG,IAAIC,aAAJ,GAAoBC,iBAApB,CAAsCL,UAAtC,CAAhB;AACA,YAAMM,MAAM,GAAGC,QAAQ,CAACC,aAAT,CAAuB,QAAvB,CAAf;AACA,YAAMC,GAAG,GAAGH,MAAM,CAACI,UAAP,CAAkB,IAAlB,CAAZ;AACA,YAAMC,GAAG,GAAG,IAAIC,KAAJ,EAAZ,CALkB,CAOlB;;AACA,YAAMC,WAAW,GAAGb,UAAU,CAACc,qBAAX,EAApB;AACAR,MAAAA,MAAM,CAACS,KAAP,GAAeF,WAAW,CAACE,KAA3B;AACAT,MAAAA,MAAM,CAACU,MAAP,GAAgBH,WAAW,CAACG,MAA5B;;AAEAL,MAAAA,GAAG,CAACM,MAAJ,GAAa,MAAM;AACf;AACAR,QAAAA,GAAG,CAACS,SAAJ,GAAgB,SAAhB;AACAT,QAAAA,GAAG,CAACU,QAAJ,CAAa,CAAb,EAAgB,CAAhB,EAAmBb,MAAM,CAACS,KAA1B,EAAiCT,MAAM,CAACU,MAAxC,EAHe,CAKf;;AACAP,QAAAA,GAAG,CAACW,SAAJ,CAAcT,GAAd,EAAmB,CAAnB,EAAsB,CAAtB,EANe,CAQf;;AACA,cAAMU,OAAO,GAAGf,MAAM,CAACgB,SAAP,CAAiB,WAAjB,CAAhB;AACA,cAAMC,YAAY,GAAGhB,QAAQ,CAACC,aAAT,CAAuB,GAAvB,CAArB;AACAe,QAAAA,YAAY,CAACC,QAAb,GAAyB,GAAEzB,KAAK,CAAC0B,OAAN,CAAc,MAAd,EAAsB,GAAtB,CAA2B,IAAG,IAAI9L,IAAJ,GAAWiE,WAAX,GAAyBzC,KAAzB,CAA+B,GAA/B,EAAoC,CAApC,CAAuC,MAAhG;AACAoK,QAAAA,YAAY,CAACG,IAAb,GAAoBL,OAApB;AACAE,QAAAA,YAAY,CAACI,KAAb;AACH,OAdD;;AAgBAhB,MAAAA,GAAG,CAACiB,GAAJ,GAAU,+BAA+BC,IAAI,CAACC,QAAQ,CAACC,kBAAkB,CAAC5B,OAAD,CAAnB,CAAT,CAA7C;AACH;AACJ,GA/BD;;AAiCA,QAAM6B,yBAAyB,GAAG,MAAM;AACpC,QAAI,CAACpD,gBAAgB,GAAG3B,MAApB,IAA8B,CAAC2B,gBAAgB,GAAGxB,QAAtD,EAAgE;AAEhE,UAAM6E,QAAQ,GAAG3Q,IAAI,CAAC4Q,KAAL,CAAWC,QAAX,EAAjB,CAHoC,CAKpC;;AACA,UAAMC,OAAO,GAAG,CAAC,SAAD,EAAY,GAAGxD,gBAAgB,GAAGxB,QAAnB,CAA4BlI,GAA5B,CAAiCmN,IAAD,IAAUA,IAAI,CAAC/H,IAA/C,CAAf,EAAqE,OAArE,CAAhB,CANoC,CAQpC;;AACA,UAAM4B,SAAS,GAAG0C,gBAAgB,GAAG3B,MAAnB,CAA0B/H,GAA1B,CAA+B4G,KAAD,IAAW;AACvD,YAAMwG,GAAG,GAAG;AACRC,QAAAA,OAAO,EAAEzG,KAAK,CAACxB;AADP,OAAZ,CADuD,CAKvD;;AACAsE,MAAAA,gBAAgB,GAAGxB,QAAnB,CAA4BpD,OAA5B,CAAqCqI,IAAD,IAAU;AAC1CC,QAAAA,GAAG,CAACD,IAAI,CAAC/H,IAAN,CAAH,GAAiBwB,KAAK,CAACuG,IAAI,CAAC/H,IAAN,CAAL,IAAoB,CAArC;AACH,OAFD,EANuD,CAUvD;;AACAgI,MAAAA,GAAG,CAAC,OAAD,CAAH,GAAexG,KAAK,CAAC3B,KAAN,IAAe,CAA9B;AAEA,aAAOmI,GAAP;AACH,KAdiB,CAAlB,CAToC,CAyBpC;;AACA,UAAME,QAAQ,GAAG;AACbD,MAAAA,OAAO,EAAE;AADI,KAAjB,CA1BoC,CA8BpC;;AACA3D,IAAAA,gBAAgB,GAAGxB,QAAnB,CAA4BpD,OAA5B,CAAqCqI,IAAD,IAAU;AAC1CG,MAAAA,QAAQ,CAACH,IAAI,CAAC/H,IAAN,CAAR,GAAsB+H,IAAI,CAACrL,KAAL,IAAc,CAApC;AACH,KAFD,EA/BoC,CAmCpC;;AACAwL,IAAAA,QAAQ,CAAC,OAAD,CAAR,GAAoB5D,gBAAgB,GAAG3B,MAAnB,CAA0BwF,MAA1B,CAAiC,CAACC,GAAD,EAAM5G,KAAN,KAAgB4G,GAAG,IAAI5G,KAAK,CAAC3B,KAAN,IAAe,CAAnB,CAApD,EAA2E,CAA3E,CAApB,CApCoC,CAsCpC;;AACA,UAAMwI,UAAU,GAAG,CAAC,GAAGzG,SAAJ,EAAesG,QAAf,CAAnB,CAvCoC,CAyCpC;;AACA,UAAMI,SAAS,GAAGtR,IAAI,CAAC4Q,KAAL,CAAWW,aAAX,CAAyBF,UAAzB,EAAqC;AAAEG,MAAAA,MAAM,EAAEV;AAAV,KAArC,CAAlB,CA1CoC,CA4CpC;;AACA,UAAMW,KAAK,GAAGzR,IAAI,CAAC4Q,KAAL,CAAWc,YAAX,CAAwBJ,SAAS,CAAC,MAAD,CAAjC,CAAd,CA7CoC,CA+CpC;;AACA,UAAMK,YAAY,GAAG,EAArB;;AACA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIH,KAAK,CAACI,CAAN,CAAQC,CAA7B,EAAgCF,CAAC,EAAjC,EAAqC;AACjCD,MAAAA,YAAY,CAACC,CAAD,CAAZ,GAAkB;AAAEnC,QAAAA,KAAK,EAAE;AAAT,OAAlB,CADiC,CACA;AACpC;;AACD6B,IAAAA,SAAS,CAAC,OAAD,CAAT,GAAqBK,YAArB,CApDoC,CAsDpC;;AACA,SAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIN,KAAK,CAACI,CAAN,CAAQC,CAA7B,EAAgCC,CAAC,EAAjC,EAAqC;AACjC,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIP,KAAK,CAACI,CAAN,CAAQI,CAAR,GAAY,CAAjC,EAAoCD,CAAC,EAArC,EAAyC;AACrC,cAAME,WAAW,GAAGlS,IAAI,CAAC4Q,KAAL,CAAWuB,WAAX,CAAuB;AAAEF,UAAAA,CAAC,EAAED,CAAL;AAAQF,UAAAA,CAAC,EAAEC;AAAX,SAAvB,CAApB;;AACA,YAAIT,SAAS,CAACY,WAAD,CAAT,IAA0B,OAAOZ,SAAS,CAACY,WAAD,CAAT,CAAuBE,CAA9B,KAAoC,QAAlE,EAA4E;AACxEd,UAAAA,SAAS,CAACY,WAAD,CAAT,CAAuBG,CAAvB,GAA2B,cAA3B;AACH;AACJ;AACJ;;AAEDrS,IAAAA,IAAI,CAAC4Q,KAAL,CAAW0B,iBAAX,CAA6B3B,QAA7B,EAAuCW,SAAvC,EAAkD,yBAAlD;AACAtR,IAAAA,IAAI,CAACuS,SAAL,CAAe5B,QAAf,EAAyB,8BAAzB;AACH,GAlED;;AAoEA,QAAM6B,uBAAuB,GAAG,MAAM;AAClC,UAAMvP,IAAI,GAAG,EAAb;AACAqK,IAAAA,gBAAgB,GAAGxB,QAAnB,CAA4BpD,OAA5B,CAAqCqI,IAAD,IAAU;AAC1C;AACA9N,MAAAA,IAAI,CAACwP,IAAL,CAAU;AACNC,QAAAA,QAAQ,EAAE3B,IAAI,CAAC/H,IADT;AAEN2J,QAAAA,KAAK,EAAE,EAFD;AAGNC,QAAAA,QAAQ,EAAE,EAHJ;AAIN,qBAAa7B,IAAI,CAACrL,KAJZ;AAKNmN,QAAAA,UAAU,EAAG,GAAE9B,IAAI,CAAC/G,UAAL,CAAgBkB,OAAhB,CAAwB,CAAxB,CAA2B;AALpC,OAAV,EAF0C,CAU1C;;AACA6F,MAAAA,IAAI,CAAC7M,SAAL,CAAewE,OAAf,CAAwBoK,QAAD,IAAc;AACjC;AACA7P,QAAAA,IAAI,CAACwP,IAAL,CAAU;AACNC,UAAAA,QAAQ,EAAE3B,IAAI,CAAC/H,IADT;AAEN2J,UAAAA,KAAK,EAAEG,QAAQ,CAAC9J,IAFV;AAGN4J,UAAAA,QAAQ,EAAE,EAHJ;AAIN,uBAAaE,QAAQ,CAACpN,KAJhB;AAKNmN,UAAAA,UAAU,EAAG,GAAEC,QAAQ,CAAC9I,UAAT,CAAoBkB,OAApB,CAA4B,CAA5B,CAA+B;AALxC,SAAV,EAFiC,CAUjC;;AACA4H,QAAAA,QAAQ,CAAChK,QAAT,CAAkBJ,OAAlB,CAA2BqK,OAAD,IAAa;AACnC9P,UAAAA,IAAI,CAACwP,IAAL,CAAU;AACNC,YAAAA,QAAQ,EAAE3B,IAAI,CAAC/H,IADT;AAEN2J,YAAAA,KAAK,EAAEG,QAAQ,CAAC9J,IAFV;AAGN4J,YAAAA,QAAQ,EAAEG,OAAO,CAAC/J,IAHZ;AAIN,yBAAa+J,OAAO,CAACrN,KAJf;AAKNmN,YAAAA,UAAU,EAAG,GAAEE,OAAO,CAAC/I,UAAR,CAAmBkB,OAAnB,CAA2B,CAA3B,CAA8B;AALvC,WAAV;AAOH,SARD;AASH,OApBD,EAX0C,CAiC1C;;AACAjI,MAAAA,IAAI,CAACwP,IAAL,CAAU;AACNC,QAAAA,QAAQ,EAAG,SAAQ3B,IAAI,CAAC/H,IAAK,EADvB;AAEN2J,QAAAA,KAAK,EAAE,EAFD;AAGNC,QAAAA,QAAQ,EAAE,EAHJ;AAIN,qBAAa7B,IAAI,CAACrL,KAJZ;AAKNmN,QAAAA,UAAU,EAAG,GAAE9B,IAAI,CAAC/G,UAAL,CAAgBkB,OAAhB,CAAwB,CAAxB,CAA2B;AALpC,OAAV;AAOH,KAzCD,EAFkC,CA6ClC;;AACAjI,IAAAA,IAAI,CAACwP,IAAL,CAAU;AACNC,MAAAA,QAAQ,EAAE,eADJ;AAENC,MAAAA,KAAK,EAAE,EAFD;AAGNC,MAAAA,QAAQ,EAAE,EAHJ;AAIN,mBAAatF,gBAAgB,GAAGxB,QAAnB,CAA4BqF,MAA5B,CAAmC,CAACC,GAAD,EAAML,IAAN,KAAeK,GAAG,GAAGL,IAAI,CAACrL,KAA7D,EAAoE,CAApE,CAJP;AAKNmN,MAAAA,UAAU,EAAE;AALN,KAAV;AAQA,UAAMG,EAAE,GAAGhT,IAAI,CAAC4Q,KAAL,CAAWW,aAAX,CAAyBtO,IAAzB,CAAX;AACA,UAAMgQ,EAAE,GAAGjT,IAAI,CAAC4Q,KAAL,CAAWC,QAAX,EAAX;AACA7Q,IAAAA,IAAI,CAAC4Q,KAAL,CAAW0B,iBAAX,CAA6BW,EAA7B,EAAiCD,EAAjC,EAAqC,qBAArC;AACAhT,IAAAA,IAAI,CAACuS,SAAL,CAAeU,EAAf,EAAmB,0BAAnB;AACH,GA1DD;;AA4DA,QAAMC,QAAQ,GAAIrF,OAAD,IAAa;AAC1B;AACA,UAAM,CAACrE,IAAD,EAAOC,KAAP,EAAcC,GAAd,IAAqBmE,OAAO,CAAChI,KAAR,CAAc,GAAd,EAAmBjC,GAAnB,CAAwB4I,GAAD,IAASxF,QAAQ,CAACwF,GAAD,EAAM,EAAN,CAAxC,CAA3B,CAF0B,CAG1B;;AACA,UAAMpF,IAAI,GAAG,IAAI/C,IAAJ,CAASmF,IAAT,EAAeC,KAAK,GAAG,CAAvB,EAA0BC,GAA1B,CAAb;AACA,WAAOtC,IAAI,CAACK,MAAL,OAAkB,CAAzB,CAL0B,CAKE;AAC/B,GAND;;AAQA,QAAM0L,QAAQ,GAAItF,OAAD,IAAa;AAC1B;AACA,UAAM,CAACrE,IAAD,EAAOC,KAAP,EAAcC,GAAd,IAAqBmE,OAAO,CAAChI,KAAR,CAAc,GAAd,EAAmBjC,GAAnB,CAAwB4I,GAAD,IAASxF,QAAQ,CAACwF,GAAD,EAAM,EAAN,CAAxC,CAA3B,CAF0B,CAG1B;;AACA,UAAMpF,IAAI,GAAG,IAAI/C,IAAJ,CAASmF,IAAT,EAAeC,KAAK,GAAG,CAAvB,EAA0BC,GAA1B,CAAb;AACA,WAAOtC,IAAI,CAACK,MAAL,OAAkB,CAAzB,CAL0B,CAKE;AAC/B,GAND;;AAQA,QAAM2L,gBAAgB,GAAG,MAAM;AAC3B,UAAMhH,IAAI,GAAG,CAAC,GAAGkB,gBAAgB,GAAGlB,IAAvB,CAAb;AACA,UAAMiH,SAAS,GAAG,EAAlB;AACA,QAAIzB,CAAC,GAAG,CAAR;;AAEA,WAAOA,CAAC,GAAGxF,IAAI,CAACzH,MAAhB,EAAwB;AACpB;AACA;AACA,UAAIuO,QAAQ,CAAC9G,IAAI,CAACwF,CAAD,CAAJ,CAAQlI,GAAT,CAAR,IAAyBkI,CAAC,GAAG,CAAJ,GAAQxF,IAAI,CAACzH,MAAtC,IAAgDwO,QAAQ,CAAC/G,IAAI,CAACwF,CAAC,GAAG,CAAL,CAAJ,CAAYlI,GAAb,CAA5D,EAA+E;AAC3E;AACA,cAAM4J,aAAa,GAAGlH,IAAI,CAACwF,CAAD,CAAJ,CAAQlI,GAA9B;AACA,cAAM6J,WAAW,GAAGnH,IAAI,CAACwF,CAAC,GAAG,CAAL,CAAJ,CAAYlI,GAAhC;AACA,cAAM8J,SAAS,GAAGpH,IAAI,CAACP,KAAL,CAAW+F,CAAX,EAAcA,CAAC,GAAG,CAAlB,EAAqBT,MAArB,CAA4B,CAACC,GAAD,EAAM1H,GAAN,KAAc0H,GAAG,GAAG1H,GAAG,CAAChE,KAApD,EAA2D,CAA3D,CAAlB;AAEA2N,QAAAA,SAAS,CAACZ,IAAV,CAAe;AACXlN,UAAAA,IAAI,EAAE,eADK;AAEXmE,UAAAA,GAAG,EAAG,GAAE4J,aAAc,MAAKC,WAAY,EAF5B;AAGX7N,UAAAA,KAAK,EAAE8N,SAHI;AAIXxH,UAAAA,cAAc,EAAEC,cAAc,CAACuH,SAAD;AAJnB,SAAf,EAN2E,CAa3E;;AACA5B,QAAAA,CAAC,IAAI,CAAL;AACH,OAfD,MAeO;AACH;AACA,YAAI6B,CAAC,GAAG7B,CAAR,CAFG,CAGH;;AACA,eAAO6B,CAAC,GAAGrH,IAAI,CAACzH,MAAT,IAAmB,EAAEuO,QAAQ,CAAC9G,IAAI,CAACqH,CAAD,CAAJ,CAAQ/J,GAAT,CAAR,IAAyB+J,CAAC,GAAG,CAAJ,GAAQrH,IAAI,CAACzH,MAAtC,IAAgDwO,QAAQ,CAAC/G,IAAI,CAACqH,CAAC,GAAG,CAAL,CAAJ,CAAY/J,GAAb,CAA1D,CAA1B,EAAwG;AACpG+J,UAAAA,CAAC;AACJ,SANE,CAQH;;;AACA,YAAIA,CAAC,GAAG7B,CAAR,EAAW;AACP,gBAAM8B,uBAAuB,GAAGtH,IAAI,CAACwF,CAAD,CAAJ,CAAQlI,GAAxC;AACA,gBAAMiK,qBAAqB,GAAGvH,IAAI,CAACqH,CAAC,GAAG,CAAL,CAAJ,CAAY/J,GAA1C;AACA,gBAAMkK,mBAAmB,GAAGxH,IAAI,CAACP,KAAL,CAAW+F,CAAX,EAAc6B,CAAd,EAAiBtC,MAAjB,CAAwB,CAACC,GAAD,EAAM1H,GAAN,KAAc0H,GAAG,GAAG1H,GAAG,CAAChE,KAAhD,EAAuD,CAAvD,CAA5B;AAEA2N,UAAAA,SAAS,CAACZ,IAAV,CAAe;AACXlN,YAAAA,IAAI,EAAE,iBADK;AAEXmE,YAAAA,GAAG,EAAG,GAAEgK,uBAAwB,MAAKC,qBAAsB,EAFhD;AAGXjO,YAAAA,KAAK,EAAEkO,mBAHI;AAIX5H,YAAAA,cAAc,EAAEC,cAAc,CAAC2H,mBAAD;AAJnB,WAAf,EALO,CAYP;;AACAhC,UAAAA,CAAC,GAAG6B,CAAJ;AACH,SAdD,MAcO;AACH;AACA7B,UAAAA,CAAC;AACJ;AACJ;AACJ;;AAED,WAAOyB,SAAP;AACH,GAtDD;;AAwDA,QAAMQ,gBAAgB,GAAG,CAAChP,KAAD,EAAQiP,OAAR,KAAoB;AACzC,QAAIA,OAAO,KAAK,IAAhB,EAAsB;AAClB/R,MAAAA,WAAW,CAAC+R,OAAD,CAAX;AACH;AACJ,GAJD;;AAMA,QAAMC,gBAAgB,GAAIC,SAAD,IAAe;AACpCnT,IAAAA,gBAAgB,CAACmT,SAAD,CAAhB;AACH,GAFD,CA3wBoB,CA+wBpB;;;AACA,QAAMC,cAAc,GAAG,CAACC,YAAD,EAAerC,CAAf,KAAqB;AACxC;AACA,QAAIA,CAAJ,EAAO;AACHA,MAAAA,CAAC,CAACsC,eAAF;AACAtC,MAAAA,CAAC,CAACuC,cAAF;AACH,KALuC,CAOxC;;;AACA,UAAMC,aAAa,GAAG/G,gBAAgB,EAAtC;AACA,UAAMgH,QAAQ,GAAGD,aAAa,CAACvI,QAAd,CAAuBpB,IAAvB,CAA6BqG,IAAD,IAAUA,IAAI,CAAC/H,IAAL,KAAckL,YAApD,CAAjB;AAEA,QAAI,CAACI,QAAD,IAAa,CAACA,QAAQ,CAACpQ,SAAvB,IAAoCoQ,QAAQ,CAACpQ,SAAT,CAAmBS,MAAnB,KAA8B,CAAtE,EAAyE,OAXjC,CAaxC;;AACA,UAAM4P,QAAQ,GAAGD,QAAQ,CAACpQ,SAAT,CAAmBN,GAAnB,CAAwBkP,QAAD,IAAe,GAAEoB,YAAa,IAAGpB,QAAQ,CAAC9J,IAAK,EAAtE,CAAjB;AACA,UAAMwL,WAAW,GAAGD,QAAQ,CAACE,IAAT,CAAeC,GAAD,IAASlS,aAAa,CAACkS,GAAD,CAApC,CAApB,CAfwC,CAiBxC;;AACA,UAAMC,gBAAgB,GAAG,EAAE,GAAGnS;AAAL,KAAzB;AAEA+R,IAAAA,QAAQ,CAAC7L,OAAT,CAAkBgM,GAAD,IAAS;AACtBC,MAAAA,gBAAgB,CAACD,GAAD,CAAhB,GAAwB,CAACF,WAAzB;AACH,KAFD,EApBwC,CAwBxC;;AACA,QAAI,CAACA,WAAL,EAAkB;AACdjS,MAAAA,oBAAoB,CAAEqS,IAAD,KAAW,EAC5B,GAAGA,IADyB;AAE5B,SAACV,YAAD,GAAgB;AAFY,OAAX,CAAD,CAApB;AAIH,KA9BuC,CAgCxC;;;AACAzR,IAAAA,gBAAgB,CAACkS,gBAAD,CAAhB;AACH,GAlCD,CAhxBoB,CAozBpB;;;AACA,QAAME,oBAAoB,GAAG,CAACX,YAAD,EAAeY,UAAf,KAA8B;AACvDvS,IAAAA,oBAAoB,CAAEqS,IAAD,KAAW,EAC5B,GAAGA,IADyB;AAE5B,OAACV,YAAD,GAAgBY;AAFY,KAAX,CAAD,CAApB,CADuD,CAMvD;;AACA,QAAI,CAACA,UAAL,EAAiB;AACb,YAAMT,aAAa,GAAG/G,gBAAgB,EAAtC;AACA,YAAMgH,QAAQ,GAAGD,aAAa,CAACvI,QAAd,CAAuBpB,IAAvB,CAA6BqG,IAAD,IAAUA,IAAI,CAAC/H,IAAL,KAAckL,YAApD,CAAjB;;AAEA,UAAII,QAAQ,IAAIA,QAAQ,CAACpQ,SAAzB,EAAoC;AAChC,cAAMyQ,gBAAgB,GAAG,EAAE,GAAGnS;AAAL,SAAzB;AAEA8R,QAAAA,QAAQ,CAACpQ,SAAT,CAAmBwE,OAAnB,CAA4BoK,QAAD,IAAc;AACrC,gBAAMiC,OAAO,GAAI,GAAEb,YAAa,IAAGpB,QAAQ,CAAC9J,IAAK,EAAjD;AACA2L,UAAAA,gBAAgB,CAACI,OAAD,CAAhB,GAA4B,KAA5B;AACH,SAHD;AAKAtS,QAAAA,gBAAgB,CAACkS,gBAAD,CAAhB;AACH;AACJ;AACJ,GAtBD,CArzBoB,CA60BpB;;;AACA,QAAMK,gBAAgB,GAAG,CAACD,OAAD,EAAUD,UAAV,KAAyB;AAC9CrS,IAAAA,gBAAgB,CAAEmS,IAAD,KAAW,EACxB,GAAGA,IADqB;AAExB,OAACG,OAAD,GAAWD;AAFa,KAAX,CAAD,CAAhB;AAIH,GALD,CA90BoB,CAq1BpB;;;AACA,QAAMG,iBAAiB,GAAIf,YAAD,IAAkB;AACxC,UAAMG,aAAa,GAAG/G,gBAAgB,EAAtC;AACA,UAAMgH,QAAQ,GAAGD,aAAa,CAACvI,QAAd,CAAuBpB,IAAvB,CAA6BqG,IAAD,IAAUA,IAAI,CAAC/H,IAAL,KAAckL,YAApD,CAAjB;AAEA,QAAI,CAACI,QAAD,IAAa,CAACA,QAAQ,CAACpQ,SAA3B,EAAsC,OAAO,KAAP,CAJE,CAMxC;;AACA,WAAOoQ,QAAQ,CAACpQ,SAAT,CAAmBuQ,IAAnB,CAAyB3B,QAAD,IAActQ,aAAa,CAAE,GAAE0R,YAAa,IAAGpB,QAAQ,CAAC9J,IAAK,EAAlC,CAAb,KAAsD,IAA5F,CAAP;AACH,GARD;;AAUA,MAAItI,OAAJ,EAAa;AACT,wBACI,QAAC,QAAD;AACI,MAAA,EAAE,EAAE;AACAwU,QAAAA,KAAK,EAAE,MADP;AAEAC,QAAAA,MAAM,EAAGC,KAAD,IAAWA,KAAK,CAACD,MAAN,CAAaE,MAAb,GAAsB,CAFzC;AAGAlH,QAAAA,eAAe,EAAE;AAHjB,OADR;AAMI,MAAA,IAAI,EAAEzN,OANV;AAAA,6BAQI,QAAC,GAAD;AACI,QAAA,EAAE,EAAE;AACA4U,UAAAA,OAAO,EAAE,MADT;AAEAC,UAAAA,aAAa,EAAE,QAFf;AAGAC,UAAAA,UAAU,EAAE,QAHZ;AAIAC,UAAAA,GAAG,EAAE;AAJL,SADR;AAAA,gCAQI,QAAC,gBAAD;AACI,UAAA,IAAI,EAAE,EADV;AAEI,UAAA,SAAS,EAAE,CAFf;AAGI,UAAA,EAAE,EAAE;AACAP,YAAAA,KAAK,EAAE;AADP;AAHR;AAAA;AAAA;AAAA;AAAA,gBARJ,eAeI,QAAC,UAAD;AACI,UAAA,OAAO,EAAC,IADZ;AAEI,UAAA,EAAE,EAAE;AACAA,YAAAA,KAAK,EAAE,SADP;AAEAQ,YAAAA,UAAU,EAAE;AAFZ,WAFR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAfJ;AAAA;AAAA;AAAA;AAAA;AAAA;AARJ;AAAA;AAAA;AAAA;AAAA,YADJ;AAoCH,GAr4BmB,CAu4BpB;;;AACA,QAAMrB,aAAa,GAAG/G,gBAAgB,EAAtC;AACA,QAAM3B,MAAM,GAAG,CAAC,GAAG,IAAIhI,GAAJ,CAAQrD,SAAS,CAACsD,GAAV,CAAeC,IAAD,IAAUA,IAAI,CAAC8C,UAA7B,CAAR,CAAJ,CAAf;AAEA,sBACI,QAAC,GAAD;AAAK,IAAA,EAAE,EAAE;AAAEyH,MAAAA,OAAO,EAAE;AAAX,KAAT;AAAA,4BACI,QAAC,sBAAD;AAAA,8BACI,QAAC,oBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cADJ,eAEI,QAAC,cAAD;AAAA,gCACI,QAAC,WAAD;AAAA,kCACI,QAAC,oBAAD;AAAsB,YAAA,WAAW,EAAE3O,cAAnC;AAAmD,YAAA,MAAM,EAAEC,EAA3D;AAAA,oCACI,QAAC,iBAAD;AACI,cAAA,KAAK,EAAC,eADV;AAEI,cAAA,KAAK,EAAEwB,SAAS,CAAC,CAAD,CAFpB;AAGI,cAAA,QAAQ,EAAGsE,QAAD,IAAc;AACpB;AACA,oBAAIA,QAAQ,IAAI,CAACmQ,KAAK,CAACnQ,QAAQ,CAACoQ,OAAT,EAAD,CAAtB,EAA4C;AACxC,wBAAMC,QAAQ,GAAG,IAAIxR,IAAJ,CAASmB,QAAT,CAAjB;AACAqQ,kBAAAA,QAAQ,CAACvR,QAAT,CAAkB,EAAlB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B;AACAnD,kBAAAA,YAAY,CAAC,CAAC0U,QAAD,EAAW3U,SAAS,CAAC,CAAD,CAApB,CAAD,CAAZ;AACH;AACJ,eAVL;AAWI,cAAA,WAAW,EAAG4U,MAAD,iBAAY,QAAC,SAAD,OAAeA,MAAf;AAAuB,gBAAA,IAAI,EAAC;AAA5B;AAAA;AAAA;AAAA;AAAA,sBAX7B;AAYI,cAAA,WAAW,EAAC,YAZhB;AAaI,cAAA,EAAE,EAAE;AACA,4CAA4B;AACxB,4CAA0B;AACtBC,oBAAAA,WAAW,EAAE;AADS;AADF,iBAD5B;AAMA,qDAAqC;AACjCb,kBAAAA,KAAK,EAAE;AAD0B;AANrC;AAbR;AAAA;AAAA;AAAA;AAAA,oBADJ,eAyBI,QAAC,iBAAD;AACI,cAAA,KAAK,EAAC,aADV;AAEI,cAAA,KAAK,EAAEhU,SAAS,CAAC,CAAD,CAFpB;AAGI,cAAA,QAAQ,EAAGsE,QAAD,IAAc;AACpB;AACA,oBAAIA,QAAQ,IAAI,CAACmQ,KAAK,CAACnQ,QAAQ,CAACoQ,OAAT,EAAD,CAAtB,EAA4C;AACxC,wBAAMC,QAAQ,GAAG,IAAIxR,IAAJ,CAASmB,QAAT,CAAjB;AACAqQ,kBAAAA,QAAQ,CAACvR,QAAT,CAAkB,EAAlB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B;AACAnD,kBAAAA,YAAY,CAAC,CAACD,SAAS,CAAC,CAAD,CAAV,EAAe2U,QAAf,CAAD,CAAZ;AACH;AACJ,eAVL;AAWI,cAAA,WAAW,EAAGC,MAAD,iBAAY,QAAC,SAAD,OAAeA,MAAf;AAAuB,gBAAA,IAAI,EAAC;AAA5B;AAAA;AAAA;AAAA;AAAA,sBAX7B;AAYI,cAAA,WAAW,EAAC,YAZhB;AAaI,cAAA,EAAE,EAAE;AACA,4CAA4B;AACxB,4CAA0B;AACtBC,oBAAAA,WAAW,EAAE;AADS;AADF,iBAD5B;AAMA,qDAAqC;AACjCb,kBAAAA,KAAK,EAAE;AAD0B;AANrC;AAbR;AAAA;AAAA;AAAA;AAAA,oBAzBJ;AAAA;AAAA;AAAA;AAAA;AAAA,kBADJ,eAmDI,QAAC,UAAD;AACI,YAAA,IAAI,EAAC,OADT;AAEI,YAAA,OAAO,EAAEtQ,iBAFb;AAGI,YAAA,EAAE,EAAE;AACAyJ,cAAAA,MAAM,EAAE,mBADR;AAEAC,cAAAA,YAAY,EAAE,MAFd;AAGAF,cAAAA,OAAO,EAAE;AAHT,aAHR;AAAA,mCASI,QAAC,cAAD;AAAgB,cAAA,QAAQ,EAAC;AAAzB;AAAA;AAAA;AAAA;AAAA;AATJ;AAAA;AAAA;AAAA;AAAA,kBAnDJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBADJ,eAiEI,QAAC,WAAD;AAAA,kCACI,QAAC,UAAD;AACI,YAAA,IAAI,EAAC,MADT;AAEI,YAAA,KAAK,EAAEhN,SAFX;AAGI,YAAA,QAAQ,EAAGyQ,CAAD,IAAOvM,gBAAgB,CAAC,OAAD,EAAUuM,CAAV,CAHrC;AAII,YAAA,IAAI,EAAC,OAJT;AAKI,YAAA,eAAe,EAAE;AAAEmE,cAAAA,MAAM,EAAE;AAAV,aALrB;AAMI,YAAA,KAAK,EAAC;AANV;AAAA;AAAA;AAAA;AAAA,kBADJ,eASI,QAAC,UAAD;AACI,YAAA,IAAI,EAAC,MADT;AAEI,YAAA,KAAK,EAAE1U,OAFX;AAGI,YAAA,QAAQ,EAAGuQ,CAAD,IAAOvM,gBAAgB,CAAC,KAAD,EAAQuM,CAAR,CAHrC;AAII,YAAA,IAAI,EAAC,OAJT;AAKI,YAAA,eAAe,EAAE;AAAEmE,cAAAA,MAAM,EAAE;AAAV,aALrB;AAMI,YAAA,KAAK,EAAC;AANV;AAAA;AAAA;AAAA;AAAA,kBATJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBAjEJ,eAoFI,QAAC,WAAD;AAAa,UAAA,EAAE,EAAE;AAAEC,YAAAA,IAAI,EAAE;AAAR,WAAjB;AAAA,iCACI,QAAC,YAAD;AACI,YAAA,QAAQ,MADZ;AAEI,YAAA,IAAI,EAAC,OAFT;AAGI,YAAA,KAAK,EAAEnV,iBAHX;AAII,YAAA,QAAQ,EAAEkF,oBAJd;AAKI,YAAA,OAAO,EAAE9D,kBALb;AAMI,YAAA,WAAW,EAAG4T,MAAD,iBACT,QAAC,SAAD,OACQA,MADR;AAEI,cAAA,WAAW,EAAC,YAFhB;AAGI,cAAA,IAAI,EAAC,OAHT;AAII,cAAA,EAAE,EAAE;AACAI,gBAAAA,QAAQ,EAAE,GADV;AAEA,4CAA4B;AACxB5H,kBAAAA,YAAY,EAAE,MADU;AAExBoB,kBAAAA,MAAM,EAAE;AAFgB;AAF5B;AAJR;AAAA;AAAA;AAAA;AAAA,oBAPR;AAoBI,YAAA,UAAU,EAAE,CAACyG,QAAD,EAAWC,WAAX,KACRD,QAAQ,CAACvS,GAAT,CAAa,CAACyS,MAAD,EAASC,KAAT,kBACT,eAAC,UAAD,OACQF,WAAW,CAAC;AAAEE,gBAAAA;AAAF,eAAD,CADnB;AAEI,cAAA,GAAG,EAAED,MAFT;AAGI,cAAA,KAAK,EAAEA,MAHX;AAII,cAAA,QAAQ,MAJZ;AAKI,cAAA,QAAQ,EAAED,WAAW,CAAC;AAAEE,gBAAAA;AAAF,eAAD,CAAX,CAAuBC,QALrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cADJ;AArBR;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,gBApFJ,eAuHI,QAAC,WAAD;AAAa,UAAA,EAAE,EAAE;AAAEN,YAAAA,IAAI,EAAE;AAAR,WAAjB;AAAA,iCACI,QAAC,YAAD;AACI,YAAA,QAAQ,MADZ;AAEI,YAAA,IAAI,EAAC,OAFT;AAGI,YAAA,KAAK,EAAEjV,qBAHX;AAII,YAAA,QAAQ,EAAEiF,wBAJd;AAKI,YAAA,OAAO,EAAE7D,sBALb;AAMI,YAAA,WAAW,EAAG0T,MAAD,iBACT,QAAC,SAAD,OACQA,MADR;AAEI,cAAA,WAAW,EAAC,WAFhB;AAGI,cAAA,IAAI,EAAC,OAHT;AAII,cAAA,EAAE,EAAE;AACAI,gBAAAA,QAAQ,EAAE,GADV;AAEA,4CAA4B;AACxB5H,kBAAAA,YAAY,EAAE,MADU;AAExBoB,kBAAAA,MAAM,EAAE;AAFgB;AAF5B;AAJR;AAAA;AAAA;AAAA;AAAA,oBAPR;AAoBI,YAAA,UAAU,EAAE,CAACyG,QAAD,EAAWC,WAAX,KACRD,QAAQ,CAACvS,GAAT,CAAa,CAACyS,MAAD,EAASC,KAAT,kBACT,eAAC,UAAD,OACQF,WAAW,CAAC;AAAEE,gBAAAA;AAAF,eAAD,CADnB;AAEI,cAAA,GAAG,EAAED,MAFT;AAGI,cAAA,KAAK,EAAEA,MAHX;AAII,cAAA,QAAQ,MAJZ;AAKI,cAAA,QAAQ,EAAED,WAAW,CAAC;AAAEE,gBAAAA;AAAF,eAAD,CAAX,CAAuBC,QALrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cADJ;AArBR;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,gBAvHJ;AAAA;AAAA;AAAA;AAAA;AAAA,cAFJ,eA6JI,QAAC,IAAD;AACI,QAAA,QAAQ,EAAEvU,cADd;AAEI,QAAA,IAAI,EAAEwU,OAAO,CAACxU,cAAD,CAFjB;AAGI,QAAA,OAAO,EAAE+C,iBAHb;AAII,QAAA,UAAU,EAAE;AACR0R,UAAAA,EAAE,EAAE;AACAC,YAAAA,EAAE,EAAE,CADJ;AAEAC,YAAAA,SAAS,EAAE,2BAFX;AAGArI,YAAAA,YAAY,EAAE,KAHd;AAIA,mCAAuB;AACnBsI,cAAAA,QAAQ,EAAE,UADS;AAEnBxI,cAAAA,OAAO,EAAE,UAFU;AAGnB,yBAAW;AACPD,gBAAAA,eAAe,EAAE;AADV;AAHQ;AAJvB;AADI,SAJhB;AAAA,gCAmBI,QAAC,QAAD;AAAU,UAAA,OAAO,EAAE,MAAMnJ,qBAAqB,CAAC,OAAD,CAA9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAnBJ,eAoBI,QAAC,QAAD;AAAU,UAAA,OAAO,EAAE,MAAMA,qBAAqB,CAAC,WAAD,CAA9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBApBJ,eAqBI,QAAC,QAAD;AAAU,UAAA,OAAO,EAAE,MAAMA,qBAAqB,CAAC,OAAD,CAA9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBArBJ,eAsBI,QAAC,QAAD;AAAU,UAAA,OAAO,EAAE,MAAMA,qBAAqB,CAAC,QAAD,CAA9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAtBJ;AAAA;AAAA;AAAA;AAAA;AAAA,cA7JJ,eAsLI,QAAC,WAAD;AACI,QAAA,KAAK,EAAEpE,aADX;AAEI,QAAA,SAAS,MAFb;AAGI,QAAA,QAAQ,EAAE,CAACiE,KAAD,EAAQgS,QAAR,KAAqBhW,gBAAgB,CAACgW,QAAQ,IAAI,KAAb,CAHnD;AAII,sBAAW,iBAJf;AAAA,gCAMI,QAAC,YAAD;AAAc,UAAA,KAAK,EAAC,KAApB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBANJ,EAOKlL,MAAM,CAAC/H,GAAP,CAAY4G,KAAD,iBACR,QAAC,YAAD;AAA0B,UAAA,KAAK,EAAEA,KAAjC;AAAA,oBACKA;AADL,WAAmBA,KAAnB;AAAA;AAAA;AAAA;AAAA,gBADH,CAPL,eAYI,QAAC,MAAD;AACI,UAAA,gBAAgB,EAAE6J,aAAa,CAAC1I,MADpC;AAEI,UAAA,cAAc,EAAE0I,aAAa,CAAC1O,KAFlC;AAGI,UAAA,iBAAiB,EAAE0O,aAAa,CAACrQ,KAHrC;AAII,UAAA,cAAc,EAAEqQ,aAAa,CAACzI,SAJlC;AAKI,UAAA,cAAc,EAAEyI,aAAa,CAACvI;AALlC;AAAA;AAAA;AAAA;AAAA,gBAZJ;AAAA;AAAA;AAAA;AAAA;AAAA,cAtLJ;AAAA;AAAA;AAAA;AAAA;AAAA,YADJ,eA6MI,QAAC,sBAAD;AAAA,8BACI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cADJ,eAEI,QAAC,cAAD;AACI,QAAA,SAAS,EAAEvP,KADf;AAEI,QAAA,EAAE,EAAE;AAAEua,UAAAA,YAAY,EAAE,MAAhB;AAAwBxI,UAAAA,YAAY,EAAE,KAAtC;AAA6CqI,UAAAA,SAAS,EAAE;AAAxD,SAFR;AAAA,+BAII,QAAC,KAAD;AAAA,kCACI,QAAC,SAAD;AAAA,mCACI,QAAC,QAAD;AAAA,sCACI,QAAC,eAAD;AAAiB,gBAAA,SAAS,EAAC,QAA3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBADJ,EAEKtC,aAAa,CAACvI,QAAd,IACGuI,aAAa,CAACvI,QAAd,CAAuBlI,GAAvB,CAA4BmN,IAAD,iBACvB,QAAC,eAAD;AAAiC,gBAAA,SAAS,EAAC,QAA3C;AAAoD,gBAAA,KAAK,EAAC,OAA1D;AAAA,0BACKA,IAAI,CAAC/H;AADV,iBAAsB+H,IAAI,CAAC/H,IAA3B;AAAA;AAAA;AAAA;AAAA,sBADJ,CAHR,eAQI,QAAC,eAAD;AAAiB,gBAAA,SAAS,EAAC,QAA3B;AAAoC,gBAAA,KAAK,EAAC,OAA1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBARJ;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,kBADJ,eAeI,QAAC,SAAD;AAAA,uBACKqL,aAAa,CAAC1I,MAAd,IACG0I,aAAa,CAAC1I,MAAd,CAAqB/H,GAArB,CAA0B4G,KAAD,iBACrB,QAAC,cAAD;AAAA,sCACI,QAAC,eAAD;AAAA,0BAAkBA,KAAK,CAACxB;AAAxB;AAAA;AAAA;AAAA;AAAA,sBADJ,EAEKqL,aAAa,CAACvI,QAAd,IACGuI,aAAa,CAACvI,QAAd,CAAuBlI,GAAvB,CAA4BmN,IAAD,iBACvB,QAAC,eAAD;AAAoD,gBAAA,KAAK,EAAC,OAA1D;AAAA,0BACK9E,cAAc,CAACzB,KAAK,CAACuG,IAAI,CAAC/H,IAAN,CAAL,IAAoB,CAArB;AADnB,iBAAuB,GAAEwB,KAAK,CAACxB,IAAK,IAAG+H,IAAI,CAAC/H,IAAK,EAAjD;AAAA;AAAA;AAAA;AAAA,sBADJ,CAHR,eAQI,QAAC,eAAD;AAAiB,gBAAA,KAAK,EAAC,OAAvB;AAAA,0BAAgCiD,cAAc,CAACzB,KAAK,CAAC3B,KAAN,IAAe,CAAhB;AAA9C;AAAA;AAAA;AAAA;AAAA,sBARJ;AAAA,eAAqB2B,KAAK,CAACxB,IAA3B;AAAA;AAAA;AAAA;AAAA,oBADJ,CAFR,eAcI,QAAC,QAAD;AAAA,sCACI,QAAC,eAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBADJ,EAEKqL,aAAa,CAACvI,QAAd,IACGuI,aAAa,CAACvI,QAAd,CAAuBlI,GAAvB,CAA4BmN,IAAD,iBACvB,QAAC,eAAD;AAA4C,gBAAA,KAAK,EAAC,OAAlD;AAAA,0BACK9E,cAAc,CAAC8E,IAAI,CAACrL,KAAL,IAAc,CAAf;AADnB,iBAAuB,SAAQqL,IAAI,CAAC/H,IAAK,EAAzC;AAAA;AAAA;AAAA;AAAA,sBADJ,CAHR,eAQI,QAAC,eAAD;AAAiB,gBAAA,KAAK,EAAC,OAAvB;AAAA,0BACKiD,cAAc,CACXoI,aAAa,CAAC1I,MAAd,GAAuB0I,aAAa,CAAC1I,MAAd,CAAqBwF,MAArB,CAA4B,CAACC,GAAD,EAAM5G,KAAN,KAAgB4G,GAAG,IAAI5G,KAAK,CAAC3B,KAAN,IAAe,CAAnB,CAA/C,EAAsE,CAAtE,CAAvB,GAAkG,CADvF;AADnB;AAAA;AAAA;AAAA;AAAA,sBARJ;AAAA;AAAA;AAAA;AAAA;AAAA,oBAdJ;AAAA;AAAA;AAAA;AAAA;AAAA,kBAfJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAJJ;AAAA;AAAA;AAAA;AAAA,cAFJ,eAoDI,QAAC,GAAD;AAAK,QAAA,EAAE,EAAE;AAAE6N,UAAAA,EAAE,EAAE,CAAN;AAASpB,UAAAA,OAAO,EAAE,MAAlB;AAA0ByB,UAAAA,cAAc,EAAE,QAA1C;AAAoDvB,UAAAA,UAAU,EAAE,QAAhE;AAA0EC,UAAAA,GAAG,EAAE;AAA/E,SAAT;AAAA,+BACI,QAAC,UAAD;AAAY,UAAA,KAAK,EAAC,kBAAlB;AAAA,iCACI,QAAC,UAAD;AAAY,YAAA,OAAO,EAAE/E,yBAArB;AAAgD,YAAA,IAAI,EAAC,OAArD;AAAA,mCACI,QAAC,YAAD;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,cApDJ;AAAA;AAAA;AAAA;AAAA;AAAA,YA7MJ,eA0QI,QAAC,sBAAD;AAAA,8BACI,QAAC,oBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cADJ,eAGI,QAAC,cAAD;AAAA,gCACI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAE4E,YAAAA,OAAO,EAAE,MAAX;AAAmBE,YAAAA,UAAU,EAAE,QAA/B;AAAyCuB,YAAAA,cAAc,EAAE,eAAzD;AAA0EC,YAAAA,EAAE,EAAE;AAA9E,WAAT;AAAA,kCACI,QAAC,UAAD;AAAA,0DAAyClV,QAAQ,KAAK,OAAb,GAAuB,KAAvB,GAA+B,YAAxE;AAAA;AAAA;AAAA;AAAA;AAAA,kBADJ,eAEI,QAAC,GAAD;AAAK,YAAA,EAAE,EAAE;AAAEwT,cAAAA,OAAO,EAAE,MAAX;AAAmBE,cAAAA,UAAU,EAAE;AAA/B,aAAT;AAAA,oCACI,QAAC,iBAAD;AACI,cAAA,KAAK,EAAE1T,QADX;AAEI,cAAA,SAAS,MAFb;AAGI,cAAA,QAAQ,EAAE+R,gBAHd;AAII,cAAA,IAAI,EAAC,OAJT;AAKI,cAAA,EAAE,EAAE;AACAoD,gBAAAA,EAAE,EAAE,CADJ;AAEA,2CAA2B;AACvB,oCAAkB;AACd9I,oBAAAA,eAAe,EAAE,SADH;AAEd+G,oBAAAA,KAAK,EAAE,OAFO;AAGd,+BAAW;AACP/G,sBAAAA,eAAe,EAAE;AADV;AAHG;AADK;AAF3B,eALR;AAAA,sCAkBI,QAAC,YAAD;AAAc,gBAAA,KAAK,EAAC,OAApB;AAA4B,8BAAW,cAAvC;AAAA,uCACI,QAAC,UAAD;AAAY,kBAAA,KAAK,EAAC,cAAlB;AAAA,yCACI,QAAC,GAAD;AACI,oBAAA,EAAE,EAAE;AACAyI,sBAAAA,QAAQ,EAAE,MADV;AAEAlB,sBAAAA,UAAU,EAAE,QAFZ;AAGAR,sBAAAA,KAAK,EAAEpT,QAAQ,KAAK,OAAb,GAAuB,OAAvB,GAAiC;AAHxC,qBADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,sBAlBJ,eA+BI,QAAC,YAAD;AAAc,gBAAA,KAAK,EAAC,OAApB;AAA4B,8BAAW,aAAvC;AAAA,uCACI,QAAC,UAAD;AAAY,kBAAA,KAAK,EAAC,+CAAlB;AAAA,yCACI,QAAC,GAAD;AACI,oBAAA,EAAE,EAAE;AACA8U,sBAAAA,QAAQ,EAAE,MADV;AAEAlB,sBAAAA,UAAU,EAAE,QAFZ;AAGAR,sBAAAA,KAAK,EAAEpT,QAAQ,KAAK,OAAb,GAAuB,OAAvB,GAAiC;AAHxC,qBADR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,sBA/BJ;AAAA;AAAA;AAAA;AAAA;AAAA,oBADJ,eA8CI,QAAC,UAAD;AAAY,cAAA,KAAK,EAAC,sBAAlB;AAAA,qCACI,QAAC,UAAD;AACI,gBAAA,IAAI,EAAC,OADT;AAEI,gBAAA,OAAO,EAAE,MACLyM,aAAa,CACTzL,WADS,EAEThB,QAAQ,KAAK,OAAb,GAAuB,8BAAvB,GAAwD,4BAF/C,CAHrB;AAAA,uCASI,QAAC,YAAD;AAAc,kBAAA,QAAQ,EAAC;AAAvB;AAAA;AAAA;AAAA;AAAA;AATJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,oBA9CJ;AAAA;AAAA;AAAA;AAAA;AAAA,kBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBADJ,eAgEI,QAAC,mBAAD;AAAqB,UAAA,KAAK,EAAC,MAA3B;AAAkC,UAAA,MAAM,EAAE,GAA1C;AAAA,iCACI,QAAC,QAAD;AACI,YAAA,GAAG,EAAEgB,WADT;AAEI,YAAA,IAAI,EAAEhB,QAAQ,KAAK,OAAb,GAAuBuS,aAAa,CAACjI,IAArC,GAA4CgH,gBAAgB,EAFtE;AAGI,YAAA,OAAO,EAAE,EAHb;AAII,YAAA,UAAU,EAAE,EAJhB;AAKI,YAAA,MAAM,EAAE;AAAE8D,cAAAA,GAAG,EAAE,EAAP;AAAWC,cAAAA,KAAK,EAAE,EAAlB;AAAsBC,cAAAA,IAAI,EAAE,EAA5B;AAAgCC,cAAAA,MAAM,EAAE;AAAxC,aALZ;AAAA,oCAOI,QAAC,aAAD;AAAe,cAAA,eAAe,EAAC;AAA/B;AAAA;AAAA;AAAA;AAAA,oBAPJ,eAQI,QAAC,KAAD;AACI,cAAA,OAAO,EAAC,KADZ;AAEI,cAAA,aAAa,EAAG3R,KAAD,IAAW;AACtB;AACA,oBAAI5D,QAAQ,KAAK,OAAb,IAAwB4D,KAAK,CAACmB,QAAN,CAAe,KAAf,CAA5B,EAAmD;AAC/C;AACA,wBAAM,CAACL,SAAD,EAAYC,OAAZ,IAAuBf,KAAK,CAACG,KAAN,CAAY,KAAZ,CAA7B,CAF+C,CAG/C;;AACA,yBAAQ,GAAEqH,UAAU,CAAC1G,SAAD,CAAY,MAAK0G,UAAU,CAACzG,OAAD,CAAU,EAAzD;AACH,iBAPqB,CAQtB;;;AACA,uBAAOyG,UAAU,CAACxH,KAAD,CAAjB;AACH,eAZL;AAaI,cAAA,KAAK,EAAE,CAAC,EAbZ;AAcI,cAAA,UAAU,EAAC,KAdf;AAeI,cAAA,MAAM,EAAE;AAfZ;AAAA;AAAA;AAAA;AAAA,oBARJ,eAyBI,QAAC,KAAD;AAAO,cAAA,aAAa,EAAGA,KAAD,IAAY,MAAK,CAACA,KAAK,GAAG,IAAT,EAAewF,OAAf,CAAuB,CAAvB,CAA0B,GAAjE;AAAqE,cAAA,IAAI,EAAE;AAAE0L,gBAAAA,QAAQ,EAAE;AAAZ;AAA3E;AAAA;AAAA;AAAA;AAAA,oBAzBJ,eA0BI,QAAC,OAAD;AACI,cAAA,SAAS,EAAE,CAAClR,KAAD,EAAQsD,IAAR,KAAiB,CAACiD,cAAc,CAACvG,KAAD,CAAf,EAAwB,OAAxB,CADhC;AAEI,cAAA,cAAc,EAAGwI,KAAD,IAAW;AACvB,oBAAIpM,QAAQ,KAAK,KAAjB,EAAwB;AACpB,sBAAI;AACA;AACA,wBAAI,OAAOoM,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,CAACrH,QAAN,CAAe,GAAf,CAA7B,IAAoDqH,KAAK,CAACrI,KAAN,CAAY,GAAZ,EAAiBlB,MAAjB,KAA4B,CAApF,EAAuF;AACnF,4BAAM2S,KAAK,GAAGpJ,KAAK,CAACrI,KAAN,CAAY,GAAZ,CAAd;AACA,4BAAM2D,IAAI,GAAGxC,QAAQ,CAACsQ,KAAK,CAAC,CAAD,CAAN,CAArB;AACA,4BAAM7N,KAAK,GAAGzC,QAAQ,CAACsQ,KAAK,CAAC,CAAD,CAAN,CAAR,GAAqB,CAAnC;AACA,4BAAM5N,GAAG,GAAG1C,QAAQ,CAACsQ,KAAK,CAAC,CAAD,CAAN,CAApB;;AAEA,0BAAI,CAAC3B,KAAK,CAACnM,IAAD,CAAN,IAAgB,CAACmM,KAAK,CAAClM,KAAD,CAAtB,IAAiC,CAACkM,KAAK,CAACjM,GAAD,CAA3C,EAAkD;AAC9C,8BAAMtC,IAAI,GAAG,IAAI/C,IAAJ,CAASmF,IAAT,EAAeC,KAAf,EAAsBC,GAAtB,EAA2B,EAA3B,EAA+B,CAA/B,EAAkC,CAAlC,CAAb;AACA,+BAAOxJ,MAAM,CAACkH,IAAD,EAAO,YAAP,CAAb;AACH;AACJ;;AACD,2BAAO0G,MAAM,CAACI,KAAD,CAAb;AACH,mBAdD,CAcE,OAAOhL,KAAP,EAAc;AACZ;AAAoBC,oBAAAA,OAAO,CAACD,KAAR,CAAc,GAAGE,KAAK,CAAE,8BAAF,EAAgC,wBAAhC,EAA0DF,KAA1D,CAAtB;AACpB,2BAAO4K,MAAM,CAACI,KAAD,CAAb;AACH;AACJ,iBAnBD,MAmBO;AACH,sBAAI;AACA;AACA,0BAAMpB,OAAO,GAAG9F,QAAQ,CAACkH,KAAD,CAAxB;;AACA,wBAAIyH,KAAK,CAAC7I,OAAD,CAAL,IAAkBA,OAAO,GAAG,CAA5B,IAAiCA,OAAO,GAAG,EAA/C,EAAmD;AAC/C,6BAAQ,UAASoB,KAAM,EAAvB;AACH;;AAED,0BAAM9J,KAAK,GAAG,IAAIC,IAAJ,EAAd;AACA,0BAAMmF,IAAI,GAAGpF,KAAK,CAACI,WAAN,EAAb,CARA,CAUA;;AACA,0BAAM6C,cAAc,GAAG,IAAIhD,IAAJ,CAASmF,IAAT,EAAe,CAAf,EAAkB,CAAlB,CAAvB,CAXA,CAaA;;AACA,0BAAMwD,SAAS,GAAG3F,cAAc,CAACI,MAAf,MAA2B,CAA7C;AACA,0BAAM8P,YAAY,GAAG,IAAIlT,IAAJ,CAASgD,cAAT,CAArB;AACAkQ,oBAAAA,YAAY,CAACnS,OAAb,CACIiC,cAAc,CAAChC,OAAf,MAA4B2H,SAAS,IAAI,CAAb,GAAiB,IAAIA,SAArB,GAAiC,IAAIA,SAAjE,CADJ,EAhBA,CAoBA;;AACA,0BAAMwK,SAAS,GAAG,IAAInT,IAAJ,CAASkT,YAAT,CAAlB;AACAC,oBAAAA,SAAS,CAACpS,OAAV,CAAkBmS,YAAY,CAAClS,OAAb,KAAyB,CAACyH,OAAO,GAAG,CAAX,IAAgB,CAA3D,EAtBA,CAwBA;;AACA,0BAAM2K,QAAQ,GAAG,IAAIpT,IAAJ,CAASmT,SAAT,CAAjB;AACAC,oBAAAA,QAAQ,CAACrS,OAAT,CAAiBoS,SAAS,CAACnS,OAAV,KAAsB,CAAvC;AAEA,2BAAQ,UAAS6I,KAAM,KAAIhO,MAAM,CAACsX,SAAD,EAAY,OAAZ,CAAqB,MAAKtX,MAAM,CAACuX,QAAD,EAAW,OAAX,CAAoB,EAArF;AACH,mBA7BD,CA6BE,OAAOvU,KAAP,EAAc;AACZ;AAAoBC,oBAAAA,OAAO,CAACD,KAAR,CAAc,GAAGE,KAAK,CAAE,8BAAF,EAAgC,wBAAhC,EAA0DF,KAA1D,CAAtB;AACpB,2BAAQ,UAASgL,KAAM,EAAvB;AACH;AACJ;AACJ,eAzDL;AA0DI,cAAA,YAAY,EAAE;AAAE0I,gBAAAA,QAAQ,EAAE;AAAZ;AA1DlB;AAAA;AAAA;AAAA;AAAA,oBA1BJ,eAsFI,QAAC,GAAD;AACI,cAAA,OAAO,EAAC,OADZ;AAEI,cAAA,IAAI,EAAC,SAFT;AAGI,cAAA,KAAK,EAAE;AACHc,gBAAAA,QAAQ,EAAE,KADP;AAEHC,gBAAAA,SAAS,EAAE,CAACjS,KAAD,EAAQkS,KAAR,EAAetB,KAAf,KAAyB;AAChC;AACA,wBAAMuB,QAAQ,GAAG,CAAC/V,QAAQ,KAAK,KAAb,GAAqBuS,aAAa,CAACjI,IAAnC,GAA0CiI,aAAa,CAACzH,KAAzD,EAAgEuE,MAAhE,CACb,CAAC2G,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGC,IAAI,CAACrS,KADb,EAEb,CAFa,CAAjB,CAFgC,CAOhC;;AACA,wBAAMsS,eAAe,GAAItS,KAAK,GAAGmS,QAAT,GAAqB,GAA7C,CARgC,CAUhC;;AACA,wBAAMI,UAAU,GACZ3B,KAAK,MAAMxU,QAAQ,KAAK,KAAb,GAAqBuS,aAAa,CAACjI,IAAd,CAAmBzH,MAAnB,GAA4B,CAAjD,GAAqD0P,aAAa,CAACzH,KAAd,CAAoBjI,MAApB,GAA6B,CAAxF,CADT;;AAGA,sBAAIsT,UAAJ,EAAgB;AACZ;AACA,0BAAMC,gBAAgB,GAAG,CAACpW,QAAQ,KAAK,KAAb,GAAqBuS,aAAa,CAACjI,IAAnC,GAA0CiI,aAAa,CAACzH,KAAzD,EACpBf,KADoB,CACd,CADc,EACX,CAAC,CADU,EAEpBsF,MAFoB,CAEb,CAAC2G,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGvQ,IAAI,CAAC4Q,KAAL,CAAYJ,IAAI,CAACrS,KAAL,GAAamS,QAAd,GAA0B,IAArC,IAA6C,EAFrD,EAEyD,CAFzD,CAAzB,CAFY,CAMZ;;AACA,2BAAQ,GAAE,CAAC,MAAMK,gBAAP,EAAyBhN,OAAzB,CAAiC,CAAjC,CAAoC,GAA9C;AACH,mBAtB+B,CAwBhC;;;AACA,yBAAQ,GAAE3D,IAAI,CAAC4Q,KAAL,CAAWH,eAAe,GAAG,EAA7B,IAAmC,EAAG,GAAhD;AACH,iBA5BE;AA6BHpB,gBAAAA,QAAQ,EAAE;AA7BP,eAHX;AAAA,wBAmCK,CAAC9U,QAAQ,KAAK,OAAb,GAAuBuS,aAAa,CAACjI,IAArC,GAA4CgH,gBAAgB,EAA7D,EAAiExP,GAAjE,CAAqE,CAACgU,KAAD,EAAQtB,KAAR,kBAClE,QAAC,IAAD;AAA4B,gBAAA,IAAI,EAAEsB,KAAK,CAACrS,IAAN,KAAe,eAAf,GAAiC,SAAjC,GAA6C;AAA/E,iBAAY,QAAO+Q,KAAM,EAAzB;AAAA;AAAA;AAAA;AAAA,sBADH;AAnCL;AAAA;AAAA;AAAA;AAAA,oBAtFJ;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,gBAhEJ;AAAA;AAAA;AAAA;AAAA;AAAA,cAHJ,eAqMI,QAAC,cAAD;AAAA,gCACI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAEhB,YAAAA,OAAO,EAAE,MAAX;AAAmBE,YAAAA,UAAU,EAAE,QAA/B;AAAyCuB,YAAAA,cAAc,EAAE,eAAzD;AAA0EC,YAAAA,EAAE,EAAE;AAA9E,WAAT;AAAA,kCACI,QAAC,UAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBADJ,eAEI,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,sBAAlB;AAAA,mCACI,QAAC,UAAD;AAAY,cAAA,IAAI,EAAC,OAAjB;AAAyB,cAAA,OAAO,EAAE,MAAMzI,aAAa,CAAC5L,YAAD,EAAe,+BAAf,CAArD;AAAA,qCACI,QAAC,YAAD;AAAc,gBAAA,QAAQ,EAAC;AAAvB;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,kBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBADJ,eASI,QAAC,mBAAD;AAAqB,UAAA,KAAK,EAAC,MAA3B;AAAkC,UAAA,MAAM,EAAE,GAA1C;AAAA,iCACI,QAAC,QAAD;AACI,YAAA,GAAG,EAAEA,YADT;AAEI,YAAA,IAAI,EAAE0R,aAAa,CAAC1O,KAAd,CAAoB/B,GAApB,CAAyBsF,IAAD,IAAU;AACpC,oBAAML,KAAK,GAAGwL,aAAa,CAAC1O,KAAd,CAAoBwL,MAApB,CAA2B,CAAC2G,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGC,IAAI,CAACrS,KAArD,EAA4D,CAA5D,CAAd;AACA,oBAAMsE,UAAU,GAAId,IAAI,CAACxD,KAAL,GAAamD,KAAd,GAAuB,GAA1C;AACA,qBAAO,EACH,GAAGK,IADA;AAEHc,gBAAAA,UAFG;AAGHoO,gBAAAA,SAAS,EAAE/Y,eAAe,CAAC6J,IAAI,CAACxD,KAAN,EAAamD,KAAb;AAHvB,eAAP;AAKH,aARK,CAFV;AAWI,YAAA,MAAM,EAAC,UAXX;AAYI,YAAA,MAAM,EAAE;AAAEqO,cAAAA,GAAG,EAAE,CAAP;AAAUC,cAAAA,KAAK,EAAE,EAAjB;AAAqBC,cAAAA,IAAI,EAAE,EAA3B;AAA+BC,cAAAA,MAAM,EAAE;AAAvC,aAZZ;AAAA,oCAcI,QAAC,aAAD;AAAe,cAAA,eAAe,EAAC,KAA/B;AAAqC,cAAA,UAAU,EAAE;AAAjD;AAAA;AAAA;AAAA;AAAA,oBAdJ,eAeI,QAAC,KAAD;AAAO,cAAA,IAAI,EAAC,QAAZ;AAAqB,cAAA,aAAa,EAAG3R,KAAD,IAAY,MAAK,CAACA,KAAK,GAAG,IAAT,EAAewF,OAAf,CAAuB,CAAvB,CAA0B;AAA/E;AAAA;AAAA;AAAA;AAAA,oBAfJ,eAgBI,QAAC,KAAD;AAAO,cAAA,OAAO,EAAC,MAAf;AAAsB,cAAA,IAAI,EAAC,UAA3B;AAAsC,cAAA,KAAK,EAAE,EAA7C;AAAiD,cAAA,IAAI,EAAE;AAAEmN,gBAAAA,IAAI,EAAE;AAAR;AAAvD;AAAA;AAAA;AAAA;AAAA,oBAhBJ,eAiBI,QAAC,OAAD;AACI,cAAA,SAAS,EAAE,CAAC3S,KAAD,EAAQsD,IAAR,EAAcsP,KAAd,KAAwB;AAC/B,sBAAMtO,UAAU,GAAItE,KAAK,GAAG2O,aAAa,CAAC1O,KAAd,CAAoBwL,MAApB,CAA2B,CAAC2G,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGC,IAAI,CAACrS,KAArD,EAA4D,CAA5D,CAAT,GAA2E,GAA9F;AACA,uBAAO,CAAE,GAAEuG,cAAc,CAACvG,KAAD,CAAQ,KAAIsE,UAAU,CAACkB,OAAX,CAAmB,CAAnB,CAAsB,IAApD,EAAyD,OAAzD,CAAP;AACH,eAJL;AAKI,cAAA,cAAc,EAAGgD,KAAD,IAAY,SAAQA,KAAM,EAL9C;AAMI,cAAA,YAAY,EAAE;AACVC,gBAAAA,eAAe,EAAE,MADP;AAEVE,gBAAAA,MAAM,EAAE,mBAFE;AAGVC,gBAAAA,YAAY,EAAE,KAHJ;AAIVF,gBAAAA,OAAO,EAAE;AAJC;AANlB;AAAA;AAAA;AAAA;AAAA,oBAjBJ,eA8BI,QAAC,GAAD;AACI,cAAA,OAAO,EAAC,OADZ;AAEI,cAAA,IAAI,EAAC,QAFT;AAGI,cAAA,IAAI,EAAC,SAHT;AAII,cAAA,UAAU,EAAE;AAAEiK,gBAAAA,IAAI,EAAE;AAAR,eAJhB;AAKI,cAAA,KAAK,EAAE;AACHX,gBAAAA,QAAQ,EAAE,OADP;AAEHa,gBAAAA,OAAO,EAAE;AAAA,sBAAC;AAAE7S,oBAAAA;AAAF,mBAAD;AAAA,yBAAgB,MAAK,CAACA,KAAK,GAAG,IAAT,EAAewF,OAAf,CAAuB,CAAvB,CAA0B,GAA/C;AAAA,iBAFN;AAGH0L,gBAAAA,QAAQ,EAAE;AAHP,eALX;AAAA,wBAWKvC,aAAa,CAAC1O,KAAd,CAAoB/B,GAApB,CAAwB,CAACgU,KAAD,EAAQtB,KAAR,KAAkB;AACvC,sBAAMzN,KAAK,GAAGwL,aAAa,CAAC1O,KAAd,CAAoBwL,MAApB,CAA2B,CAAC2G,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGC,IAAI,CAACrS,KAArD,EAA4D,CAA5D,CAAd;AACA,oCAAO,QAAC,IAAD;AAA4B,kBAAA,IAAI,EAAErG,eAAe,CAACuY,KAAK,CAAClS,KAAP,EAAcmD,KAAd;AAAjD,mBAAY,QAAOyN,KAAM,EAAzB;AAAA;AAAA;AAAA;AAAA,wBAAP;AACH,eAHA;AAXL;AAAA;AAAA;AAAA;AAAA,oBA9BJ;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,gBATJ,eA0DI,QAAC,aAAD;AAAA,kCACI,QAAC,UAAD;AAAY,YAAA,OAAO,EAAC,SAApB;AAA8B,YAAA,EAAE,EAAE;AAAEpB,cAAAA,KAAK,EAAE,MAAT;AAAiBQ,cAAAA,UAAU,EAAE;AAA7B,aAAlC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBADJ,EAIKpW,gBAAgB,GAAGsE,GAAnB,CAAuB,CAACsR,KAAD,EAAQoB,KAAR,kBACpB,QAAC,GAAD;AAAiB,YAAA,SAAS,EAAC,WAA3B;AAAuC,YAAA,EAAE,EAAE;AAAEnI,cAAAA,eAAe,EAAE+G;AAAnB;AAA3C,aAAUoB,KAAV;AAAA;AAAA;AAAA;AAAA,kBADH,CAJL,eAOI,QAAC,UAAD;AAAY,YAAA,OAAO,EAAC,SAApB;AAA8B,YAAA,EAAE,EAAE;AAAEpB,cAAAA,KAAK,EAAE,MAAT;AAAiBQ,cAAAA,UAAU,EAAE;AAA7B,aAAlC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAPJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBA1DJ;AAAA;AAAA;AAAA;AAAA;AAAA,cArMJ,eA4QI,QAAC,eAAD;AAAA,gCACI,QAAC,cAAD;AAAA,kCACI,QAAC,GAAD;AAAK,YAAA,EAAE,EAAE;AAAEJ,cAAAA,OAAO,EAAE,MAAX;AAAmBE,cAAAA,UAAU,EAAE,QAA/B;AAAyCuB,cAAAA,cAAc,EAAE,eAAzD;AAA0EC,cAAAA,EAAE,EAAE;AAA9E,aAAT;AAAA,oCACI,QAAC,UAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBADJ,eAEI,QAAC,UAAD;AAAY,cAAA,KAAK,EAAC,sBAAlB;AAAA,qCACI,QAAC,UAAD;AACI,gBAAA,IAAI,EAAC,OADT;AAEI,gBAAA,OAAO,EAAE,MAAMzI,aAAa,CAAC3L,gBAAD,EAAmB,mCAAnB,CAFhC;AAAA,uCAII,QAAC,YAAD;AAAc,kBAAA,QAAQ,EAAC;AAAvB;AAAA;AAAA;AAAA;AAAA;AAJJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,oBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,kBADJ,eAYI,QAAC,mBAAD;AAAqB,YAAA,KAAK,EAAC,MAA3B;AAAkC,YAAA,MAAM,EAAE,GAA1C;AAAA,mCACI,QAAC,QAAD;AAAU,cAAA,GAAG,EAAEA,gBAAf;AAAA,sCACI,QAAC,GAAD;AACI,gBAAA,IAAI,EAAEyR,aAAa,CAACrQ,KADxB;AAEI,gBAAA,OAAO,EAAC,OAFZ;AAGI,gBAAA,OAAO,EAAC,MAHZ;AAII,gBAAA,EAAE,EAAC,KAJP;AAKI,gBAAA,EAAE,EAAC,KALP;AAMI,gBAAA,WAAW,EAAE,EANjB;AAOI,gBAAA,KAAK,EAAE,SAAuE;AAAA,sBAAtE;AAAEwU,oBAAAA,EAAF;AAAMC,oBAAAA,EAAN;AAAUC,oBAAAA,QAAV;AAAoBC,oBAAAA,WAApB;AAAiCC,oBAAAA,WAAjC;AAA8ClT,oBAAAA,KAA9C;AAAqDsE,oBAAAA;AAArD,mBAAsE;AAC1E,wBAAM6O,MAAM,GAAGtR,IAAI,CAACuR,EAAL,GAAU,GAAzB;AACA,wBAAMC,MAAM,GAAGH,WAAW,GAAG,EAA7B;AACA,wBAAMI,CAAC,GAAGR,EAAE,GAAGO,MAAM,GAAGxR,IAAI,CAAC0R,GAAL,CAAS,CAACP,QAAD,GAAYG,MAArB,CAAxB;AACA,wBAAMK,CAAC,GAAGT,EAAE,GAAGM,MAAM,GAAGxR,IAAI,CAAC4R,GAAL,CAAS,CAACT,QAAD,GAAYG,MAArB,CAAxB;AACA,sCACI;AAAA,4CACI;AACI,sBAAA,CAAC,EAAEG,CADP;AAEI,sBAAA,CAAC,EAAEE,CAFP;AAGI,sBAAA,IAAI,EAAC,MAHT;AAII,sBAAA,UAAU,EAAEF,CAAC,GAAGR,EAAJ,GAAS,OAAT,GAAmB,KAJnC;AAKI,sBAAA,gBAAgB,EAAC,SALrB;AAAA,gCAOM,GAAExO,UAAH,aAAGA,UAAH,uBAAGA,UAAU,CAAEkB,OAAZ,CAAoB,CAApB,CAAuB;AAP/B;AAAA;AAAA;AAAA;AAAA,4BADJ,eAUI;AACI,sBAAA,CAAC,EAAE8N,CADP;AAEI,sBAAA,CAAC,EAAEE,CAAC,GAAG,EAFX;AAGI,sBAAA,IAAI,EAAC,MAHT;AAII,sBAAA,UAAU,EAAEF,CAAC,GAAGR,EAAJ,GAAS,OAAT,GAAmB,KAJnC;AAKI,sBAAA,gBAAgB,EAAC,SALrB;AAMI,sBAAA,KAAK,EAAE;AAAE5B,wBAAAA,QAAQ,EAAE;AAAZ,uBANX;AAAA,gCAQK3K,cAAc,CAACvG,KAAD;AARnB;AAAA;AAAA;AAAA;AAAA,4BAVJ;AAAA;AAAA;AAAA;AAAA;AAAA,0BADJ;AAuBH,iBAnCL;AAAA,0BAqCK2O,aAAa,CAACrQ,KAAd,CAAoBJ,GAApB,CAAwB,CAACgU,KAAD,EAAQtB,KAAR,kBACrB,QAAC,IAAD;AAA4B,kBAAA,IAAI,EAAE/W,MAAM,CAAC+W,KAAK,GAAG/W,MAAM,CAACoF,MAAhB;AAAxC,mBAAY,QAAO2R,KAAM,EAAzB;AAAA;AAAA;AAAA;AAAA,wBADH;AArCL;AAAA;AAAA;AAAA;AAAA,sBADJ,eA0CI,QAAC,OAAD;AAAS,gBAAA,OAAO,eAAE,QAAC,aAAD;AAAA;AAAA;AAAA;AAAA;AAAlB;AAAA;AAAA;AAAA;AAAA,sBA1CJ,eA2CI,QAAC,MAAD;AACI,gBAAA,aAAa,EAAC,QADlB;AAEI,gBAAA,MAAM,EAAE,EAFZ;AAGI,gBAAA,SAAS,EAAE,CAAC5Q,KAAD,EAAQkS,KAAR,kBAAkB;AAAM,kBAAA,KAAK,EAAE;AAAE1C,oBAAAA,KAAK,EAAE,MAAT;AAAiBkE,oBAAAA,WAAW,EAAE;AAA9B,mBAAb;AAAA,4BAAsD1T;AAAtD;AAAA;AAAA;AAAA;AAAA;AAHjC;AAAA;AAAA;AAAA;AAAA,sBA3CJ;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,kBAZJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBADJ,eAkEI,QAAC,cAAD;AAAA,kCACI,QAAC,GAAD;AAAK,YAAA,EAAE,EAAE;AAAE4P,cAAAA,OAAO,EAAE,MAAX;AAAmBE,cAAAA,UAAU,EAAE,QAA/B;AAAyCuB,cAAAA,cAAc,EAAE,eAAzD;AAA0EC,cAAAA,EAAE,EAAE;AAA9E,aAAT;AAAA,oCACI,QAAC,UAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBADJ,eAEI,QAAC,UAAD;AAAY,cAAA,KAAK,EAAC,sBAAlB;AAAA,qCACI,QAAC,UAAD;AAAY,gBAAA,IAAI,EAAC,OAAjB;AAAyB,gBAAA,OAAO,EAAE,MAAMzI,aAAa,CAAC1L,aAAD,EAAgB,iCAAhB,CAArD;AAAA,uCACI,QAAC,YAAD;AAAc,kBAAA,QAAQ,EAAC;AAAvB;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,oBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,kBADJ,eASI,QAAC,mBAAD;AAAqB,YAAA,KAAK,EAAC,MAA3B;AAAkC,YAAA,MAAM,EAAE,GAA1C;AAAA,mCACI,QAAC,QAAD;AACI,cAAA,GAAG,EAAEA,aADT;AAEI,cAAA,IAAI,EAAEwR,aAAa,CAACzI,SAFxB;AAGI,cAAA,OAAO,EAAE,EAHb;AAII,cAAA,UAAU,EAAE,EAJhB;AAKI,cAAA,MAAM,EAAE;AAAEsL,gBAAAA,GAAG,EAAE,EAAP;AAAWC,gBAAAA,KAAK,EAAE,EAAlB;AAAsBC,gBAAAA,IAAI,EAAE,EAA5B;AAAgCC,gBAAAA,MAAM,EAAE;AAAxC,eALZ;AAAA,sCAOI,QAAC,aAAD;AAAe,gBAAA,eAAe,EAAC;AAA/B;AAAA;AAAA;AAAA;AAAA,sBAPJ,eAQI,QAAC,KAAD;AACI,gBAAA,OAAO,EAAC,MADZ;AAEI,gBAAA,aAAa,EAAG3R,KAAD,IAAW;AACtB,yBAAOA,KAAK,CACPG,KADE,CACI,GADJ,EAEFjC,GAFE,CAEGyV,IAAD,IAAUA,IAAI,CAACC,MAAL,CAAY,CAAZ,CAFZ,EAGFC,IAHE,CAGG,EAHH,EAIFC,WAJE,EAAP;AAKH;AARL;AAAA;AAAA;AAAA;AAAA,sBARJ,EAiBO,GAjBP,eAkBI,QAAC,KAAD;AAAO,gBAAA,aAAa,EAAG9T,KAAD,IAAY,MAAK,CAACA,KAAK,GAAG,IAAT,EAAewF,OAAf,CAAuB,CAAvB,CAA0B;AAAjE;AAAA;AAAA;AAAA;AAAA,sBAlBJ,eAmBI,QAAC,OAAD;AACI,gBAAA,SAAS,EAAE,CAACxF,KAAD,EAAQsD,IAAR,KAAiB,CAACiD,cAAc,CAACvG,KAAD,CAAf,EAAwB,OAAxB,CADhC;AAEI,gBAAA,cAAc,EAAGwI,KAAD,IAAY,GAAEA,KAAM;AAFxC;AAAA;AAAA;AAAA;AAAA,sBAnBJ,eAuBI,QAAC,GAAD;AACI,gBAAA,OAAO,EAAC,OADZ;AAEI,gBAAA,IAAI,EAAC,SAFT;AAGI,gBAAA,KAAK,EAAE;AACHwJ,kBAAAA,QAAQ,EAAE,KADP;AAEHC,kBAAAA,SAAS,EAAGjS,KAAD,IAAW;AAClB,0BAAMmS,QAAQ,GAAGxD,aAAa,CAACzI,SAAd,CAAwBuF,MAAxB,CAA+B,CAAC2G,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGC,IAAI,CAAClP,KAAzD,EAAgE,CAAhE,CAAjB;AACA,2BAAQ,GAAE,CAAEnD,KAAK,GAAGmS,QAAT,GAAqB,GAAtB,EAA2B3M,OAA3B,CAAmC,CAAnC,CAAsC,GAAhD;AACH;AALE,iBAHX;AAAA,0BAWKmJ,aAAa,CAACzI,SAAd,CAAwBhI,GAAxB,CAA4B,CAACgU,KAAD,EAAQtB,KAAR,kBACzB,QAAC,IAAD;AAEI,kBAAA,IAAI,EAAC,SAFT;AAGI,kBAAA,OAAO,EAAE1V,aAAa,KAAK,KAAlB,IAA2BA,aAAa,KAAKgX,KAAK,CAAC5O,IAAnD,GAA0D,CAA1D,GAA8D,GAH3E;AAII,kBAAA,MAAM,EAAC,SAJX;AAKI,kBAAA,OAAO,EAAE,MAAM+K,gBAAgB,CAAC6D,KAAK,CAAC5O,IAAP;AALnC,mBACU,QAAOsN,KAAM,EADvB;AAAA;AAAA;AAAA;AAAA,wBADH;AAXL;AAAA;AAAA;AAAA;AAAA,sBAvBJ;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,kBATJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBAlEJ;AAAA;AAAA;AAAA;AAAA;AAAA,cA5QJ,eAwYI,QAAC,GAAD;AAAA,gCACI,QAAC,UAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBADJ,eAEI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAEU,YAAAA,EAAE,EAAE;AAAN,WAAT;AAAA,qBACK3C,aAAa,CAACvI,QAAd,CAAuBlI,GAAvB,CAA4BmN,IAAD,iBACxB,QAAC,SAAD;AAEI,YAAA,QAAQ,EAAEzO,iBAAiB,CAACyO,IAAI,CAAC/H,IAAN,CAAjB,IAAgC,KAF9C;AAGI,YAAA,QAAQ,EAAE,CAACyQ,CAAD,EAAI3E,UAAJ,KAAmBD,oBAAoB,CAAC9D,IAAI,CAAC/H,IAAN,EAAY8L,UAAZ,CAHrD;AAAA,oCAKI,QAAC,gBAAD;AACI,cAAA,UAAU,eAAE,QAAC,cAAD;AAAgB,gBAAA,EAAE,EAAE;AAAEI,kBAAAA,KAAK,EAAE;AAAT;AAApB;AAAA;AAAA;AAAA;AAAA,sBADhB;AAEI,+BAAgB,GAAEnE,IAAI,CAAC/H,IAAK,UAFhC;AAGI,cAAA,EAAE,EAAG,GAAE+H,IAAI,CAAC/H,IAAK,SAHrB;AAII,cAAA,EAAE,EAAE;AACA0Q,gBAAAA,OAAO,EAAE,SADT;AAEAxE,gBAAAA,KAAK,EAAE,OAFP;AAGA,kCAAkB;AACdyE,kBAAAA,SAAS,EAAE;AADG,iBAHlB;AAMA,+DAA+C;AAC3CC,kBAAAA,MAAM,EAAE;AADmC;AAN/C,eAJR;AAAA,qCAeI,QAAC,GAAD;AAAK,gBAAA,EAAE,EAAE;AAAEtE,kBAAAA,OAAO,EAAE,MAAX;AAAmByB,kBAAAA,cAAc,EAAE,eAAnC;AAAoDtH,kBAAAA,KAAK,EAAE,MAA3D;AAAmE+F,kBAAAA,UAAU,EAAE;AAA/E,iBAAT;AAAA,wCACI,QAAC,GAAD;AAAK,kBAAA,EAAE,EAAE;AAAEF,oBAAAA,OAAO,EAAE,MAAX;AAAmBE,oBAAAA,UAAU,EAAE,QAA/B;AAAyCC,oBAAAA,GAAG,EAAE;AAA9C,mBAAT;AAAA,yCACI,QAAC,UAAD;AAAY,oBAAA,UAAU,EAAC,MAAvB;AAA8B,oBAAA,KAAK,EAAC,OAApC;AAAA,8BACK1E,IAAI,CAAC/H;AADV;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,wBADJ,eAMI,QAAC,UAAD;AAAY,kBAAA,OAAO,EAAC,OAApB;AAA4B,kBAAA,KAAK,EAAC,OAAlC;AAAA,6BACKiD,cAAc,CAAC8E,IAAI,CAACrL,KAAN,CADnB,QACmCqL,IAAI,CAAC/G,UAAL,CAAgBkB,OAAhB,CAAwB,CAAxB,CADnC;AAAA;AAAA;AAAA;AAAA;AAAA,wBANJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAfJ;AAAA;AAAA;AAAA;AAAA,oBALJ,eA+BI,QAAC,gBAAD;AAAA,qCACI,QAAC,GAAD;AAAK,gBAAA,EAAE,EAAE;AAAE8L,kBAAAA,EAAE,EAAE;AAAN,iBAAT;AAAA,wCACI,QAAC,GAAD;AACI,kBAAA,EAAE,EAAE;AACA1B,oBAAAA,OAAO,EAAE,MADT;AAEAE,oBAAAA,UAAU,EAAE,QAFZ;AAGAC,oBAAAA,GAAG,EAAE,CAHL;AAIAuB,oBAAAA,EAAE,EAAE,CAJJ;AAKA6C,oBAAAA,CAAC,EAAE,CALH;AAMAC,oBAAAA,YAAY,EAAE;AANd,mBADR;AAAA,0CAUI,QAAC,UAAD;AACI,oBAAA,SAAS,EAAC,MADd;AAEI,oBAAA,OAAO,EAAGjI,CAAD,IAAO;AACZ,0BAAI,CAACoD,iBAAiB,CAAClE,IAAI,CAAC/H,IAAN,CAAtB,EAAmC;AAC/BiL,wBAAAA,cAAc,CAAClD,IAAI,CAAC/H,IAAN,EAAY6I,CAAZ,CAAd;AACH;AACJ,qBANL;AAOI,oBAAA,EAAE,EAAE;AACAqD,sBAAAA,KAAK,EAAE,WADP;AAEA6E,sBAAAA,MAAM,EAAE,SAFR;AAGAC,sBAAAA,cAAc,EAAE;AAHhB,qBAPR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAVJ,eAyBI,QAAC,UAAD;AACI,oBAAA,SAAS,EAAC,MADd;AAEI,oBAAA,OAAO,EAAGnI,CAAD,IAAO;AACZ,0BAAIoD,iBAAiB,CAAClE,IAAI,CAAC/H,IAAN,CAArB,EAAkC;AAC9BiL,wBAAAA,cAAc,CAAClD,IAAI,CAAC/H,IAAN,EAAY6I,CAAZ,CAAd;AACH;AACJ,qBANL;AAOI,oBAAA,EAAE,EAAE;AACAqD,sBAAAA,KAAK,EAAE,WADP;AAEA6E,sBAAAA,MAAM,EAAE,SAFR;AAGAC,sBAAAA,cAAc,EAAE;AAHhB,qBAPR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAzBJ;AAAA;AAAA;AAAA;AAAA;AAAA,wBADJ,EA0CKjJ,IAAI,CAAC7M,SAAL,CAAeN,GAAf,CAAoBkP,QAAD,IAAc;AAC9B,wBAAMiC,OAAO,GAAI,GAAEhE,IAAI,CAAC/H,IAAK,IAAG8J,QAAQ,CAAC9J,IAAK,EAA9C;AACA,wBAAM8L,UAAU,GAAGtS,aAAa,CAACuS,OAAD,CAAb,IAA0B,KAA7C;AAEA,sCACI,QAAC,SAAD;AAEI,oBAAA,QAAQ,EAAED,UAFd;AAGI,oBAAA,QAAQ,EAAE,CAAC2E,CAAD,EAAIQ,cAAJ,KAAuBjF,gBAAgB,CAACD,OAAD,EAAUkF,cAAV,CAHrD;AAAA,4CAKI,QAAC,gBAAD;AACI,sBAAA,UAAU,eAAE,QAAC,cAAD;AAAgB,wBAAA,EAAE,EAAE;AAAE/E,0BAAAA,KAAK,EAAE;AAAT;AAApB;AAAA;AAAA;AAAA;AAAA,8BADhB;AAEI,uCAAgB,GAAEH,OAAQ,UAF9B;AAGI,sBAAA,EAAE,EAAG,GAAEA,OAAQ,SAHnB;AAII,sBAAA,EAAE,EAAE;AACA2E,wBAAAA,OAAO,EAAE,WADT;AAEAxE,wBAAAA,KAAK,EAAE,OAFP;AAGA,0CAAkB;AACdyE,0BAAAA,SAAS,EAAE;AADG,yBAHlB;AAMA,uEAA+C;AAC3CC,0BAAAA,MAAM,EAAE;AADmC;AAN/C,uBAJR;AAAA,6CAeI,QAAC,GAAD;AACI,wBAAA,EAAE,EAAE;AACAtE,0BAAAA,OAAO,EAAE,MADT;AAEAyB,0BAAAA,cAAc,EAAE,eAFhB;AAGAtH,0BAAAA,KAAK,EAAE,MAHP;AAIA+F,0BAAAA,UAAU,EAAE;AAJZ,yBADR;AAAA,gDAQI,QAAC,UAAD;AAAY,0BAAA,UAAU,EAAC,MAAvB;AAA8B,0BAAA,KAAK,EAAC,OAApC;AAAA,oCACK1C,QAAQ,CAAC9J;AADd;AAAA;AAAA;AAAA;AAAA,gCARJ,eAWI,QAAC,UAAD;AAAY,0BAAA,OAAO,EAAC,OAApB;AAA4B,0BAAA,KAAK,EAAC,OAAlC;AAAA,qCACKiD,cAAc,CAAC6G,QAAQ,CAACpN,KAAV,CADnB,QACuCoN,QAAQ,CAAC9I,UAAT,CAAoBkB,OAApB,CAA4B,CAA5B,CADvC;AAAA;AAAA;AAAA;AAAA;AAAA,gCAXJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAfJ;AAAA;AAAA;AAAA;AAAA,4BALJ,eAoCI,QAAC,gBAAD;AAAA,6CACI,QAAC,cAAD;AAAgB,wBAAA,SAAS,EAAE3O,KAA3B;AAAkC,wBAAA,EAAE,EAAE;AAAEya,0BAAAA,EAAE,EAAE;AAAN,yBAAtC;AAAA,+CACI,QAAC,KAAD;AAAO,0BAAA,IAAI,EAAC,OAAZ;AAAA,kDACI,QAAC,SAAD;AAAA,mDACI,QAAC,QAAD;AAAA,sDACI,QAAC,eAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sCADJ,eAEI,QAAC,eAAD;AAAiB,gCAAA,KAAK,EAAC,OAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sCAFJ,eAGI,QAAC,eAAD;AAAiB,gCAAA,KAAK,EAAC,OAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sCAHJ;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,kCADJ,eAQI,QAAC,SAAD;AAAA,sCACKlE,QAAQ,CAAChK,QAAT,CAAkBlF,GAAlB,CAAuBmP,OAAD,iBACnB,QAAC,cAAD;AAAA,sDAGI,QAAC,SAAD;AAAA,0CAAYA,OAAO,CAAC/J;AAApB;AAAA;AAAA;AAAA;AAAA,sCAHJ,eAII,QAAC,SAAD;AAAW,gCAAA,KAAK,EAAC,OAAjB;AAAA,0CACKiD,cAAc,CAAC8G,OAAO,CAACrN,KAAT;AADnB;AAAA;AAAA;AAAA;AAAA,sCAJJ,eAOI,QAAC,SAAD;AAAW,gCAAA,KAAK,EAAC,OAAjB;AAAA,2CACKqN,OAAO,CAAC/I,UAAR,CAAmBkB,OAAnB,CAA2B,CAA3B,CADL;AAAA;AAAA;AAAA;AAAA;AAAA,sCAPJ;AAAA,+BACU,GAAE6F,IAAI,CAAC/H,IAAK,IAAG8J,QAAQ,CAAC9J,IAAK,IAAG+J,OAAO,CAAC/J,IAAK,EADvD;AAAA;AAAA;AAAA;AAAA,oCADH;AADL;AAAA;AAAA;AAAA;AAAA,kCARJ;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,4BApCJ;AAAA,qBACS+L,OADT;AAAA;AAAA;AAAA;AAAA,0BADJ;AAmEH,iBAvEA,CA1CL;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,oBA/BJ;AAAA,aACShE,IAAI,CAAC/H,IADd;AAAA;AAAA;AAAA;AAAA,kBADH,CADL,eAwJI,QAAC,GAAD;AAAK,YAAA,EAAE,EAAE;AAAE0N,cAAAA,EAAE,EAAE,CAAN;AAASmD,cAAAA,CAAC,EAAE,CAAZ;AAAeH,cAAAA,OAAO,EAAE,SAAxB;AAAmCpL,cAAAA,YAAY,EAAE;AAAjD,aAAT;AAAA,mCACI,QAAC,GAAD;AAAK,cAAA,EAAE,EAAE;AAAEgH,gBAAAA,OAAO,EAAE,MAAX;AAAmByB,gBAAAA,cAAc,EAAE,eAAnC;AAAoDrB,gBAAAA,UAAU,EAAE;AAAhE,eAAT;AAAA,sCACI,QAAC,UAAD;AAAY,gBAAA,OAAO,EAAC,WAApB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBADJ,eAEI,QAAC,GAAD;AAAK,gBAAA,EAAE,EAAE;AAAEJ,kBAAAA,OAAO,EAAE,MAAX;AAAmBG,kBAAAA,GAAG,EAAE;AAAxB,iBAAT;AAAA,wCACI,QAAC,UAAD;AAAY,kBAAA,OAAO,EAAC,WAApB;AAAA,4BACKxJ,cAAc,CAACoI,aAAa,CAACvI,QAAd,CAAuBqF,MAAvB,CAA8B,CAACC,GAAD,EAAML,IAAN,KAAeK,GAAG,GAAGL,IAAI,CAACrL,KAAxD,EAA+D,CAA/D,CAAD;AADnB;AAAA;AAAA;AAAA;AAAA,wBADJ,eAII,QAAC,UAAD;AAAY,kBAAA,OAAO,EAAC,WAApB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAJJ;AAAA;AAAA;AAAA;AAAA;AAAA,sBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,kBAxJJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBAFJ,eAsKI,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AAAE4P,YAAAA,OAAO,EAAE,MAAX;AAAmByB,YAAAA,cAAc,EAAE,QAAnC;AAA6CC,YAAAA,EAAE,EAAE;AAAjD,WAAT;AAAA,iCACI,QAAC,UAAD;AAAY,YAAA,KAAK,EAAC,kBAAlB;AAAA,mCACI,QAAC,UAAD;AAAY,cAAA,OAAO,EAAExE,uBAArB;AAA8C,cAAA,IAAI,EAAC,OAAnD;AAAA,qCACI,QAAC,YAAD;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,gBAtKJ;AAAA;AAAA;AAAA;AAAA;AAAA,cAxYJ;AAAA;AAAA;AAAA;AAAA;AAAA,YA1QJ;AAAA;AAAA;AAAA;AAAA;AAAA,UADJ;AAo0BH,CA/sDD;;GAAMnS,S;;KAAAA,S;AAgtDN,eAAeA,SAAf;AACA;;AAA0B;;AAAqB;;AAAoB;;AAAC,SAAS6Z,KAAT,GAAgB;AAAC,MAAG;AAAC,WAAO,CAAC,GAAEC,IAAH,EAAS,2BAAT,KAAyC,CAAC,GAAEA,IAAH,EAAS,onuCAAT,CAAhD;AAAgruC,GAApruC,CAAoruC,OAAMtI,CAAN,EAAQ,CAAE;AAAC;;AAAA;AAAC;;AAA0B,SAASrO,KAAT;AAAe;AAAgBoO,CAA/B,EAAsD;AAAA,oCAAFQ,CAAE;AAAFA,IAAAA,CAAE;AAAA;;AAAC,MAAG;AAAC8H,IAAAA,KAAK,GAAGE,UAAR,CAAmBxI,CAAnB,EAAsBQ,CAAtB;AAA0B,GAA9B,CAA8B,OAAMP,CAAN,EAAQ,CAAE;;AAAC,SAAOO,CAAP;AAAS;;AAAA;AAAC;;AAA0B,SAASiI,KAAT;AAAe;AAAgBzI,CAA/B,EAAsD;AAAA,qCAAFQ,CAAE;AAAFA,IAAAA,CAAE;AAAA;;AAAC,MAAG;AAAC8H,IAAAA,KAAK,GAAGI,YAAR,CAAqB1I,CAArB,EAAwBQ,CAAxB;AAA4B,GAAhC,CAAgC,OAAMP,CAAN,EAAQ,CAAE;;AAAC,SAAOO,CAAP;AAAS;;AAAA;AAAC;;AAA0B,SAAShP,KAAT;AAAe;AAAgBwO,CAA/B,EAAsD;AAAA,qCAAFQ,CAAE;AAAFA,IAAAA,CAAE;AAAA;;AAAC,MAAG;AAAC8H,IAAAA,KAAK,GAAGK,YAAR,CAAqB3I,CAArB,EAAwBQ,CAAxB;AAA4B,GAAhC,CAAgC,OAAMP,CAAN,EAAQ,CAAE;;AAAC,SAAOO,CAAP;AAAS;;AAAA;AAAC;;AAA0B,SAASoI,KAAT;AAAe;AAAgBpI,CAA/B,EAAiC;AAAC,MAAG;AAAC8H,IAAAA,KAAK,GAAGO,WAAR,CAAoBrI,CAApB;AAAwB,GAA5B,CAA4B,OAAMP,CAAN,EAAQ,CAAE;;AAAC,SAAOO,CAAP;AAAU;;AAAA;AAAC;;AAA0B,SAASsI,KAAT;AAAe;AAAgBtI,CAA/B;AAAkC;AAAgBR,CAAlD,EAAoD;AAAC,MAAG;AAACsI,IAAAA,KAAK,GAAGS,cAAR,CAAuBvI,CAAvB,EAA0BR,CAA1B;AAA8B,GAAlC,CAAkC,OAAMC,CAAN,EAAQ,CAAE;;AAAC,SAAOO,CAAP;AAAU;;AAAA;AAAC", "sourcesContent": ["/* eslint-disable */\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport {\r\n    Box,\r\n    Button,\r\n    ButtonGroup,\r\n    Table,\r\n    TableBody,\r\n    TableCell,\r\n    TableContainer,\r\n    TableHead,\r\n    TableRow,\r\n    Paper,\r\n    Typography,\r\n    ToggleButton,\r\n    ToggleButtonGroup,\r\n    Menu,\r\n    MenuItem,\r\n    TextField,\r\n    IconButton,\r\n    Autocomplete,\r\n    CircularProgress,\r\n    Backdrop,\r\n    Tooltip as MuiTooltip,\r\n    Accordion,\r\n    AccordionSummary,\r\n    AccordionDetails\r\n} from '@mui/material';\r\nimport { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Tooltip, Legend } from 'recharts';\r\nimport axios from 'axios';\r\nimport { getSalesDashboardPromise, getSalesDashboardGoalsPromise } from 'services/salesDashboard';\r\nimport { ParticipacionesSection } from './stores/ParticipacionesSection';\r\nimport { ParticipacionesTitle } from './stores/ParticipacionesTitle';\r\nimport { StyledTableCell } from './stores/StyledTableCell';\r\nimport { StyledTableRow } from './stores/StyledTableRow';\r\nimport { TotalRow } from './stores/TotalRow';\r\nimport { DivisionRow } from './stores/DivisionRow';\r\nimport { LineDetailsTable } from './stores/LineDetailsTable';\r\nimport { ChartsContainer } from './stores/ChartsContainer';\r\nimport { ChartContainer } from './stores/ChartContainer';\r\nimport { FiltersWrapper } from './stores/FiltersWrapper';\r\nimport { ChartTitle } from './stores/ChartTitle';\r\nimport { FilterGroup } from './stores/FilterGroup';\r\nimport { FilterChip } from './stores/FilterChip';\r\nimport { CompactDatePicker } from './stores/CompactDatePicker';\r\nimport { TimeSelect } from './stores/TimeSelect';\r\nimport { StoreSelect } from './stores/StoreSelect';\r\nimport { HeatMapLegend } from './stores/HeatMapLegend';\r\nimport { getHeatMapColor, getHeatMapColors } from './stores/functions';\r\nimport { COLORS } from './stores/colors';\r\nimport { LocalizationProvider } from '@mui/x-date-pickers';\r\nimport { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\r\nimport { es } from 'date-fns/locale';\r\nimport FilterListIcon from '@mui/icons-material/FilterList';\r\nimport DownloadIcon from '@mui/icons-material/Download';\r\nimport ExpandMoreIcon from '@mui/icons-material/ExpandMore';\r\nimport UnfoldMoreIcon from '@mui/icons-material/UnfoldMore';\r\nimport UnfoldLessIcon from '@mui/icons-material/UnfoldLess';\r\nimport * as XLSX from 'xlsx';\r\nimport AIChat from './AIChat';\r\nimport { format } from 'date-fns';\r\nimport CalendarViewDayIcon from '@mui/icons-material/CalendarViewDay';\r\nimport CalendarViewWeekIcon from '@mui/icons-material/CalendarViewWeek';\r\n\r\nconst StoresTab = () => {\r\n    const [salesData, setSalesData] = useState([]);\r\n    const [goalsData, setGoalsData] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [selectedStore, setSelectedStore] = useState('all');\r\n    const [selectedLineNames, setSelectedLineNames] = useState([]);\r\n    const [selectedRealLineNames, setSelectedRealLineNames] = useState([]);\r\n    const [dateRange, setDateRange] = useState([null, null]);\r\n    const [startTime, setStartTime] = useState('00:00');\r\n    const [endTime, setEndTime] = useState('23:59');\r\n    const [availableDates, setAvailableDates] = useState([]);\r\n    const [apiMode, setApiMode] = useState(0);\r\n    const [apiModeGoals, setApiModeGoals] = useState(0);\r\n    const [viewMode, setViewMode] = useState('daily');\r\n    const [filterAnchorEl, setFilterAnchorEl] = useState(null);\r\n    const [availableLineNames, setAvailableLineNames] = useState([]);\r\n    const [availableRealLineNames, setAvailableRealLineNames] = useState([]);\r\n\r\n    // Estados para acordeones\r\n    const [expandedDivisions, setExpandedDivisions] = useState({});\r\n    const [expandedLines, setExpandedLines] = useState({});\r\n    // Referencia para almacenar la estructura de datos procesada sin recalcular\r\n    const processedDataRef = useRef(null);\r\n\r\n    const hourChartRef = useRef(null);\r\n    const divisionChartRef = useRef(null);\r\n    const storeChartRef = useRef(null);\r\n    const dayChartRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        const fetchGoalsData = async () => {\r\n            setLoading(true);\r\n            try {\r\n                let response;\r\n                if (apiModeGoals === 0) {\r\n                    response = await getSalesDashboardGoalsPromise();\r\n                    setGoalsData(response.data);\r\n                }\r\n            } catch (error) {\r\n                /* eslint-disable */console.error(...oo_tx(`300546921_104_16_104_66_11`,'Error fetching goals data:', error));\r\n                if (apiModeGoals === 0) {\r\n                    setApiModeGoals(1);\r\n                }\r\n            }\r\n        };\r\n\r\n        fetchGoalsData();\r\n    }, [apiModeGoals]);\r\n\r\n    useEffect(() => {\r\n        const fetchSalesData = async () => {\r\n            try {\r\n                let response;\r\n                let arrayData = [];\r\n                if (apiMode === 0) {\r\n                    response = await getSalesDashboardPromise();\r\n                    /* eslint-disable */console.log(...oo_oo(`300546921_121_20_121_81_4`,'Sales data from Api Enviroment:', response.data));\r\n                    arrayData = response.data;\r\n                    setSalesData(response.data);\r\n                } else {\r\n                    response = await axios.get('https://supermercadosmia.siansystem.com/admin/apiSian/pbi/sales');\r\n                    /* eslint-disable */console.log(...oo_oo(`300546921_126_20_126_76_4`,'Sales data from Prod:', response.data.data));\r\n                    arrayData = response.data.data;\r\n                    setSalesData(response.data.data);\r\n                }\r\n                // Procesar fechas únicas\r\n                const dates = [...new Set(arrayData.map((sale) => sale.emission_date))].sort();\r\n                setAvailableDates(dates);\r\n\r\n                // Procesar divisiones únicas\r\n                const lines = [...new Set(arrayData.map((sale) => sale.division_name))].sort();\r\n                setAvailableLineNames(lines);\r\n\r\n                // Procesar líneas reales únicas\r\n                const realLines = [...new Set(arrayData.map((sale) => sale.line_name))].sort();\r\n                setAvailableRealLineNames(realLines);\r\n\r\n                // Establecer fecha inicial y final\r\n                const today = new Date();\r\n                today.setHours(12, 0, 0, 0);\r\n                const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\r\n                firstDayOfMonth.setHours(12, 0, 0, 0);\r\n                setDateRange([firstDayOfMonth, today]);\r\n            } catch (error) {\r\n                /* eslint-disable */console.error(...oo_tx(`300546921_149_16_149_66_11`,'Error fetching sales data:', error));\r\n                if (apiMode === 0) {\r\n                    setApiMode(1);\r\n                }\r\n            } finally {\r\n                setTimeout(() => {\r\n                    setLoading(false);\r\n                }, 500);\r\n            }\r\n        };\r\n\r\n        if (goalsData.length > 0) {\r\n            fetchSalesData();\r\n        }\r\n    }, [apiMode, goalsData]);\r\n\r\n    const handleFilterClick = (event) => {\r\n        setFilterAnchorEl(event.currentTarget);\r\n    };\r\n\r\n    const handleFilterClose = () => {\r\n        setFilterAnchorEl(null);\r\n    };\r\n\r\n    const handleDateRangePreset = (preset) => {\r\n        const today = new Date();\r\n        let start = new Date();\r\n        let end = new Date();\r\n\r\n        switch (preset) {\r\n            case 'today':\r\n                start = today;\r\n                end = today;\r\n                break;\r\n            case 'yesterday':\r\n                start.setDate(today.getDate() - 1);\r\n                end = start;\r\n                break;\r\n            case 'last7':\r\n                start.setDate(today.getDate() - 7);\r\n                end = today;\r\n                break;\r\n            case 'last30':\r\n                start.setDate(today.getDate() - 30);\r\n                end = today;\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n\r\n        setDateRange([start, end]);\r\n        handleFilterClose();\r\n    };\r\n\r\n    const handleTimeChange = (type, event) => {\r\n        const newValue = event.target.value;\r\n        if (type === 'start') {\r\n            setStartTime(newValue);\r\n        } else {\r\n            setEndTime(newValue);\r\n        }\r\n\r\n        // Convertir la hora para el filtrado\r\n        const [hours, minutes] = newValue.split(':').map(Number);\r\n        const timeValue = hours + minutes / 60;\r\n\r\n        // Actualizar el filtrado aquí\r\n    };\r\n\r\n    const handleLineNameChange = (event, newValue) => {\r\n        setSelectedLineNames(newValue);\r\n    };\r\n\r\n    const handleRealLineNameChange = (event, newValue) => {\r\n        setSelectedRealLineNames(newValue);\r\n    };\r\n\r\n    const getFilteredData = () => {\r\n        if (!dateRange[0] || !dateRange[1]) return [];\r\n\r\n        return salesData.filter((sale) => {\r\n            // Convertir la fecha de emisión a fecha local\r\n            const saleDate = new Date(sale.emission_date + 'T00:00:00');\r\n            const saleHour = new Date(sale.register_date).getHours();\r\n\r\n            // Crear fechas de inicio y fin del día\r\n            const startDate = new Date(dateRange[0]);\r\n            startDate.setHours(0, 0, 0, 0);\r\n\r\n            const endDate = new Date(dateRange[1]);\r\n            endDate.setHours(23, 59, 59, 999);\r\n\r\n            const storeMatch = selectedStore === 'all' || sale.store_name === selectedStore;\r\n            const lineMatch = selectedLineNames.length === 0 || selectedLineNames.includes(sale.division_name);\r\n            const realLineMatch = selectedRealLineNames.length === 0 || selectedRealLineNames.includes(sale.line_name);\r\n            const timeMatch = saleHour >= parseInt(startTime.split(':')[0]) && saleHour <= parseInt(endTime.split(':')[0]);\r\n            const dateMatch = saleDate >= startDate && saleDate <= endDate;\r\n\r\n            return storeMatch && dateMatch && timeMatch && lineMatch && realLineMatch;\r\n        });\r\n    };\r\n\r\n    const getFilteredDataWithoutStore = () => {\r\n        if (!dateRange[0] || !dateRange[1]) return [];\r\n\r\n        return salesData.filter((sale) => {\r\n            // Convertir la fecha de emisión a fecha local\r\n            const saleDate = new Date(sale.emission_date + 'T00:00:00');\r\n            const saleHour = new Date(sale.register_date).getHours();\r\n\r\n            // Crear fechas de inicio y fin del día\r\n            const startDate = new Date(dateRange[0]);\r\n            startDate.setHours(0, 0, 0, 0);\r\n\r\n            const endDate = new Date(dateRange[1]);\r\n            endDate.setHours(23, 59, 59, 999);\r\n\r\n            const lineMatch = selectedLineNames.length === 0 || selectedLineNames.includes(sale.division_name);\r\n            const realLineMatch = selectedRealLineNames.length === 0 || selectedRealLineNames.includes(sale.line_name);\r\n            const timeMatch = saleHour >= parseInt(startTime.split(':')[0]) && saleHour <= parseInt(endTime.split(':')[0]);\r\n            const dateMatch = saleDate >= startDate && saleDate <= endDate;\r\n\r\n            return dateMatch && timeMatch && lineMatch && realLineMatch;\r\n        });\r\n    };\r\n\r\n    // Función auxiliar para obtener el número de semana\r\n    const getWeekNumber = (date) => {\r\n        const firstDayOfYear = new Date(date.getFullYear(), 0, 1);\r\n        const pastDaysOfYear = (date - firstDayOfYear) / 86400000;\r\n        return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);\r\n    };\r\n\r\n    const processData = () => {\r\n        const filteredData = getFilteredData();\r\n        const allFilteredData = getFilteredDataWithoutStore();\r\n        const salesByStore = {};\r\n        const allSalesByStore = {};\r\n        const salesByLine = {};\r\n        const salesByRealLine = {};\r\n        const salesByHour = {};\r\n        const salesByDay = {};\r\n        const salesByWeek = {};\r\n        // Nuevo objeto para mapear ventas por tienda y línea\r\n        const salesByStoreAndLine = {};\r\n        let totalSales = 0;\r\n\r\n        // Obtener la fecha actual en formato YYYY-MM-DD\r\n        const today = new Date().toISOString().split('T')[0];\r\n\r\n        // Función para obtener número de semana ISO\r\n        const getISOWeekNumber = (date) => {\r\n            const d = new Date(date);\r\n            d.setHours(0, 0, 0, 0);\r\n            // Jueves de la semana actual\r\n            d.setDate(d.getDate() + 4 - (d.getDay() || 7));\r\n            // Primer día del año\r\n            const yearStart = new Date(d.getFullYear(), 0, 1);\r\n            // Calcular semana ISO\r\n            return Math.ceil(((d - yearStart) / 86400000 + 1) / 7);\r\n        };\r\n\r\n        // Procesar datos filtrados para las métricas principales\r\n        filteredData.forEach((sale) => {\r\n            const saleDate = sale.emission_date;\r\n            const saleTotal = parseFloat(sale.total);\r\n            totalSales += saleTotal;\r\n\r\n            // Procesar ventas por línea\r\n            if (!salesByLine[sale.division_name]) {\r\n                salesByLine[sale.division_name] = {\r\n                    total: 0,\r\n                    realLines: {}\r\n                };\r\n            }\r\n            salesByLine[sale.division_name].total += saleTotal;\r\n\r\n            if (!salesByLine[sale.division_name].realLines[sale.line_name]) {\r\n                salesByLine[sale.division_name].realLines[sale.line_name] = {\r\n                    total: 0,\r\n                    sublines: {}\r\n                };\r\n            }\r\n            salesByLine[sale.division_name].realLines[sale.line_name].total += saleTotal;\r\n\r\n            // Agregar sublineas\r\n            if (!salesByLine[sale.division_name].realLines[sale.line_name].sublines[sale.subline_name]) {\r\n                salesByLine[sale.division_name].realLines[sale.line_name].sublines[sale.subline_name] = 0;\r\n            }\r\n            salesByLine[sale.division_name].realLines[sale.line_name].sublines[sale.subline_name] += saleTotal;\r\n\r\n            // Crear estructura para ventas por tienda y línea\r\n            if (!salesByStoreAndLine[sale.store_name]) {\r\n                salesByStoreAndLine[sale.store_name] = {};\r\n            }\r\n            if (!salesByStoreAndLine[sale.store_name][sale.division_name]) {\r\n                salesByStoreAndLine[sale.store_name][sale.division_name] = 0;\r\n            }\r\n            salesByStoreAndLine[sale.store_name][sale.division_name] += saleTotal;\r\n\r\n            if (!salesByStore[sale.store_name]) {\r\n                salesByStore[sale.store_name] = {\r\n                    name: sale.store_name,\r\n                    total: 0,\r\n                    todaySales: 0\r\n                };\r\n            }\r\n            salesByStore[sale.store_name].total += saleTotal;\r\n\r\n            if (saleDate === today) {\r\n                salesByStore[sale.store_name].todaySales += saleTotal;\r\n            }\r\n\r\n            const hour = new Date(sale.register_date).getHours();\r\n            const hourKey = `${hour.toString().padStart(2, '0')}:00`;\r\n            if (!salesByHour[hourKey]) {\r\n                salesByHour[hourKey] = 0;\r\n            }\r\n            salesByHour[hourKey] += saleTotal;\r\n\r\n            // Agregar ventas por día - Normalizamos el formato de fecha\r\n            const formattedDate = saleDate.split('T')[0]; // Aseguramos formato YYYY-MM-DD\r\n            if (!salesByDay[formattedDate]) {\r\n                salesByDay[formattedDate] = 0;\r\n            }\r\n            salesByDay[formattedDate] += saleTotal;\r\n\r\n            // Calcular la semana de la venta usando el estándar ISO 8601\r\n            const dateParts = formattedDate.split('-');\r\n            const year = parseInt(dateParts[0]);\r\n            const month = parseInt(dateParts[1]) - 1; // Meses en JS son 0-11\r\n            const day = parseInt(dateParts[2]);\r\n\r\n            // Crear fecha con hora fija para evitar problemas de zona horaria\r\n            const date = new Date(year, month, day, 12, 0, 0);\r\n            const weekNumber = getISOWeekNumber(date);\r\n            const yearWeek = `${year}-W${weekNumber.toString().padStart(2, '0')}`;\r\n\r\n            if (!salesByWeek[yearWeek]) {\r\n                salesByWeek[yearWeek] = 0;\r\n            }\r\n            salesByWeek[yearWeek] += saleTotal;\r\n        });\r\n\r\n        // Procesar datos sin filtro de tienda para el gráfico\r\n        allFilteredData.forEach((sale) => {\r\n            const saleDate = sale.emission_date;\r\n            const saleTotal = parseFloat(sale.total);\r\n\r\n            if (!allSalesByStore[sale.store_name]) {\r\n                allSalesByStore[sale.store_name] = {\r\n                    name: sale.store_name,\r\n                    total: 0,\r\n                    todaySales: 0\r\n                };\r\n            }\r\n            allSalesByStore[sale.store_name].total += saleTotal;\r\n\r\n            if (saleDate === today) {\r\n                allSalesByStore[sale.store_name].todaySales += saleTotal;\r\n            }\r\n        });\r\n\r\n        // Convertir y ordenar las Línea\r\n        const sortedLines = Object.entries(salesByLine)\r\n            .map(([name, data]) => ({\r\n                name,\r\n                value: data.total,\r\n                percentage: (data.total / totalSales) * 100,\r\n                realLines: Object.entries(data.realLines)\r\n                    .map(([realName, lineData]) => ({\r\n                        name: realName,\r\n                        value: lineData.total,\r\n                        percentage: (lineData.total / totalSales) * 100,\r\n                        sublines: Object.entries(lineData.sublines)\r\n                            .map(([sublineName, value]) => ({\r\n                                name: sublineName,\r\n                                value,\r\n                                percentage: (value / totalSales) * 100\r\n                            }))\r\n                            .sort((a, b) => b.value - a.value)\r\n                    }))\r\n                    .sort((a, b) => b.value - a.value)\r\n            }))\r\n            .sort((a, b) => b.value - a.value);\r\n\r\n        // Crear estructura detallada de tiendas con sus ventas por línea\r\n        const storesArray = Object.values(salesByStore).map((store) => {\r\n            const storeGoals = goalsData.find((g) => g.store_name === store.name) || {};\r\n            const storeData = {\r\n                name: store.name,\r\n                total: store.total,\r\n                todaySales: store.todaySales,\r\n                monthlyGoal: parseFloat(storeGoals.period_goal),\r\n                dailyGoal: parseFloat(storeGoals.today_goal),\r\n                monthlyProgress: ((store.total / parseFloat(storeGoals.period_goal)) * 100).toFixed(2),\r\n                dailyProgress: ((store.todaySales / parseFloat(storeGoals.today_goal)) * 100).toFixed(2)\r\n            };\r\n\r\n            // Agregar ventas por línea para esta tienda\r\n            const storeLineData = salesByStoreAndLine[store.name] || {};\r\n            // Agregar propiedades por cada línea directamente al objeto tienda\r\n            sortedLines.forEach((lineInfo) => {\r\n                const lineName = lineInfo.name;\r\n                storeData[lineName] = storeLineData[lineName] || 0;\r\n            });\r\n\r\n            return storeData;\r\n        });\r\n\r\n        const allStoresArray = Object.values(allSalesByStore).map((store) => {\r\n            const storeGoals = goalsData.find((g) => g.store_name === store.name) || {};\r\n            return {\r\n                name: store.name,\r\n                total: store.total,\r\n                todaySales: store.todaySales,\r\n                monthlyGoal: parseFloat(storeGoals.period_goal),\r\n                dailyGoal: parseFloat(storeGoals.today_goal),\r\n                monthlyProgress: ((store.total / parseFloat(storeGoals.period_goal)) * 100).toFixed(2),\r\n                dailyProgress: ((store.todaySales / parseFloat(storeGoals.today_goal)) * 100).toFixed(2)\r\n            };\r\n        });\r\n\r\n        // Crear estructura para ventas totales por línea\r\n        const linesSummary = Object.keys(salesByLine).map((lineName) => {\r\n            let total = 0;\r\n            Object.values(salesByStoreAndLine).forEach((storeLines) => {\r\n                total += storeLines[lineName] || 0;\r\n            });\r\n            return {\r\n                name: lineName,\r\n                value: total\r\n            };\r\n        });\r\n        return {\r\n            stores: storesArray,\r\n            allStores: allStoresArray,\r\n            lines: sortedLines.slice(0, 4),\r\n            allLines: sortedLines,\r\n            linesByStore: salesByStoreAndLine,\r\n            linesSummary: linesSummary,\r\n            hours: Object.entries(salesByHour)\r\n                .map(([hour, value]) => ({\r\n                    hour,\r\n                    value,\r\n                    formattedValue: formatCurrency(value)\r\n                }))\r\n                .sort((a, b) => {\r\n                    const hourA = parseInt(a.hour.split(':')[0]);\r\n                    const hourB = parseInt(a.hour.split(':')[0]);\r\n                    return hourA - hourB;\r\n                }),\r\n            days: Object.entries(salesByDay)\r\n                .map(([day, value]) => ({\r\n                    day,\r\n                    value,\r\n                    formattedValue: formatCurrency(value)\r\n                }))\r\n                .sort((a, b) => {\r\n                    // Ordenar por fecha usando componentes individuales para evitar problemas de timezone\r\n                    const [yearA, monthA, dayA] = a.day.split('-').map((num) => parseInt(num));\r\n                    const [yearB, monthB, dayB] = b.day.split('-').map((num) => parseInt(num));\r\n\r\n                    if (yearA !== yearB) return yearA - yearB;\r\n                    if (monthA !== monthB) return monthA - monthB;\r\n                    return dayA - dayB;\r\n                }),\r\n            weeks: Object.entries(salesByWeek)\r\n                .map(([yearWeek, value]) => {\r\n                    const [year, weekPart] = yearWeek.split('-W');\r\n                    const weekNum = parseInt(weekPart);\r\n\r\n                    // Calculamos la fecha del primer día de la semana (lunes)\r\n                    const firstDayOfWeek = new Date(parseInt(year), 0, 1);\r\n                    // Ajustamos al lunes\r\n                    const dayOfWeek = firstDayOfWeek.getDay() || 7;\r\n                    firstDayOfWeek.setDate(firstDayOfWeek.getDate() + (dayOfWeek <= 4 ? 1 - dayOfWeek : 8 - dayOfWeek));\r\n\r\n                    // Calculamos el último día (domingo)\r\n                    const lastDayOfWeek = new Date(firstDayOfWeek);\r\n                    lastDayOfWeek.setDate(firstDayOfWeek.getDate() + 6);\r\n\r\n                    // Formatos de fecha\r\n                    const formatDate = (d) => {\r\n                        return d.toISOString().split('T')[0];\r\n                    };\r\n\r\n                    return {\r\n                        week: weekNum.toString(),\r\n                        year: year,\r\n                        yearWeek: yearWeek,\r\n                        value,\r\n                        firstDay: formatDate(firstDayOfWeek),\r\n                        lastDay: formatDate(lastDayOfWeek),\r\n                        formattedValue: formatCurrency(value)\r\n                    };\r\n                })\r\n                .sort((a, b) => {\r\n                    if (a.year !== b.year) return parseInt(a.year) - parseInt(b.year);\r\n                    return parseInt(a.week) - parseInt(b.week);\r\n                })\r\n        };\r\n    };\r\n\r\n    const getProcessedData = () => {\r\n        if (!processedDataRef.current) {\r\n            processedDataRef.current = processData();\r\n        }\r\n        return processedDataRef.current;\r\n    };\r\n\r\n    // Función optimizada para detectar cambios que requieren recalcular los datos\r\n    useEffect(() => {\r\n        // Cuando cambian los datos o filtros, invalidamos la caché\r\n        processedDataRef.current = null;\r\n    }, [salesData, selectedStore, selectedLineNames, selectedRealLineNames, dateRange, startTime, endTime]);\r\n\r\n    const formatCurrency = (value) => {\r\n        return new Intl.NumberFormat('es-PE', {\r\n            style: 'currency',\r\n            currency: 'PEN',\r\n            minimumFractionDigits: 2\r\n        }).format(value);\r\n    };\r\n\r\n    const formatDate = (dateStr) => {\r\n        const date = new Date(dateStr + 'T00:00:00');\r\n        const day = String(date.getDate()).padStart(2, '0');\r\n        const month = String(date.getMonth() + 1).padStart(2, '0');\r\n        return `${day}/${month}`;\r\n    };\r\n\r\n    const CustomTooltip = ({ active, payload, label }) => {\r\n        if (active && payload && payload.length) {\r\n            return (\r\n                <Box\r\n                    sx={{\r\n                        backgroundColor: 'white',\r\n                        padding: '10px',\r\n                        border: '1px solid #ccc',\r\n                        borderRadius: '4px'\r\n                    }}\r\n                >\r\n                    <Typography variant=\"subtitle2\">{payload[0].name}</Typography>\r\n                    <Typography variant=\"body2\" color=\"textSecondary\">\r\n                        {formatCurrency(payload[0].value)}\r\n                    </Typography>\r\n                    <Typography variant=\"body2\" color=\"primary\">\r\n                        {payload[0].payload.percentage?.toFixed(2)}%\r\n                    </Typography>\r\n                </Box>\r\n            );\r\n        }\r\n        return null;\r\n    };\r\n\r\n    const downloadChart = (chartRef, title) => {\r\n        if (chartRef.current) {\r\n            const svgElement = chartRef.current.container.children[0];\r\n            const svgData = new XMLSerializer().serializeToString(svgElement);\r\n            const canvas = document.createElement('canvas');\r\n            const ctx = canvas.getContext('2d');\r\n            const img = new Image();\r\n\r\n            // Configurar el tamaño del canvas basado en el SVG\r\n            const boundingBox = svgElement.getBoundingClientRect();\r\n            canvas.width = boundingBox.width;\r\n            canvas.height = boundingBox.height;\r\n\r\n            img.onload = () => {\r\n                // Fondo blanco\r\n                ctx.fillStyle = '#FFFFFF';\r\n                ctx.fillRect(0, 0, canvas.width, canvas.height);\r\n\r\n                // Dibujar el gráfico\r\n                ctx.drawImage(img, 0, 0);\r\n\r\n                // Convertir a PNG y descargar\r\n                const pngFile = canvas.toDataURL('image/png');\r\n                const downloadLink = document.createElement('a');\r\n                downloadLink.download = `${title.replace(/\\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.png`;\r\n                downloadLink.href = pngFile;\r\n                downloadLink.click();\r\n            };\r\n\r\n            img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));\r\n        }\r\n    };\r\n\r\n    const exportLinesSummaryToExcel = () => {\r\n        if (!getProcessedData().stores || !getProcessedData().allLines) return;\r\n\r\n        const workbook = XLSX.utils.book_new();\r\n\r\n        // Preparar los encabezados\r\n        const headers = ['Tiendas', ...getProcessedData().allLines.map((line) => line.name), 'Total'];\r\n\r\n        // Preparar los datos de las tiendas\r\n        const storeData = getProcessedData().stores.map((store) => {\r\n            const row = {\r\n                Tiendas: store.name\r\n            };\r\n\r\n            // Añadir cada valor de línea para esta tienda\r\n            getProcessedData().allLines.forEach((line) => {\r\n                row[line.name] = store[line.name] || 0;\r\n            });\r\n\r\n            // Añadir el total de la tienda\r\n            row['Total'] = store.total || 0;\r\n\r\n            return row;\r\n        });\r\n\r\n        // Añadir fila de totales\r\n        const totalRow = {\r\n            Tiendas: 'Total General'\r\n        };\r\n\r\n        // Añadir totales por línea\r\n        getProcessedData().allLines.forEach((line) => {\r\n            totalRow[line.name] = line.value || 0;\r\n        });\r\n\r\n        // Añadir total general\r\n        totalRow['Total'] = getProcessedData().stores.reduce((sum, store) => sum + (store.total || 0), 0);\r\n\r\n        // Combinar los datos\r\n        const exportData = [...storeData, totalRow];\r\n\r\n        // Crear hoja de trabajo\r\n        const worksheet = XLSX.utils.json_to_sheet(exportData, { header: headers });\r\n\r\n        // Formatear las celdas como moneda y ajustar el ancho de las columnas\r\n        const range = XLSX.utils.decode_range(worksheet['!ref']);\r\n\r\n        // Establecer anchos de columna para asegurar que los valores sean visibles\r\n        const columnWidths = [];\r\n        for (let i = 0; i <= range.e.c; i++) {\r\n            columnWidths[i] = { width: 15 }; // Ancho estándar para todas las columnas\r\n        }\r\n        worksheet['!cols'] = columnWidths;\r\n\r\n        // Aplicar formato de moneda\r\n        for (let C = 1; C <= range.e.c; C++) {\r\n            for (let R = 1; R <= range.e.r + 1; R++) {\r\n                const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });\r\n                if (worksheet[cellAddress] && typeof worksheet[cellAddress].v === 'number') {\r\n                    worksheet[cellAddress].z = '\"S/\"#,##0.00';\r\n                }\r\n            }\r\n        }\r\n\r\n        XLSX.utils.book_append_sheet(workbook, worksheet, 'Ventas_Por_Tienda_Linea');\r\n        XLSX.writeFile(workbook, 'ventas_por_tienda_linea.xlsx');\r\n    };\r\n\r\n    const exportLinesTableToExcel = () => {\r\n        const data = [];\r\n        getProcessedData().allLines.forEach((line) => {\r\n            // Add division row\r\n            data.push({\r\n                División: line.name,\r\n                Línea: '',\r\n                Sublínea: '',\r\n                'Total S/.': line.value,\r\n                Porcentaje: `${line.percentage.toFixed(2)}%`\r\n            });\r\n\r\n            // Add real lines with their sublines\r\n            line.realLines.forEach((realLine) => {\r\n                // Add line row\r\n                data.push({\r\n                    División: line.name,\r\n                    Línea: realLine.name,\r\n                    Sublínea: '',\r\n                    'Total S/.': realLine.value,\r\n                    Porcentaje: `${realLine.percentage.toFixed(2)}%`\r\n                });\r\n\r\n                // Add sublines\r\n                realLine.sublines.forEach((subline) => {\r\n                    data.push({\r\n                        División: line.name,\r\n                        Línea: realLine.name,\r\n                        Sublínea: subline.name,\r\n                        'Total S/.': subline.value,\r\n                        Porcentaje: `${subline.percentage.toFixed(2)}%`\r\n                    });\r\n                });\r\n            });\r\n\r\n            // Add division total\r\n            data.push({\r\n                División: `Total ${line.name}`,\r\n                Línea: '',\r\n                Sublínea: '',\r\n                'Total S/.': line.value,\r\n                Porcentaje: `${line.percentage.toFixed(2)}%`\r\n            });\r\n        });\r\n\r\n        // Add grand total\r\n        data.push({\r\n            División: 'Total General',\r\n            Línea: '',\r\n            Sublínea: '',\r\n            'Total S/.': getProcessedData().allLines.reduce((sum, line) => sum + line.value, 0),\r\n            Porcentaje: '100.00%'\r\n        });\r\n\r\n        const ws = XLSX.utils.json_to_sheet(data);\r\n        const wb = XLSX.utils.book_new();\r\n        XLSX.utils.book_append_sheet(wb, ws, 'Ventas por División');\r\n        XLSX.writeFile(wb, 'ventas_por_division.xlsx');\r\n    };\r\n\r\n    const isMonday = (dateStr) => {\r\n        // Asegurarnos de que la fecha esté en formato YYYY-MM-DD\r\n        const [year, month, day] = dateStr.split('-').map((num) => parseInt(num, 10));\r\n        // En JavaScript, los meses van de 0-11, por eso restamos 1 al mes\r\n        const date = new Date(year, month - 1, day);\r\n        return date.getDay() === 1; // 0 es domingo, 1 es lunes\r\n    };\r\n\r\n    const isSunday = (dateStr) => {\r\n        // Asegurarnos de que la fecha esté en formato YYYY-MM-DD\r\n        const [year, month, day] = dateStr.split('-').map((num) => parseInt(num, 10));\r\n        // En JavaScript, los meses van de 0-11, por eso restamos 1 al mes\r\n        const date = new Date(year, month - 1, day);\r\n        return date.getDay() === 0; // 0 es domingo\r\n    };\r\n\r\n    const getMixedViewData = () => {\r\n        const days = [...getProcessedData().days];\r\n        const mixedData = [];\r\n        let i = 0;\r\n\r\n        while (i < days.length) {\r\n            // Si el día actual es lunes y hay suficientes días para una semana completa\r\n            // y el último día es domingo (semana completa)\r\n            if (isMonday(days[i].day) && i + 6 < days.length && isSunday(days[i + 6].day)) {\r\n                // Crear una entrada semanal completa\r\n                const weekStartDate = days[i].day;\r\n                const weekEndDate = days[i + 6].day;\r\n                const weekTotal = days.slice(i, i + 7).reduce((sum, day) => sum + day.value, 0);\r\n\r\n                mixedData.push({\r\n                    type: 'complete_week',\r\n                    day: `${weekStartDate} - ${weekEndDate}`,\r\n                    value: weekTotal,\r\n                    formattedValue: formatCurrency(weekTotal)\r\n                });\r\n\r\n                // Avanzar al siguiente día después de la semana\r\n                i += 7;\r\n            } else {\r\n                // Buscar días consecutivos que no forman una semana completa\r\n                let j = i;\r\n                // Avanzar hasta encontrar el inicio de una semana completa o el final del array\r\n                while (j < days.length && !(isMonday(days[j].day) && j + 6 < days.length && isSunday(days[j + 6].day))) {\r\n                    j++;\r\n                }\r\n\r\n                // Si encontramos al menos un día para agrupar\r\n                if (j > i) {\r\n                    const incompleteWeekStartDate = days[i].day;\r\n                    const incompleteWeekEndDate = days[j - 1].day;\r\n                    const incompleteWeekTotal = days.slice(i, j).reduce((sum, day) => sum + day.value, 0);\r\n\r\n                    mixedData.push({\r\n                        type: 'incomplete_week',\r\n                        day: `${incompleteWeekStartDate} - ${incompleteWeekEndDate}`,\r\n                        value: incompleteWeekTotal,\r\n                        formattedValue: formatCurrency(incompleteWeekTotal)\r\n                    });\r\n\r\n                    // Avanzar después del grupo de días\r\n                    i = j;\r\n                } else {\r\n                    // Este caso no debería ocurrir, pero lo mantenemos por seguridad\r\n                    i++;\r\n                }\r\n            }\r\n        }\r\n\r\n        return mixedData;\r\n    };\r\n\r\n    const handleViewChange = (event, newView) => {\r\n        if (newView !== null) {\r\n            setViewMode(newView);\r\n        }\r\n    };\r\n\r\n    const handleStoreClick = (storeName) => {\r\n        setSelectedStore(storeName);\r\n    };\r\n\r\n    // Simplificamos las funciones para mejor manejo\r\n    const toggleAllLines = (divisionName, e) => {\r\n        // Detener la propagación para evitar clic en acordeón\r\n        if (e) {\r\n            e.stopPropagation();\r\n            e.preventDefault();\r\n        }\r\n\r\n        // Obtenemos la lista de líneas una sola vez\r\n        const processedData = getProcessedData();\r\n        const division = processedData.allLines.find((line) => line.name === divisionName);\r\n\r\n        if (!division || !division.realLines || division.realLines.length === 0) return;\r\n\r\n        // Verificamos si al menos una línea está abierta\r\n        const lineKeys = division.realLines.map((realLine) => `${divisionName}-${realLine.name}`);\r\n        const anyLineOpen = lineKeys.some((key) => expandedLines[key]);\r\n\r\n        // Creamos un nuevo objeto de estado directamente con los valores deseados\r\n        const newExpandedLines = { ...expandedLines };\r\n\r\n        lineKeys.forEach((key) => {\r\n            newExpandedLines[key] = !anyLineOpen;\r\n        });\r\n\r\n        // Si vamos a expandir, aseguramos que la división esté abierta\r\n        if (!anyLineOpen) {\r\n            setExpandedDivisions((prev) => ({\r\n                ...prev,\r\n                [divisionName]: true\r\n            }));\r\n        }\r\n\r\n        // Actualización única del estado\r\n        setExpandedLines(newExpandedLines);\r\n    };\r\n\r\n    // Manejador para expandir/colapsar la división principal\r\n    const handleDivisionExpand = (divisionName, isExpanded) => {\r\n        setExpandedDivisions((prev) => ({\r\n            ...prev,\r\n            [divisionName]: isExpanded\r\n        }));\r\n\r\n        // Si se está colapsando la división, cerramos todas sus líneas\r\n        if (!isExpanded) {\r\n            const processedData = getProcessedData();\r\n            const division = processedData.allLines.find((line) => line.name === divisionName);\r\n\r\n            if (division && division.realLines) {\r\n                const newExpandedLines = { ...expandedLines };\r\n\r\n                division.realLines.forEach((realLine) => {\r\n                    const lineKey = `${divisionName}-${realLine.name}`;\r\n                    newExpandedLines[lineKey] = false;\r\n                });\r\n\r\n                setExpandedLines(newExpandedLines);\r\n            }\r\n        }\r\n    };\r\n\r\n    // Manejador para la expansión individual de líneas\r\n    const handleLineExpand = (lineKey, isExpanded) => {\r\n        setExpandedLines((prev) => ({\r\n            ...prev,\r\n            [lineKey]: isExpanded\r\n        }));\r\n    };\r\n\r\n    // Función optimizada para verificar si alguna línea está abierta\r\n    const isAnyLineExpanded = (divisionName) => {\r\n        const processedData = getProcessedData();\r\n        const division = processedData.allLines.find((line) => line.name === divisionName);\r\n\r\n        if (!division || !division.realLines) return false;\r\n\r\n        // Verificamos más eficientemente usando some() en lugar de un bucle\r\n        return division.realLines.some((realLine) => expandedLines[`${divisionName}-${realLine.name}`] === true);\r\n    };\r\n\r\n    if (loading) {\r\n        return (\r\n            <Backdrop\r\n                sx={{\r\n                    color: '#fff',\r\n                    zIndex: (theme) => theme.zIndex.drawer + 1,\r\n                    backgroundColor: 'rgba(255, 255, 255, 0.8)'\r\n                }}\r\n                open={loading}\r\n            >\r\n                <Box\r\n                    sx={{\r\n                        display: 'flex',\r\n                        flexDirection: 'column',\r\n                        alignItems: 'center',\r\n                        gap: 2\r\n                    }}\r\n                >\r\n                    <CircularProgress\r\n                        size={60}\r\n                        thickness={4}\r\n                        sx={{\r\n                            color: '#b3256e'\r\n                        }}\r\n                    />\r\n                    <Typography\r\n                        variant=\"h6\"\r\n                        sx={{\r\n                            color: '#b3256e',\r\n                            fontWeight: 'bold'\r\n                        }}\r\n                    >\r\n                        Cargando datos...\r\n                    </Typography>\r\n                </Box>\r\n            </Backdrop>\r\n        );\r\n    }\r\n\r\n    // Calculamos los datos procesados una sola vez\r\n    const processedData = getProcessedData();\r\n    const stores = [...new Set(salesData.map((sale) => sale.store_name))];\r\n\r\n    return (\r\n        <Box sx={{ padding: '20px' }}>\r\n            <ParticipacionesSection>\r\n                <ParticipacionesTitle>Filtros</ParticipacionesTitle>\r\n                <FiltersWrapper>\r\n                    <FilterGroup>\r\n                        <LocalizationProvider dateAdapter={AdapterDateFns} locale={es}>\r\n                            <CompactDatePicker\r\n                                label=\"Fecha inicial\"\r\n                                value={dateRange[0]}\r\n                                onChange={(newValue) => {\r\n                                    // Prevenir valores nulos o inválidos\r\n                                    if (newValue && !isNaN(newValue.getTime())) {\r\n                                        const adjusted = new Date(newValue);\r\n                                        adjusted.setHours(12, 0, 0, 0);\r\n                                        setDateRange([adjusted, dateRange[1]]);\r\n                                    }\r\n                                }}\r\n                                renderInput={(params) => <TextField {...params} size=\"small\" />}\r\n                                inputFormat=\"dd/MM/yyyy\"\r\n                                sx={{\r\n                                    '& .MuiOutlinedInput-root': {\r\n                                        '&.Mui-focused fieldset': {\r\n                                            borderColor: '#b3256e'\r\n                                        }\r\n                                    },\r\n                                    '& .MuiInputLabel-root.Mui-focused': {\r\n                                        color: '#b3256e'\r\n                                    }\r\n                                }}\r\n                            />\r\n                            <CompactDatePicker\r\n                                label=\"Fecha final\"\r\n                                value={dateRange[1]}\r\n                                onChange={(newValue) => {\r\n                                    // Prevenir valores nulos o inválidos\r\n                                    if (newValue && !isNaN(newValue.getTime())) {\r\n                                        const adjusted = new Date(newValue);\r\n                                        adjusted.setHours(12, 0, 0, 0);\r\n                                        setDateRange([dateRange[0], adjusted]);\r\n                                    }\r\n                                }}\r\n                                renderInput={(params) => <TextField {...params} size=\"small\" />}\r\n                                inputFormat=\"dd/MM/yyyy\"\r\n                                sx={{\r\n                                    '& .MuiOutlinedInput-root': {\r\n                                        '&.Mui-focused fieldset': {\r\n                                            borderColor: '#b3256e'\r\n                                        }\r\n                                    },\r\n                                    '& .MuiInputLabel-root.Mui-focused': {\r\n                                        color: '#b3256e'\r\n                                    }\r\n                                }}\r\n                            />\r\n                        </LocalizationProvider>\r\n                        <IconButton\r\n                            size=\"small\"\r\n                            onClick={handleFilterClick}\r\n                            sx={{\r\n                                border: '1px solid #e0e0e0',\r\n                                borderRadius: '18px',\r\n                                padding: '8px'\r\n                            }}\r\n                        >\r\n                            <FilterListIcon fontSize=\"small\" />\r\n                        </IconButton>\r\n                    </FilterGroup>\r\n\r\n                    <FilterGroup>\r\n                        <TimeSelect\r\n                            type=\"time\"\r\n                            value={startTime}\r\n                            onChange={(e) => handleTimeChange('start', e)}\r\n                            size=\"small\"\r\n                            InputLabelProps={{ shrink: true }}\r\n                            label=\"Desde\"\r\n                        />\r\n                        <TimeSelect\r\n                            type=\"time\"\r\n                            value={endTime}\r\n                            onChange={(e) => handleTimeChange('end', e)}\r\n                            size=\"small\"\r\n                            InputLabelProps={{ shrink: true }}\r\n                            label=\"Hasta\"\r\n                        />\r\n                    </FilterGroup>\r\n\r\n                    <FilterGroup sx={{ flex: 1 }}>\r\n                        <Autocomplete\r\n                            multiple\r\n                            size=\"small\"\r\n                            value={selectedLineNames}\r\n                            onChange={handleLineNameChange}\r\n                            options={availableLineNames}\r\n                            renderInput={(params) => (\r\n                                <TextField\r\n                                    {...params}\r\n                                    placeholder=\"Divisiones\"\r\n                                    size=\"small\"\r\n                                    sx={{\r\n                                        minWidth: 200,\r\n                                        '& .MuiOutlinedInput-root': {\r\n                                            borderRadius: '18px',\r\n                                            height: '36px'\r\n                                        }\r\n                                    }}\r\n                                />\r\n                            )}\r\n                            renderTags={(selected, getTagProps) =>\r\n                                selected.map((option, index) => (\r\n                                    <FilterChip\r\n                                        {...getTagProps({ index })}\r\n                                        key={option}\r\n                                        label={option}\r\n                                        selected\r\n                                        onDelete={getTagProps({ index }).onDelete}\r\n                                    />\r\n                                ))\r\n                            }\r\n                        />\r\n                    </FilterGroup>\r\n\r\n                    <FilterGroup sx={{ flex: 1 }}>\r\n                        <Autocomplete\r\n                            multiple\r\n                            size=\"small\"\r\n                            value={selectedRealLineNames}\r\n                            onChange={handleRealLineNameChange}\r\n                            options={availableRealLineNames}\r\n                            renderInput={(params) => (\r\n                                <TextField\r\n                                    {...params}\r\n                                    placeholder=\"Líneas\"\r\n                                    size=\"small\"\r\n                                    sx={{\r\n                                        minWidth: 200,\r\n                                        '& .MuiOutlinedInput-root': {\r\n                                            borderRadius: '18px',\r\n                                            height: '36px'\r\n                                        }\r\n                                    }}\r\n                                />\r\n                            )}\r\n                            renderTags={(selected, getTagProps) =>\r\n                                selected.map((option, index) => (\r\n                                    <FilterChip\r\n                                        {...getTagProps({ index })}\r\n                                        key={option}\r\n                                        label={option}\r\n                                        selected\r\n                                        onDelete={getTagProps({ index }).onDelete}\r\n                                    />\r\n                                ))\r\n                            }\r\n                        />\r\n                    </FilterGroup>\r\n                </FiltersWrapper>\r\n\r\n                <Menu\r\n                    anchorEl={filterAnchorEl}\r\n                    open={Boolean(filterAnchorEl)}\r\n                    onClose={handleFilterClose}\r\n                    PaperProps={{\r\n                        sx: {\r\n                            mt: 1,\r\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\r\n                            borderRadius: '8px',\r\n                            '& .MuiMenuItem-root': {\r\n                                fontSize: '0.875rem',\r\n                                padding: '8px 16px',\r\n                                '&:hover': {\r\n                                    backgroundColor: '#b3256e10'\r\n                                }\r\n                            }\r\n                        }\r\n                    }}\r\n                >\r\n                    <MenuItem onClick={() => handleDateRangePreset('today')}>Hoy</MenuItem>\r\n                    <MenuItem onClick={() => handleDateRangePreset('yesterday')}>Ayer</MenuItem>\r\n                    <MenuItem onClick={() => handleDateRangePreset('last7')}>Últimos 7 días</MenuItem>\r\n                    <MenuItem onClick={() => handleDateRangePreset('last30')}>Últimos 30 días</MenuItem>\r\n                </Menu>\r\n\r\n                <StoreSelect\r\n                    value={selectedStore}\r\n                    exclusive\r\n                    onChange={(event, newStore) => setSelectedStore(newStore || 'all')}\r\n                    aria-label=\"store selection\"\r\n                >\r\n                    <ToggleButton value=\"all\">Todas</ToggleButton>\r\n                    {stores.map((store) => (\r\n                        <ToggleButton key={store} value={store}>\r\n                            {store}\r\n                        </ToggleButton>\r\n                    ))}\r\n                    <AIChat\r\n                        salesAdvanceData={processedData.stores}\r\n                        salesHoursData={processedData.hours}\r\n                        salesDivisionData={processedData.lines}\r\n                        salesStoreData={processedData.allStores}\r\n                        salesLinesData={processedData.allLines}\r\n                    />\r\n                </StoreSelect>\r\n            </ParticipacionesSection>\r\n\r\n            <ParticipacionesSection>\r\n                <h3>Ventas Por División</h3>\r\n                <TableContainer\r\n                    component={Paper}\r\n                    sx={{ marginBottom: '20px', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}\r\n                >\r\n                    <Table>\r\n                        <TableHead>\r\n                            <TableRow>\r\n                                <StyledTableCell className=\"header\">Tiendas</StyledTableCell>\r\n                                {processedData.allLines &&\r\n                                    processedData.allLines.map((line) => (\r\n                                        <StyledTableCell key={line.name} className=\"header\" align=\"right\">\r\n                                            {line.name}\r\n                                        </StyledTableCell>\r\n                                    ))}\r\n                                <StyledTableCell className=\"header\" align=\"right\">\r\n                                    Total\r\n                                </StyledTableCell>\r\n                            </TableRow>\r\n                        </TableHead>\r\n                        <TableBody>\r\n                            {processedData.stores &&\r\n                                processedData.stores.map((store) => (\r\n                                    <StyledTableRow key={store.name}>\r\n                                        <StyledTableCell>{store.name}</StyledTableCell>\r\n                                        {processedData.allLines &&\r\n                                            processedData.allLines.map((line) => (\r\n                                                <StyledTableCell key={`${store.name}-${line.name}`} align=\"right\">\r\n                                                    {formatCurrency(store[line.name] || 0)}\r\n                                                </StyledTableCell>\r\n                                            ))}\r\n                                        <StyledTableCell align=\"right\">{formatCurrency(store.total || 0)}</StyledTableCell>\r\n                                    </StyledTableRow>\r\n                                ))}\r\n                            <TotalRow>\r\n                                <StyledTableCell>Total General</StyledTableCell>\r\n                                {processedData.allLines &&\r\n                                    processedData.allLines.map((line) => (\r\n                                        <StyledTableCell key={`total-${line.name}`} align=\"right\">\r\n                                            {formatCurrency(line.value || 0)}\r\n                                        </StyledTableCell>\r\n                                    ))}\r\n                                <StyledTableCell align=\"right\">\r\n                                    {formatCurrency(\r\n                                        processedData.stores ? processedData.stores.reduce((sum, store) => sum + (store.total || 0), 0) : 0\r\n                                    )}\r\n                                </StyledTableCell>\r\n                            </TotalRow>\r\n                        </TableBody>\r\n                    </Table>\r\n                </TableContainer>\r\n                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 2 }}>\r\n                    <MuiTooltip title=\"Exportar a Excel\">\r\n                        <IconButton onClick={exportLinesSummaryToExcel} size=\"small\">\r\n                            <DownloadIcon />\r\n                        </IconButton>\r\n                    </MuiTooltip>\r\n                </Box>\r\n            </ParticipacionesSection>\r\n\r\n            <ParticipacionesSection>\r\n                <ParticipacionesTitle>Participaciones</ParticipacionesTitle>\r\n\r\n                <ChartContainer>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>\r\n                        <ChartTitle>Participación de Ventas por {viewMode === 'daily' ? 'Día' : 'Semana/Día'}</ChartTitle>\r\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\r\n                            <ToggleButtonGroup\r\n                                value={viewMode}\r\n                                exclusive\r\n                                onChange={handleViewChange}\r\n                                size=\"small\"\r\n                                sx={{\r\n                                    mr: 2,\r\n                                    '& .MuiToggleButton-root': {\r\n                                        '&.Mui-selected': {\r\n                                            backgroundColor: '#b94b84',\r\n                                            color: 'white',\r\n                                            '&:hover': {\r\n                                                backgroundColor: '#a03c71'\r\n                                            }\r\n                                        }\r\n                                    }\r\n                                }}\r\n                            >\r\n                                <ToggleButton value=\"daily\" aria-label=\"vista diaria\">\r\n                                    <MuiTooltip title=\"Vista Diaria\">\r\n                                        <Box\r\n                                            sx={{\r\n                                                fontSize: '12px',\r\n                                                fontWeight: 'medium',\r\n                                                color: viewMode === 'daily' ? 'white' : 'inherit'\r\n                                            }}\r\n                                        >\r\n                                            Días\r\n                                        </Box>\r\n                                    </MuiTooltip>\r\n                                </ToggleButton>\r\n                                <ToggleButton value=\"mixed\" aria-label=\"vista mixta\">\r\n                                    <MuiTooltip title=\"Vista Mixta (Semanas Completas e Incompletas)\">\r\n                                        <Box\r\n                                            sx={{\r\n                                                fontSize: '12px',\r\n                                                fontWeight: 'medium',\r\n                                                color: viewMode === 'mixed' ? 'white' : 'inherit'\r\n                                            }}\r\n                                        >\r\n                                            Semanas\r\n                                        </Box>\r\n                                    </MuiTooltip>\r\n                                </ToggleButton>\r\n                            </ToggleButtonGroup>\r\n                            <MuiTooltip title=\"Descargar gráfico\">\r\n                                <IconButton\r\n                                    size=\"small\"\r\n                                    onClick={() =>\r\n                                        downloadChart(\r\n                                            dayChartRef,\r\n                                            viewMode === 'daily' ? 'Participación_Ventas_por_Día' : 'Participación_Ventas_Mixta'\r\n                                        )\r\n                                    }\r\n                                >\r\n                                    <DownloadIcon fontSize=\"small\" />\r\n                                </IconButton>\r\n                            </MuiTooltip>\r\n                        </Box>\r\n                    </Box>\r\n                    <ResponsiveContainer width=\"100%\" height={400}>\r\n                        <BarChart\r\n                            ref={dayChartRef}\r\n                            data={viewMode === 'daily' ? processedData.days : getMixedViewData()}\r\n                            barSize={60}\r\n                            maxBarSize={60}\r\n                            margin={{ top: 30, right: 30, left: 20, bottom: 5 }}\r\n                        >\r\n                            <CartesianGrid strokeDasharray=\"3 3\" />\r\n                            <XAxis\r\n                                dataKey=\"day\"\r\n                                tickFormatter={(value) => {\r\n                                    // Si estamos en modo mixto y el valor contiene un guion, es un rango de semana\r\n                                    if (viewMode === 'mixed' && value.includes(' - ')) {\r\n                                        // Extraer las fechas de inicio y fin\r\n                                        const [startDate, endDate] = value.split(' - ');\r\n                                        // Formatear ambas fechas\r\n                                        return `${formatDate(startDate)} - ${formatDate(endDate)}`;\r\n                                    }\r\n                                    // Para días individuales, usar el formatDate normal\r\n                                    return formatDate(value);\r\n                                }}\r\n                                angle={-45}\r\n                                textAnchor=\"end\"\r\n                                height={80}\r\n                            />\r\n                            <YAxis tickFormatter={(value) => `S/ ${(value / 1000).toFixed(1)}K`} tick={{ fontSize: 11 }} />\r\n                            <Tooltip\r\n                                formatter={(value, name) => [formatCurrency(value), 'Venta']}\r\n                                labelFormatter={(label) => {\r\n                                    if (viewMode === 'day') {\r\n                                        try {\r\n                                            // Verificar si el label es un formato de fecha válido\r\n                                            if (typeof label === 'string' && label.includes('-') && label.split('-').length === 3) {\r\n                                                const parts = label.split('-');\r\n                                                const year = parseInt(parts[0]);\r\n                                                const month = parseInt(parts[1]) - 1;\r\n                                                const day = parseInt(parts[2]);\r\n\r\n                                                if (!isNaN(year) && !isNaN(month) && !isNaN(day)) {\r\n                                                    const date = new Date(year, month, day, 12, 0, 0);\r\n                                                    return format(date, 'yyyy-MM-dd');\r\n                                                }\r\n                                            }\r\n                                            return String(label);\r\n                                        } catch (error) {\r\n                                            /* eslint-disable */console.error(...oo_tx(`300546921_1352_44_1352_90_11`,'Error formatting date:', error));\r\n                                            return String(label);\r\n                                        }\r\n                                    } else {\r\n                                        try {\r\n                                            // Validar que el número de semana esté en rango (1-53)\r\n                                            const weekNum = parseInt(label);\r\n                                            if (isNaN(weekNum) || weekNum < 1 || weekNum > 53) {\r\n                                                return `Semana ${label}`;\r\n                                            }\r\n\r\n                                            const today = new Date();\r\n                                            const year = today.getFullYear();\r\n\r\n                                            // Calcula el primer día del año\r\n                                            const firstDayOfYear = new Date(year, 0, 1);\r\n\r\n                                            // Ajusta al primer día de la semana (lunes=1, domingo=0)\r\n                                            const dayOfWeek = firstDayOfYear.getDay() || 7;\r\n                                            const firstWeekday = new Date(firstDayOfYear);\r\n                                            firstWeekday.setDate(\r\n                                                firstDayOfYear.getDate() + (dayOfWeek <= 4 ? 1 - dayOfWeek : 8 - dayOfWeek)\r\n                                            );\r\n\r\n                                            // Calcula la primera fecha de la semana solicitada\r\n                                            const firstDate = new Date(firstWeekday);\r\n                                            firstDate.setDate(firstWeekday.getDate() + (weekNum - 1) * 7);\r\n\r\n                                            // Calcula el último día (domingo)\r\n                                            const lastDate = new Date(firstDate);\r\n                                            lastDate.setDate(firstDate.getDate() + 6);\r\n\r\n                                            return `Semana ${label}: ${format(firstDate, 'dd/MM')} - ${format(lastDate, 'dd/MM')}`;\r\n                                        } catch (error) {\r\n                                            /* eslint-disable */console.error(...oo_tx(`300546921_1386_44_1386_90_11`,'Error formatting week:', error));\r\n                                            return `Semana ${label}`;\r\n                                        }\r\n                                    }\r\n                                }}\r\n                                contentStyle={{ fontSize: 12 }}\r\n                            />\r\n                            <Bar\r\n                                dataKey=\"value\"\r\n                                fill=\"#b3256e\"\r\n                                label={{\r\n                                    position: 'top',\r\n                                    formatter: (value, entry, index) => {\r\n                                        // Calcula la suma total\r\n                                        const totalSum = (viewMode === 'day' ? processedData.days : processedData.weeks).reduce(\r\n                                            (acc, curr) => acc + curr.value,\r\n                                            0\r\n                                        );\r\n\r\n                                        // Calcula el porcentaje sin redondear para uso interno\r\n                                        const exactPercentage = (value / totalSum) * 100;\r\n\r\n                                        // Para evitar que los porcentajes sumen más de 100%, ajusta el último elemento\r\n                                        const isLastItem =\r\n                                            index === (viewMode === 'day' ? processedData.days.length - 1 : processedData.weeks.length - 1);\r\n\r\n                                        if (isLastItem) {\r\n                                            // Suma de todos los porcentajes excepto el último\r\n                                            const otherPercentages = (viewMode === 'day' ? processedData.days : processedData.weeks)\r\n                                                .slice(0, -1)\r\n                                                .reduce((acc, curr) => acc + Math.round((curr.value / totalSum) * 1000) / 10, 0);\r\n\r\n                                            // El último porcentaje es el complemento para llegar a 100%\r\n                                            return `${(100 - otherPercentages).toFixed(1)}%`;\r\n                                        }\r\n\r\n                                        // Para los demás elementos, redondea normalmente\r\n                                        return `${Math.round(exactPercentage * 10) / 10}%`;\r\n                                    },\r\n                                    fontSize: 11\r\n                                }}\r\n                            >\r\n                                {(viewMode === 'daily' ? processedData.days : getMixedViewData()).map((entry, index) => (\r\n                                    <Cell key={`cell-${index}`} fill={entry.type === 'complete_week' ? '#4a148c' : '#b3256e'} />\r\n                                ))}\r\n                            </Bar>\r\n                        </BarChart>\r\n                    </ResponsiveContainer>\r\n                </ChartContainer>\r\n\r\n                <ChartContainer>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>\r\n                        <ChartTitle>Participación de Ventas por Hora</ChartTitle>\r\n                        <MuiTooltip title=\"Descargar gráfico\">\r\n                            <IconButton size=\"small\" onClick={() => downloadChart(hourChartRef, 'Participación_Ventas_por_Hora')}>\r\n                                <DownloadIcon fontSize=\"small\" />\r\n                            </IconButton>\r\n                        </MuiTooltip>\r\n                    </Box>\r\n                    <ResponsiveContainer width=\"100%\" height={300}>\r\n                        <BarChart\r\n                            ref={hourChartRef}\r\n                            data={processedData.hours.map((hour) => {\r\n                                const total = processedData.hours.reduce((acc, curr) => acc + curr.value, 0);\r\n                                const percentage = (hour.value / total) * 100;\r\n                                return {\r\n                                    ...hour,\r\n                                    percentage,\r\n                                    fillColor: getHeatMapColor(hour.value, total)\r\n                                };\r\n                            })}\r\n                            layout=\"vertical\"\r\n                            margin={{ top: 5, right: 30, left: 40, bottom: 5 }}\r\n                        >\r\n                            <CartesianGrid strokeDasharray=\"3 3\" horizontal={false} />\r\n                            <XAxis type=\"number\" tickFormatter={(value) => `S/ ${(value / 1000).toFixed(1)}K`} />\r\n                            <YAxis dataKey=\"hour\" type=\"category\" width={60} tick={{ fill: '#666' }} />\r\n                            <Tooltip\r\n                                formatter={(value, name, props) => {\r\n                                    const percentage = (value / processedData.hours.reduce((acc, curr) => acc + curr.value, 0)) * 100;\r\n                                    return [`${formatCurrency(value)} (${percentage.toFixed(1)}%)`, 'Venta'];\r\n                                }}\r\n                                labelFormatter={(label) => `Hora: ${label}`}\r\n                                contentStyle={{\r\n                                    backgroundColor: '#fff',\r\n                                    border: '1px solid #e0e0e0',\r\n                                    borderRadius: '4px',\r\n                                    padding: '8px'\r\n                                }}\r\n                            />\r\n                            <Bar\r\n                                dataKey=\"value\"\r\n                                name=\"Ventas\"\r\n                                fill=\"#b3256e\"\r\n                                background={{ fill: '#f5f5f5' }}\r\n                                label={{\r\n                                    position: 'right',\r\n                                    content: ({ value }) => `S/ ${(value / 1000).toFixed(1)}K`,\r\n                                    fontSize: 11\r\n                                }}\r\n                            >\r\n                                {processedData.hours.map((entry, index) => {\r\n                                    const total = processedData.hours.reduce((acc, curr) => acc + curr.value, 0);\r\n                                    return <Cell key={`cell-${index}`} fill={getHeatMapColor(entry.value, total)} />;\r\n                                })}\r\n                            </Bar>\r\n                        </BarChart>\r\n                    </ResponsiveContainer>\r\n                    <HeatMapLegend>\r\n                        <Typography variant=\"caption\" sx={{ color: '#666', fontWeight: 'bold' }}>\r\n                            &lt;2%\r\n                        </Typography>\r\n                        {getHeatMapColors().map((color, index) => (\r\n                            <Box key={index} className=\"color-box\" sx={{ backgroundColor: color }} />\r\n                        ))}\r\n                        <Typography variant=\"caption\" sx={{ color: '#666', fontWeight: 'bold' }}>\r\n                            &gt;8%\r\n                        </Typography>\r\n                    </HeatMapLegend>\r\n                </ChartContainer>\r\n\r\n                <ChartsContainer>\r\n                    <ChartContainer>\r\n                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>\r\n                            <ChartTitle>Participación de Ventas por División</ChartTitle>\r\n                            <MuiTooltip title=\"Descargar gráfico\">\r\n                                <IconButton\r\n                                    size=\"small\"\r\n                                    onClick={() => downloadChart(divisionChartRef, 'Participación_Ventas_por_División')}\r\n                                >\r\n                                    <DownloadIcon fontSize=\"small\" />\r\n                                </IconButton>\r\n                            </MuiTooltip>\r\n                        </Box>\r\n                        <ResponsiveContainer width=\"100%\" height={300}>\r\n                            <PieChart ref={divisionChartRef}>\r\n                                <Pie\r\n                                    data={processedData.lines}\r\n                                    dataKey=\"value\"\r\n                                    nameKey=\"name\"\r\n                                    cx=\"50%\"\r\n                                    cy=\"50%\"\r\n                                    outerRadius={80}\r\n                                    label={({ cx, cy, midAngle, innerRadius, outerRadius, value, percentage }) => {\r\n                                        const RADIAN = Math.PI / 180;\r\n                                        const radius = outerRadius + 25;\r\n                                        const x = cx + radius * Math.cos(-midAngle * RADIAN);\r\n                                        const y = cy + radius * Math.sin(-midAngle * RADIAN);\r\n                                        return (\r\n                                            <g>\r\n                                                <text\r\n                                                    x={x}\r\n                                                    y={y}\r\n                                                    fill=\"#666\"\r\n                                                    textAnchor={x > cx ? 'start' : 'end'}\r\n                                                    dominantBaseline=\"central\"\r\n                                                >\r\n                                                    {`${percentage?.toFixed(1)}%`}\r\n                                                </text>\r\n                                                <text\r\n                                                    x={x}\r\n                                                    y={y + 15}\r\n                                                    fill=\"#666\"\r\n                                                    textAnchor={x > cx ? 'start' : 'end'}\r\n                                                    dominantBaseline=\"central\"\r\n                                                    style={{ fontSize: '0.8em' }}\r\n                                                >\r\n                                                    {formatCurrency(value)}\r\n                                                </text>\r\n                                            </g>\r\n                                        );\r\n                                    }}\r\n                                >\r\n                                    {processedData.lines.map((entry, index) => (\r\n                                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\r\n                                    ))}\r\n                                </Pie>\r\n                                <Tooltip content={<CustomTooltip />} />\r\n                                <Legend\r\n                                    verticalAlign=\"bottom\"\r\n                                    height={36}\r\n                                    formatter={(value, entry) => <span style={{ color: '#666', marginRight: '10px' }}>{value}</span>}\r\n                                />\r\n                            </PieChart>\r\n                        </ResponsiveContainer>\r\n                    </ChartContainer>\r\n\r\n                    <ChartContainer>\r\n                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>\r\n                            <ChartTitle>Participación de Ventas por Tienda</ChartTitle>\r\n                            <MuiTooltip title=\"Descargar gráfico\">\r\n                                <IconButton size=\"small\" onClick={() => downloadChart(storeChartRef, 'Participación_Ventas_por_Tienda')}>\r\n                                    <DownloadIcon fontSize=\"small\" />\r\n                                </IconButton>\r\n                            </MuiTooltip>\r\n                        </Box>\r\n                        <ResponsiveContainer width=\"100%\" height={400}>\r\n                            <BarChart\r\n                                ref={storeChartRef}\r\n                                data={processedData.allStores}\r\n                                barSize={60}\r\n                                maxBarSize={60}\r\n                                margin={{ top: 30, right: 30, left: 20, bottom: 5 }}\r\n                            >\r\n                                <CartesianGrid strokeDasharray=\"3 3\" />\r\n                                <XAxis\r\n                                    dataKey=\"name\"\r\n                                    tickFormatter={(value) => {\r\n                                        return value\r\n                                            .split(' ')\r\n                                            .map((word) => word.charAt(0))\r\n                                            .join('')\r\n                                            .toUpperCase();\r\n                                    }}\r\n                                />{' '}\r\n                                <YAxis tickFormatter={(value) => `S/ ${(value / 1000).toFixed(1)}K`} />\r\n                                <Tooltip\r\n                                    formatter={(value, name) => [formatCurrency(value), 'Venta']}\r\n                                    labelFormatter={(label) => `${label}`}\r\n                                />\r\n                                <Bar\r\n                                    dataKey=\"total\"\r\n                                    fill=\"#b3256e\"\r\n                                    label={{\r\n                                        position: 'top',\r\n                                        formatter: (value) => {\r\n                                            const totalSum = processedData.allStores.reduce((acc, curr) => acc + curr.total, 0);\r\n                                            return `${((value / totalSum) * 100).toFixed(1)}%`;\r\n                                        }\r\n                                    }}\r\n                                >\r\n                                    {processedData.allStores.map((entry, index) => (\r\n                                        <Cell\r\n                                            key={`cell-${index}`}\r\n                                            fill=\"#b3256e\"\r\n                                            opacity={selectedStore === 'all' || selectedStore === entry.name ? 1 : 0.3}\r\n                                            cursor=\"pointer\"\r\n                                            onClick={() => handleStoreClick(entry.name)}\r\n                                        />\r\n                                    ))}\r\n                                </Bar>\r\n                            </BarChart>\r\n                        </ResponsiveContainer>\r\n                    </ChartContainer>\r\n                </ChartsContainer>\r\n                <Box>\r\n                    <ChartTitle>Detalle de Ventas por División, Líneas y Sublíneas</ChartTitle>\r\n                    <Box sx={{ mb: 1 }}>\r\n                        {processedData.allLines.map((line) => (\r\n                            <Accordion\r\n                                key={line.name}\r\n                                expanded={expandedDivisions[line.name] || false}\r\n                                onChange={(_, isExpanded) => handleDivisionExpand(line.name, isExpanded)}\r\n                            >\r\n                                <AccordionSummary\r\n                                    expandIcon={<ExpandMoreIcon sx={{ color: 'white' }} />}\r\n                                    aria-controls={`${line.name}-content`}\r\n                                    id={`${line.name}-header`}\r\n                                    sx={{\r\n                                        bgcolor: '#9e187d',\r\n                                        color: 'white',\r\n                                        '&.Mui-expanded': {\r\n                                            minHeight: '48px'\r\n                                        },\r\n                                        '& .MuiAccordionSummary-content.Mui-expanded': {\r\n                                            margin: '12px 0'\r\n                                        }\r\n                                    }}\r\n                                >\r\n                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>\r\n                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                                            <Typography fontWeight=\"bold\" color=\"white\">\r\n                                                {line.name}\r\n                                            </Typography>\r\n                                        </Box>\r\n                                        <Typography variant=\"body2\" color=\"white\">\r\n                                            {formatCurrency(line.value)} ({line.percentage.toFixed(2)}%)\r\n                                        </Typography>\r\n                                    </Box>\r\n                                </AccordionSummary>\r\n                                <AccordionDetails>\r\n                                    <Box sx={{ mb: 2 }}>\r\n                                        <Box\r\n                                            sx={{\r\n                                                display: 'flex',\r\n                                                alignItems: 'center',\r\n                                                gap: 2,\r\n                                                mb: 2,\r\n                                                p: 1,\r\n                                                borderBottom: '1px solid rgba(158, 24, 125, 0.1)'\r\n                                            }}\r\n                                        >\r\n                                            <Typography\r\n                                                component=\"span\"\r\n                                                onClick={(e) => {\r\n                                                    if (!isAnyLineExpanded(line.name)) {\r\n                                                        toggleAllLines(line.name, e);\r\n                                                    }\r\n                                                }}\r\n                                                sx={{\r\n                                                    color: '#2D58E4FF',\r\n                                                    cursor: 'pointer',\r\n                                                    textDecoration: 'underline'\r\n                                                }}\r\n                                            >\r\n                                                Expandir todo\r\n                                            </Typography>\r\n                                            <Typography\r\n                                                component=\"span\"\r\n                                                onClick={(e) => {\r\n                                                    if (isAnyLineExpanded(line.name)) {\r\n                                                        toggleAllLines(line.name, e);\r\n                                                    }\r\n                                                }}\r\n                                                sx={{\r\n                                                    color: '#2D58E4FF',\r\n                                                    cursor: 'pointer',\r\n                                                    textDecoration: 'underline'\r\n                                                }}\r\n                                            >\r\n                                                Contraer todo\r\n                                            </Typography>\r\n                                        </Box>\r\n                                        {line.realLines.map((realLine) => {\r\n                                            const lineKey = `${line.name}-${realLine.name}`;\r\n                                            const isExpanded = expandedLines[lineKey] || false;\r\n\r\n                                            return (\r\n                                                <Accordion\r\n                                                    key={lineKey}\r\n                                                    expanded={isExpanded}\r\n                                                    onChange={(_, isLineExpanded) => handleLineExpand(lineKey, isLineExpanded)}\r\n                                                >\r\n                                                    <AccordionSummary\r\n                                                        expandIcon={<ExpandMoreIcon sx={{ color: 'white' }} />}\r\n                                                        aria-controls={`${lineKey}-content`}\r\n                                                        id={`${lineKey}-header`}\r\n                                                        sx={{\r\n                                                            bgcolor: '#8A848AFF',\r\n                                                            color: 'white',\r\n                                                            '&.Mui-expanded': {\r\n                                                                minHeight: '48px'\r\n                                                            },\r\n                                                            '& .MuiAccordionSummary-content.Mui-expanded': {\r\n                                                                margin: '12px 0'\r\n                                                            }\r\n                                                        }}\r\n                                                    >\r\n                                                        <Box\r\n                                                            sx={{\r\n                                                                display: 'flex',\r\n                                                                justifyContent: 'space-between',\r\n                                                                width: '100%',\r\n                                                                alignItems: 'center'\r\n                                                            }}\r\n                                                        >\r\n                                                            <Typography fontWeight=\"bold\" color=\"white\">\r\n                                                                {realLine.name}\r\n                                                            </Typography>\r\n                                                            <Typography variant=\"body2\" color=\"white\">\r\n                                                                {formatCurrency(realLine.value)} ({realLine.percentage.toFixed(2)}%)\r\n                                                            </Typography>\r\n                                                        </Box>\r\n                                                    </AccordionSummary>\r\n                                                    <AccordionDetails>\r\n                                                        <TableContainer component={Paper} sx={{ mb: 2 }}>\r\n                                                            <Table size=\"small\">\r\n                                                                <TableHead>\r\n                                                                    <TableRow>\r\n                                                                        <StyledTableCell>Sublínea</StyledTableCell>\r\n                                                                        <StyledTableCell align=\"right\">Total S/.</StyledTableCell>\r\n                                                                        <StyledTableCell align=\"right\">Porcentaje</StyledTableCell>\r\n                                                                    </TableRow>\r\n                                                                </TableHead>\r\n                                                                <TableBody>\r\n                                                                    {realLine.sublines.map((subline) => (\r\n                                                                        <StyledTableRow\r\n                                                                            key={`${line.name}-${realLine.name}-${subline.name}`}\r\n                                                                        >\r\n                                                                            <TableCell>{subline.name}</TableCell>\r\n                                                                            <TableCell align=\"right\">\r\n                                                                                {formatCurrency(subline.value)}\r\n                                                                            </TableCell>\r\n                                                                            <TableCell align=\"right\">\r\n                                                                                {subline.percentage.toFixed(2)}%\r\n                                                                            </TableCell>\r\n                                                                        </StyledTableRow>\r\n                                                                    ))}\r\n                                                                </TableBody>\r\n                                                            </Table>\r\n                                                        </TableContainer>\r\n                                                    </AccordionDetails>\r\n                                                </Accordion>\r\n                                            );\r\n                                        })}\r\n                                    </Box>\r\n                                </AccordionDetails>\r\n                            </Accordion>\r\n                        ))}\r\n                        <Box sx={{ mt: 2, p: 1, bgcolor: '#f5f5f5', borderRadius: 1 }}>\r\n                            <Box sx={{ display: 'flex', justifyContent: 'space-between', fontWeight: 'bold' }}>\r\n                                <Typography variant=\"subtitle1\">Total General</Typography>\r\n                                <Box sx={{ display: 'flex', gap: 4 }}>\r\n                                    <Typography variant=\"subtitle1\">\r\n                                        {formatCurrency(processedData.allLines.reduce((sum, line) => sum + line.value, 0))}\r\n                                    </Typography>\r\n                                    <Typography variant=\"subtitle1\">100.00%</Typography>\r\n                                </Box>\r\n                            </Box>\r\n                        </Box>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>\r\n                        <MuiTooltip title=\"Exportar a Excel\">\r\n                            <IconButton onClick={exportLinesTableToExcel} size=\"small\">\r\n                                <DownloadIcon />\r\n                            </IconButton>\r\n                        </MuiTooltip>\r\n                    </Box>\r\n                </Box>\r\n            </ParticipacionesSection>\r\n        </Box>\r\n    );\r\n};\r\nexport default StoresTab;\r\n/* istanbul ignore next *//* c8 ignore start *//* eslint-disable */;function oo_cm(){try{return (0,eval)(\"globalThis._console_ninja\") || (0,eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x418f23=_0x33f3;(function(_0x2c70e5,_0x70d422){var _0x45fe32=_0x33f3,_0x244e11=_0x2c70e5();while(!![]){try{var _0xe599a4=parseInt(_0x45fe32(0xb0))/0x1*(parseInt(_0x45fe32(0xa1))/0x2)+-parseInt(_0x45fe32(0x15e))/0x3+-parseInt(_0x45fe32(0x109))/0x4*(parseInt(_0x45fe32(0xc2))/0x5)+parseInt(_0x45fe32(0x191))/0x6+-parseInt(_0x45fe32(0x11d))/0x7*(parseInt(_0x45fe32(0x9c))/0x8)+parseInt(_0x45fe32(0xe1))/0x9+-parseInt(_0x45fe32(0x15f))/0xa*(-parseInt(_0x45fe32(0x148))/0xb);if(_0xe599a4===_0x70d422)break;else _0x244e11['push'](_0x244e11['shift']());}catch(_0x630c67){_0x244e11['push'](_0x244e11['shift']());}}}(_0x4e19,0xaaec1));var G=Object[_0x418f23(0xe5)],V=Object[_0x418f23(0x103)],ee=Object['getOwnPropertyDescriptor'],te=Object[_0x418f23(0xdf)],ne=Object[_0x418f23(0xd9)],re=Object[_0x418f23(0x119)][_0x418f23(0xf2)],ie=(_0x24c79a,_0x5c1c97,_0x1147c3,_0x2138d8)=>{var _0x36a3cf=_0x418f23;if(_0x5c1c97&&typeof _0x5c1c97==_0x36a3cf(0x117)||typeof _0x5c1c97==_0x36a3cf(0x13e)){for(let _0x5c0210 of te(_0x5c1c97))!re[_0x36a3cf(0xda)](_0x24c79a,_0x5c0210)&&_0x5c0210!==_0x1147c3&&V(_0x24c79a,_0x5c0210,{'get':()=>_0x5c1c97[_0x5c0210],'enumerable':!(_0x2138d8=ee(_0x5c1c97,_0x5c0210))||_0x2138d8[_0x36a3cf(0x14e)]});}return _0x24c79a;},j=(_0x1f84af,_0x39bbd1,_0xf2cf2e)=>(_0xf2cf2e=_0x1f84af!=null?G(ne(_0x1f84af)):{},ie(_0x39bbd1||!_0x1f84af||!_0x1f84af['__es'+'Module']?V(_0xf2cf2e,'default',{'value':_0x1f84af,'enumerable':!0x0}):_0xf2cf2e,_0x1f84af)),q=class{constructor(_0x14d9ea,_0x61266e,_0x21d732,_0x659164,_0x2ce13a,_0x1b0c0c){var _0x4b2850=_0x418f23,_0x34d24c,_0x26bffd,_0xeab781,_0x4b345e;this['global']=_0x14d9ea,this[_0x4b2850(0xe0)]=_0x61266e,this['port']=_0x21d732,this[_0x4b2850(0x173)]=_0x659164,this[_0x4b2850(0x131)]=_0x2ce13a,this['eventReceivedCallback']=_0x1b0c0c,this[_0x4b2850(0x159)]=!0x0,this['_allowedToConnectOnSend']=!0x0,this[_0x4b2850(0xee)]=!0x1,this[_0x4b2850(0xa0)]=!0x1,this[_0x4b2850(0x160)]=((_0x26bffd=(_0x34d24c=_0x14d9ea['process'])==null?void 0x0:_0x34d24c[_0x4b2850(0x116)])==null?void 0x0:_0x26bffd['NEXT_RUNTIME'])===_0x4b2850(0x9e),this[_0x4b2850(0x174)]=!((_0x4b345e=(_0xeab781=this[_0x4b2850(0xf8)][_0x4b2850(0x104)])==null?void 0x0:_0xeab781['versions'])!=null&&_0x4b345e[_0x4b2850(0xc8)])&&!this[_0x4b2850(0x160)],this[_0x4b2850(0xe6)]=null,this[_0x4b2850(0xfc)]=0x0,this[_0x4b2850(0xf1)]=0x14,this['_webSocketErrorDocsLink']=_0x4b2850(0xcd),this[_0x4b2850(0xb8)]=(this[_0x4b2850(0x174)]?_0x4b2850(0xaf):_0x4b2850(0x9f))+this[_0x4b2850(0xc9)];}async[_0x418f23(0xac)](){var _0x4a1673=_0x418f23,_0x2d8a6c,_0x2fabb9;if(this[_0x4a1673(0xe6)])return this[_0x4a1673(0xe6)];let _0x338282;if(this[_0x4a1673(0x174)]||this[_0x4a1673(0x160)])_0x338282=this[_0x4a1673(0xf8)][_0x4a1673(0x17c)];else{if((_0x2d8a6c=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])!=null&&_0x2d8a6c[_0x4a1673(0xcc)])_0x338282=(_0x2fabb9=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])==null?void 0x0:_0x2fabb9[_0x4a1673(0xcc)];else try{let _0x6adc18=await import(_0x4a1673(0x17f));_0x338282=(await import((await import(_0x4a1673(0x14c)))[_0x4a1673(0xb2)](_0x6adc18['join'](this[_0x4a1673(0x173)],_0x4a1673(0x9a)))['toString']()))[_0x4a1673(0x164)];}catch{try{_0x338282=require(require(_0x4a1673(0x17f))['join'](this[_0x4a1673(0x173)],'ws'));}catch{throw new Error('failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket');}}}return this[_0x4a1673(0xe6)]=_0x338282,_0x338282;}[_0x418f23(0xe2)](){var _0x560a95=_0x418f23;this[_0x560a95(0xa0)]||this[_0x560a95(0xee)]||this[_0x560a95(0xfc)]>=this[_0x560a95(0xf1)]||(this['_allowedToConnectOnSend']=!0x1,this['_connecting']=!0x0,this[_0x560a95(0xfc)]++,this['_ws']=new Promise((_0x48a2aa,_0x1b9b87)=>{var _0x3507cc=_0x560a95;this[_0x3507cc(0xac)]()['then'](_0x2d9634=>{var _0x4649cf=_0x3507cc;let _0x18b292=new _0x2d9634(_0x4649cf(0x185)+(!this['_inBrowser']&&this['dockerizedApp']?_0x4649cf(0x15a):this[_0x4649cf(0xe0)])+':'+this['port']);_0x18b292[_0x4649cf(0x16f)]=()=>{var _0x37af5c=_0x4649cf;this['_allowedToSend']=!0x1,this[_0x37af5c(0x162)](_0x18b292),this['_attemptToReconnectShortly'](),_0x1b9b87(new Error('logger\\\\x20websocket\\\\x20error'));},_0x18b292[_0x4649cf(0xf5)]=()=>{var _0x5c5b5c=_0x4649cf;this[_0x5c5b5c(0x174)]||_0x18b292[_0x5c5b5c(0xe7)]&&_0x18b292[_0x5c5b5c(0xe7)]['unref']&&_0x18b292[_0x5c5b5c(0xe7)]['unref'](),_0x48a2aa(_0x18b292);},_0x18b292[_0x4649cf(0xb6)]=()=>{this['_allowedToConnectOnSend']=!0x0,this['_disposeWebsocket'](_0x18b292),this['_attemptToReconnectShortly']();},_0x18b292[_0x4649cf(0x121)]=_0xf360ec=>{var _0x34c0e1=_0x4649cf;try{if(!(_0xf360ec!=null&&_0xf360ec[_0x34c0e1(0x99)])||!this[_0x34c0e1(0x12f)])return;let _0x5a655a=JSON[_0x34c0e1(0x13d)](_0xf360ec[_0x34c0e1(0x99)]);this['eventReceivedCallback'](_0x5a655a['method'],_0x5a655a[_0x34c0e1(0xab)],this[_0x34c0e1(0xf8)],this[_0x34c0e1(0x174)]);}catch{}};})['then'](_0x382d9b=>(this['_connected']=!0x0,this[_0x3507cc(0xa0)]=!0x1,this[_0x3507cc(0x12c)]=!0x1,this[_0x3507cc(0x159)]=!0x0,this['_connectAttemptCount']=0x0,_0x382d9b))['catch'](_0x469147=>(this[_0x3507cc(0xee)]=!0x1,this[_0x3507cc(0xa0)]=!0x1,console[_0x3507cc(0xed)](_0x3507cc(0x169)+this[_0x3507cc(0xc9)]),_0x1b9b87(new Error(_0x3507cc(0x12a)+(_0x469147&&_0x469147[_0x3507cc(0xb4)])))));}));}[_0x418f23(0x162)](_0x391e4c){var _0x18bf98=_0x418f23;this[_0x18bf98(0xee)]=!0x1,this[_0x18bf98(0xa0)]=!0x1;try{_0x391e4c['onclose']=null,_0x391e4c[_0x18bf98(0x16f)]=null,_0x391e4c[_0x18bf98(0xf5)]=null;}catch{}try{_0x391e4c[_0x18bf98(0xb9)]<0x2&&_0x391e4c[_0x18bf98(0x141)]();}catch{}}['_attemptToReconnectShortly'](){var _0x4846b6=_0x418f23;clearTimeout(this[_0x4846b6(0xa3)]),!(this[_0x4846b6(0xfc)]>=this[_0x4846b6(0xf1)])&&(this['_reconnectTimeout']=setTimeout(()=>{var _0xc0d1ae=_0x4846b6,_0x3b3b8b;this[_0xc0d1ae(0xee)]||this[_0xc0d1ae(0xa0)]||(this[_0xc0d1ae(0xe2)](),(_0x3b3b8b=this[_0xc0d1ae(0xd2)])==null||_0x3b3b8b[_0xc0d1ae(0x120)](()=>this['_attemptToReconnectShortly']()));},0x1f4),this['_reconnectTimeout'][_0x4846b6(0x188)]&&this[_0x4846b6(0xa3)][_0x4846b6(0x188)]());}async[_0x418f23(0x11e)](_0x592dff){var _0x123097=_0x418f23;try{if(!this[_0x123097(0x159)])return;this[_0x123097(0x12c)]&&this[_0x123097(0xe2)](),(await this['_ws'])[_0x123097(0x11e)](JSON[_0x123097(0xdd)](_0x592dff));}catch(_0x3558e1){this['_extendedWarning']?console[_0x123097(0xed)](this['_sendErrorMessage']+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)])):(this[_0x123097(0x167)]=!0x0,console[_0x123097(0xed)](this[_0x123097(0xb8)]+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)]),_0x592dff)),this[_0x123097(0x159)]=!0x1,this[_0x123097(0xb3)]();}}};function H(_0x21a490,_0x6209b7,_0x32bdf1,_0x32048a,_0x5bcdf6,_0x3f8a6e,_0xb987a3,_0x3abcb6=oe){var _0x372163=_0x418f23;let _0x52a2ac=_0x32bdf1[_0x372163(0x190)](',')[_0x372163(0x12e)](_0x230c9d=>{var _0x1b5d4e=_0x372163,_0x4a53bb,_0x1cde39,_0x106ea9,_0x3f43e6;try{if(!_0x21a490['_console_ninja_session']){let _0x24bfb9=((_0x1cde39=(_0x4a53bb=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x4a53bb['versions'])==null?void 0x0:_0x1cde39[_0x1b5d4e(0xc8)])||((_0x3f43e6=(_0x106ea9=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x106ea9[_0x1b5d4e(0x116)])==null?void 0x0:_0x3f43e6[_0x1b5d4e(0xd6)])==='edge';(_0x5bcdf6===_0x1b5d4e(0x110)||_0x5bcdf6===_0x1b5d4e(0x155)||_0x5bcdf6==='astro'||_0x5bcdf6==='angular')&&(_0x5bcdf6+=_0x24bfb9?_0x1b5d4e(0x10c):_0x1b5d4e(0x124)),_0x21a490['_console_ninja_session']={'id':+new Date(),'tool':_0x5bcdf6},_0xb987a3&&_0x5bcdf6&&!_0x24bfb9&&console['log'](_0x1b5d4e(0xfe)+(_0x5bcdf6[_0x1b5d4e(0x13c)](0x0)[_0x1b5d4e(0x100)]()+_0x5bcdf6['substr'](0x1))+',','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)',_0x1b5d4e(0xbb));}let _0x4eb2eb=new q(_0x21a490,_0x6209b7,_0x230c9d,_0x32048a,_0x3f8a6e,_0x3abcb6);return _0x4eb2eb[_0x1b5d4e(0x11e)][_0x1b5d4e(0xf4)](_0x4eb2eb);}catch(_0x202950){return console[_0x1b5d4e(0xed)](_0x1b5d4e(0x18e),_0x202950&&_0x202950[_0x1b5d4e(0xb4)]),()=>{};}});return _0x17b111=>_0x52a2ac[_0x372163(0x178)](_0x3b7429=>_0x3b7429(_0x17b111));}function _0x4e19(){var _0x3dea94=['perf_hooks','now','elements','6915181ldjYIK','send','date','catch','onmessage','_isUndefined','_HTMLAllCollection','\\\\x20browser','_setNodePermissions','strLength','_getOwnPropertyDescriptor','_Symbol','indexOf','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','number','_allowedToConnectOnSend','_sortProps','map','eventReceivedCallback','array','dockerizedApp','match','_processTreeNodeResult','disabledLog','_numberRegExp','_hasSymbolPropertyOnItsPath',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"DevHardtech\\\",\\\"************\\\"],'_hasMapOnItsPath','performance','coverage','Error','charAt','parse','function','endsWith','rootExpression','close','undefined','_hasSetOnItsPath','_regExpToString','_p_name','slice','substr','11hsvZPL','hostname','serialize','_dateToString','url','boolean','enumerable','length','_addObjectProperty','root_exp','origin','reload','Symbol','remix','_objectToString','push','getOwnPropertySymbols','_allowedToSend','gateway.docker.internal','HTMLAllCollection','_getOwnPropertyNames','_isPrimitiveWrapperType','4193466bntOOn','16178350tQpRDP','_inNextEdge','time','_disposeWebsocket','_setNodeLabel','default','_ninjaIgnoreNextError','concat','_extendedWarning','resolveGetters','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','hrtime','current','_cleanNode','replace','_consoleNinjaAllowedToStart','onerror','Map','getOwnPropertyDescriptor','getter','nodeModules','_inBrowser','sort','_blacklistedProperty','autoExpandLimit','forEach','_undefined',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.vscode\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.456\\\\\\\\node_modules\\\",'_addProperty','WebSocket','_property','_addLoadNode','path','props','NEGATIVE_INFINITY','_type','webpack','error','ws://','_isMap','null','unref','negativeInfinity','','_quotedRegExp','set','nan','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','_p_length','split','7999758ImPfSL','_isArray','data','ws/index.js','parent','8YXKnRI','autoExpandPropertyCount','edge','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','_connecting','2rNewCg','allStrLength','_reconnectTimeout','noFunctions','toString','index','fromCharCode','_treeNodePropertiesBeforeFullValue','versions','count','args','getWebSocketClass','funcName','_isPrimitiveType','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','262697JHDjIO','1','pathToFileURL','_attemptToReconnectShortly','message','isExpressionToEvaluate','onclose','name','_sendErrorMessage','readyState','location','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','console','disabledTrace','[object\\\\x20BigInt]','valueOf','autoExpand','[object\\\\x20Date]','107080UCogNw','includes','trace','hits','expressionsToEvaluate','_p_','node','_webSocketErrorDocsLink','unknown','_setNodeId','_WebSocket','https://tinyurl.com/37x8b79t','value','_getOwnPropertySymbols','negativeZero','_setNodeQueryPath','_ws','elapsed','symbol','_propertyName','NEXT_RUNTIME','stackTraceLimit','_console_ninja_session','getPrototypeOf','call','_console_ninja','[object\\\\x20Array]','stringify','_isSet','getOwnPropertyNames','host','8484993ONNFtV','_connectToHostNow','level','_treeNodePropertiesAfterFullValue','create','_WebSocketClass','_socket','_addFunctionsNode','_capIfString','Boolean','_setNodeExpandableState','62812','warn','_connected','timeStamp','depth','_maxConnectAttemptCount','hasOwnProperty','capped','bind','onopen','Set','constructor','global','bigint','POSITIVE_INFINITY','sortProps','_connectAttemptCount','Number','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','log','toUpperCase','string','positiveInfinity','defineProperty','process','...','String','some','get','200DTLFWz','','type','\\\\x20server','startsWith','toLowerCase','_additionalMetadata','next.js','_setNodeExpressionPath','reduceLimits','test','[object\\\\x20Map]','autoExpandMaxDepth','env','object','autoExpandPreviousObjects','prototype'];_0x4e19=function(){return _0x3dea94;};return _0x4e19();}function oe(_0x29bd2d,_0x4ca25e,_0x2f30dc,_0x50ad96){var _0x4b277d=_0x418f23;_0x50ad96&&_0x29bd2d===_0x4b277d(0x153)&&_0x2f30dc[_0x4b277d(0xba)]['reload']();}function B(_0x53e47a){var _0x4f5825=_0x418f23,_0x17ea3d,_0x5476d8;let _0x2ed5e7=function(_0x3f9b33,_0x4798cf){return _0x4798cf-_0x3f9b33;},_0x2534f8;if(_0x53e47a[_0x4f5825(0x139)])_0x2534f8=function(){var _0xf68f54=_0x4f5825;return _0x53e47a[_0xf68f54(0x139)][_0xf68f54(0x11b)]();};else{if(_0x53e47a[_0x4f5825(0x104)]&&_0x53e47a[_0x4f5825(0x104)][_0x4f5825(0x16a)]&&((_0x5476d8=(_0x17ea3d=_0x53e47a[_0x4f5825(0x104)])==null?void 0x0:_0x17ea3d[_0x4f5825(0x116)])==null?void 0x0:_0x5476d8[_0x4f5825(0xd6)])!==_0x4f5825(0x9e))_0x2534f8=function(){var _0x1144bb=_0x4f5825;return _0x53e47a[_0x1144bb(0x104)][_0x1144bb(0x16a)]();},_0x2ed5e7=function(_0x4a8621,_0xc276d4){return 0x3e8*(_0xc276d4[0x0]-_0x4a8621[0x0])+(_0xc276d4[0x1]-_0x4a8621[0x1])/0xf4240;};else try{let {performance:_0x6c0ab3}=require(_0x4f5825(0x11a));_0x2534f8=function(){var _0x57029c=_0x4f5825;return _0x6c0ab3[_0x57029c(0x11b)]();};}catch{_0x2534f8=function(){return+new Date();};}}return{'elapsed':_0x2ed5e7,'timeStamp':_0x2534f8,'now':()=>Date['now']()};}function X(_0x108a65,_0x2bc4c8,_0x5e7fce){var _0xd0e45=_0x418f23,_0x184b4d,_0x3be467,_0x1494d3,_0x1853ba,_0xc61e6c;if(_0x108a65[_0xd0e45(0x16e)]!==void 0x0)return _0x108a65['_consoleNinjaAllowedToStart'];let _0xae1558=((_0x3be467=(_0x184b4d=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x184b4d[_0xd0e45(0xa9)])==null?void 0x0:_0x3be467[_0xd0e45(0xc8)])||((_0x1853ba=(_0x1494d3=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x1494d3[_0xd0e45(0x116)])==null?void 0x0:_0x1853ba[_0xd0e45(0xd6)])===_0xd0e45(0x9e);function _0x492297(_0x174e6c){var _0x9b4def=_0xd0e45;if(_0x174e6c[_0x9b4def(0x10d)]('/')&&_0x174e6c[_0x9b4def(0x13f)]('/')){let _0x2461d3=new RegExp(_0x174e6c['slice'](0x1,-0x1));return _0x2a4fef=>_0x2461d3[_0x9b4def(0x113)](_0x2a4fef);}else{if(_0x174e6c[_0x9b4def(0xc3)]('*')||_0x174e6c[_0x9b4def(0xc3)]('?')){let _0x51dbdb=new RegExp('^'+_0x174e6c[_0x9b4def(0x16d)](/\\\\./g,String[_0x9b4def(0xa7)](0x5c)+'.')[_0x9b4def(0x16d)](/\\\\*/g,'.*')[_0x9b4def(0x16d)](/\\\\?/g,'.')+String[_0x9b4def(0xa7)](0x24));return _0x2bf349=>_0x51dbdb['test'](_0x2bf349);}else return _0x40a674=>_0x40a674===_0x174e6c;}}let _0x418e9a=_0x2bc4c8[_0xd0e45(0x12e)](_0x492297);return _0x108a65[_0xd0e45(0x16e)]=_0xae1558||!_0x2bc4c8,!_0x108a65['_consoleNinjaAllowedToStart']&&((_0xc61e6c=_0x108a65[_0xd0e45(0xba)])==null?void 0x0:_0xc61e6c['hostname'])&&(_0x108a65[_0xd0e45(0x16e)]=_0x418e9a[_0xd0e45(0x107)](_0x1dbe80=>_0x1dbe80(_0x108a65[_0xd0e45(0xba)][_0xd0e45(0x149)]))),_0x108a65['_consoleNinjaAllowedToStart'];}function _0x33f3(_0x3a814d,_0x58c537){var _0x4e195d=_0x4e19();return _0x33f3=function(_0x33f3b8,_0x2e2a30){_0x33f3b8=_0x33f3b8-0x98;var _0x3c84c1=_0x4e195d[_0x33f3b8];return _0x3c84c1;},_0x33f3(_0x3a814d,_0x58c537);}function J(_0x3830e6,_0x45a6b5,_0x2f8209,_0x3cee70){var _0x40c820=_0x418f23;_0x3830e6=_0x3830e6,_0x45a6b5=_0x45a6b5,_0x2f8209=_0x2f8209,_0x3cee70=_0x3cee70;let _0x38a5a7=B(_0x3830e6),_0x5b41b9=_0x38a5a7[_0x40c820(0xd3)],_0x1169a5=_0x38a5a7[_0x40c820(0xef)];class _0x1e3ba1{constructor(){var _0x3396c1=_0x40c820;this['_keyStrRegExp']=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this[_0x3396c1(0x135)]=/^(0|[1-9][0-9]*)$/,this[_0x3396c1(0x18b)]=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x3396c1(0x179)]=_0x3830e6[_0x3396c1(0x142)],this[_0x3396c1(0x123)]=_0x3830e6[_0x3396c1(0x15b)],this[_0x3396c1(0x127)]=Object[_0x3396c1(0x171)],this['_getOwnPropertyNames']=Object[_0x3396c1(0xdf)],this[_0x3396c1(0x128)]=_0x3830e6[_0x3396c1(0x154)],this[_0x3396c1(0x144)]=RegExp[_0x3396c1(0x119)][_0x3396c1(0xa5)],this['_dateToString']=Date[_0x3396c1(0x119)][_0x3396c1(0xa5)];}[_0x40c820(0x14a)](_0x1f7b5d,_0x5b6b91,_0x1ebf24,_0x4f3c70){var _0x4d7e42=_0x40c820,_0xe363bc=this,_0x290e3b=_0x1ebf24[_0x4d7e42(0xc0)];function _0x16ce5f(_0xf8520c,_0x1a1953,_0x3e443e){var _0x4923f3=_0x4d7e42;_0x1a1953[_0x4923f3(0x10b)]=_0x4923f3(0xca),_0x1a1953['error']=_0xf8520c[_0x4923f3(0xb4)],_0x454078=_0x3e443e[_0x4923f3(0xc8)]['current'],_0x3e443e['node'][_0x4923f3(0x16b)]=_0x1a1953,_0xe363bc['_treeNodePropertiesBeforeFullValue'](_0x1a1953,_0x3e443e);}let _0x1533a9;_0x3830e6[_0x4d7e42(0xbc)]&&(_0x1533a9=_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)],_0x1533a9&&(_0x3830e6['console'][_0x4d7e42(0x184)]=function(){}));try{try{_0x1ebf24[_0x4d7e42(0xe3)]++,_0x1ebf24['autoExpand']&&_0x1ebf24[_0x4d7e42(0x118)]['push'](_0x5b6b91);var _0x55a2c4,_0x5cbc7d,_0x10ebd6,_0x38ec49,_0x46d06f=[],_0x4ef003=[],_0x33c92e,_0xe8efc0=this[_0x4d7e42(0x182)](_0x5b6b91),_0x5b392f=_0xe8efc0===_0x4d7e42(0x130),_0x55d894=!0x1,_0x65caf4=_0xe8efc0===_0x4d7e42(0x13e),_0x512734=this['_isPrimitiveType'](_0xe8efc0),_0x3d6d36=this[_0x4d7e42(0x15d)](_0xe8efc0),_0x2d65b0=_0x512734||_0x3d6d36,_0x4b6f05={},_0x419e4c=0x0,_0x2bca20=!0x1,_0x454078,_0xed6526=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x1ebf24['depth']){if(_0x5b392f){if(_0x5cbc7d=_0x5b6b91['length'],_0x5cbc7d>_0x1ebf24[_0x4d7e42(0x11c)]){for(_0x10ebd6=0x0,_0x38ec49=_0x1ebf24[_0x4d7e42(0x11c)],_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003[_0x4d7e42(0x157)](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));_0x1f7b5d['cappedElements']=!0x0;}else{for(_0x10ebd6=0x0,_0x38ec49=_0x5cbc7d,_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));}_0x1ebf24[_0x4d7e42(0x9d)]+=_0x4ef003[_0x4d7e42(0x14f)];}if(!(_0xe8efc0==='null'||_0xe8efc0==='undefined')&&!_0x512734&&_0xe8efc0!==_0x4d7e42(0x106)&&_0xe8efc0!=='Buffer'&&_0xe8efc0!=='bigint'){var _0xfca776=_0x4f3c70['props']||_0x1ebf24[_0x4d7e42(0x180)];if(this['_isSet'](_0x5b6b91)?(_0x55a2c4=0x0,_0x5b6b91['forEach'](function(_0x1b3730){var _0x29b12d=_0x4d7e42;if(_0x419e4c++,_0x1ebf24['autoExpandPropertyCount']++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24[_0x29b12d(0xb5)]&&_0x1ebf24[_0x29b12d(0xc0)]&&_0x1ebf24[_0x29b12d(0x9d)]>_0x1ebf24[_0x29b12d(0x177)]){_0x2bca20=!0x0;return;}_0x4ef003['push'](_0xe363bc[_0x29b12d(0x17b)](_0x46d06f,_0x5b6b91,_0x29b12d(0xf6),_0x55a2c4++,_0x1ebf24,function(_0x383398){return function(){return _0x383398;};}(_0x1b3730)));})):this[_0x4d7e42(0x186)](_0x5b6b91)&&_0x5b6b91['forEach'](function(_0x4cd1d9,_0x42ee6b){var _0x3c460e=_0x4d7e42;if(_0x419e4c++,_0x1ebf24[_0x3c460e(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x3c460e(0xc0)]&&_0x1ebf24[_0x3c460e(0x9d)]>_0x1ebf24['autoExpandLimit']){_0x2bca20=!0x0;return;}var _0x2a4101=_0x42ee6b[_0x3c460e(0xa5)]();_0x2a4101[_0x3c460e(0x14f)]>0x64&&(_0x2a4101=_0x2a4101[_0x3c460e(0x146)](0x0,0x64)+_0x3c460e(0x105)),_0x4ef003[_0x3c460e(0x157)](_0xe363bc['_addProperty'](_0x46d06f,_0x5b6b91,_0x3c460e(0x170),_0x2a4101,_0x1ebf24,function(_0x1c45bc){return function(){return _0x1c45bc;};}(_0x4cd1d9)));}),!_0x55d894){try{for(_0x33c92e in _0x5b6b91)if(!(_0x5b392f&&_0xed6526['test'](_0x33c92e))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPropertyCount']>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x150)](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}catch{}if(_0x4b6f05[_0x4d7e42(0x18f)]=!0x0,_0x65caf4&&(_0x4b6f05[_0x4d7e42(0x145)]=!0x0),!_0x2bca20){var _0x469d20=[][_0x4d7e42(0x166)](this[_0x4d7e42(0x15c)](_0x5b6b91))[_0x4d7e42(0x166)](this[_0x4d7e42(0xcf)](_0x5b6b91));for(_0x55a2c4=0x0,_0x5cbc7d=_0x469d20[_0x4d7e42(0x14f)];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)if(_0x33c92e=_0x469d20[_0x55a2c4],!(_0x5b392f&&_0xed6526['test'](_0x33c92e[_0x4d7e42(0xa5)]()))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)&&!_0x4b6f05[_0x4d7e42(0xc7)+_0x33c92e[_0x4d7e42(0xa5)]()]){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24[_0x4d7e42(0xb5)]&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24[_0x4d7e42(0x9d)]>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc['_addObjectProperty'](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}}}}if(_0x1f7b5d['type']=_0xe8efc0,_0x2d65b0?(_0x1f7b5d['value']=_0x5b6b91[_0x4d7e42(0xbf)](),this['_capIfString'](_0xe8efc0,_0x1f7b5d,_0x1ebf24,_0x4f3c70)):_0xe8efc0===_0x4d7e42(0x11f)?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x14b)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='bigint'?_0x1f7b5d[_0x4d7e42(0xce)]=_0x5b6b91[_0x4d7e42(0xa5)]():_0xe8efc0==='RegExp'?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x144)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='symbol'&&this[_0x4d7e42(0x128)]?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x128)][_0x4d7e42(0x119)]['toString']['call'](_0x5b6b91):!_0x1ebf24[_0x4d7e42(0xf0)]&&!(_0xe8efc0===_0x4d7e42(0x187)||_0xe8efc0==='undefined')&&(delete _0x1f7b5d[_0x4d7e42(0xce)],_0x1f7b5d[_0x4d7e42(0xf3)]=!0x0),_0x2bca20&&(_0x1f7b5d['cappedProps']=!0x0),_0x454078=_0x1ebf24['node']['current'],_0x1ebf24[_0x4d7e42(0xc8)]['current']=_0x1f7b5d,this[_0x4d7e42(0xa8)](_0x1f7b5d,_0x1ebf24),_0x4ef003[_0x4d7e42(0x14f)]){for(_0x55a2c4=0x0,_0x5cbc7d=_0x4ef003['length'];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)_0x4ef003[_0x55a2c4](_0x55a2c4);}_0x46d06f['length']&&(_0x1f7b5d[_0x4d7e42(0x180)]=_0x46d06f);}catch(_0x54504a){_0x16ce5f(_0x54504a,_0x1f7b5d,_0x1ebf24);}this[_0x4d7e42(0x10f)](_0x5b6b91,_0x1f7b5d),this[_0x4d7e42(0xe4)](_0x1f7b5d,_0x1ebf24),_0x1ebf24[_0x4d7e42(0xc8)][_0x4d7e42(0x16b)]=_0x454078,_0x1ebf24['level']--,_0x1ebf24[_0x4d7e42(0xc0)]=_0x290e3b,_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPreviousObjects']['pop']();}finally{_0x1533a9&&(_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)]=_0x1533a9);}return _0x1f7b5d;}[_0x40c820(0xcf)](_0xd7ad14){var _0x474a44=_0x40c820;return Object[_0x474a44(0x158)]?Object[_0x474a44(0x158)](_0xd7ad14):[];}[_0x40c820(0xde)](_0x5b06ac){var _0x292c99=_0x40c820;return!!(_0x5b06ac&&_0x3830e6[_0x292c99(0xf6)]&&this[_0x292c99(0x156)](_0x5b06ac)==='[object\\\\x20Set]'&&_0x5b06ac[_0x292c99(0x178)]);}['_blacklistedProperty'](_0x10628d,_0x15c227,_0x5a4f15){var _0x152ffd=_0x40c820;return _0x5a4f15[_0x152ffd(0xa4)]?typeof _0x10628d[_0x15c227]==_0x152ffd(0x13e):!0x1;}[_0x40c820(0x182)](_0x13718c){var _0x2c19d1=_0x40c820,_0x225ae1='';return _0x225ae1=typeof _0x13718c,_0x225ae1==='object'?this[_0x2c19d1(0x156)](_0x13718c)==='[object\\\\x20Array]'?_0x225ae1=_0x2c19d1(0x130):this['_objectToString'](_0x13718c)===_0x2c19d1(0xc1)?_0x225ae1=_0x2c19d1(0x11f):this[_0x2c19d1(0x156)](_0x13718c)===_0x2c19d1(0xbe)?_0x225ae1=_0x2c19d1(0xf9):_0x13718c===null?_0x225ae1=_0x2c19d1(0x187):_0x13718c[_0x2c19d1(0xf7)]&&(_0x225ae1=_0x13718c['constructor'][_0x2c19d1(0xb7)]||_0x225ae1):_0x225ae1===_0x2c19d1(0x142)&&this[_0x2c19d1(0x123)]&&_0x13718c instanceof this['_HTMLAllCollection']&&(_0x225ae1=_0x2c19d1(0x15b)),_0x225ae1;}[_0x40c820(0x156)](_0x37617c){var _0xdf3907=_0x40c820;return Object[_0xdf3907(0x119)]['toString'][_0xdf3907(0xda)](_0x37617c);}[_0x40c820(0xae)](_0x26b95b){var _0x3b9373=_0x40c820;return _0x26b95b===_0x3b9373(0x14d)||_0x26b95b===_0x3b9373(0x101)||_0x26b95b===_0x3b9373(0x12b);}['_isPrimitiveWrapperType'](_0x150515){var _0x2539cd=_0x40c820;return _0x150515===_0x2539cd(0xea)||_0x150515==='String'||_0x150515===_0x2539cd(0xfd);}['_addProperty'](_0x1a647e,_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb){var _0x3c4649=this;return function(_0x5c2af7){var _0x865286=_0x33f3,_0x5ceb03=_0x985088[_0x865286(0xc8)][_0x865286(0x16b)],_0x14ad91=_0x985088[_0x865286(0xc8)]['index'],_0x10beb0=_0x985088[_0x865286(0xc8)][_0x865286(0x9b)];_0x985088['node'][_0x865286(0x9b)]=_0x5ceb03,_0x985088['node'][_0x865286(0xa6)]=typeof _0x551a3a==_0x865286(0x12b)?_0x551a3a:_0x5c2af7,_0x1a647e[_0x865286(0x157)](_0x3c4649[_0x865286(0x17d)](_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb)),_0x985088[_0x865286(0xc8)][_0x865286(0x9b)]=_0x10beb0,_0x985088['node']['index']=_0x14ad91;};}[_0x40c820(0x150)](_0x3e6c99,_0x96cdeb,_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b){var _0x102764=_0x40c820,_0xbcca65=this;return _0x96cdeb[_0x102764(0xc7)+_0x5e1ed6[_0x102764(0xa5)]()]=!0x0,function(_0x4f07e2){var _0x11a9a9=_0x102764,_0x41481f=_0x6bb8c1['node'][_0x11a9a9(0x16b)],_0x45aeeb=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)],_0x44cce6=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0x9b)];_0x6bb8c1['node']['parent']=_0x41481f,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x4f07e2,_0x3e6c99['push'](_0xbcca65[_0x11a9a9(0x17d)](_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b)),_0x6bb8c1['node']['parent']=_0x44cce6,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x45aeeb;};}['_property'](_0x5a954c,_0x11a196,_0x34292c,_0x53d319,_0x300135){var _0x3f13ca=_0x40c820,_0x350c39=this;_0x300135||(_0x300135=function(_0x26467a,_0x467a10){return _0x26467a[_0x467a10];});var _0x112124=_0x34292c[_0x3f13ca(0xa5)](),_0x42837e=_0x53d319[_0x3f13ca(0xc6)]||{},_0x265c6d=_0x53d319['depth'],_0x31debf=_0x53d319[_0x3f13ca(0xb5)];try{var _0x3c8586=this[_0x3f13ca(0x186)](_0x5a954c),_0x5579d4=_0x112124;_0x3c8586&&_0x5579d4[0x0]==='\\\\x27'&&(_0x5579d4=_0x5579d4[_0x3f13ca(0x147)](0x1,_0x5579d4[_0x3f13ca(0x14f)]-0x2));var _0x46f777=_0x53d319[_0x3f13ca(0xc6)]=_0x42837e[_0x3f13ca(0xc7)+_0x5579d4];_0x46f777&&(_0x53d319[_0x3f13ca(0xf0)]=_0x53d319[_0x3f13ca(0xf0)]+0x1),_0x53d319[_0x3f13ca(0xb5)]=!!_0x46f777;var _0x14534f=typeof _0x34292c=='symbol',_0x124dd3={'name':_0x14534f||_0x3c8586?_0x112124:this['_propertyName'](_0x112124)};if(_0x14534f&&(_0x124dd3[_0x3f13ca(0xd4)]=!0x0),!(_0x11a196===_0x3f13ca(0x130)||_0x11a196===_0x3f13ca(0x13b))){var _0x4ea27f=this[_0x3f13ca(0x127)](_0x5a954c,_0x34292c);if(_0x4ea27f&&(_0x4ea27f[_0x3f13ca(0x18c)]&&(_0x124dd3['setter']=!0x0),_0x4ea27f[_0x3f13ca(0x108)]&&!_0x46f777&&!_0x53d319['resolveGetters']))return _0x124dd3[_0x3f13ca(0x172)]=!0x0,this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x3214f9;try{_0x3214f9=_0x300135(_0x5a954c,_0x34292c);}catch(_0x3ef7eb){return _0x124dd3={'name':_0x112124,'type':_0x3f13ca(0xca),'error':_0x3ef7eb['message']},this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x4c3356=this[_0x3f13ca(0x182)](_0x3214f9),_0x18e3f8=this[_0x3f13ca(0xae)](_0x4c3356);if(_0x124dd3[_0x3f13ca(0x10b)]=_0x4c3356,_0x18e3f8)this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x7d7701=_0x3f13ca;_0x124dd3['value']=_0x3214f9[_0x7d7701(0xbf)](),!_0x46f777&&_0x350c39[_0x7d7701(0xe9)](_0x4c3356,_0x124dd3,_0x53d319,{});});else{var _0x275cea=_0x53d319[_0x3f13ca(0xc0)]&&_0x53d319['level']<_0x53d319['autoExpandMaxDepth']&&_0x53d319[_0x3f13ca(0x118)][_0x3f13ca(0x129)](_0x3214f9)<0x0&&_0x4c3356!==_0x3f13ca(0x13e)&&_0x53d319['autoExpandPropertyCount']<_0x53d319['autoExpandLimit'];_0x275cea||_0x53d319[_0x3f13ca(0xe3)]<_0x265c6d||_0x46f777?(this['serialize'](_0x124dd3,_0x3214f9,_0x53d319,_0x46f777||{}),this[_0x3f13ca(0x10f)](_0x3214f9,_0x124dd3)):this['_processTreeNodeResult'](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x2b8765=_0x3f13ca;_0x4c3356===_0x2b8765(0x187)||_0x4c3356===_0x2b8765(0x142)||(delete _0x124dd3[_0x2b8765(0xce)],_0x124dd3[_0x2b8765(0xf3)]=!0x0);});}return _0x124dd3;}finally{_0x53d319['expressionsToEvaluate']=_0x42837e,_0x53d319[_0x3f13ca(0xf0)]=_0x265c6d,_0x53d319['isExpressionToEvaluate']=_0x31debf;}}[_0x40c820(0xe9)](_0x3711dd,_0x3273d6,_0x30712a,_0x2bfd2c){var _0x22c791=_0x40c820,_0x3ed3d6=_0x2bfd2c['strLength']||_0x30712a[_0x22c791(0x126)];if((_0x3711dd==='string'||_0x3711dd==='String')&&_0x3273d6[_0x22c791(0xce)]){let _0xffdbb0=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x14f)];_0x30712a['allStrLength']+=_0xffdbb0,_0x30712a[_0x22c791(0xa2)]>_0x30712a['totalStrLength']?(_0x3273d6[_0x22c791(0xf3)]='',delete _0x3273d6[_0x22c791(0xce)]):_0xffdbb0>_0x3ed3d6&&(_0x3273d6[_0x22c791(0xf3)]=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x147)](0x0,_0x3ed3d6),delete _0x3273d6[_0x22c791(0xce)]);}}['_isMap'](_0x261c40){var _0xd2ec63=_0x40c820;return!!(_0x261c40&&_0x3830e6['Map']&&this[_0xd2ec63(0x156)](_0x261c40)===_0xd2ec63(0x114)&&_0x261c40[_0xd2ec63(0x178)]);}[_0x40c820(0xd5)](_0x24e250){var _0x49be73=_0x40c820;if(_0x24e250[_0x49be73(0x132)](/^\\\\d+$/))return _0x24e250;var _0x1d38d;try{_0x1d38d=JSON['stringify'](''+_0x24e250);}catch{_0x1d38d='\\\\x22'+this['_objectToString'](_0x24e250)+'\\\\x22';}return _0x1d38d['match'](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x1d38d=_0x1d38d[_0x49be73(0x147)](0x1,_0x1d38d[_0x49be73(0x14f)]-0x2):_0x1d38d=_0x1d38d[_0x49be73(0x16d)](/'/g,'\\\\x5c\\\\x27')[_0x49be73(0x16d)](/\\\\\\\\\\\"/g,'\\\\x22')[_0x49be73(0x16d)](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x1d38d;}[_0x40c820(0x133)](_0x1b7571,_0x8f439b,_0x2c2981,_0x188fd5){var _0xf97f10=_0x40c820;this[_0xf97f10(0xa8)](_0x1b7571,_0x8f439b),_0x188fd5&&_0x188fd5(),this[_0xf97f10(0x10f)](_0x2c2981,_0x1b7571),this['_treeNodePropertiesAfterFullValue'](_0x1b7571,_0x8f439b);}[_0x40c820(0xa8)](_0x231eb3,_0x5ea482){var _0x31f690=_0x40c820;this['_setNodeId'](_0x231eb3,_0x5ea482),this[_0x31f690(0xd1)](_0x231eb3,_0x5ea482),this[_0x31f690(0x111)](_0x231eb3,_0x5ea482),this[_0x31f690(0x125)](_0x231eb3,_0x5ea482);}[_0x40c820(0xcb)](_0x10d2e1,_0x3c8083){}[_0x40c820(0xd1)](_0x53d949,_0x188c67){}[_0x40c820(0x163)](_0x3f8259,_0x16e80a){}[_0x40c820(0x122)](_0x25a3a3){return _0x25a3a3===this['_undefined'];}[_0x40c820(0xe4)](_0x44987b,_0x4ed592){var _0x37fb3e=_0x40c820;this[_0x37fb3e(0x163)](_0x44987b,_0x4ed592),this[_0x37fb3e(0xeb)](_0x44987b),_0x4ed592[_0x37fb3e(0xfb)]&&this[_0x37fb3e(0x12d)](_0x44987b),this['_addFunctionsNode'](_0x44987b,_0x4ed592),this[_0x37fb3e(0x17e)](_0x44987b,_0x4ed592),this[_0x37fb3e(0x16c)](_0x44987b);}['_additionalMetadata'](_0x1c2784,_0x4c1dde){var _0x4f21c1=_0x40c820;try{_0x1c2784&&typeof _0x1c2784[_0x4f21c1(0x14f)]==_0x4f21c1(0x12b)&&(_0x4c1dde[_0x4f21c1(0x14f)]=_0x1c2784[_0x4f21c1(0x14f)]);}catch{}if(_0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x12b)||_0x4c1dde[_0x4f21c1(0x10b)]==='Number'){if(isNaN(_0x4c1dde[_0x4f21c1(0xce)]))_0x4c1dde[_0x4f21c1(0x18d)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];else switch(_0x4c1dde[_0x4f21c1(0xce)]){case Number[_0x4f21c1(0xfa)]:_0x4c1dde[_0x4f21c1(0x102)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case Number['NEGATIVE_INFINITY']:_0x4c1dde[_0x4f21c1(0x189)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case 0x0:this['_isNegativeZero'](_0x4c1dde[_0x4f21c1(0xce)])&&(_0x4c1dde[_0x4f21c1(0xd0)]=!0x0);break;}}else _0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x13e)&&typeof _0x1c2784['name']==_0x4f21c1(0x101)&&_0x1c2784[_0x4f21c1(0xb7)]&&_0x4c1dde['name']&&_0x1c2784[_0x4f21c1(0xb7)]!==_0x4c1dde[_0x4f21c1(0xb7)]&&(_0x4c1dde[_0x4f21c1(0xad)]=_0x1c2784[_0x4f21c1(0xb7)]);}['_isNegativeZero'](_0x289882){var _0x1b66c9=_0x40c820;return 0x1/_0x289882===Number[_0x1b66c9(0x181)];}['_sortProps'](_0x3992ee){var _0x3db550=_0x40c820;!_0x3992ee[_0x3db550(0x180)]||!_0x3992ee[_0x3db550(0x180)][_0x3db550(0x14f)]||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0x130)||_0x3992ee[_0x3db550(0x10b)]==='Map'||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0xf6)||_0x3992ee[_0x3db550(0x180)][_0x3db550(0x175)](function(_0x57a739,_0x31b40b){var _0x5dcaae=_0x3db550,_0x3d0d50=_0x57a739[_0x5dcaae(0xb7)][_0x5dcaae(0x10e)](),_0xd6d4fc=_0x31b40b[_0x5dcaae(0xb7)]['toLowerCase']();return _0x3d0d50<_0xd6d4fc?-0x1:_0x3d0d50>_0xd6d4fc?0x1:0x0;});}[_0x40c820(0xe8)](_0x12537a,_0x57f3dc){var _0x2884a4=_0x40c820;if(!(_0x57f3dc[_0x2884a4(0xa4)]||!_0x12537a['props']||!_0x12537a[_0x2884a4(0x180)][_0x2884a4(0x14f)])){for(var _0x53c006=[],_0x347d6e=[],_0x52e85a=0x0,_0x273297=_0x12537a['props']['length'];_0x52e85a<_0x273297;_0x52e85a++){var _0x1ee5b3=_0x12537a[_0x2884a4(0x180)][_0x52e85a];_0x1ee5b3[_0x2884a4(0x10b)]==='function'?_0x53c006[_0x2884a4(0x157)](_0x1ee5b3):_0x347d6e['push'](_0x1ee5b3);}if(!(!_0x347d6e[_0x2884a4(0x14f)]||_0x53c006[_0x2884a4(0x14f)]<=0x1)){_0x12537a[_0x2884a4(0x180)]=_0x347d6e;var _0x15f515={'functionsNode':!0x0,'props':_0x53c006};this[_0x2884a4(0xcb)](_0x15f515,_0x57f3dc),this[_0x2884a4(0x163)](_0x15f515,_0x57f3dc),this[_0x2884a4(0xeb)](_0x15f515),this[_0x2884a4(0x125)](_0x15f515,_0x57f3dc),_0x15f515['id']+='\\\\x20f',_0x12537a['props']['unshift'](_0x15f515);}}}['_addLoadNode'](_0x5bea6e,_0x14049e){}[_0x40c820(0xeb)](_0x199084){}[_0x40c820(0x98)](_0xf50c17){var _0x35cb98=_0x40c820;return Array['isArray'](_0xf50c17)||typeof _0xf50c17==_0x35cb98(0x117)&&this['_objectToString'](_0xf50c17)===_0x35cb98(0xdc);}[_0x40c820(0x125)](_0x3ea390,_0x54c209){}[_0x40c820(0x16c)](_0x25cdb9){var _0x1aa0a5=_0x40c820;delete _0x25cdb9[_0x1aa0a5(0x136)],delete _0x25cdb9[_0x1aa0a5(0x143)],delete _0x25cdb9[_0x1aa0a5(0x138)];}['_setNodeExpressionPath'](_0x17f351,_0x40c77e){}}let _0x459cb0=new _0x1e3ba1(),_0x218fe5={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x11fc4c={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x482c8e(_0x50675f,_0x2f7559,_0x19c481,_0x2c8a95,_0x245f16,_0x24484e){var _0x4c67af=_0x40c820;let _0x3ce9b8,_0x1af844;try{_0x1af844=_0x1169a5(),_0x3ce9b8=_0x2f8209[_0x2f7559],!_0x3ce9b8||_0x1af844-_0x3ce9b8['ts']>0x1f4&&_0x3ce9b8[_0x4c67af(0xaa)]&&_0x3ce9b8['time']/_0x3ce9b8[_0x4c67af(0xaa)]<0x64?(_0x2f8209[_0x2f7559]=_0x3ce9b8={'count':0x0,'time':0x0,'ts':_0x1af844},_0x2f8209[_0x4c67af(0xc5)]={}):_0x1af844-_0x2f8209[_0x4c67af(0xc5)]['ts']>0x32&&_0x2f8209['hits']['count']&&_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x161)]/_0x2f8209[_0x4c67af(0xc5)]['count']<0x64&&(_0x2f8209[_0x4c67af(0xc5)]={});let _0x157126=[],_0x1464d6=_0x3ce9b8[_0x4c67af(0x112)]||_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]?_0x11fc4c:_0x218fe5,_0x553948=_0x2ff7b4=>{var _0x3a1316=_0x4c67af;let _0x5d676c={};return _0x5d676c[_0x3a1316(0x180)]=_0x2ff7b4[_0x3a1316(0x180)],_0x5d676c['elements']=_0x2ff7b4['elements'],_0x5d676c[_0x3a1316(0x126)]=_0x2ff7b4[_0x3a1316(0x126)],_0x5d676c['totalStrLength']=_0x2ff7b4['totalStrLength'],_0x5d676c['autoExpandLimit']=_0x2ff7b4[_0x3a1316(0x177)],_0x5d676c[_0x3a1316(0x115)]=_0x2ff7b4['autoExpandMaxDepth'],_0x5d676c['sortProps']=!0x1,_0x5d676c['noFunctions']=!_0x45a6b5,_0x5d676c['depth']=0x1,_0x5d676c[_0x3a1316(0xe3)]=0x0,_0x5d676c['expId']='root_exp_id',_0x5d676c[_0x3a1316(0x140)]=_0x3a1316(0x151),_0x5d676c['autoExpand']=!0x0,_0x5d676c[_0x3a1316(0x118)]=[],_0x5d676c[_0x3a1316(0x9d)]=0x0,_0x5d676c[_0x3a1316(0x168)]=!0x0,_0x5d676c['allStrLength']=0x0,_0x5d676c[_0x3a1316(0xc8)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0x5d676c;};for(var _0x33de8f=0x0;_0x33de8f<_0x245f16[_0x4c67af(0x14f)];_0x33de8f++)_0x157126[_0x4c67af(0x157)](_0x459cb0[_0x4c67af(0x14a)]({'timeNode':_0x50675f===_0x4c67af(0x161)||void 0x0},_0x245f16[_0x33de8f],_0x553948(_0x1464d6),{}));if(_0x50675f==='trace'||_0x50675f===_0x4c67af(0x184)){let _0x1d9735=Error[_0x4c67af(0xd7)];try{Error[_0x4c67af(0xd7)]=0x1/0x0,_0x157126[_0x4c67af(0x157)](_0x459cb0['serialize']({'stackNode':!0x0},new Error()['stack'],_0x553948(_0x1464d6),{'strLength':0x1/0x0}));}finally{Error[_0x4c67af(0xd7)]=_0x1d9735;}}return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':_0x157126,'id':_0x2f7559,'context':_0x24484e}]};}catch(_0x2d5a77){return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':[{'type':_0x4c67af(0xca),'error':_0x2d5a77&&_0x2d5a77[_0x4c67af(0xb4)]}],'id':_0x2f7559,'context':_0x24484e}]};}finally{try{if(_0x3ce9b8&&_0x1af844){let _0xff386f=_0x1169a5();_0x3ce9b8[_0x4c67af(0xaa)]++,_0x3ce9b8['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x3ce9b8['ts']=_0xff386f,_0x2f8209[_0x4c67af(0xc5)]['count']++,_0x2f8209[_0x4c67af(0xc5)]['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x2f8209['hits']['ts']=_0xff386f,(_0x3ce9b8[_0x4c67af(0xaa)]>0x32||_0x3ce9b8[_0x4c67af(0x161)]>0x64)&&(_0x3ce9b8['reduceLimits']=!0x0),(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0xaa)]>0x3e8||_0x2f8209[_0x4c67af(0xc5)]['time']>0x12c)&&(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]=!0x0);}}catch{}}}return _0x482c8e;}((_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x296e29,_0x567fe9,_0x14adfa,_0x6b3989,_0x593945,_0x42f609)=>{var _0x543ef9=_0x418f23;if(_0x12a02f[_0x543ef9(0xdb)])return _0x12a02f[_0x543ef9(0xdb)];if(!X(_0x12a02f,_0x14adfa,_0x1164b7))return _0x12a02f[_0x543ef9(0xdb)]={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x12a02f[_0x543ef9(0xdb)];let _0x5a7d78=B(_0x12a02f),_0x236b4f=_0x5a7d78[_0x543ef9(0xd3)],_0x57b9d9=_0x5a7d78['timeStamp'],_0x163b61=_0x5a7d78[_0x543ef9(0x11b)],_0x384cd9={'hits':{},'ts':{}},_0x9c7997=J(_0x12a02f,_0x6b3989,_0x384cd9,_0x296e29),_0x3ffb36=_0xa1ec34=>{_0x384cd9['ts'][_0xa1ec34]=_0x57b9d9();},_0x4ce4d2=(_0x173258,_0x2e0c6d)=>{var _0x3b6e53=_0x543ef9;let _0x2b64c1=_0x384cd9['ts'][_0x2e0c6d];if(delete _0x384cd9['ts'][_0x2e0c6d],_0x2b64c1){let _0x557981=_0x236b4f(_0x2b64c1,_0x57b9d9());_0x593a90(_0x9c7997(_0x3b6e53(0x161),_0x173258,_0x163b61(),_0x50e896,[_0x557981],_0x2e0c6d));}},_0x46c5f3=_0x1f105e=>{var _0x1152c8=_0x543ef9,_0x4a2783;return _0x1164b7===_0x1152c8(0x110)&&_0x12a02f[_0x1152c8(0x152)]&&((_0x4a2783=_0x1f105e==null?void 0x0:_0x1f105e[_0x1152c8(0xab)])==null?void 0x0:_0x4a2783[_0x1152c8(0x14f)])&&(_0x1f105e[_0x1152c8(0xab)][0x0][_0x1152c8(0x152)]=_0x12a02f[_0x1152c8(0x152)]),_0x1f105e;};_0x12a02f[_0x543ef9(0xdb)]={'consoleLog':(_0x204f4b,_0x3e1804)=>{var _0x309615=_0x543ef9;_0x12a02f['console'][_0x309615(0xff)][_0x309615(0xb7)]!==_0x309615(0x134)&&_0x593a90(_0x9c7997(_0x309615(0xff),_0x204f4b,_0x163b61(),_0x50e896,_0x3e1804));},'consoleTrace':(_0x267a3f,_0x51c339)=>{var _0x4c4943=_0x543ef9,_0x33fd8c,_0x24e61f;_0x12a02f[_0x4c4943(0xbc)][_0x4c4943(0xff)]['name']!==_0x4c4943(0xbd)&&((_0x24e61f=(_0x33fd8c=_0x12a02f[_0x4c4943(0x104)])==null?void 0x0:_0x33fd8c['versions'])!=null&&_0x24e61f[_0x4c4943(0xc8)]&&(_0x12a02f[_0x4c4943(0x165)]=!0x0),_0x593a90(_0x46c5f3(_0x9c7997(_0x4c4943(0xc4),_0x267a3f,_0x163b61(),_0x50e896,_0x51c339))));},'consoleError':(_0xf7f1fc,_0x1384d7)=>{var _0x28d83c=_0x543ef9;_0x12a02f['_ninjaIgnoreNextError']=!0x0,_0x593a90(_0x46c5f3(_0x9c7997(_0x28d83c(0x184),_0xf7f1fc,_0x163b61(),_0x50e896,_0x1384d7)));},'consoleTime':_0x2ad865=>{_0x3ffb36(_0x2ad865);},'consoleTimeEnd':(_0x3c91cf,_0x308c8b)=>{_0x4ce4d2(_0x308c8b,_0x3c91cf);},'autoLog':(_0x4bbc9f,_0x3599a3)=>{var _0x598cfa=_0x543ef9;_0x593a90(_0x9c7997(_0x598cfa(0xff),_0x3599a3,_0x163b61(),_0x50e896,[_0x4bbc9f]));},'autoLogMany':(_0x158592,_0x29b77d)=>{var _0x425f64=_0x543ef9;_0x593a90(_0x9c7997(_0x425f64(0xff),_0x158592,_0x163b61(),_0x50e896,_0x29b77d));},'autoTrace':(_0x3f5f9d,_0xc378ab)=>{var _0x377a7d=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x377a7d(0xc4),_0xc378ab,_0x163b61(),_0x50e896,[_0x3f5f9d])));},'autoTraceMany':(_0x2c6f73,_0x35405b)=>{var _0x4f4e7f=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x4f4e7f(0xc4),_0x2c6f73,_0x163b61(),_0x50e896,_0x35405b)));},'autoTime':(_0x4915d6,_0xaaf0db,_0x4c1f1e)=>{_0x3ffb36(_0x4c1f1e);},'autoTimeEnd':(_0x397624,_0x436d57,_0x47b9b8)=>{_0x4ce4d2(_0x436d57,_0x47b9b8);},'coverage':_0x45a646=>{var _0x1e9860=_0x543ef9;_0x593a90({'method':_0x1e9860(0x13a),'version':_0x296e29,'args':[{'id':_0x45a646}]});}};let _0x593a90=H(_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x593945,_0x42f609),_0x50e896=_0x12a02f[_0x543ef9(0xd8)];return _0x12a02f[_0x543ef9(0xdb)];})(globalThis,'127.0.0.1',_0x418f23(0xec),_0x418f23(0x17a),_0x418f23(0x183),'1.0.0','1751470503250',_0x418f23(0x137),_0x418f23(0x18a),_0x418f23(0x10a),_0x418f23(0xb1));\");}catch(e){}};/* istanbul ignore next */function oo_oo(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleLog(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_tr(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleTrace(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_tx(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleError(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_ts(/**@type{any}**/v){try{oo_cm().consoleTime(v);}catch(e){} return v;};/* istanbul ignore next */function oo_te(/**@type{any}**/v, /**@type{any}**/i){try{oo_cm().consoleTimeEnd(v, i);}catch(e){} return v;};/*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/"]}, "metadata": {}, "sourceType": "module"}