<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class MerchandiseMaster
 * 
 * @property int $product_id
 * @property int $warehouse_id
 * @property int|null $kardex_id
 * @property int|null $warehouse_area_id
 * @property int $area_count
 * @property string|null $inventory_status
 * 
 * @property Kardex|null $kardex
 * @property Merchandise $merchandise
 * @property Warehouse $warehouse
 * @property WarehouseArea|null $warehouse_area
 * @property Collection|MerchandiseMasterArea[] $merchandise_master_areas
 *
 * @package App\Models
 */
class MerchandiseMaster extends Model
{
	protected $table = 'merchandise_master';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'product_id' => 'int',
		'warehouse_id' => 'int',
		'kardex_id' => 'int',
		'warehouse_area_id' => 'int',
		'area_count' => 'int'
	];

	protected $fillable = [
		'kardex_id',
		'warehouse_area_id',
		'area_count',
		'inventory_status'
	];

	public function kardex()
	{
		return $this->belongsTo(Kardex::class);
	}

	public function merchandise()
	{
		return $this->belongsTo(Merchandise::class, 'product_id');
	}

	public function warehouse()
	{
		return $this->belongsTo(Warehouse::class);
	}

	public function warehouse_area()
	{
		return $this->belongsTo(WarehouseArea::class);
	}

	public function merchandise_master_areas()
	{
		return $this->hasMany(MerchandiseMasterArea::class, 'product_id');
	}
}
