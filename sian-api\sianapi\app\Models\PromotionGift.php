<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PromotionGift
 * 
 * @property int $promotion_gift_id
 * @property string $promotion_gift_code
 * @property int $promotion_item_id
 * @property int $gift_number
 * @property int $option_count
 * @property bool $is_removable
 * 
 * @property PromotionItem $promotion_item
 * @property Collection|PromotionGiftOption[] $promotion_gift_options
 *
 * @package App\Models
 */
class PromotionGift extends Model
{
	protected $table = 'promotion_gift';
	protected $primaryKey = 'promotion_gift_id';
	public $timestamps = false;

	protected $casts = [
		'promotion_item_id' => 'int',
		'gift_number' => 'int',
		'option_count' => 'int',
		'is_removable' => 'bool'
	];

	protected $fillable = [
		'promotion_gift_code',
		'promotion_item_id',
		'gift_number',
		'option_count',
		'is_removable'
	];

	public function promotion_item()
	{
		return $this->belongsTo(PromotionItem::class);
	}

	public function promotion_gift_options()
	{
		return $this->hasMany(PromotionGiftOption::class);
	}
}
