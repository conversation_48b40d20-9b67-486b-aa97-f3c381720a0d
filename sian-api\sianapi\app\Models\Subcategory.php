<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Subcategory
 *
 * @property int $subcategory_id
 * @property string $subcategory_name
 * @property string $alias
 * @property bool $status
 * @property int $category_id
 * @property bool $web_enabled
 * @property string|null $transaction_code
 *
 * @property Category $category
 * @property Collection|ObjectModel[] $objects
 * @property Collection|Section[] $sections
 *
 * @package App\Models
 */
class Subcategory extends Model
{
	protected $table = 'subcategory';
	protected $primaryKey = 'subcategory_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'category_id' => 'int',
		'web_enabled' => 'bool'
	];

	protected $fillable = [
		'subcategory_name',
		'alias',
		'status',
		'category_id',
		'web_enabled',
		'transaction_code'
	];

	public function category()
	{
		return $this->belongsTo(Category::class);
	}

	public function objects()
	{
		return $this->hasMany(ObjectModel::class);
	}

	public function sections()
	{
		return $this->hasMany(Section::class);
	}
}
