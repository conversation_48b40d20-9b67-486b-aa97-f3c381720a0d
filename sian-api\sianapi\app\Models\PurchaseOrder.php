<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PurchaseOrder
 *
 * @property int $movement_id
 * @property Carbon $delivery_date
 * @property int $warehouse_id
 *
 * @property CommercialMovement $commercial_movement
 * @property Warehouse $warehouse
 *
 * @package App\Models
 */
class PurchaseOrder extends Model
{
    const ROUTE = 'logistic/purchaseOrder';
	protected $table = 'purchase_order';
	protected $primaryKey = 'movement_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'warehouse_id' => 'int'
	];

	protected $dates = [
		'delivery_date'
	];

	protected $fillable = [
		'delivery_date',
		'warehouse_id'
	];

	public function commercial_movement()
	{
		return $this->belongsTo(CommercialMovement::class, 'movement_id');
	}

	public function warehouse()
	{
		return $this->belongsTo(Warehouse::class);
	}
}
