<?php

namespace App\Http\Resources\Procedures;

use Illuminate\Http\Resources\Json\JsonResource;

class TypeDocumentaryProcedureResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            'id' => $this->id,
            'code' => $this->codename,
            'name' => $this->name,
            'is_active' => $this->is_active,
        ];
    }
}
