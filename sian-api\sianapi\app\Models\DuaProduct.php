<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class DuaProduct
 * 
 * @property int $item_id
 * @property float $kilos
 * @property float $ufob_pen
 * @property float $tfob_pen
 * @property float $freight_pen
 * @property float $cfr_pen
 * @property float $insurance_pen
 * @property float $ad_valorem_pen
 * @property float $cif_pen
 * @property float $other_pen
 * @property float $tcost_pen
 * @property float $ucost_pen
 * @property float $ufob_usd
 * @property float $tfob_usd
 * @property float $freight_usd
 * @property float $cfr_usd
 * @property float $insurance_usd
 * @property float $ad_valorem_usd
 * @property float $cif_usd
 * @property float $other_usd
 * @property float $tcost_usd
 * @property float $ucost_usd
 * 
 * @property CommercialMovementProduct $commercial_movement_product
 *
 * @package App\Models
 */
class DuaProduct extends Model
{
	protected $table = 'dua_product';
	protected $primaryKey = 'item_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'item_id' => 'int',
		'kilos' => 'float',
		'ufob_pen' => 'float',
		'tfob_pen' => 'float',
		'freight_pen' => 'float',
		'cfr_pen' => 'float',
		'insurance_pen' => 'float',
		'ad_valorem_pen' => 'float',
		'cif_pen' => 'float',
		'other_pen' => 'float',
		'tcost_pen' => 'float',
		'ucost_pen' => 'float',
		'ufob_usd' => 'float',
		'tfob_usd' => 'float',
		'freight_usd' => 'float',
		'cfr_usd' => 'float',
		'insurance_usd' => 'float',
		'ad_valorem_usd' => 'float',
		'cif_usd' => 'float',
		'other_usd' => 'float',
		'tcost_usd' => 'float',
		'ucost_usd' => 'float'
	];

	protected $fillable = [
		'kilos',
		'ufob_pen',
		'tfob_pen',
		'freight_pen',
		'cfr_pen',
		'insurance_pen',
		'ad_valorem_pen',
		'cif_pen',
		'other_pen',
		'tcost_pen',
		'ucost_pen',
		'ufob_usd',
		'tfob_usd',
		'freight_usd',
		'cfr_usd',
		'insurance_usd',
		'ad_valorem_usd',
		'cif_usd',
		'other_usd',
		'tcost_usd',
		'ucost_usd'
	];

	public function commercial_movement_product()
	{
		return $this->belongsTo(CommercialMovementProduct::class, 'item_id');
	}
}
