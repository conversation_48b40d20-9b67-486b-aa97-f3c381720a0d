<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class OperationDynamic
 * 
 * @property string $operation_code
 * @property int $entry_number
 * @property string $column
 * @property string $account_code
 * @property string $type
 * @property string $field
 * @property string|null $formula
 * @property bool $fee
 * @property int $order
 * @property bool $locked
 * 
 * @property Account $account
 * @property ScenarioField $scenario_field
 * @property Operation $operation
 *
 * @package App\Models
 */
class OperationDynamic extends Model
{
	protected $table = 'operation_dynamic';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'entry_number' => 'int',
		'fee' => 'bool',
		'order' => 'int',
		'locked' => 'bool'
	];

	protected $fillable = [
		'type',
		'formula',
		'fee',
		'order',
		'locked'
	];

	public function account()
	{
		return $this->belongsTo(Account::class, 'account_code');
	}

	public function scenario_field()
	{
		return $this->belongsTo(ScenarioField::class, 'type')
					->where('scenario_field.type', '=', 'operation_dynamic.type')
					->where('scenario_field.field', '=', 'operation_dynamic.field');
	}

	public function operation()
	{
		return $this->belongsTo(Operation::class, 'operation_code');
	}
}
