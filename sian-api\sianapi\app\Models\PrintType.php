<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PrintType
 * 
 * @property int $print_type_id
 * @property string $print_type_name
 * @property bool $repeatedly
 * @property bool $editable
 * @property bool $removable
 * @property bool $print_for_null
 * @property bool $required_format
 * 
 * @property Collection|DocumentSerie[] $document_series
 *
 * @package App\Models
 */
class PrintType extends Model
{
	protected $table = 'print_type';
	protected $primaryKey = 'print_type_id';
	public $timestamps = false;

	protected $casts = [
		'repeatedly' => 'bool',
		'editable' => 'bool',
		'removable' => 'bool',
		'print_for_null' => 'bool',
		'required_format' => 'bool'
	];

	protected $fillable = [
		'print_type_name',
		'repeatedly',
		'editable',
		'removable',
		'print_for_null',
		'required_format'
	];

	public function document_series()
	{
		return $this->hasMany(DocumentSerie::class);
	}
}
