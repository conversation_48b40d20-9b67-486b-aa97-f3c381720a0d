<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class NetworkingActivity
 * 
 * @property int $networking_activity_id
 * @property string $networking_activity_name
 * @property int $networking_activity_type_id
 * @property int $networking_project_id
 * @property int $technical_id
 * @property string|null $description
 * @property string $estimated_time
 * @property Carbon $start_date
 * @property Carbon $end_date
 * @property Carbon $estimated_date
 * @property string|null $time_done
 * @property string $state
 * 
 * @property NetworkingActivityType $networking_activity_type
 * @property NetworkingProject $networking_project
 * @property Technical $technical
 *
 * @package App\Models
 */
class NetworkingActivity extends Model
{
	protected $table = 'networking_activity';
	protected $primaryKey = 'networking_activity_id';
	public $timestamps = false;

	protected $casts = [
		'networking_activity_type_id' => 'int',
		'networking_project_id' => 'int',
		'technical_id' => 'int'
	];

	protected $dates = [
		'start_date',
		'end_date',
		'estimated_date'
	];

	protected $fillable = [
		'networking_activity_name',
		'networking_activity_type_id',
		'networking_project_id',
		'technical_id',
		'description',
		'estimated_time',
		'start_date',
		'end_date',
		'estimated_date',
		'time_done',
		'state'
	];

	public function networking_activity_type()
	{
		return $this->belongsTo(NetworkingActivityType::class);
	}

	public function networking_project()
	{
		return $this->belongsTo(NetworkingProject::class);
	}

	public function technical()
	{
		return $this->belongsTo(Technical::class);
	}
}
