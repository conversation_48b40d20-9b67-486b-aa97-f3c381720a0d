<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class News
 *
 * @property int $news_id
 * @property string $body
 * @property int $object_id
 *
 * @property ObjectModel $object
 *
 * @package App\Models
 */
class News extends Model
{
	protected $table = 'news';
	protected $primaryKey = 'news_id';
	public $timestamps = false;

	protected $casts = [
		'object_id' => 'int'
	];

	protected $fillable = [
		'body',
		'object_id'
	];

	public function object()
	{
		return $this->belongsTo(ObjectModel::class);
	}
}
