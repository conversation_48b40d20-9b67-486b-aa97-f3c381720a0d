<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class CashboxMovement
 *
 * @property int $movement_id
 * @property float $total_pen
 * @property float $itf_pen
 * @property float $port_pen
 * @property float $desgravamen_pen
 * @property float $interest_pen
 * @property float $cash_round_pen
 * @property float $cash_round_income_pen
 * @property float $real_pen
 * @property float $cash_pen
 * @property float $total_usd
 * @property float $itf_usd
 * @property float $port_usd
 * @property float $desgravamen_usd
 * @property float $interest_usd
 * @property float $cash_round_usd
 * @property float $cash_round_income_usd
 * @property float $real_usd
 * @property float $cash_usd
 * @property string $type
 * @property string|null $reference_number
 * @property string|null $credit_card
 * @property bool $calculate_cash_round
 * @property bool $calculate_change
 * @property int $cashbox_id
 * @property bool $confirmed
 *
 * @property Cashbox $cashbox
 * @property Movement $movement
 *
 * @package App\Models
 */
class CashboxMovement extends Model
{

    const PAYMENT_METHOD_CASH = 'Efectivo';
    const PAYMENT_METHOD_BANK_CHECK = 'Cheque';
    const PAYMENT_METHOD_BANK_DEPOSIT = 'Depósito';
    const PAYMENT_METHOD_CREDIT_CARD = 'Tarjeta';
    const PAYMENT_METHOD_DIGITAL_WALLET = 'Billetera Digital';
    const PAYMENT_METHOD_AGREEMENT = 'Convenio';
    const PAYMENT_METHOD_OTHER = 'Otro';
	protected $table = 'cashbox_movement';
	protected $primaryKey = 'movement_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'total_pen' => 'float',
		'itf_pen' => 'float',
		'port_pen' => 'float',
		'desgravamen_pen' => 'float',
		'interest_pen' => 'float',
		'cash_round_pen' => 'float',
		'cash_round_income_pen' => 'float',
		'real_pen' => 'float',
		'cash_pen' => 'float',
		'total_usd' => 'float',
		'itf_usd' => 'float',
		'port_usd' => 'float',
		'desgravamen_usd' => 'float',
		'interest_usd' => 'float',
		'cash_round_usd' => 'float',
		'cash_round_income_usd' => 'float',
		'real_usd' => 'float',
		'cash_usd' => 'float',
		'calculate_cash_round' => 'bool',
		'calculate_change' => 'bool',
		'cashbox_id' => 'int',
		'confirmed' => 'bool'
	];

	protected $fillable = [
		'total_pen',
		'itf_pen',
		'port_pen',
		'desgravamen_pen',
		'interest_pen',
		'cash_round_pen',
		'cash_round_income_pen',
		'real_pen',
		'cash_pen',
		'total_usd',
		'itf_usd',
		'port_usd',
		'desgravamen_usd',
		'interest_usd',
		'cash_round_usd',
		'cash_round_income_usd',
		'real_usd',
		'cash_usd',
		'type',
		'reference_number',
		'credit_card',
		'calculate_cash_round',
		'calculate_change',
		'cashbox_id',
		'confirmed'
	];

	public function cashbox()
	{
		return $this->belongsTo(Cashbox::class);
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}
}
