<?php

namespace App\Http\Resources\Administration;

use Illuminate\Http\Resources\Json\JsonResource;

class SimpleBusinessUnitResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->business_unit_id,
            'name' =>  $this->business_unit_name
        ];
    }
}
