<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ShoppingCartItem
 * 
 * @property int $shopping_cart_item_id
 * @property int $shopping_cart_id
 * @property int $product_id
 * @property float $equivalence
 * @property string $product_type
 * @property float $pres_quantity
 * @property float $unit_quantity
 * @property int $igv_affection
 * @property float $price
 * @property float $total
 * @property bool $item_number
 * 
 * @property Presentation $presentation
 * @property ShoppingCart $shopping_cart
 *
 * @package App\Models
 */
class ShoppingCartItem extends Model
{
	protected $table = 'shopping_cart_item';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'shopping_cart_item_id' => 'int',
		'shopping_cart_id' => 'int',
		'product_id' => 'int',
		'equivalence' => 'float',
		'pres_quantity' => 'float',
		'unit_quantity' => 'float',
		'igv_affection' => 'int',
		'price' => 'float',
		'total' => 'float',
		'item_number' => 'bool'
	];

	protected $fillable = [
		'shopping_cart_item_id',
		'product_type',
		'pres_quantity',
		'unit_quantity',
		'igv_affection',
		'price',
		'total',
		'item_number'
	];

	public function presentation()
	{
		return $this->belongsTo(Presentation::class, 'product_id')
					->where('presentation.product_id', '=', 'shopping_cart_item.product_id')
					->where('presentation.equivalence', '=', 'shopping_cart_item.equivalence');
	}

	public function shopping_cart()
	{
		return $this->belongsTo(ShoppingCart::class);
	}
}
