<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class EmailQueue
 *
 * @property int $email_queue_id
 * @property string $type
 * @property string $from_name
 * @property string $subject
 * @property string $message
 * @property string $state
 * @property Carbon|null $sent_date
 * @property string|null $response
 *
 * @package App\Models
 */
class EmailQueue extends Model
{
	protected $table = 'email_queue';
	protected $primaryKey = 'email_queue_id';
	public $timestamps = false;

    const OWNER = 'EmailQueue';
    const STATE_PENDING = 'Pendiente';
    const STATE_SUCCESS = 'Enviado';
    const RESPONSE_SUCCESS = 'success';
    public $reciver_emails = [];
    public $attachments = [];

	protected $dates = [
		'sent_date'
	];

    protected $fillable = [
        'type',
        'from_name',
        'subject',
        'message',
        'state',
        'sent_date',
        'response'
    ];

    public function emails() {
        return $this->hasMany(Email::class, 'owner_id', 'email_queue_id')
            ->where('owner', self::OWNER);
    }

    public function attachments() {
        return $this->hasMany(Resource::class, 'owner_id', 'email_queue_id')
            ->where('owner', self::OWNER);
    }

    public static function getEmailQueueWithEmails($limit = null) {
        $all_pending_emails = self::select('email_queue.*', 'email.email_address', 'email.email_type')
            ->join('email', function ($join) {
                $join->on('email.owner_id', '=', 'email_queue.email_queue_id')
                    ->where('email.owner', '=', self::OWNER)
                    ->where('email.order', '=', 1);
            })
            ->where('email_queue.state', self::STATE_PENDING);

        if ($limit != null) {
            $all_pending_emails->limit($limit);
        }

        $all_pending_emails = $all_pending_emails->get();

        foreach ($all_pending_emails as $email) {
            $email->reciver_emails = Email::where('owner', '=', self::OWNER)
                ->where('owner_id', '=', $email->email_queue_id)
                ->where('order', '!=', 1)
                ->get();
            $email->attachments = Resource::where('owner', "=", self::OWNER)
                ->where('owner_id', '=', $email->email_queue_id)
                ->get();
        }

        return $all_pending_emails;
    }

}
