<?php

namespace App\Models\Procedures;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class SpReportsSupplyStocks {

    public static function execute($mode = 'IR', $s_warehouseIds, $s_divisionIds = "", $s_lineIds = "", $s_sublineIds = "", $s_product_name = "", $s_productIds = "", $i_with_stock = 0, $s_domain = "", $s_sort = "", $s_order = "", $page, $pageSize) {
        try {
            //code...

            $a_params = [
                ':xmode' => $mode,
                ':xwarehouse_ids' => $s_warehouseIds,
                ':xdivision_ids' => $s_divisionIds,
                ':xline_ids' => $s_lineIds,
                ':xsubline_ids' => $s_sublineIds,
                ':xproduct_ids' => $s_productIds,
                ':xproduct_name' => $s_product_name,
                ':xwith_stock' => $i_with_stock,
                ':xdomain' => $s_domain,
                ':xsort' => $s_sort,
                ':xorder' => $s_order,
                ':xlimit' => 0,
                ':xoffset' => 0,
            ];

            $a_total_result = DB::select(
                "call sp_reports_supply_stocks(:xmode, :xwarehouse_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xproduct_name, :xwith_stock, :xdomain, :xsort, :xorder, :xlimit, :xoffset)",
                $a_params
            );

            $i_pageSize = intval($pageSize);
            $i_page = intval($page);
            $i_offset = ($i_page - 1) * $i_pageSize;
            $i_totalItems = count($a_total_result);

            $a_params[':xlimit'] = $i_pageSize;
            $a_params[':xoffset'] = $i_offset;

            $data = DB::select(
                "call sp_reports_supply_stocks(:xmode, :xwarehouse_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xproduct_name, :xwith_stock, :xdomain, :xsort, :xorder, :xlimit, :xoffset)",
                $a_params
            );

            return [
                'pagination' => [
                    'page' => $i_page,
                    'pageSize' => $i_pageSize,
                    'totalRecords' => $i_totalItems,
                    'totalPages' => ceil($i_totalItems / $i_pageSize)
                ],
                'data' => $data
            ];

        } catch (\Exception $ex) {
            Log::error($ex->getTraceAsString());
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }
}

