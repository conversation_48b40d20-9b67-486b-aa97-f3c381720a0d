<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Service
 *
 * @property int $product_id
 * @property int $service_category_id
 *
 * @property Product $product
 * @property ServiceCategory $serviceCategory
 *
 * @package App\Models
 */
class Service extends Model {
    protected $table = 'service';
    protected $primaryKey = 'product_id';
    public $incrementing = false;
    public $timestamps = false;

    protected $casts = [
        'product_id' => 'int'
    ];

    public function product() {
        return $this->belongsTo(Product::class);
    }

    public function serviceCategory() {
        return $this->belongsTo(ServiceCategory::class);
    }
}
