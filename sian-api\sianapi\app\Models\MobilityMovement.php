<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class MobilityMovement
 * 
 * @property int $movement_id
 * @property float $amount_pen
 * @property float $amount_usd
 * 
 * @property Movement $movement
 * @property Collection|MobilityPerson[] $mobility_people
 *
 * @package App\Models
 */
class MobilityMovement extends Model
{
	protected $table = 'mobility_movement';
	protected $primaryKey = 'movement_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'amount_pen' => 'float',
		'amount_usd' => 'float'
	];

	protected $fillable = [
		'amount_pen',
		'amount_usd'
	];

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function mobility_people()
	{
		return $this->hasMany(MobilityPerson::class, 'movement_id');
	}
}
