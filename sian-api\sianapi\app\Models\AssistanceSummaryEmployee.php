<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AssistanceSummaryEmployee
 * 
 * @property int $assistance_summary_employee_id
 * @property string $assistance_summary_employee_code
 * @property int $assistance_summary_id
 * @property int $person_id
 * @property int $work_days_calc
 * @property int $work_days_employee
 * @property int $absences
 * @property float $work_hours
 * @property float $extrahours1
 * @property float $extrahours2
 * @property float $night_hours
 * @property float $holiday_hours
 * @property int $lateness
 * 
 * @property AssistanceSummary $assistance_summary
 * @property Employee $employee
 * @property Collection|AssistanceSummaryDate[] $assistance_summary_dates
 * @property Collection|PayrollMovementHeader[] $payroll_movement_headers
 *
 * @package App\Models
 */
class AssistanceSummaryEmployee extends Model
{
	protected $table = 'assistance_summary_employee';
	protected $primaryKey = 'assistance_summary_employee_id';
	public $timestamps = false;

	protected $casts = [
		'assistance_summary_id' => 'int',
		'person_id' => 'int',
		'work_days_calc' => 'int',
		'work_days_employee' => 'int',
		'absences' => 'int',
		'work_hours' => 'float',
		'extrahours1' => 'float',
		'extrahours2' => 'float',
		'night_hours' => 'float',
		'holiday_hours' => 'float',
		'lateness' => 'int'
	];

	protected $fillable = [
		'assistance_summary_employee_code',
		'assistance_summary_id',
		'person_id',
		'work_days_calc',
		'work_days_employee',
		'absences',
		'work_hours',
		'extrahours1',
		'extrahours2',
		'night_hours',
		'holiday_hours',
		'lateness'
	];

	public function assistance_summary()
	{
		return $this->belongsTo(AssistanceSummary::class);
	}

	public function employee()
	{
		return $this->belongsTo(Employee::class, 'person_id');
	}

	public function assistance_summary_dates()
	{
		return $this->hasMany(AssistanceSummaryDate::class);
	}

	public function payroll_movement_headers()
	{
		return $this->hasMany(PayrollMovementHeader::class);
	}
}
