<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ApiUserVariant
 * 
 * @property int $api_user_variant_id
 * @property string $variant_code
 * @property bool $default
 * @property int|null $store_id
 * @property int|null $warehouse_id
 * @property int|null $cashbox_id
 * @property int|null $destination_cashbox_id
 * @property int|null $business_unit_id
 * @property int $api_user_id
 * @property string|null $default_price
 * @property int $order
 * @property bool $is_removable
 * @property int $admin_variant_count
 * @property int $cashbox_variant_count
 * 
 * @property ApiUser $api_user
 * @property BusinessUnit|null $business_unit
 * @property Cashbox|null $cashbox
 * @property Store|null $store
 * @property Warehouse|null $warehouse
 * @property Collection|ApiUserAdminVariant[] $api_user_admin_variants
 * @property Collection|ApiUserCashboxVariant[] $api_user_cashbox_variants
 * @property Collection|Movement[] $movements
 *
 * @package App\Models
 */
class ApiUserVariant extends Model
{
	protected $table = 'api_user_variant';
	protected $primaryKey = 'api_user_variant_id';
	public $timestamps = false;

	protected $casts = [
		'default' => 'bool',
		'store_id' => 'int',
		'warehouse_id' => 'int',
		'cashbox_id' => 'int',
		'destination_cashbox_id' => 'int',
		'business_unit_id' => 'int',
		'api_user_id' => 'int',
		'order' => 'int',
		'is_removable' => 'bool',
		'admin_variant_count' => 'int',
		'cashbox_variant_count' => 'int'
	];

	protected $fillable = [
		'variant_code',
		'default',
		'store_id',
		'warehouse_id',
		'cashbox_id',
		'destination_cashbox_id',
		'business_unit_id',
		'api_user_id',
		'default_price',
		'order',
		'is_removable',
		'admin_variant_count',
		'cashbox_variant_count'
	];

	public function api_user()
	{
		return $this->belongsTo(ApiUser::class);
	}

	public function business_unit()
	{
		return $this->belongsTo(BusinessUnit::class);
	}

	public function cashbox()
	{
		return $this->belongsTo(Cashbox::class, 'destination_cashbox_id');
	}

	public function store()
	{
		return $this->belongsTo(Store::class);
	}

	public function warehouse()
	{
		return $this->belongsTo(Warehouse::class);
	}

	public function api_user_admin_variants()
	{
		return $this->hasMany(ApiUserAdminVariant::class);
	}

	public function api_user_cashbox_variants()
	{
		return $this->hasMany(ApiUserCashboxVariant::class);
	}

	public function movements()
	{
		return $this->hasMany(Movement::class);
	}
}
