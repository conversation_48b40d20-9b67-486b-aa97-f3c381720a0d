<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Batch
 * 
 * @property int $batch_id
 * @property string $batch_code
 * @property int $product_id
 * @property Carbon $expiration_date
 * @property Carbon $register_date
 * @property int $user_register
 * 
 * @property Merchandise $merchandise
 * @property Person $person
 * @property Collection|BatchMovement[] $batch_movements
 *
 * @package App\Models
 */
class Batch extends Model
{
	protected $table = 'batch';
	protected $primaryKey = 'batch_id';
	public $timestamps = false;

	protected $casts = [
		'product_id' => 'int',
		'user_register' => 'int'
	];

	protected $dates = [
		'expiration_date',
		'register_date'
	];

	protected $fillable = [
		'batch_code',
		'product_id',
		'expiration_date',
		'register_date',
		'user_register'
	];

	public function merchandise()
	{
		return $this->belongsTo(Merchandise::class, 'product_id');
	}

	public function person()
	{
		return $this->belongsTo(Person::class, 'user_register');
	}

	public function batch_movements()
	{
		return $this->hasMany(BatchMovement::class);
	}
}
