<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ExecutionTime
 * 
 * @property int $execution_time_id
 * @property string $code
 * @property string $route
 * @property Carbon $datetime
 * @property string $request_type
 * @property bool $redirected
 * @property float $time
 * @property float|null $transaction
 *
 * @package App\Models
 */
class ExecutionTime extends Model
{
	protected $table = 'execution_time';
	protected $primaryKey = 'execution_time_id';
	public $timestamps = false;

	protected $casts = [
		'redirected' => 'bool',
		'time' => 'float',
		'transaction' => 'float'
	];

	protected $dates = [
		'datetime'
	];

	protected $fillable = [
		'code',
		'route',
		'datetime',
		'request_type',
		'redirected',
		'time',
		'transaction'
	];
}
