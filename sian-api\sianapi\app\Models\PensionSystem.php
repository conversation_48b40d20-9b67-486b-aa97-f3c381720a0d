<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PensionSystem
 * 
 * @property int $pension_system_id
 * @property int $person_id
 * @property string $pension_system_name
 * @property float $contribution
 * @property float $insurance_tax
 * @property float $var_comission
 * @property float $mix_comission
 * @property string $field
 * @property bool $status
 * @property bool $is_private
 * 
 * @property Person $person
 * @property Collection|Employee[] $employees
 * @property Collection|PayrollMovementHeader[] $payroll_movement_headers
 * @property Collection|PensionSystemCommission[] $pension_system_commissions
 *
 * @package App\Models
 */
class PensionSystem extends Model
{
	protected $table = 'pension_system';
	protected $primaryKey = 'pension_system_id';
	public $timestamps = false;

	protected $casts = [
		'person_id' => 'int',
		'contribution' => 'float',
		'insurance_tax' => 'float',
		'var_comission' => 'float',
		'mix_comission' => 'float',
		'status' => 'bool',
		'is_private' => 'bool'
	];

	protected $fillable = [
		'person_id',
		'pension_system_name',
		'contribution',
		'insurance_tax',
		'var_comission',
		'mix_comission',
		'field',
		'status',
		'is_private'
	];

	public function person()
	{
		return $this->belongsTo(Person::class);
	}

	public function employees()
	{
		return $this->hasMany(Employee::class);
	}

	public function payroll_movement_headers()
	{
		return $this->hasMany(PayrollMovementHeader::class);
	}

	public function pension_system_commissions()
	{
		return $this->hasMany(PensionSystemCommission::class);
	}
}
