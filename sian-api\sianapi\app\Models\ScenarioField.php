<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ScenarioField
 * 
 * @property int $scenario_field_id
 * @property string $type
 * @property string $field
 * @property string $label
 * @property float $value_in
 * @property float $value_out
 * @property int $order
 * @property bool $editable
 * 
 * @property Collection|Concept[] $concepts
 * @property Collection|OperationDynamic[] $operation_dynamics
 *
 * @package App\Models
 */
class ScenarioField extends Model
{
	protected $table = 'scenario_field';
	public $incrementing = false;
	public $timestamps = false;

	CONST FIELD_AMORTIZATION = 'amortization';
	CONST FIELD_REAL = 'real';

	protected $casts = [
		'scenario_field_id' => 'int',
		'value_in' => 'float',
		'value_out' => 'float',
		'order' => 'int',
		'editable' => 'bool'
	];

	protected $fillable = [
		'scenario_field_id',
		'label',
		'value_in',
		'value_out',
		'order',
		'editable'
	];

	public function concepts()
	{
		return $this->hasMany(Concept::class, 'type');
	}

	public function operation_dynamics()
	{
		return $this->hasMany(OperationDynamic::class, 'type');
	}
}
