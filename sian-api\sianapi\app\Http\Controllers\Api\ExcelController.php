<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use PhpOffice\PhpSpreadsheet\IOFactory;


class ExcelController extends Controller {
    /**
     *
     *
     * @return \Illuminate\Http\Response
     */

    public function processExcel(Request $request) {
        if (count($request->allFiles()) == 0) {
            return response()->json(['success' => false, 'error' => 'No hay archivos que subir'], 400);
        }

        $file = $request->file('file');


        $spreadsheet = IOFactory::load($file);

        $data = [];
        foreach ($spreadsheet->getWorksheetIterator() as $worksheet) {
            $sheetData = [];


            foreach ($worksheet->getRowIterator() as $row) {
                $rowData = [];
                foreach ($row->getCellIterator() as $cell) {
                    $cellValue = $cell->getValue();

                    if ($cell->getValue() instanceof \PhpOffice\PhpSpreadsheet\RichText\RichText) {
                        $cellValue = $cell->getValue()->getPlainText();
                    }

                    if (strpos($cellValue, '=') === 0) {
                        $calculatedValue = $worksheet->getCell($cell->getCoordinate())->getCalculatedValue();
                        $rowData[] = $calculatedValue;
                    } else {
                        $rowData[] = $cellValue;
                    }
                }
                $sheetData[] = $rowData;
            }
            $data[] = $sheetData;
        }


        $result = [];

        foreach ($data as $index_s => $sheet) {

            $headers = $sheet[0];

            foreach ($sheet as $index => $row) {
                if ($index === 0) {
                    continue;
                }

                $hasEmpty = true;
                $rowData = [];

                foreach ($headers as $key => $header) {
                    if ($header != "") {
                        if ($row[$key] !== null) {
                            $hasEmpty = false;
                        }

                        $cellValue = $row[$key];

                        if (str_contains(strtolower($header), 'fecha') && is_numeric($cellValue) && $cellValue >= 1 && $cellValue < 2958466) {
                            $cellValue = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($cellValue)->format('Y-m-d');
                        }
                        $rowData[$header] = $cellValue;
                    }
                }

                if (!$hasEmpty) {
                    $result[$index_s][] = $rowData;
                }
            }
        }
        return response()->json(['success' => true, 'data' => $result], 200);
    }
}
