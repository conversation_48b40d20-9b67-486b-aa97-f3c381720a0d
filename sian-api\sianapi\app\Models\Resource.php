<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Resource
 * 
 * @property string $owner
 * @property int $owner_id
 * @property string $type
 * @property int $resource_number
 * @property string $url
 * @property string|null $title
 * @property string|null $description
 * @property string|null $target_url
 * @property int|null $width
 * @property int|null $height
 * 
 *
 * @package App\Models
 */
class Resource extends Model
{
	protected $table = 'resource';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'owner_id' => 'int',
		'resource_number' => 'int',
		'width' => 'int',
		'height' => 'int'
	];

	protected $fillable = [
		'url',
		'title',
		'description',
		'target_url',
		'width',
		'height'
	];

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}
}
