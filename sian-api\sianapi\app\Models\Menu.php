<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Menu
 * 
 * @property int $menu_id
 * @property string $shortname
 * @property string $menu_title
 * @property int $order
 * @property string $module
 * @property string $controller
 * @property string $action
 * @property string $parent_menu_id
 * @property string $path
 * 
 * @property Action $action
 *
 * @package App\Models
 */
class Menu extends Model
{
	protected $table = 'menu';
	public $incrementing = false;
	public $timestamps = false;

    protected $casts = [
		'menu_id' => 'int',
		'order' => 'int'
	];

	public function action()
	{
		return $this->belongsTo(Menu::class, 'menu_parent')
					->where('menu.parent_menu_id', '=', 'actionchild.module_parent')
					->where('action.controller', '=', 'actionchild.controller_parent')
					->where('action.action', '=', 'actionchild.action_parent')
					->where('action.owner', '=', 'actionchild.owner_parent');
	}
}
