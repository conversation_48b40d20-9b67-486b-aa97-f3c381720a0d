<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Owner
 *
 * @property string $owner
 * @property string $owner_name
 * @property string|null $module
 * @property string|null $controller
 * @property string|null $action
 *
 * @property Collection|Account[] $accounts
 * @property Collection|Action[] $actions
 * @property Collection|Actionassignment[] $actionassignments
 * @property Collection|Address[] $addresses
 * @property Collection|BankAccount[] $bank_accounts
 * @property Collection|Changelog[] $changelogs
 * @property Collection|Email[] $emails
 * @property Collection|Entry[] $entries
 * @property Collection|EntryGroup[] $entry_groups
 * @property Collection|Hit[] $hits
 * @property Collection|Inventory[] $inventories
 * @property Collection|Item[] $items
 * @property Collection|Keyword[] $keywords
 * @property Collection|OwnerGroup[] $owner_groups
 * @property Collection|OwnerName[] $owner_names
 * @property Collection|OwnerPair[] $owner_pairs
 * @property Collection|Phone[] $phones
 * @property Collection|ProductList[] $product_lists
 * @property Collection|RawPrinter[] $raw_printers
 * @property Collection|Resource[] $resources
 * @property Collection|Skill[] $skills
 * @property Collection|TransactionDetail[] $transaction_details
 *
 * @package App\Models
 */
class Owner extends Model
{
	protected $table = 'owner';
	protected $primaryKey = 'owner';
	public $incrementing = false;
	public $timestamps = false;

    const OWNER_MOVEMENT = 'Movement';

	protected $fillable = [
		'owner_name',
		'module',
		'controller',
		'action'
	];

	public function action()
	{
		return $this->belongsTo(Action::class, 'module')
					->where('action.module', '=', 'owner.module')
					->where('action.controller', '=', 'owner.controller')
					->where('action.action', '=', 'owner.action');
	}

	public function accounts()
	{
		return $this->hasMany(Account::class, 'owner');
	}

	public function actions()
	{
		return $this->hasMany(Action::class, 'owner');
	}

	public function actionassignments()
	{
		return $this->hasMany(Actionassignment::class, 'owner');
	}

	public function addresses()
	{
		return $this->hasMany(Address::class, 'owner');
	}

	public function bank_accounts()
	{
		return $this->hasMany(BankAccount::class, 'owner');
	}

	public function changelogs()
	{
		return $this->hasMany(Changelog::class, 'owner');
	}

	public function emails()
	{
		return $this->hasMany(Email::class, 'owner');
	}

	public function entries()
	{
		return $this->hasMany(Entry::class, 'owner');
	}

	public function entry_groups()
	{
		return $this->hasMany(EntryGroup::class, 'owner');
	}

	public function hits()
	{
		return $this->hasMany(Hit::class, 'owner');
	}

	public function inventories()
	{
		return $this->hasMany(Inventory::class, 'owner');
	}

	public function items()
	{
		return $this->hasMany(Item::class, 'owner');
	}

	public function keywords()
	{
		return $this->hasMany(Keyword::class, 'owner');
	}

	public function owner_groups()
	{
		return $this->hasMany(OwnerGroup::class, 'owner');
	}

	public function owner_names()
	{
		return $this->hasMany(OwnerName::class, 'owner');
	}

	public function owner_pairs()
	{
		return $this->hasMany(OwnerPair::class, 'owner');
	}

	public function phones()
	{
		return $this->hasMany(Phone::class, 'owner');
	}

	public function product_lists()
	{
		return $this->hasMany(ProductList::class, 'owner');
	}

	public function raw_printers()
	{
		return $this->hasMany(RawPrinter::class, 'owner');
	}

	public function resources()
	{
		return $this->hasMany(Resource::class, 'owner');
	}

	public function skills()
	{
		return $this->hasMany(Skill::class, 'owner');
	}

	public function transaction_details()
	{
		return $this->hasMany(TransactionDetail::class, 'owner');
	}
}
