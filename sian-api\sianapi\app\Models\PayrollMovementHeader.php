<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PayrollMovementHeader
 * 
 * @property int $movement_id
 * @property int $person_id
 * @property int|null $assistance_summary_employee_id
 * @property int|null $pension_system_id
 * @property string|null $type_commision
 * @property float $salary
 * @property int $children
 * @property int|null $age
 * @property int|null $human_category_id
 * @property int $combination_id
 * @property int $work_days_calc
 * @property int $work_days_employee
 * @property int $absences
 * @property float $work_hours
 * @property float $extrahours1
 * @property float $extrahours2
 * @property float $night_hours
 * @property float $holiday_hours
 * @property int $lateness
 * 
 * @property HumanCategory|null $human_category
 * @property AssistanceSummaryEmployee|null $assistance_summary_employee
 * @property Combination $combination
 * @property Employee $employee
 * @property PayrollMovement $payroll_movement
 * @property PensionSystem|null $pension_system
 * @property Collection|PayrollMovementDetail[] $payroll_movement_details
 *
 * @package App\Models
 */
class PayrollMovementHeader extends Model
{
	protected $table = 'payroll_movement_header';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'person_id' => 'int',
		'assistance_summary_employee_id' => 'int',
		'pension_system_id' => 'int',
		'salary' => 'float',
		'children' => 'int',
		'age' => 'int',
		'human_category_id' => 'int',
		'combination_id' => 'int',
		'work_days_calc' => 'int',
		'work_days_employee' => 'int',
		'absences' => 'int',
		'work_hours' => 'float',
		'extrahours1' => 'float',
		'extrahours2' => 'float',
		'night_hours' => 'float',
		'holiday_hours' => 'float',
		'lateness' => 'int'
	];

	protected $fillable = [
		'assistance_summary_employee_id',
		'pension_system_id',
		'type_commision',
		'salary',
		'children',
		'age',
		'human_category_id',
		'combination_id',
		'work_days_calc',
		'work_days_employee',
		'absences',
		'work_hours',
		'extrahours1',
		'extrahours2',
		'night_hours',
		'holiday_hours',
		'lateness'
	];

	public function human_category()
	{
		return $this->belongsTo(HumanCategory::class);
	}

	public function assistance_summary_employee()
	{
		return $this->belongsTo(AssistanceSummaryEmployee::class);
	}

	public function combination()
	{
		return $this->belongsTo(Combination::class);
	}

	public function employee()
	{
		return $this->belongsTo(Employee::class, 'person_id');
	}

	public function payroll_movement()
	{
		return $this->belongsTo(PayrollMovement::class, 'movement_id');
	}

	public function pension_system()
	{
		return $this->belongsTo(PensionSystem::class);
	}

	public function payroll_movement_details()
	{
		return $this->hasMany(PayrollMovementDetail::class, 'movement_id');
	}
}
