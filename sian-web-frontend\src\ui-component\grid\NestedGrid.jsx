import React, { useState } from 'react';
import MUIDataTable from 'mui-datatables';
import { makeStyles } from '@mui/styles';
import CustomOptionsMUIDtb from 'assets/customization/mui-datatable/CustomOptionsMUIDtb';
import useGridColumn from 'hooks/useGridColumn';

const defaultOptions = {
    search: true,
    download: false,
    print: false,
    sort: true,
    viewColumns: true,
    filter: true,
    filterType: 'multiselect',
    responsive: 'vertical',
    fixedHeader: true,
    fixedSelectColumn: true,
    textLabels: CustomOptionsMUIDtb.textLabels,
    jumpToPage: true,
    resizableColumns: false,
    draggableColumns: {
        enabled: true
    },
    selectableRows: 'none',
    selectableRowsOnClick: false,
    pagination: false,
    confirmFilters: false,
    rowHover: true,
    toolbar: false,
    setTableProps: () => ({
        size: 'small'
    })
};

const useTableStyles = makeStyles({
    tableRoot: {
        border: 'none',
        boxShadow: 'none',
        backgroundColor: 'transparent'
    },
    paper: {
        backgroundColor: 'transparent !important'
    },
    table: {
        backgroundColor: 'transparent'
    }
});

export default function NestedGrid({
    columns = [],
    data = [],
    options = defaultOptions,
    onSortChange = null,
    RenderNestedContent,
    title = ''
}) {
    const tableClasses = useTableStyles();
    const { gridColumns, handleColumnVisibilityChange } = useGridColumn(columns);
    const [expandedRows, setExpandedRows] = useState([]);

    const nestedOptions = {
        ...options,
        expandableRows: !!RenderNestedContent,
        rowsExpanded: expandedRows,
        ...(RenderNestedContent && {
            renderExpandableRow: (rowData) => (
                <tr>
                    <td colSpan={gridColumns.length + 1}>
                        <RenderNestedContent row={rowData} />
                    </td>
                </tr>
            )
        }),
        onColumnViewChange: (changedColumn, action) => {
            handleColumnVisibilityChange(changedColumn, action === 'add');
        },
        onTableChange: (action, tableState) => {
            if (action === 'sort' && onSortChange) {
                const { name, direction } = tableState.sortOrder;
                onSortChange(name, direction);
            }
        },
        onRowExpansionChange: (curExpanded, allExpanded) => setExpandedRows(allExpanded.map((row) => row.dataIndex))
    };

    return (
        <MUIDataTable classes={{ root: tableClasses.tableRoot }} columns={gridColumns} data={data} options={nestedOptions} title={title} />
    );
}
