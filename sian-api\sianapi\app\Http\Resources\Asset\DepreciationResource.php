<?php

namespace App\Http\Resources\Asset;

use App\Http\Resources\Administration\SimpleBusinessUnitResource;
use App\Http\Resources\Common\SimpleMovementResource;
use App\Models\FixedAsset;
use Illuminate\Http\Resources\Json\JsonResource;

class DepreciationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->depreciation_id,
            'fixedAsset' => new SimpleFixedAssetResource($this->fixedAsset),
            'businesUnit' => new SimpleBusinessUnitResource($this->businessUnit),
            'year' => $this->year,
            'period' => $this->period,
            'rate' =>$this->rate,
            'initialValue' => $this->initialValue,
            'historicalDepreciation' => $this->historical_depreciation,
            'annualDepreciation' => $this->annual_depreciation,
            'periodDepreciation' => $this->period_depreciation,
            'totalDepreciation' => $this->totalDepreciation,
            'finalValue' => $this->final_value,
            'locked' => $this->locked,
            'movement' => new SimpleMovementResource($this->movement)
        ];
    }
}
