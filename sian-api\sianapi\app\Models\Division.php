<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Division
 *
 * @property int $division_id
 * @property string $division_name
 * @property string $alias
 * @property string $description
 * @property bool $status
 * @property bool $frontend
 * @property bool $outstanding
 * @property bool $web_enabled
 * @property int $web_order
 * @property string|null $transaction_code
 *
 * @property Collection|Line[] $lines
 *
 * @package App\Models
 */
class Division extends Model
{
    const DIVISION_FOOD_NAME = 'FOOD';
	protected $table = 'division';
	protected $primaryKey = 'division_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'frontend' => 'bool',
		'outstanding' => 'bool',
		'web_enabled' => 'bool',
		'web_order' => 'int'
	];

	protected $fillable = [
		'division_name',
		'alias',
		'description',
		'status',
		'frontend',
		'outstanding',
		'web_enabled',
		'web_order',
		'transaction_code'
	];

	public function lines()
	{
		return $this->hasMany(Line::class);
	}
}
