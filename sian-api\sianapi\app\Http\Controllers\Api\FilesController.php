<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class FilesController extends Controller {


    /**
     *
     *
     * @return \Illuminate\Http\Response
     */


    public function index() {

        $results = self::getFileRules();

        $response = [
            'success' => true,
            'data' => $results
        ];

        return response()->json($response, 200);
    }

    public static function getFileRules() {
        return DB::table('global_var as GV')->select('GV.max_attachments as maxAttachments', 'GV.max_attachment_size as maxAttachmentSize')->first();
    }

}
