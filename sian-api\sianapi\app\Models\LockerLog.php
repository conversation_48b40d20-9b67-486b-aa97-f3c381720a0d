<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class LockerLog
 * 
 * @property int $locker_log_id
 * @property string $log_code
 * @property Carbon $log_date
 * @property string $code
 * @property string|null $description
 * @property bool $is_error
 * @property string $owner
 * @property int $owner_id
 * @property string|null $image_url
 * @property string|null $screenshot_url
 * @property int|null $locker_access_id
 * 
 * @property LockerAccess|null $locker_access
 *
 * @package App\Models
 */
class LockerLog extends Model
{
	protected $table = 'locker_log';
	protected $primaryKey = 'locker_log_id';
	public $timestamps = false;

	protected $casts = [
		'is_error' => 'bool',
		'owner_id' => 'int',
		'locker_access_id' => 'int'
	];

	protected $dates = [
		'log_date'
	];

	protected $fillable = [
		'log_code',
		'log_date',
		'code',
		'description',
		'is_error',
		'owner',
		'owner_id',
		'image_url',
		'screenshot_url',
		'locker_access_id'
	];

	public function locker_access()
	{
		return $this->belongsTo(LockerAccess::class);
	}
}
