<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class KardexRange
 * 
 * @property int $product_id
 * @property int $warehouse_id
 * @property Carbon $begin_date
 * @property Carbon $end_date
 * @property int $kardex_id
 * 
 * @property Kardex $kardex
 * @property Merchandise $merchandise
 * @property Warehouse $warehouse
 *
 * @package App\Models
 */
class KardexRange extends Model
{
	protected $table = 'kardex_range';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'product_id' => 'int',
		'warehouse_id' => 'int',
		'kardex_id' => 'int'
	];

	protected $dates = [
		'begin_date',
		'end_date'
	];

	protected $fillable = [
		'kardex_id'
	];

	public function kardex()
	{
		return $this->belongsTo(Kardex::class);
	}

	public function merchandise()
	{
		return $this->belongsTo(Merchandise::class, 'product_id');
	}

	public function warehouse()
	{
		return $this->belongsTo(Warehouse::class);
	}
}
