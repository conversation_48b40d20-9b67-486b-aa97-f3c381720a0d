<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Organization
 *
 * @property int $organization_id
 * @property string $organization_code
 * @property string|null $corporation_code
 * @property string $name
 * @property string $slogan
 * @property string $about
 * @property string|null $history
 * @property string $mision
 * @property string $vision
 * @property string|null $quality_policy
 * @property string $quality_policy_v
 * @property string $moral
 * @property string|null $web
 * @property string|null $web_name
 * @property int $person_id
 * @property string|null $locker_termsconditions
 * @property string|null $sale_conditions
 *
 * @property Person $person
 * @property GlobalVar $global_var
 *
 * @package App\Models
 */
class Organization extends Model
{
	protected $table = 'organization';
	protected $primaryKey = 'organization_id';
	public $timestamps = false;

	protected $casts = [
		'person_id' => 'int'
	];

	protected $fillable = [
		'organization_code',
		'corporation_code',
		'name',
		'slogan',
		'about',
		'history',
		'mision',
		'vision',
		'quality_policy',
		'quality_policy_v',
		'moral',
		'web',
		'web_name',
		'person_id',
		'locker_termsconditions',
		'sale_conditions'
	];

	public function person()
	{
		return $this->belongsTo(Person::class);
	}

	public function global_var()
	{
		return $this->hasOne(GlobalVar::class);
	}
}
