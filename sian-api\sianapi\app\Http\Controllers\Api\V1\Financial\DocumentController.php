<?php

namespace App\Http\Controllers\Api\V1\Financial;

use App\Models\Account;
use App\Models\CashboxMovement;
use App\Models\ExchangeRate;
use App\Models\Fake\Currency;
use App\Models\GlobalVar;
use App\Models\Owner;
use App\Models\OwnerPair;
use App\Models\PaymentScheduleDate;
use App\Models\ProductList;
use App\Models\PurchaseOrder;
use App\Models\Scenario;
use App\Models\User;
use App\Models\Procedures\SpGetPairedOwners;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\BankAccount;
use App\Models\EntryGroup;
use App\Models\ScenarioField;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;


class DocumentController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    public function index(Request $request)
    {

        $validate = Validator::make($request->all(), [
            "page" => "required|integer",
            "pageSize" => "required|integer",
            "documentOrigin" => "required|string",
            "startDateEmision" => "date",
            "endDateEmision" => "date|after_or_equal:startDateEmision|required_with:startDateEmision",
            "startDateExpiration" => "date",
            "endDateExpiration" => "date|after_or_equal:startDateExpiration|required_with:startDateExpiration",
            "ruc" => "sometimes|integer|string",
            "documentNumber" => "sometimes | string",
            "documentType" => "sometimes|string",
            "documentDetail" => "sometimes | string"
        ]);


        if ($validate->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validate->errors()
            ], 400);
        }

        $documentOrigin = $request->query('documentOrigin');

        switch ($documentOrigin) {
            case 'EG':
                return $this->getAccountingEntryDocuments($request);
            case 'M':
                return $this->getMovementDocuments($request);
            default:
                return $this->getAccountingEntryDocuments($request);
        }
    }

    private function getAccountingEntryDocuments(Request $request) {

        try {

            $limit_retention_amount_pen = GlobalVar::getValue('no_retention_max_amount_pen');
            $limit_retention_amount_usd = ExchangeRate::convertToDollars($limit_retention_amount_pen);

            $page = intval($request->query('page'));
            $pageSize = intval($request->query('pageSize'));
            $startIndex = ($page - 1) * $pageSize;

            $documentsWithPrePayProgramed = DB::table('commercial_movement as CM')
                ->join('movement as M', 'M.movement_id', '=', 'CM.movement_id')
                ->join('movement_link as ML', 'ML.movement_id', '=', 'CM.movement_id')
                ->join('movement as MP', 'MP.movement_id', '=', 'ML.movement_parent_id')
                ->join('commercial_movement as PMP', 'PMP.movement_id', '=', 'MP.movement_id')
                ->join('payment_schedule_detail as PD', 'PD.movement_id', '=', 'MP.movement_id')
                ->join('payment_schedule_date as PSD', 'PSD.payment_schedule_detail_id', '=', 'PD.payment_schedule_detail_id')
                ->where('M.status', 1)
                ->where('MP.status', 1)
                ->where('MP.route', PurchaseOrder::ROUTE)
                ->where('PMP.advance_balance', '>', 0)
                ->where('PSD.state', PaymentScheduleDate::STATE_OPEN)
                ->pluck('CM.movement_id');

            $query = DB::table('entry_group as EG')
                ->join('movement as M', 'M.movement_id', '=', 'EG.movement_id')
                ->join('ability as AB', 'AB.movement_id', '=', 'M.movement_id')
                ->join('advanced_option as AO', function ($join) {
                    $join->on('AO.movement_id', '=', 'M.movement_id')
                        ->where('AO.lock_payment', '=', 0);
                })
                ->join('document as D', 'D.document_code', '=', 'M.document_code')
                ->join('operation as O', 'O.operation_code', '=', 'M.operation_code')
                ->join('scenario as S', 'S.route', '=', 'EG.route')
                ->join('person as XP', 'XP.person_id', '=', 'EG.owner_id')
                ->join('person as PER', 'PER.person_id', '=', 'M.person_id')
                ->join('store as ST', 'ST.store_id', '=', 'M.store_id')
                ->join('business_unit as BU', 'BU.business_unit_id', '=', 'M.business_unit_id')
                ->leftJoin('project as P', 'P.project_id', '=', 'M.project_id')
                ->leftJoin('movement_link as ML', function ($join) {
                    $join->on('ML.movement_id', '=', 'M.movement_id')
                        ->where('ML.status', '=', 1)
                        ->where('ML.parent_route', '=', 'logistic/purchaseOrder');
                })
                ->leftJoin('movement_link as ML2', function ($join) {
                    $join->on('ML2.movement_id', '=', 'ML.movement_parent_id')
                        ->where('ML2.status', '=', 1)
                        ->where('ML2.parent_route', '=', 'logistic/prePurchaseOrder');
                })
                ->leftJoin('movement as MP', 'MP.movement_id', '=', 'ML2.movement_parent_id')
                ->leftJoin('person as RXP', 'RXP.person_id', '=', 'MP.aux_person_id')
                ->leftJoin('commercial_movement as CM', 'CM.movement_id', '=', 'M.movement_id')
                ->leftJoin('commercial_movement as PCM', 'PCM.movement_id', '=', 'ML.movement_parent_id')
                ->leftJoin('entry as E', 'E.entry_id', '=', 'EG.pk')
                ->whereIn('EG.route', [
                    'accounting/fecline',
                    'accounting/leasing',
                    'accounting/loan',
                    'accounting/manual',
                    'accounting/operatingCost',
                    'commercial/creditNote1',
                    'commercial/creditNote2',
                    'commercial/creditNote3',
                    'financial/providerLetter',
                    'human/payroll',
                    'logistic/debitNote',
                    'logistic/mobility',
                    'logistic/operatingCost',
                    'logistic/purchaseBill',
                    'logistic/advancePurchaseBill',
                    'logistic/purcharseOrder',
                    'treasury/preCollect',
                ])
                ->where('M.status', '=', 1)
                ->where('EG.COLUMN', '=', 'Haber')
                ->where('EG.to_do', true)
                ->where('EG.is_paid', '=', 0)
                ->where('EG.balance', '>', 0)
                // ->where('EG.checked', '=', 1)
                ->where(function ($query) {
                    $query->where(function ($query) {
                        $query->where('EG.is_payable', '=', 1)
                            ->where('EG.checked', '=', 1);
                    })
                        ->orWhere(function ($query) {
                            $query->where(function ($query) {
                                $query->where('EG.is_payable', '=', 0)
                                    ->where('EG.checked', '=', 0);
                            })->where(function ($query) {
                                $query->where('AB.is_dispatched', '=', 1)
                                    ->orWhere('AO.allow_pay', '=', 1);
                            });
                        });
                })
                ->whereNotIn('M.document_code', [
                    'VCSS',
                    'VC',
                    'SYS',
                    'RSCS',
                    'RSC',
                    'RICS',
                    'RIC',
                    'PLAS',
                    'OGMS',
                    'FNC1',
                    'OGM',
                    'MMCS',
                    'MMC',
                    'BNC1'
                ])
                ->whereNotIn('M.movement_id', $documentsWithPrePayProgramed)
                ->where(function ($query) {
                    $query->where(function ($query) {
                        $query->whereIn('CM.payment_method', [
                            CashboxMovement::PAYMENT_METHOD_BANK_CHECK,
                            CashboxMovement::PAYMENT_METHOD_BANK_DEPOSIT,
                            CashboxMovement::PAYMENT_METHOD_OTHER
                        ]);
                    })
                        ->orWhere(function ($query) {
                            $query->where('CM.movement_id', '=', NULL);
                        });
                });


            if ($request->has('startDateEmision') && $request->has('endDateEmision')) {
                $query->whereDate('EG.emission_date', '>=', $request->input('startDateEmision'));
                $query->whereDate('EG.emission_date', '<=', $request->input('endDateEmision'));
            }

            if ($request->has('startDateExpiration') && $request->has('endDateExpiration')) {
                $query->whereDate('EG.expiration_date', '>=', $request->input('startDateExpiration'));
                $query->whereDate('EG.expiration_date', '<=', $request->input('endDateExpiration'));
            }

            if ($request->has('ruc')) {
                $query->where('XP.identification_number', 'LIKE', '%' . $request->input('ruc') . '%');
            }

            if ($request->has('documentNumber')) {
                $query->where(DB::raw("CONCAT(M.document_code, '-', M.document_serie, '-', CAST(M.document_correlative AS INTEGER))"), 'LIKE', '%' . $request->input('documentNumber') . '%');
            }

            if ($request->has('documentType')) {
                $statusString = $request->input('documentType');
                $statusArray = explode(',', $statusString);
                $query->whereIn('M.document_code', $statusArray);
            }

            if ($request->has('documentDetail')) {
                $detailString = $request->input('documentDetail');
                $detailArray = explode(',', $detailString);
                $query->whereIn('EG.detail', $detailArray);
            }

            if ($request->has('documentType')) {
                $statusString = $request->input('documentType');
                $statusArray = explode(',', $statusString);
                $query->whereIn('M.document_code', $statusArray);
            }

            if ($request->has('requestPaymentPersons')) {
                $requestingUserSelected = $request->input('requestPaymentPersons');
                $arrayRequestingUser = explode(",", $requestingUserSelected);
                $query->whereIn(DB::raw("COALESCE(RXP.person_id, PER.person_id)"), $arrayRequestingUser);
            }

            if ($request->has('businessPartner')) {
                $bussinessPartnerSelected = $request->input('businessPartner');
                $arrayBussinessPartner = explode(",", $bussinessPartnerSelected);
                $query->whereIn("XP.person_id", $arrayBussinessPartner);
            }

            if ($request->has('businessUnit')) {
                $bussinessUnitSelected = $request->input('businessUnit');
                $arrayBussinessUnit = explode(",", $bussinessUnitSelected);
                $query->whereIn("BU.business_unit_id", $arrayBussinessUnit);
            }

            if ($request->has('store')) {
                $storeSelected = $request->input('store');
                $arrayStore = explode(",", $storeSelected);
                $query->whereIn("ST.store_id", $arrayStore);
            }

            if ($request->has('project')) {
                $projectSelected = $request->input('project');
                $arrayProject = explode(",", $projectSelected);
                $query->whereIn("P.project_id", $arrayProject);
            }

            if ($request->has('showDetraction')) {
                $valueShowDetraction = $request->input('showDetraction');
                if ($valueShowDetraction === 'true') {
                    $query->where("E.field", '=', 'det');
                } else {
                    $query->where("E.field", '!=', 'det');
                }
            }

            $totalItems = $query->count();

            $results = $query->select(
                DB::raw("CONCAT(EG.entry_group_id, '-EG') AS pk"),
                'EG.entry_group_id as entryGroupId',
                'EG.movement_id as movementId',
                "EG.account_code as accountCode",
                'EG.detail as detail',
                'ST.store_name AS store',
                'BU.business_unit_name AS businessUnit',
                'P.short_name AS project',
                DB::raw("CONCAT(M.document_code, '-', M.document_serie, '-', CAST(M.document_correlative AS INTEGER)) AS document"),
                'D.document_name as documentType',
                'M.document_code as documentCode',
                'M.document_serie as documentSerie',
                DB::raw("CAST(M.document_correlative AS INTEGER) as documentCorrelative"),
                'S.title',
                'XP.identification_number as identificationNumber',
                DB::raw("REPLACE(XP.person_name, ',', ' ') AS auxPersonName"),
                'EG.emission_date as emissionDate',
                'EG.expiration_date as expirationDate',
                'EG.CONDITION AS paymentCondition',
                'EG.days_overdue as daysOverdue',
                DB::raw('DATEDIFF(EG.expiration_date, CURDATE()) AS daysRemaining'),
                'M.currency AS currency',
                'EG.total',
                'EG.balance',
                'EG.balance_percent as balancePercent',
                'EG.is_paid as isPaid',
                'EG.is_payable as isPayable',
                'EG.checked as isProvisioned',
                DB::raw("(E.field = 'det') AS isDetraction"),
                'D.sunat_code as sunatCode',
                DB::raw("'EG' as documentOrigin"),
                'M.route as route',
                DB::raw("REPLACE(COALESCE(RXP.person_name, PER.person_name), ',', ' ') AS personName"),
                DB::raw("IF(CM.movement_id IS NULL, '" . CashboxMovement::PAYMENT_METHOD_OTHER . "', COALESCE(CM.payment_method, '" . CashboxMovement::PAYMENT_METHOD_OTHER . "')) as paymentMethod"),
                DB::raw(value: "(CM.det_pen > 0) as hasDetraction"),
                DB::raw("IF(M.currency = '" . Currency::PEN . "', (EG.total > " . $limit_retention_amount_pen . "),(EG.total > " . $limit_retention_amount_usd . ")) as exceedsRetentionAmount"),
                "PCM.movement_id as parentMovement_id",
                DB::raw("IF(PCM.movement_id IS NOT NULL,
                    IF(MP.currency = '" . Currency::PEN . "',
                        PCM.total_pen > " . $limit_retention_amount_pen . ",
                        PCM.total_usd > " . $limit_retention_amount_usd . "
                    ),
                    0
                ) AS parentExceedsRetentionAmount"),
                "XP.retention as bpIsRetentionAgent",
                "XP.perception as bpIsPerceptionAgent",
                "XP.good_contributor as goodContributor",
                DB::raw('(CM.igv_pen = 0) as no_igv'),
                "M.custom_category",
                "O.require_conformity as requireConformity",
                "AB.is_dispatched",
                "CM.merchandise_count",
                "CM.service_count",
            )
                ->orderBy('M.movement_id', 'desc')
                // ->orderByRaw("CASE WHEN EG.expiration_date BETWEEN DATE_SUB(CURDATE(), INTERVAL 3 MONTH) AND CURDATE() THEN 1 WHEN EG.expiration_date = CURDATE() THEN 2 WHEN EG.expiration_date < DATE_SUB(CURDATE(), INTERVAL 3 MONTH) THEN 4 ELSE 3 END, EG.expiration_date ASC")
                ->offset($startIndex)->limit($pageSize)->get();

            foreach ($results as $result) {
                $paymentScheduleId = DB::table('payment_schedule_date as PSDT')
                    ->join('payment_schedule_detail as PSD', 'PSD.payment_schedule_detail_id', '=', 'PSDT.payment_schedule_detail_id')
                    ->where('PSDT.state', '=', PaymentScheduleDate::STATE_OPEN)
                    ->where('PSD.entry_group_id', '=', $result->entryGroupId)
                    ->select('PSD.payment_schedule_id as paymentScheduleID')
                    ->first();
                $result->paymentScheduleID = $paymentScheduleId ? $paymentScheduleId->paymentScheduleID : null;
            }

            $response = [
                'success' => true,
                'pagination' => [
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'totalRecords' => $totalItems,
                    'totalPages' => ceil($totalItems / $pageSize)
                ],
                'data' => $results
            ];

            return response()->json($response);
        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }

    private function getMovementDocuments(Request $request)
    {
        $limit_retention_amount_pen = GlobalVar::getValue('no_retention_max_amount_pen');
        $limit_retention_amount_usd = ExchangeRate::convertToDollars($limit_retention_amount_pen);

        try {
            $page = intval($request->query('page'));
            $pageSize = intval($request->query('pageSize'));
            $startIndex = ($page - 1) * $pageSize;

            $query = DB::table('movement as M')
                ->join('ability as AB', 'AB.movement_id', '=', 'M.movement_id')
                ->join('advanced_option as AO', function ($join) {
                    $join->on('AO.movement_id', '=', 'M.movement_id')
                        ->where('AO.lock_payment', '=', 0);
                })
                ->join('document as D', 'D.document_code', '=', 'M.document_code')
                ->join('scenario as S', 'S.route', '=', 'M.route')
                ->join('person as XP', 'XP.person_id', '=', 'M.aux_person_id')
                ->join('person as PER', 'PER.person_id', '=', 'M.person_id')
                ->join('store as ST', 'ST.store_id', '=', 'M.store_id')
                ->join('business_unit as BU', 'BU.business_unit_id', '=', 'M.business_unit_id')
                ->leftJoin('project as P', 'P.project_id', '=', 'M.project_id')
                ->leftJoin('movement_link as ML', function ($join) {
                    $join->on('ML.movement_id', '=', 'M.movement_id')
                        ->where('ML.status', '=', 1)
                        ->where('ML.parent_route', '=', 'logistic/prePurchaseOrder');
                })
                ->leftJoin('movement_link as CML', function ($join) {
                    $join->on('CML.movement_parent_id', '=', 'M.movement_id')
                        ->where('CML.parent_route', '=', 'logistic/purchaseOrder')
                        ->where('CML.route', '=', 'logistic/purchaseBill')
                        ->where('CML.status', '=', 1);
                })
                ->leftJoin('movement as MP', 'MP.movement_id', '=', 'ML.movement_parent_id')
                ->leftJoin('person as RXP', 'RXP.person_id', '=', 'MP.aux_person_id')
                ->leftJoin('commercial_movement as CM', 'CM.movement_id', '=', 'M.movement_id')
                ->leftJoin('product_list AS PL', function ($join) {
                    $join->on('PL.owner_id', '=', 'M.movement_id')
                        ->where('PL.owner', '=', Owner::OWNER_MOVEMENT);
                })
                ->where('M.status', "=", 1)
                ->whereIn('M.route', [
                    'logistic/purchaseOrder',
                    'treasury/cashOutOrder'
                ])
                ->whereNull('CML.movement_id')
                ->where(function ($query) {
                    $query->where(function ($query) {
                        $query->where('M.route', 'logistic/purchaseOrder')
                            ->where('CM.advance_balance', '>', 0)
                            ->where('CM.advance_amount', '>', 0)
                            ->where('AB.is_upgraded', '=', 0)
                            ->where('AB.upgrade_confirmed', '=', '1');
                    })
                        ->orWhere(function ($query) {
                            $query->where('M.route', 'treasury/cashOutOrder')
                                ->where('PL.balance_pen', '>', 0)
                                ->where('PL.balance_usd', '>', 0)
                                ->where('AB.pay_confirmed', '=', '1');
                        });
                })
                ->where(function ($query) {
                    $query->where(function ($query) {
                        $query->whereNotNull('CM.movement_id')
                            ->whereIn('CM.payment_method', [
                                CashboxMovement::PAYMENT_METHOD_BANK_CHECK,
                                CashboxMovement::PAYMENT_METHOD_BANK_DEPOSIT,
                                CashboxMovement::PAYMENT_METHOD_OTHER
                            ]);
                    })
                        ->orWhere(function ($query) {
                            $query->whereNull('CM.movement_id');
                        });
                });

            if ($request->has('startDateEmision') && $request->has('endDateEmision')) {
                $query->whereDate('M.emission_date', '>=', $request->input('startDateEmision'));
                $query->whereDate('M.emission_date', '<=', $request->input('endDateEmision'));
            }

            if ($request->has('startDateExpiration') && $request->has('endDateExpiration')) {
                $query->whereDate('M.expiration_date', '>=', $request->input('startDateExpiration'));
                $query->whereDate('M.expiration_date', '<=', $request->input('endDateExpiration'));
            }

            if ($request->has('ruc')) {
                $query->where('XP.identification_number', 'LIKE', '%' . $request->input('ruc') . '%');
            }

            if ($request->has('documentNumber')) {
                $query->where(DB::raw("CONCAT(M.document_code, '-', M.document_serie, '-', CAST(M.document_correlative AS INTEGER))"), 'LIKE', '%' . $request->input('documentNumber') . '%');
            }

            if ($request->has('documentType')) {
                $statusString = $request->input('documentType');
                $statusArray = explode(',', $statusString);
                $query->whereIn('M.document_code', $statusArray);
            }
            if ($request->has('requestPaymentPersons')) {
                $requestingUserSelected = $request->input('requestPaymentPersons');
                $arrayRequestingUser = explode(",", $requestingUserSelected);
                $query->whereIn(DB::raw("COALESCE(RXP.person_id, PER.person_id)"), $arrayRequestingUser);
            }

            if ($request->has('businessPartner')) {
                $bussinessPartnerSelected = $request->input('businessPartner');
                $arrayBussinessPartner = explode(",", $bussinessPartnerSelected);
                $query->whereIn("XP.person_id", $arrayBussinessPartner);
            }

            if ($request->has('businessUnit')) {
                $bussinessUnitSelected = $request->input('businessUnit');
                $arrayBussinessUnit = explode(",", $bussinessUnitSelected);
                $query->whereIn("BU.business_unit_id", $arrayBussinessUnit);
            }

            if ($request->has('store')) {
                $storeSelected = $request->input('store');
                $arrayStore = explode(",", $storeSelected);
                $query->whereIn("ST.store_id", $arrayStore);
            }

            if ($request->has('project')) {
                $projectSelected = $request->input('project');
                $arrayProject = explode(",", $projectSelected);
                $query->whereIn("P.project_id", $arrayProject);
            }

            $totalItems = $query->count();

            $results = $query->select(
                DB::raw("CONCAT(M.movement_id, '-M') AS pk"),
                DB::raw("NULL as entryGroupId"),
                'M.movement_id AS movementId',
                'ST.store_name AS store',
                'BU.business_unit_name AS businessUnit',
                'P.short_name AS project',
                DB::raw("CASE
                        WHEN M.route = 'logistic/purchaseOrder' THEN 'Adelanto de Proveedor'
                        WHEN M.route = 'treasury/cashOutOrder' THEN
                            CASE
                                WHEN PL.requirement_type = 'prePay' THEN '" . ProductList::LABEL_TYPE_PRE_PAY . "'
                                WHEN PL.requirement_type = 'refund' THEN '" . ProductList::LABEL_TYPE_REFUND . "'
                                ELSE ''
                            END
                        ELSE ''
                    END AS detail"),
                DB::raw("CONCAT(M.document_code, '-', M.document_serie, '-', CAST(M.document_correlative AS INTEGER)) AS document"),
                'D.document_name AS documentType',
                'M.document_code AS documentCode',
                'M.document_serie AS documentSerie',
                DB::raw("CAST(M.document_correlative AS INTEGER) AS documentCorrelative"),
                'S.title',
                DB::raw("XP.identification_number AS identificationNumber"),
                DB::raw("REPLACE(XP.person_name, ',', ' ') AS auxPersonName"),
                'M.emission_date as emissionDate',
                'M.expiration_date as expirationDate',
                DB::raw("CASE
                        WHEN M.route = 'logistic/purchaseOrder' THEN CM.CONDITION
                        WHEN M.route = 'treasury/cashOutOrder' THEN ''
                    END AS paymentCondition"),
                DB::raw("NULL as daysOverdue"),
                DB::raw('DATEDIFF(M.expiration_date, CURDATE()) AS daysRemaining'),
                'M.currency AS currency',
                DB::raw("CASE
                        WHEN M.route = 'logistic/purchaseOrder' THEN  CM.advance_amount
                        WHEN M.route = 'treasury/cashOutOrder' THEN
                            CASE
                                WHEN M.currency = 'pen' THEN PL.total_pen
                                WHEN M.currency = 'usd' THEN PL.total_usd
                                ELSE 0
                            END
                        ELSE ''
                    END AS total"),
                DB::raw("CASE
                        WHEN M.route = 'logistic/purchaseOrder' THEN CM.advance_balance
                        WHEN M.route = 'treasury/cashOutOrder' THEN
                            CASE
                                WHEN M.currency = 'pen' THEN PL.balance_pen
                                WHEN M.currency = 'usd' THEN PL.balance_usd
                                ELSE 0
                            END
                        ELSE ''
                    END AS balance"),
                DB::raw("CASE
                        WHEN M.route = 'logistic/purchaseOrder' THEN CM.advance_percentage
                        WHEN M.route = 'treasury/cashOutOrder' THEN 100
                    END AS balancePercent"),
                DB::raw("true as isPaid"),
                DB::raw("AB.upgrade_confirmed as isPayable"),
                'D.sunat_code as sunatCode',
                DB::raw("'M' as documentOrigin"),
                'M.route as route',
                DB::raw("REPLACE(COALESCE(RXP.person_name, PER.person_name), ',', ' ') AS personName"),
                DB::raw("IF(CM.movement_id IS NULL, '" . CashboxMovement::PAYMENT_METHOD_BANK_DEPOSIT . "', COALESCE(CM.payment_method, '" . CashboxMovement::PAYMENT_METHOD_OTHER . "')) as paymentMethod"),
                DB::raw("IF(M.currency = '" . Currency::PEN . "', (CM.total_pen > " . $limit_retention_amount_pen . "),(CM.total_usd > " . $limit_retention_amount_usd . ")) as exceedsRetentionAmount"),
                "XP.retention as bpIsRetentionAgent",
                "XP.perception as bpIsPerceptionAgent",
                "XP.good_contributor as goodContributor",
                DB::raw("((SELECT COUNT(*) FROM item WHERE movement_id = M.movement_id AND product_type != 'M') > 0) AS hasDetraction"),
                DB::raw("IF(CM.movement_id IS NOT NULL,CM.igv_pen = 0, 1) as no_igv"),
                "M.custom_category",
                "AB.is_dispatched",
                "CM.merchandise_count",
                "CM.service_count",
            )
                ->distinct()
                ->orderByRaw("CASE WHEN M.expiration_date < CURDATE() THEN 1 WHEN M.expiration_date = CURDATE() THEN 2 ELSE 3 END, M.expiration_date ASC")
                ->offset($startIndex)->limit($pageSize)->get();

            foreach ($results as $result) {
                $paymentScheduleId = DB::table('payment_schedule_date as PSDT')
                    ->join('payment_schedule_detail as PSD', 'PSD.payment_schedule_detail_id', '=', 'PSDT.payment_schedule_detail_id')
                    ->where('PSDT.state', '=', PaymentScheduleDate::STATE_OPEN)
                    ->where('PSD.movement_id', '=', $result->movementId) //modificar por movement_id
                    ->select('PSD.payment_schedule_id as paymentScheduleID')
                    ->first();
                $result->paymentScheduleID = $paymentScheduleId ? $paymentScheduleId->paymentScheduleID : null;
            }

            $response = [
                'success' => true,
                'pagination' => [
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'totalRecords' => $totalItems,
                    'totalPages' => ceil($totalItems / $pageSize)
                ],
                'data' => $results
            ];

            return response()->json($response);
        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function getDocumentsForPay(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "startDate" => 'date',
            "user_id" => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()
            ], 400);
        }

        try {

            $today = Carbon::today()->toDateString();

            $pay_accounts_permissions = SpGetPairedOwners::executeProcedure([
                'type' => OwnerPair::TYPE_USER_PAY_ACCOUNT,
                'parent_owner' => User::OWNER,
                'parent_owner_id' => $request->input('user_id')
            ]);

            $pay_scenarios_permissions = SpGetPairedOwners::executeProcedure([
                'type' => OwnerPair::TYPE_USER_PAY_SCENARIO,
                'parent_owner' => User::OWNER,
                'parent_owner_id' => $request->input('user_id')
            ]);

            $accounts_child_owner_ids = array_map(function ($account) {
                return $account->child_owner_id;
            }, $pay_accounts_permissions);

            $scenarios_child_owner_ids = array_map(function ($scenario) {
                return $scenario->child_owner_id;
            }, $pay_scenarios_permissions);

            $account_codes = Account::whereIn('account_id', $accounts_child_owner_ids)->pluck('account_code');

            $scenario_routes = Scenario::whereIn('scenario_id', $scenarios_child_owner_ids)->pluck('route');

            $schedules_pay = DB::table('payment_schedule_date as PSDT')
                ->select('PSDT.*', 'PSD.*', 'PSD.document_origin as documentOrigin')
                ->join('payment_schedule_detail as PSD', 'PSD.payment_schedule_detail_id', '=', 'PSDT.payment_schedule_detail_id')
                ->join('movement as M', 'M.movement_id', '=', 'PSD.movement_id')
                ->leftJoin('entry_group as EG', 'EG.entry_group_id', '=', 'PSD.entry_group_id')
                ->where('PSDT.state', PaymentScheduleDate::STATE_OPEN)
                ->whereDate('PSDT.date', '<=', $today)
                ->where('M.status', '=', 1)
                ->where(function ($query) use ($account_codes, $scenario_routes) {
                    $query->whereNotNull('EG.account_code')
                        ->where(function ($query) use ($account_codes) {
                            if ($account_codes->isNotEmpty()) {
                                foreach ($account_codes as $code) {
                                    $query->orWhere('EG.account_code', 'LIKE', '%' . $code . '%');
                                }
                            } else {
                                $query->whereNull('EG.account_code');
                            }
                        })
                        ->orWhere(function ($query) use ($scenario_routes) {
                            if ($scenario_routes->isNotEmpty()) {
                                $query->whereIn('M.route', $scenario_routes);
                            } else {
                                $query->whereNull('M.route');
                            }
                        });
                })
                ->get();

            $results = [];

            foreach ($schedules_pay as $schedule_pay) {
                switch ($schedule_pay->documentOrigin) {
                    case 'EG':
                        $detail_data = $this->getDetailEntryGroup($schedule_pay->entry_group_id, $schedule_pay->payment_schedule_detail_id, $schedule_pay->payment_schedule_date_id);
                        break;
                    case 'M':
                        $detail_data = $this->getDetailMovement($schedule_pay->movement_id, $schedule_pay->payment_schedule_detail_id, $schedule_pay->payment_schedule_date_id);
                        break;
                    default:
                        $detail_data = [];
                        break;
                }

                if ($detail_data != null) {
                    $schedule_pay_array = (array) $schedule_pay;
                    $detail_data_array = (array) $detail_data;
                    $results[] = array_merge($schedule_pay_array, $detail_data_array);
                }
            }

            $results_with_bank_info = [];

            foreach ($results as $result) {

                $s_symbol = "<>";

                if (isset($result['isDetraction']) && $result['isDetraction'] == 1) {
                    $s_symbol = "=";
                }

                $bankAccountInfo = DB::table('bank_account as BA')
                    ->join('multitable as MT', 'MT.multi_id', '=', 'BA.entity_multi_id')
                    ->where('BA.owner_id', $result['owner_id'])
                    ->where('BA.currency', $result['currency'])
                    ->where('BA.type', $s_symbol, BankAccount::TYPE_DETRACTION)
                    ->select(
                        'BA.owner_id',
                        'BA.type',
                        'BA.currency',
                        'BA.account_number as accountNumber',
                        'BA.icc',
                        'MT.multi_id',
                        'MT.description as bankName'
                    )
                    ->get();


                $result['providerAccounts'] = $bankAccountInfo;
                $results_with_bank_info[] = $result;
            }

            $response = [
                'success' => true,
                'data' => $results_with_bank_info
            ];

            return response()->json($response);
        } catch (\Exception $ex) {
            Log::error($ex->getTraceAsString());
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }

    private function getDetailEntryGroup($entryGroupId, $paymentScheduleDetailId, $paymentScheduleDateId)
    {
        $limit_retention_amount_pen = GlobalVar::getValue('no_retention_max_amount_pen');
        $limit_retention_amount_usd = ExchangeRate::convertToDollars($limit_retention_amount_pen);

        return DB::table('entry_group as EG')
            ->join('movement as M', 'M.movement_id', '=', 'EG.movement_id')
            ->join('advanced_option as AO', function ($join) {
                $join->on('AO.movement_id', '=', 'M.movement_id')
                    ->where('AO.lock_payment', '=', 0);
            })
            ->join('scenario as S', 'S.route', '=', 'EG.route')
            ->join('person as XP', 'XP.person_id', '=', 'EG.owner_id')
            ->join('payment_schedule_detail as PSD', function ($join) use ($paymentScheduleDetailId) {
                $join->on('PSD.payment_schedule_detail_id', '=', DB::raw($paymentScheduleDetailId));
            })
            ->join('payment_schedule as PS', 'PS.payment_schedule_id', '=', 'PSD.payment_schedule_id')
            ->join('person as P', 'P.person_id', '=', 'PS.person_id')
            ->join('person as PER', 'PER.person_id', '=', 'M.person_id')
            ->leftJoin('commercial_movement as CM', 'CM.movement_id', '=', 'M.movement_id')
            ->leftJoin('entry as E', 'E.entry_id', '=', 'EG.pk')
            // ->where('EG.checked', '=', 1)
            ->leftJoin('movement_link as ML', function ($join) {
                $join->on('ML.movement_id', '=', 'M.movement_id')
                    ->where('ML.status', '=', 1)
                    ->where('ML.parent_route', '=', 'logistic/purchaseOrder');
            })
            ->leftJoin('commercial_movement as PCM', 'PCM.movement_id', '=', 'ML.movement_parent_id')
            ->where('EG.entry_group_id', '=', $entryGroupId)
            ->select(
                DB::raw("{$paymentScheduleDateId} AS paymentScheduleDateID"),
                DB::raw("{$paymentScheduleDetailId} AS paymentScheduleDetailID"),
                'PSD.payment_schedule_id as paymentScheduleID',
                'PS.emission_date as emissionDatePaymentSchedule',
                'PS.description as descriptionPaymentSchedule',
                'P.person_name as personName',
                'EG.entry_group_id as entryGroupId',
                'EG.movement_id as movementId',
                'EG.detail as detail',
                DB::raw("CONCAT(M.document_code, '-', M.document_serie, '-', CAST(M.document_correlative AS INTEGER)) AS document"),
                'S.title',
                'XP.identification_number as identificationNumber',
                DB::raw("REPLACE(XP.person_name, ',', ' ') AS auxPersonName"),
                'XP.person_id as auxPersonID',
                'EG.emission_date as emissionDate',
                'EG.expiration_date as expirationDate',
                DB::raw('DATEDIFF(EG.expiration_date, CURDATE()) AS daysRemaining'),
                'M.currency AS currency',
                'EG.total',
                'PSD.balance_amount as balance',
                'EG.owner_id',
                "EG.route",
                "EG.owner",
                "EG.account_code as accountCode",
                'EG.checked as isProvisioned',
                DB::raw("(E.field = 'det') AS isDetraction"),
                "EG.pk as entryID",
                "M.route as route",
                DB::raw("REPLACE(PER.person_name, ',', ' ') AS personName"),
                DB::raw("COALESCE(CM.payment_method, 'Otro') AS paymentMethod"),
                DB::raw("(CM.det_pen > 0) as hasDetraction"),
                DB::raw("IF(M.currency = '" . Currency::PEN . "', (EG.total > " . $limit_retention_amount_pen . "),(EG.total > " . $limit_retention_amount_usd . ")) as exceedsRetentionAmount"),
                "XP.retention as bpIsRetentionAgent",
                "XP.perception as bpIsPerceptionAgent",
                "XP.good_contributor as goodContributor",
                DB::raw("IF(PCM.movement_id IS NOT NULL,
                    IF(M.currency = '" . Currency::PEN . "',
                        PCM.total_pen > " . $limit_retention_amount_pen . ",
                        PCM.total_usd > " . $limit_retention_amount_usd . "
                    ),
                    0
                ) AS parentExceedsRetentionAmount"),
                DB::raw('(CM.igv_pen = 0) as no_igv'),
            )
            ->first();
    }

    public function getDetailFeePay(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                "entryGroup" => 'required|integer',
                'currency' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validator->errors()
                ], 400);
            }
            $o_entryGroup = EntryGroup::find($request->input('entryGroup'));

            $a_result = DB::table('entry as E')
                ->select(
                    DB::raw("IF(field = '" . ScenarioField::FIELD_AMORTIZATION . "', '" . ScenarioField::FIELD_REAL . "', field) as field"),
                    DB::raw("balance_" . $request->input('currency') . " AS balance")
                )
                ->whereRaw("entry_id IN (" . $o_entryGroup->pk . ")")
                ->get();

            $a_final = [];
            foreach ($a_result as $o_row) {
                $a_final[$o_row->field] = (float) $o_row->balance;
            }

            $a_response = $a_response = [
                'success' => false,
                'data' => []
            ];
            if (count($a_result) > 0) {

                $a_response = [
                    'success' => true,
                    'data' => $a_final
                ];
            }
            return response()->json($a_response);
        } catch (\Exception $ex) {
            Log::error('Error reposition: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error reposition: ' . $ex->getMessage(),
            ], 500);
        }
    }

    private function getDetailMovement($movementId, $paymentScheduleDetailId, $paymentScheduleDateId)
    {
        $limit_retention_amount_pen = GlobalVar::getValue('no_retention_max_amount_pen');
        $limit_retention_amount_usd = ExchangeRate::convertToDollars($limit_retention_amount_pen);

        return DB::table('movement as M')
            ->join('advanced_option as AO', function ($join) {
                $join->on('AO.movement_id', '=', 'M.movement_id')
                    ->where('AO.lock_payment', '=', 0);
            })
            ->join('scenario as S', 'S.route', '=', 'M.route')
            ->join('person as XP', 'XP.person_id', '=', 'M.aux_person_id')
            ->join('payment_schedule_detail as PSD', function ($join) use ($paymentScheduleDetailId) {
                $join->on('PSD.payment_schedule_detail_id', '=', DB::raw($paymentScheduleDetailId));
            })
            ->join('payment_schedule as PS', 'PS.payment_schedule_id', '=', 'PSD.payment_schedule_id')
            ->join('person as P', 'P.person_id', '=', 'PS.person_id')
            ->leftJoin('commercial_movement as CM', 'CM.movement_id', '=', 'M.movement_id')
            ->join('person as PER', 'PER.person_id', '=', 'M.person_id')
            ->leftJoin('product_list AS PL', function ($join) {
                $join->on('PL.owner_id', '=', 'M.movement_id')
                    ->where('PL.owner', '=', Owner::OWNER_MOVEMENT);
            })
            ->whereIn('M.route', [
                'logistic/purchaseOrder',
                'treasury/cashOutOrder'
            ])
            ->where(function ($query) {
                $query->where(function ($query) {
                    $query->where('M.route', 'logistic/purchaseOrder')
                        ->where('CM.advance_balance', '>', 0);
                })
                    ->orWhere(function ($query) {
                        $query->where('M.route', 'treasury/cashOutOrder')
                            ->where('PL.balance_pen', '>', 0)
                            ->where('PL.balance_usd', '>', 0);
                    });
            })
            ->where('M.movement_id', '=', $movementId)
            ->select(
                DB::raw("{$paymentScheduleDateId} AS paymentScheduleDateID"),
                DB::raw("{$paymentScheduleDetailId} AS paymentScheduleDetailID"),
                'PSD.payment_schedule_id as paymentScheduleID',
                'PS.emission_date as emissionDatePaymentSchedule',
                'PS.description as descriptionPaymentSchedule',
                'P.person_name as personName',
                DB::raw("NULL as entryGroupId"),
                'M.movement_id as movementId',
                DB::raw("CASE
                    WHEN M.route = 'logistic/purchaseOrder' THEN 'Adelanto de Proveedor'
                    WHEN M.route = 'treasury/cashOutOrder' THEN
                        CASE
                            WHEN PL.requirement_type = 'prePay' THEN '" . ProductList::LABEL_TYPE_PRE_PAY . "'
                            WHEN PL.requirement_type = 'refund' THEN '" . ProductList::LABEL_TYPE_REFUND . "'
                            ELSE ''
                        END
                    ELSE ''
                END AS detail"),
                DB::raw("CONCAT(M.document_code, '-', M.document_serie, '-', CAST(M.document_correlative AS INTEGER)) AS document"),
                'S.title',
                'XP.identification_number as identificationNumber',
                DB::raw("REPLACE(XP.person_name, ',', ' ') AS auxPersonName"),
                'XP.person_id as auxPersonID',
                'M.emission_date as emissionDate',
                'M.expiration_date as expirationDate',
                DB::raw('DATEDIFF(M.expiration_date, CURDATE()) AS daysRemaining'),
                'M.currency AS currency',
                DB::raw("CASE
                    WHEN M.route = 'logistic/purchaseOrder' THEN  CM.advance_amount
                    WHEN M.route = 'treasury/cashOutOrder' THEN
                        CASE
                            WHEN M.currency = 'pen' THEN PL.total_pen
                            WHEN M.currency = 'usd' THEN PL.total_usd
                            ELSE 0
                        END
                    ELSE ''
                END AS total"),
                'PSD.balance_amount as balance',
                'M.aux_person_id as owner_id',
                "M.route",
                DB::raw("'Person' AS owner"),
                DB::raw("'' AS accountCode"),
                DB::raw("NULL AS entryID"),
                'M.route as route',
                DB::raw("REPLACE(PER.person_name, ',', ' ') AS personName"),
                DB::raw("COALESCE(CM.payment_method, 'Otro') AS paymentMethod"),
                DB::raw("IF(M.currency = '" . Currency::PEN . "', (CM.total_pen > " . $limit_retention_amount_pen . "),(CM.total_usd > " . $limit_retention_amount_usd . ")) as exceedsRetentionAmount"),
                "XP.retention as bpIsRetentionAgent",
                "XP.perception as bpIsPerceptionAgent",
                "XP.good_contributor as goodContributor",
                DB::raw("((SELECT COUNT(*) FROM item WHERE movement_id = M.movement_id AND product_type != 'M') > 0) AS hasDetraction"),
                DB::raw("IF(CM.movement_id IS NOT NULL,CM.igv_pen = 0, 1) as no_igv")
            )
            ->first();
    }

    public function getRequestPaymentPersons(Request $request)
    {
        try {
            $query = DB::table('person as PER')->where('PER.status', '=', 1)->whereIn('PER.identification_type', ['1']);

            if ($request->has('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(DB::raw("REPLACE(PER.person_name, ',', ' ')"), 'like', '%' . $keyword . '%');
            }

            if ($request->has('requestPaymentPersons')) {
                $requestingUserSelected = $request->input('requestPaymentPersons');
                $arrayRequestingUser = explode(",", $requestingUserSelected);
                $query->whereNotIn("PER.person_id", $arrayRequestingUser);
            }

            if ($request->has('limit')) {
                $limit = $request->input('limit');
                $query->limit($limit);
            } else {
                $query->limit(10);
            }

            $results = $query->select(
                'PER.person_id',
                DB::raw("REPLACE(PER.person_name, ',', ' ') as person_name")
            )->distinct()->get();

            return response()->json([
                'success' => true,
                'data' => $results,
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }

    public function getBussinessPartners(Request $request)
    {
        try {
            $query = DB::table('person as BP')->where('BP.status', '=', 1)->whereIn('BP.identification_type', ['1', '6']);

            if ($request->has('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(function ($q) use ($keyword) {
                    $q->where(DB::raw("REPLACE(BP.person_name, ',', ' ')"), 'like', '%' . $keyword . '%')
                        ->orWhere('BP.identification_number', 'like', '%' . $keyword . '%');
                });
            }

            if ($request->has('businessPartner')) {
                $bussinessPartnerSelected = $request->input('businessPartner');
                $arrayBussinessPartner = explode(",", $bussinessPartnerSelected);
                $query->whereNotIn("BP.person_id", $arrayBussinessPartner);
            }

            if ($request->has('limit')) {
                $limit = $request->input('limit');
                if ($limit > 0) {
                    $query->limit($limit);
                } else {
                    $query->limit(1000);
                }
            } else {
                $query->limit(10);
            }

            $results = $query->select(
                'BP.person_id',
                DB::raw("REPLACE(BP.person_name, ',', ' ') as person_name"),
                'BP.identification_number'
            )->distinct()->get();

            return response()->json([
                'success' => true,
                'data' => $results,
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }

    private function baseQuery()
    {
        return DB::table('movement as M')
            ->join('person as PER', 'PER.person_id', '=', 'M.person_id')
            ->join('document as D', 'D.document_code', '=', 'M.document_code')
            ->join('scenario as S', 'S.route', '=', 'M.route')
            ->leftJoin('commercial_movement as CM', 'CM.movement_id', '=', 'M.movement_id')
            ->leftJoin('product_list as PL', function ($join) {
                $join->on('PL.owner_id', '=', 'M.movement_id')
                    ->where('PL.owner', '=', Owner::OWNER_MOVEMENT);
            })
            ->leftJoin('entry_group as EG', 'EG.movement_id', '=', 'M.movement_id')
            ->leftJoin('ability as AB', 'AB.movement_id', '=', 'M.movement_id')
            ->where(function ($query) {
                $query->where(function ($query) {
                    $query->where('M.status', '=', 1)
                        ->whereIn('M.route', [
                            'logistic/purchaseOrder',
                            'treasury/cashOutOrder'
                        ])
                        ->where(function ($query) {
                            $query->where(function ($query) {
                                $query->where('M.route', '=', 'logistic/purchaseOrder')
                                    ->where('CM.advance_balance', '>', 0)
                                    ->where('AB.upgrade_confirmed', '=', 1);
                            })
                                ->orWhere(function ($query) {
                                    $query->where('M.route', '=', 'treasury/cashOutOrder')
                                        ->where('PL.balance_pen', '>', 0)
                                        ->where('PL.balance_usd', '>', 0)
                                        ->where('AB.pay_confirmed', '=', 1);
                                });
                        });
                })
                    ->orWhere(function ($query) {
                        $query->whereNotNull('EG.route')
                            ->whereIn('EG.route', [
                                'accounting/fecline',
                                'accounting/leasing',
                                'accounting/loan',
                                'accounting/manual',
                                'accounting/operatingCost',
                                'commercial/creditNote1',
                                'commercial/creditNote2',
                                'commercial/creditNote3',
                                'financial/providerLetter',
                                'human/payroll',
                                'logistic/debitNote',
                                'logistic/mobility',
                                'logistic/operatingCost',
                                'logistic/purchaseBill',
                                'logistic/purcharseOrder',
                                'treasury/preCollect'
                            ])
                            ->where('EG.COLUMN', '=', 'Haber')
                            ->where('EG.to_do', '=', true)
                            // ->where('EG.checked', '=', true)
                            ->where('EG.is_paid', '=', 0)
                            ->where('EG.is_payable', '=', 1)
                            ->where('EG.balance', '>', 0);
                    });
            });
    }
}
