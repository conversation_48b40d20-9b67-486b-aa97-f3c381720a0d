<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ProductTypeSetting
 * 
 * @property int $product_type_setting_id
 * @property int $item_type_id
 * @property int|null $warehouse_id
 * @property string $purchase_account
 * @property string $purchase_discount_account
 * @property string $sale_account
 * @property string $sale_discount_account
 * @property string|null $sale_cost_account
 * @property string|null $variation_account
 * @property string|null $warehouse_account
 * @property string|null $transit_account
 * @property string|null $cost_transport_account
 * @property string|null $cost_other_account
 * @property string|null $adjust_account
 * 
 * @property Account|null $account
 * @property Multitable $multitable
 * @property Warehouse|null $warehouse
 *
 * @package App\Models
 */
class ProductTypeSetting extends Model
{
	protected $table = 'product_type_setting';
	protected $primaryKey = 'product_type_setting_id';
	public $timestamps = false;

	protected $casts = [
		'item_type_id' => 'int',
		'warehouse_id' => 'int'
	];

	protected $fillable = [
		'item_type_id',
		'warehouse_id',
		'purchase_account',
		'purchase_discount_account',
		'sale_account',
		'sale_discount_account',
		'sale_cost_account',
		'variation_account',
		'warehouse_account',
		'transit_account',
		'cost_transport_account',
		'cost_other_account',
		'adjust_account'
	];

	public function account()
	{
		return $this->belongsTo(Account::class, 'warehouse_account');
	}

	public function multitable()
	{
		return $this->belongsTo(Multitable::class, 'item_type_id');
	}

	public function warehouse()
	{
		return $this->belongsTo(Warehouse::class);
	}
}
