<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

/**
 * Class Recipe
 *
 * @property int $recipe_id
 * @property int $product_id
 * @property string|null $description
 * @property string $type
 * @property float|null $pres_quantity
 * @property float|null $equivalence
 * @property float|null $total_material_cost
 * @property float|null $margin_error_percentage
 * @property float|null $estimated_material_percentage
 * @property float|null $tax_percentage
 * @property float|null $estimated_price
 * @property float|null $price
 * @property float|null $real_material_percentage
 * @property Carbon $created_at
 * @property Carbon|null $updated_at
 * @property string $create_user_id
 * @property string|null $update_user_id
 * @property boolean $status
 * @property boolean $initialized
 *
 * @property Product $product
 * @property User $createUser
 * @property User|null $updateUser
 * @property Collection|ProductLink[] $productLinks
 * @package App\Models
 *
 */

class Recipe extends Model {
    protected $table = 'recipe';
    protected $primaryKey = 'recipe_id';
    public $timestamps = true;

    protected $fillable = [
        'product_id',
        'description',
        'pres_quantity',
        'type',
        'equivalence',
        'total_material_cost',
        'margin_error_percentage',
        'estimated_material_percentage',
        'tax_percentage',
        'estimated_price',
        'price',
        'real_material_percentage',
        'create_user_id',
        'update_user_id',
        'status',
        'initialized'
    ];

    protected $casts = [
        'pres_quentity' => 'float',
        'equivalence' => 'string',
        'total_material_cost' => 'float',
        'margin_error_percentage' => 'float',
        'estimated_material_percentage' => 'float',
        'tax_percentage' => 'float',
        'estimated_price' => 'float',
        'price' => 'float',
        'real_material_percentage' => 'float',
        'status' => 'boolean',
        'type' => 'string',
        'initialized' => 'boolean'
    ];

    public function product() {
        return $this->belongsTo(Product::class, 'product_id', 'product_id');
    }

    public function createUser() {
        return $this->belongsTo(User::class, 'create_user_id', 'user_id');
    }

    public function updateUser() {
        return $this->belongsTo(User::class, 'update_user_id', 'user_id');
    }

    public function productLinks() {
        return $this->hasMany(ProductLink::class, 'product_id', 'product_parent_id');
    }

    public function setRealMaterialPercentageAttribute($value)
    {
        $this->attributes['real_material_percentage'] = $value ?? 0;
    }
}
