<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class DepreciationGroup
 * 
 * @property int $depreciation_group_id
 * @property string $depreciation_group_name
 * @property int $depreciation_method
 * @property string $account_code_depreciation
 * @property string $account_code_cost
 * @property string|null $observation
 * @property bool $status
 * 
 * @property Account $account
 * @property Multitable $multitable
 * @property Collection|DepreciationGroupDetail[] $depreciation_group_details
 *
 * @package App\Models
 */
class DepreciationGroup extends Model
{
	protected $table = 'depreciation_group';
	protected $primaryKey = 'depreciation_group_id';
	public $timestamps = false;

	protected $casts = [
		'depreciation_method' => 'int',
		'status' => 'bool'
	];

	protected $fillable = [
		'depreciation_group_name',
		'depreciation_method',
		'account_code_depreciation',
		'account_code_cost',
		'observation',
		'status'
	];

	public function accountDepreciation()
	{
		return $this->belongsTo(Account::class, 'account_code_depreciation', 'account_code');
	}

	public function accountCost()
	{
		return $this->belongsTo(Account::class, 'account_code_cost', 'account_code');
	}

	public function depreciationMethod()
	{
		return $this->belongsTo(Multitable::class, 'depreciation_method', 'multi_id');
	}

	public function depreciationGroupDetails()
	{
		return $this->hasMany(DepreciationGroupDetail::class, 'depreciation_group_id', 'depreciation_group_id');
	}
}
