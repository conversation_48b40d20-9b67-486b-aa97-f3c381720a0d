<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Action
 * 
 * @property int $action_id
 * @property string $module
 * @property string $controller
 * @property string $action
 * @property string $owner
 * @property string $title
 * @property string|null $description
 * @property bool $type
 * @property bool $level
 * @property bool $ignore_ajax_hits
 * @property int $hits
 * 
 * @property Collection|ActionLog[] $action_logs
 * @property Collection|Actionassignment[] $actionassignments
 * @property Collection|Actionchild[] $actionchildren
 * @property CrossReport $cross_report
 * @property Collection|Owner[] $owners
 *
 * @package App\Models
 */
class Action extends Model
{
	protected $table = 'action';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'action_id' => 'int',
		'type' => 'bool',
		'level' => 'bool',
		'ignore_ajax_hits' => 'bool',
		'hits' => 'int'
	];

	protected $fillable = [
		'action_id',
		'title',
		'description',
		'type',
		'level',
		'ignore_ajax_hits',
		'hits'
	];

	public function controller()
	{
		return $this->belongsTo(Controller::class, 'module')
					->where('controller.module', '=', 'action.module')
					->where('controller.controller', '=', 'action.controller');
	}

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}

	public function action_logs()
	{
		return $this->hasMany(ActionLog::class, 'module');
	}

	public function actionassignments()
	{
		return $this->hasMany(Actionassignment::class, 'module');
	}

	public function actionchildren()
	{
		return $this->hasMany(Actionchild::class, 'module_parent');
	}

	public function cross_report()
	{
		return $this->hasOne(CrossReport::class, 'action_id', 'action_id');
	}

	public function owners()
	{
		return $this->hasMany(Owner::class, 'module');
	}
}
