{"ast": null, "code": "var _jsxFileName = \"D:\\\\www\\\\SWF\\\\sian-web-frontend\\\\src\\\\views\\\\commercial\\\\salesDashboard\\\\components\\\\PredictionModal.jsx\",\n    _s = $RefreshSig$(),\n    _s2 = $RefreshSig$();\n\n/* eslint-disable */\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, TextField, Box, Typography, IconButton, Fade, useTheme, Button, CircularProgress, alpha } from '@mui/material';\nimport CloseIcon from '@mui/icons-material/Close';\nimport SmartToyIcon from '@mui/icons-material/SmartToy';\nimport SendIcon from '@mui/icons-material/Send';\nimport ReactMarkdown from 'react-markdown';\nimport { styled } from '@mui/material/styles'; // Styled components\n\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatMessage = styled(Box)(_ref => {\n  let {\n    theme,\n    isUser\n  } = _ref;\n  return {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: isUser ? 'flex-end' : 'flex-start',\n    marginBottom: theme.spacing(2),\n    maxWidth: '80%',\n    alignSelf: isUser ? 'flex-end' : 'flex-start',\n    '& > div': {\n      padding: theme.spacing(2),\n      borderRadius: isUser ? '18px 18px 0 18px' : '18px 18px 18px 0',\n      background: isUser ? `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)` : `linear-gradient(135deg, #f0f0f0 0%, #f8f8f8 100%)`,\n      color: isUser ? '#fff' : theme.palette.text.primary,\n      boxShadow: isUser ? '0 4px 20px rgba(156, 39, 176, 0.25)' : '0 2px 10px rgba(0,0,0,0.08)',\n      transition: 'all 0.3s ease-in-out',\n      '&:hover': {\n        transform: 'translateY(-2px)',\n        boxShadow: isUser ? '0 6px 25px rgba(156, 39, 176, 0.3)' : '0 4px 15px rgba(0,0,0,0.1)'\n      },\n      '& p': {\n        margin: 0,\n        marginBottom: '0.5em',\n        '&:last-child': {\n          marginBottom: 0\n        }\n      },\n      '& pre': {\n        backgroundColor: isUser ? 'rgba(0,0,0,0.2)' : '#e0e0e0',\n        padding: theme.spacing(1),\n        borderRadius: theme.spacing(1),\n        overflowX: 'auto',\n        '& code': {\n          fontFamily: 'monospace'\n        }\n      }\n    }\n  };\n});\n\nconst ThinkingLoader = () => {\n  _s();\n\n  const theme = useTheme();\n  const [dotCount, setDotCount] = useState(0);\n  const [currentThought, setCurrentThought] = useState(0);\n  const thoughts = [\"Analizando datos de ventas\", \"Procesando información\", \"Calculando tendencias\", \"Generando predicción\", \"Evaluando escenarios\"];\n  useEffect(() => {\n    const dotInterval = setInterval(() => {\n      setDotCount(prev => (prev + 1) % 4);\n    }, 400);\n    const thoughtInterval = setInterval(() => {\n      setCurrentThought(prev => (prev + 1) % thoughts.length);\n    }, 2000);\n    return () => {\n      clearInterval(dotInterval);\n      clearInterval(thoughtInterval);\n    };\n  }, []);\n  const dots = '.'.repeat(dotCount);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      mt: 3,\n      mb: 3,\n      p: 3,\n      background: `linear-gradient(145deg, ${alpha(theme.palette.secondary.light, 0.2)} 0%, ${alpha(theme.palette.primary.light, 0.15)} 100%)`,\n      borderRadius: 3,\n      border: `1px solid ${alpha(theme.palette.primary.main, 0.12)}`,\n      backdropFilter: 'blur(8px)',\n      boxShadow: `0 8px 32px 0 ${alpha(theme.palette.primary.main, 0.1)}`\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        position: 'relative',\n        mb: 2,\n        height: 70\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'relative',\n          width: 70,\n          height: 70,\n          borderRadius: '50%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n          color: 'white',\n          animation: 'pulse 1.5s infinite',\n          '@keyframes pulse': {\n            '0%': {\n              transform: 'scale(1)',\n              boxShadow: `0 0 0 0 ${alpha(theme.palette.primary.main, 0.7)}`\n            },\n            '70%': {\n              transform: 'scale(1.05)',\n              boxShadow: `0 0 0 15px ${alpha(theme.palette.primary.main, 0)}`\n            },\n            '100%': {\n              transform: 'scale(1)',\n              boxShadow: `0 0 0 0 ${alpha(theme.palette.primary.main, 0)}`\n            }\n          },\n          '&::after': {\n            content: '\"\"',\n            position: 'absolute',\n            width: '100%',\n            height: '100%',\n            borderRadius: '50%',\n            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n            filter: 'blur(15px)',\n            opacity: 0.4,\n            zIndex: -1\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(SmartToyIcon, {\n          fontSize: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      sx: {\n        background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n        WebkitBackgroundClip: 'text',\n        WebkitTextFillColor: 'transparent',\n        backgroundClip: 'text',\n        fontWeight: 'bold',\n        minWidth: 200,\n        textAlign: 'center',\n        minHeight: 24,\n        fontSize: '1.1rem',\n        letterSpacing: '0.5px'\n      },\n      children: [thoughts[currentThought], dots]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        mt: 3,\n        justifyContent: 'center',\n        gap: 1.5\n      },\n      children: [0, 1, 2, 3, 4].map(i => /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 10,\n          height: 10,\n          borderRadius: '50%',\n          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n          opacity: i <= dotCount ? 1 : 0.3,\n          transition: 'all 0.2s ease',\n          animation: `bounce 1.4s infinite ease-in-out both`,\n          animationDelay: `${i * 0.1}s`,\n          boxShadow: `0 3px 10px ${alpha(theme.palette.primary.main, 0.4)}`,\n          '@keyframes bounce': {\n            '0%, 80%, 100%': {\n              transform: 'scale(0)'\n            },\n            '40%': {\n              transform: 'scale(1)'\n            }\n          }\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n};\n\n_s(ThinkingLoader, \"s53Bgfyy1Gvd2jt5KXy0h4KmKfw=\", false, function () {\n  return [useTheme];\n});\n\n_c = ThinkingLoader;\n\nconst PredictionModal = _ref2 => {\n  _s2();\n\n  let {\n    open,\n    onClose,\n    storeName,\n    storeData,\n    selectedTab\n  } = _ref2;\n  const theme = useTheme();\n  const [userInput, setUserInput] = useState('');\n  const [chatHistory, setChatHistory] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const chatEndRef = useRef(null);\n  const prevOpenRef = useRef(false); // OpenAI API configuration\n\n  const apiKey = process.env.REACT_APP_OPENAI_API_KEY;\n  const model = 'gpt-4o-mini';\n  const temperature = 0.7;\n  const apiEndpoint = process.env.REACT_APP_OPENAI_URL_BASE; // Scroll to bottom when chat history updates\n\n  useEffect(() => {\n    var _chatEndRef$current;\n\n    (_chatEndRef$current = chatEndRef.current) === null || _chatEndRef$current === void 0 ? void 0 : _chatEndRef$current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [chatHistory]); // Reset chat history and generate new analysis when modal opens\n\n  useEffect(() => {\n    // If modal transitions from closed to open\n    if (open && !prevOpenRef.current && storeData) {\n      // Reset chat history\n      setChatHistory([]); // Generate new analysis\n\n      setTimeout(() => {\n        generateInitialAnalysis();\n      }, 100); // Small timeout to ensure state is updated\n    } // Update previous open state\n\n\n    prevOpenRef.current = open;\n  }, [open, storeData, selectedTab, storeName]);\n\n  const generateInitialAnalysis = async () => {\n    setIsLoading(true); // Check if we're analyzing Total MIA or an individual store\n\n    const isTotal = storeName === 'Total MIA'; // Prepare store data summary based on the selected tab\n\n    let storeSummary = '';\n\n    if (selectedTab === 'day') {\n      storeSummary = `\n        ${isTotal ? 'Total MIA' : `Tienda: ${storeName}`}\n        Venta Diaria: ${storeData.today_sales}\n        Meta Diaria: ${storeData.today_goal}\n        Porcentaje de Avance: ${(parseFloat(storeData.today_sales) / parseFloat(storeData.today_goal) * 100).toFixed(2)}%\n        Restante para Meta: ${parseFloat(storeData.today_goal) - parseFloat(storeData.today_sales)}\n        Fecha: ${new Date(storeData.query_date).toLocaleDateString()}\n      `;\n    } else if (selectedTab === 'month') {\n      storeSummary = `\n        ${isTotal ? 'Total MIA' : `Tienda: ${storeName}`}\n        Venta Acumulada: ${storeData.progress_sales}\n        Meta Mensual: ${storeData.period_goal}\n        Porcentaje de Avance: ${(parseFloat(storeData.progress_sales) / parseFloat(storeData.period_goal) * 100).toFixed(2)}%\n        Restante para Meta: ${parseFloat(storeData.period_goal) - parseFloat(storeData.progress_sales)}\n        Mes: ${new Date(storeData.query_date).toLocaleString('default', {\n        month: 'long',\n        year: 'numeric'\n      })}\n      `;\n    } else {\n      // Tab 'date'\n      storeSummary = `\n        ${isTotal ? 'Total MIA' : `Tienda: ${storeName}`}\n        Venta Acumulada: ${storeData.progress_sales}\n        Meta Ajustada (${storeData.elapsed_days} días): ${storeData.date_goal}\n        Porcentaje de Avance: ${(parseFloat(storeData.progress_sales) / parseFloat(storeData.date_goal) * 100).toFixed(2)}%\n        Restante para Meta: ${parseFloat(storeData.date_goal) - parseFloat(storeData.progress_sales)}\n        Días Transcurridos: ${storeData.elapsed_days}\n        Fecha: ${new Date(storeData.query_date).toLocaleDateString()}\n      `;\n    } // Additional context for total analysis if applicable\n\n\n    let additionalContext = '';\n\n    if (isTotal) {\n      additionalContext = 'Este análisis corresponde al total consolidado de todas las tiendas MIA.';\n    } // Create prompt for initial analysis\n\n\n    const systemPrompt = `Eres un asistente de análisis de ventas para supermercados. Analiza los siguientes datos ${isTotal ? 'consolidados de todas las tiendas MIA' : `de la tienda ${storeName}`} y genera una predicción detallada que incluya:\n\n1. Un análisis del rendimiento actual basado en los datos proporcionados.\n2. Una predicción sobre si ${isTotal ? 'las tiendas en conjunto' : 'la tienda'} alcanzará su meta ${selectedTab === 'day' ? 'diaria' : selectedTab === 'month' ? 'mensual' : 'del período'} y en qué porcentaje.\n3. Factores que podrían estar afectando el rendimiento (basado en patrones típicos de retail).\n4. Recomendaciones específicas para mejorar las ventas.\n5. Usa s./ en lugar de $ para referirte a montos de dinero.\n\n### Datos ${isTotal ? 'Consolidados' : 'de la tienda'}:\n${storeSummary}\n\n${additionalContext}\n\nElabora tu respuesta de forma clara y detallada, con un enfoque profesional pero accesible.`;\n\n    try {\n      const response = await fetch(apiEndpoint, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model,\n          messages: [{\n            role: 'system',\n            content: systemPrompt\n          }],\n          temperature\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error('Error al obtener respuesta de OpenAI');\n      }\n\n      const data = await response.json();\n      const aiResponse = data.choices[0].message;\n      setChatHistory([aiResponse]);\n    } catch (error) {\n      /* eslint-disable */\n      console.error(...oo_tx(`1498926589_322_6_322_36_11`, 'Error:', error));\n      setChatHistory([{\n        role: 'assistant',\n        content: 'Lo siento, ocurrió un error al generar el análisis. Por favor, intenta nuevamente.'\n      }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleInputChange = event => {\n    setUserInput(event.target.value);\n  };\n\n  const handleKeyPress = event => {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      handleSend();\n    }\n  };\n\n  const handleSend = async () => {\n    if (!userInput.trim()) return;\n    const newMessage = {\n      role: 'user',\n      content: userInput\n    };\n    setChatHistory(prev => [...prev, newMessage]);\n    setUserInput('');\n    setIsLoading(true);\n\n    try {\n      // Check if we're analyzing Total MIA or an individual store\n      const isTotal = storeName === 'Total MIA'; // Prepare context about the store\n\n      let storeContext = '';\n\n      if (selectedTab === 'day') {\n        storeContext = `\n          ${isTotal ? 'Total MIA' : `Tienda: ${storeName}`}\n          Venta Diaria: ${storeData.today_sales}\n          Meta Diaria: ${storeData.today_goal}\n          Porcentaje de Avance: ${(parseFloat(storeData.today_sales) / parseFloat(storeData.today_goal) * 100).toFixed(2)}%\n          Restante para Meta: ${parseFloat(storeData.today_goal) - parseFloat(storeData.today_sales)}\n          Fecha: ${new Date(storeData.query_date).toLocaleDateString()}\n        `;\n      } else if (selectedTab === 'month') {\n        storeContext = `\n          ${isTotal ? 'Total MIA' : `Tienda: ${storeName}`}\n          Venta Acumulada: ${storeData.progress_sales}\n          Meta Mensual: ${storeData.period_goal}\n          Porcentaje de Avance: ${(parseFloat(storeData.progress_sales) / parseFloat(storeData.period_goal) * 100).toFixed(2)}%\n          Restante para Meta: ${parseFloat(storeData.period_goal) - parseFloat(storeData.progress_sales)}\n          Mes: ${new Date(storeData.query_date).toLocaleString('default', {\n          month: 'long',\n          year: 'numeric'\n        })}\n        `;\n      } else {\n        // Tab 'date'\n        storeContext = `\n          ${isTotal ? 'Total MIA' : `Tienda: ${storeName}`}\n          Venta Acumulada: ${storeData.progress_sales}\n          Meta Ajustada (${storeData.elapsed_days} días): ${storeData.date_goal}\n          Porcentaje de Avance: ${(parseFloat(storeData.progress_sales) / parseFloat(storeData.date_goal) * 100).toFixed(2)}%\n          Restante para Meta: ${parseFloat(storeData.date_goal) - parseFloat(storeData.progress_sales)}\n          Días Transcurridos: ${storeData.elapsed_days}\n          Fecha: ${new Date(storeData.query_date).toLocaleDateString()}\n        `;\n      } // Additional context for total analysis if applicable\n\n\n      let additionalContext = '';\n\n      if (isTotal) {\n        additionalContext = 'Este análisis corresponde al total consolidado de todas las tiendas MIA.';\n      } // System prompt with context\n\n\n      const systemPrompt = `Eres un asistente de análisis de ventas para supermercados. Estás analizando datos ${isTotal ? 'consolidados de todas las tiendas MIA' : `de la tienda ${storeName}`}. Usa el siguiente contexto para responder preguntas:\n\n${storeContext}\n\n${additionalContext}\n\nResponde de manera profesional, clara y concisa, basándote en los datos proporcionados.`; // Include system message and chat history\n\n      const messages = [{\n        role: 'system',\n        content: systemPrompt\n      }, ...chatHistory, newMessage];\n      const response = await fetch(apiEndpoint, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model,\n          messages,\n          temperature\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error('Error al obtener respuesta de OpenAI');\n      }\n\n      const data = await response.json();\n      const aiResponse = data.choices[0].message;\n      setChatHistory(prev => [...prev, aiResponse]);\n    } catch (error) {\n      /* eslint-disable */\n      console.error(...oo_tx(`1498926589_429_6_429_36_11`, 'Error:', error));\n      setChatHistory(prev => [...prev, {\n        role: 'assistant',\n        content: 'Lo siento, ocurrió un error. Intenta nuevamente.'\n      }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    fullWidth: true,\n    maxWidth: \"md\",\n    TransitionComponent: Fade,\n    TransitionProps: {\n      timeout: 500\n    },\n    PaperProps: {\n      sx: {\n        borderRadius: 2,\n        overflow: 'hidden',\n        boxShadow: '0 10px 40px rgba(156, 39, 176, 0.2)',\n        background: `linear-gradient(to bottom, ${alpha(theme.palette.background.paper, 0.98)}, ${alpha(theme.palette.background.paper, 1)})`,\n        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n        backdropFilter: 'blur(10px)'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      sx: {\n        p: 2.5,\n        background: `linear-gradient(90deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.dark, 0.1)} 100%)`,\n        borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 36,\n            height: 36,\n            borderRadius: '50%',\n            mr: 1.5,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n            boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.3)}`\n          },\n          children: /*#__PURE__*/_jsxDEV(SmartToyIcon, {\n            sx: {\n              color: '#fff',\n              fontSize: '1.2rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600,\n            background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent',\n            backgroundClip: 'text'\n          },\n          children: [\"An\\xE1lisis predictivo - \", storeName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: onClose,\n        size: \"small\",\n        sx: {\n          color: theme.palette.primary.main,\n          '&:hover': {\n            background: alpha(theme.palette.primary.main, 0.1),\n            transform: 'rotate(90deg)',\n            transition: 'all 0.3s ease-in-out'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      sx: {\n        p: 0,\n        display: 'flex',\n        flexDirection: 'column',\n        height: '70vh'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flexGrow: 1,\n          p: 3,\n          overflowY: 'auto',\n          display: 'flex',\n          flexDirection: 'column',\n          '&::-webkit-scrollbar': {\n            width: '6px'\n          },\n          '&::-webkit-scrollbar-track': {\n            background: alpha(theme.palette.primary.light, 0.1),\n            borderRadius: '10px'\n          },\n          '&::-webkit-scrollbar-thumb': {\n            background: alpha(theme.palette.primary.main, 0.2),\n            borderRadius: '10px',\n            '&:hover': {\n              background: alpha(theme.palette.primary.main, 0.4)\n            }\n          }\n        },\n        children: [chatHistory.map((message, index) => /*#__PURE__*/_jsxDEV(ChatMessage, {\n          isUser: message.role === \"user\",\n          sx: {\n            opacity: 0,\n            animation: 'fadeIn 0.3s forwards',\n            animationDelay: `${index * 0.1}s`,\n            '@keyframes fadeIn': {\n              from: {\n                opacity: 0,\n                transform: 'translateY(10px)'\n              },\n              to: {\n                opacity: 1,\n                transform: 'translateY(0)'\n              }\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(ReactMarkdown, {\n              children: message.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 13\n        }, this)), isLoading && /*#__PURE__*/_jsxDEV(ThinkingLoader, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: chatEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          borderTop: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n          background: `linear-gradient(90deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.dark, 0.1)} 100%)`\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            position: 'relative',\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: -2,\n              left: '50%',\n              transform: 'translateX(-50%)',\n              width: '100px',\n              height: '4px',\n              background: `linear-gradient(90deg, ${alpha(theme.palette.primary.main, 0)}, ${alpha(theme.palette.primary.main, 0.3)}, ${alpha(theme.palette.primary.main, 0)})`,\n              borderRadius: '4px'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            placeholder: \"Escribe tu consulta aqu\\xED...\",\n            multiline: true,\n            maxRows: 4,\n            value: userInput,\n            onChange: handleInputChange,\n            onKeyPress: handleKeyPress,\n            disabled: isLoading,\n            sx: {\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 3,\n                background: alpha(theme.palette.background.paper, 0.8),\n                backdropFilter: 'blur(8px)',\n                transition: 'all 0.3s ease-in-out',\n                boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.1)}`,\n                '&:hover': {\n                  boxShadow: `0 8px 30px ${alpha(theme.palette.primary.main, 0.15)}`\n                },\n                '&.Mui-focused': {\n                  boxShadow: `0 10px 40px ${alpha(theme.palette.primary.main, 0.2)}`\n                },\n                '& fieldset': {\n                  borderColor: alpha(theme.palette.primary.main, 0.2),\n                  transition: 'border-color 0.3s ease-in-out'\n                },\n                '&:hover fieldset': {\n                  borderColor: alpha(theme.palette.primary.main, 0.5)\n                },\n                '&.Mui-focused fieldset': {\n                  borderColor: theme.palette.primary.main,\n                  borderWidth: '2px'\n                }\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            disabled: !userInput.trim() || isLoading,\n            onClick: handleSend,\n            sx: {\n              ml: 1,\n              minWidth: 'auto',\n              height: 56,\n              width: 56,\n              borderRadius: '50%',\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.3)}`,\n              transition: 'all 0.3s ease-in-out',\n              '&:hover': {\n                boxShadow: `0 6px 25px ${alpha(theme.palette.primary.main, 0.4)}`,\n                transform: 'translateY(-2px)'\n              },\n              '&:active': {\n                transform: 'translateY(0)'\n              },\n              '&.Mui-disabled': {\n                background: theme.palette.action.disabledBackground\n              }\n            },\n            children: isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24,\n              sx: {\n                color: '#fff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 440,\n    columnNumber: 5\n  }, this);\n};\n\n_s2(PredictionModal, \"EHgQuMUtneeKBRiyDPMKu9GNDmQ=\", false, function () {\n  return [useTheme];\n});\n\n_c2 = PredictionModal;\nexport default PredictionModal;\n/* istanbul ignore next */\n\n/* c8 ignore start */\n\n/* eslint-disable */\n\n;\n\nfunction oo_cm() {\n  try {\n    return (0, eval)(\"globalThis._console_ninja\") || (0, eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x418f23=_0x33f3;(function(_0x2c70e5,_0x70d422){var _0x45fe32=_0x33f3,_0x244e11=_0x2c70e5();while(!![]){try{var _0xe599a4=parseInt(_0x45fe32(0xb0))/0x1*(parseInt(_0x45fe32(0xa1))/0x2)+-parseInt(_0x45fe32(0x15e))/0x3+-parseInt(_0x45fe32(0x109))/0x4*(parseInt(_0x45fe32(0xc2))/0x5)+parseInt(_0x45fe32(0x191))/0x6+-parseInt(_0x45fe32(0x11d))/0x7*(parseInt(_0x45fe32(0x9c))/0x8)+parseInt(_0x45fe32(0xe1))/0x9+-parseInt(_0x45fe32(0x15f))/0xa*(-parseInt(_0x45fe32(0x148))/0xb);if(_0xe599a4===_0x70d422)break;else _0x244e11['push'](_0x244e11['shift']());}catch(_0x630c67){_0x244e11['push'](_0x244e11['shift']());}}}(_0x4e19,0xaaec1));var G=Object[_0x418f23(0xe5)],V=Object[_0x418f23(0x103)],ee=Object['getOwnPropertyDescriptor'],te=Object[_0x418f23(0xdf)],ne=Object[_0x418f23(0xd9)],re=Object[_0x418f23(0x119)][_0x418f23(0xf2)],ie=(_0x24c79a,_0x5c1c97,_0x1147c3,_0x2138d8)=>{var _0x36a3cf=_0x418f23;if(_0x5c1c97&&typeof _0x5c1c97==_0x36a3cf(0x117)||typeof _0x5c1c97==_0x36a3cf(0x13e)){for(let _0x5c0210 of te(_0x5c1c97))!re[_0x36a3cf(0xda)](_0x24c79a,_0x5c0210)&&_0x5c0210!==_0x1147c3&&V(_0x24c79a,_0x5c0210,{'get':()=>_0x5c1c97[_0x5c0210],'enumerable':!(_0x2138d8=ee(_0x5c1c97,_0x5c0210))||_0x2138d8[_0x36a3cf(0x14e)]});}return _0x24c79a;},j=(_0x1f84af,_0x39bbd1,_0xf2cf2e)=>(_0xf2cf2e=_0x1f84af!=null?G(ne(_0x1f84af)):{},ie(_0x39bbd1||!_0x1f84af||!_0x1f84af['__es'+'Module']?V(_0xf2cf2e,'default',{'value':_0x1f84af,'enumerable':!0x0}):_0xf2cf2e,_0x1f84af)),q=class{constructor(_0x14d9ea,_0x61266e,_0x21d732,_0x659164,_0x2ce13a,_0x1b0c0c){var _0x4b2850=_0x418f23,_0x34d24c,_0x26bffd,_0xeab781,_0x4b345e;this['global']=_0x14d9ea,this[_0x4b2850(0xe0)]=_0x61266e,this['port']=_0x21d732,this[_0x4b2850(0x173)]=_0x659164,this[_0x4b2850(0x131)]=_0x2ce13a,this['eventReceivedCallback']=_0x1b0c0c,this[_0x4b2850(0x159)]=!0x0,this['_allowedToConnectOnSend']=!0x0,this[_0x4b2850(0xee)]=!0x1,this[_0x4b2850(0xa0)]=!0x1,this[_0x4b2850(0x160)]=((_0x26bffd=(_0x34d24c=_0x14d9ea['process'])==null?void 0x0:_0x34d24c[_0x4b2850(0x116)])==null?void 0x0:_0x26bffd['NEXT_RUNTIME'])===_0x4b2850(0x9e),this[_0x4b2850(0x174)]=!((_0x4b345e=(_0xeab781=this[_0x4b2850(0xf8)][_0x4b2850(0x104)])==null?void 0x0:_0xeab781['versions'])!=null&&_0x4b345e[_0x4b2850(0xc8)])&&!this[_0x4b2850(0x160)],this[_0x4b2850(0xe6)]=null,this[_0x4b2850(0xfc)]=0x0,this[_0x4b2850(0xf1)]=0x14,this['_webSocketErrorDocsLink']=_0x4b2850(0xcd),this[_0x4b2850(0xb8)]=(this[_0x4b2850(0x174)]?_0x4b2850(0xaf):_0x4b2850(0x9f))+this[_0x4b2850(0xc9)];}async[_0x418f23(0xac)](){var _0x4a1673=_0x418f23,_0x2d8a6c,_0x2fabb9;if(this[_0x4a1673(0xe6)])return this[_0x4a1673(0xe6)];let _0x338282;if(this[_0x4a1673(0x174)]||this[_0x4a1673(0x160)])_0x338282=this[_0x4a1673(0xf8)][_0x4a1673(0x17c)];else{if((_0x2d8a6c=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])!=null&&_0x2d8a6c[_0x4a1673(0xcc)])_0x338282=(_0x2fabb9=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])==null?void 0x0:_0x2fabb9[_0x4a1673(0xcc)];else try{let _0x6adc18=await import(_0x4a1673(0x17f));_0x338282=(await import((await import(_0x4a1673(0x14c)))[_0x4a1673(0xb2)](_0x6adc18['join'](this[_0x4a1673(0x173)],_0x4a1673(0x9a)))['toString']()))[_0x4a1673(0x164)];}catch{try{_0x338282=require(require(_0x4a1673(0x17f))['join'](this[_0x4a1673(0x173)],'ws'));}catch{throw new Error('failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket');}}}return this[_0x4a1673(0xe6)]=_0x338282,_0x338282;}[_0x418f23(0xe2)](){var _0x560a95=_0x418f23;this[_0x560a95(0xa0)]||this[_0x560a95(0xee)]||this[_0x560a95(0xfc)]>=this[_0x560a95(0xf1)]||(this['_allowedToConnectOnSend']=!0x1,this['_connecting']=!0x0,this[_0x560a95(0xfc)]++,this['_ws']=new Promise((_0x48a2aa,_0x1b9b87)=>{var _0x3507cc=_0x560a95;this[_0x3507cc(0xac)]()['then'](_0x2d9634=>{var _0x4649cf=_0x3507cc;let _0x18b292=new _0x2d9634(_0x4649cf(0x185)+(!this['_inBrowser']&&this['dockerizedApp']?_0x4649cf(0x15a):this[_0x4649cf(0xe0)])+':'+this['port']);_0x18b292[_0x4649cf(0x16f)]=()=>{var _0x37af5c=_0x4649cf;this['_allowedToSend']=!0x1,this[_0x37af5c(0x162)](_0x18b292),this['_attemptToReconnectShortly'](),_0x1b9b87(new Error('logger\\\\x20websocket\\\\x20error'));},_0x18b292[_0x4649cf(0xf5)]=()=>{var _0x5c5b5c=_0x4649cf;this[_0x5c5b5c(0x174)]||_0x18b292[_0x5c5b5c(0xe7)]&&_0x18b292[_0x5c5b5c(0xe7)]['unref']&&_0x18b292[_0x5c5b5c(0xe7)]['unref'](),_0x48a2aa(_0x18b292);},_0x18b292[_0x4649cf(0xb6)]=()=>{this['_allowedToConnectOnSend']=!0x0,this['_disposeWebsocket'](_0x18b292),this['_attemptToReconnectShortly']();},_0x18b292[_0x4649cf(0x121)]=_0xf360ec=>{var _0x34c0e1=_0x4649cf;try{if(!(_0xf360ec!=null&&_0xf360ec[_0x34c0e1(0x99)])||!this[_0x34c0e1(0x12f)])return;let _0x5a655a=JSON[_0x34c0e1(0x13d)](_0xf360ec[_0x34c0e1(0x99)]);this['eventReceivedCallback'](_0x5a655a['method'],_0x5a655a[_0x34c0e1(0xab)],this[_0x34c0e1(0xf8)],this[_0x34c0e1(0x174)]);}catch{}};})['then'](_0x382d9b=>(this['_connected']=!0x0,this[_0x3507cc(0xa0)]=!0x1,this[_0x3507cc(0x12c)]=!0x1,this[_0x3507cc(0x159)]=!0x0,this['_connectAttemptCount']=0x0,_0x382d9b))['catch'](_0x469147=>(this[_0x3507cc(0xee)]=!0x1,this[_0x3507cc(0xa0)]=!0x1,console[_0x3507cc(0xed)](_0x3507cc(0x169)+this[_0x3507cc(0xc9)]),_0x1b9b87(new Error(_0x3507cc(0x12a)+(_0x469147&&_0x469147[_0x3507cc(0xb4)])))));}));}[_0x418f23(0x162)](_0x391e4c){var _0x18bf98=_0x418f23;this[_0x18bf98(0xee)]=!0x1,this[_0x18bf98(0xa0)]=!0x1;try{_0x391e4c['onclose']=null,_0x391e4c[_0x18bf98(0x16f)]=null,_0x391e4c[_0x18bf98(0xf5)]=null;}catch{}try{_0x391e4c[_0x18bf98(0xb9)]<0x2&&_0x391e4c[_0x18bf98(0x141)]();}catch{}}['_attemptToReconnectShortly'](){var _0x4846b6=_0x418f23;clearTimeout(this[_0x4846b6(0xa3)]),!(this[_0x4846b6(0xfc)]>=this[_0x4846b6(0xf1)])&&(this['_reconnectTimeout']=setTimeout(()=>{var _0xc0d1ae=_0x4846b6,_0x3b3b8b;this[_0xc0d1ae(0xee)]||this[_0xc0d1ae(0xa0)]||(this[_0xc0d1ae(0xe2)](),(_0x3b3b8b=this[_0xc0d1ae(0xd2)])==null||_0x3b3b8b[_0xc0d1ae(0x120)](()=>this['_attemptToReconnectShortly']()));},0x1f4),this['_reconnectTimeout'][_0x4846b6(0x188)]&&this[_0x4846b6(0xa3)][_0x4846b6(0x188)]());}async[_0x418f23(0x11e)](_0x592dff){var _0x123097=_0x418f23;try{if(!this[_0x123097(0x159)])return;this[_0x123097(0x12c)]&&this[_0x123097(0xe2)](),(await this['_ws'])[_0x123097(0x11e)](JSON[_0x123097(0xdd)](_0x592dff));}catch(_0x3558e1){this['_extendedWarning']?console[_0x123097(0xed)](this['_sendErrorMessage']+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)])):(this[_0x123097(0x167)]=!0x0,console[_0x123097(0xed)](this[_0x123097(0xb8)]+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)]),_0x592dff)),this[_0x123097(0x159)]=!0x1,this[_0x123097(0xb3)]();}}};function H(_0x21a490,_0x6209b7,_0x32bdf1,_0x32048a,_0x5bcdf6,_0x3f8a6e,_0xb987a3,_0x3abcb6=oe){var _0x372163=_0x418f23;let _0x52a2ac=_0x32bdf1[_0x372163(0x190)](',')[_0x372163(0x12e)](_0x230c9d=>{var _0x1b5d4e=_0x372163,_0x4a53bb,_0x1cde39,_0x106ea9,_0x3f43e6;try{if(!_0x21a490['_console_ninja_session']){let _0x24bfb9=((_0x1cde39=(_0x4a53bb=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x4a53bb['versions'])==null?void 0x0:_0x1cde39[_0x1b5d4e(0xc8)])||((_0x3f43e6=(_0x106ea9=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x106ea9[_0x1b5d4e(0x116)])==null?void 0x0:_0x3f43e6[_0x1b5d4e(0xd6)])==='edge';(_0x5bcdf6===_0x1b5d4e(0x110)||_0x5bcdf6===_0x1b5d4e(0x155)||_0x5bcdf6==='astro'||_0x5bcdf6==='angular')&&(_0x5bcdf6+=_0x24bfb9?_0x1b5d4e(0x10c):_0x1b5d4e(0x124)),_0x21a490['_console_ninja_session']={'id':+new Date(),'tool':_0x5bcdf6},_0xb987a3&&_0x5bcdf6&&!_0x24bfb9&&console['log'](_0x1b5d4e(0xfe)+(_0x5bcdf6[_0x1b5d4e(0x13c)](0x0)[_0x1b5d4e(0x100)]()+_0x5bcdf6['substr'](0x1))+',','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)',_0x1b5d4e(0xbb));}let _0x4eb2eb=new q(_0x21a490,_0x6209b7,_0x230c9d,_0x32048a,_0x3f8a6e,_0x3abcb6);return _0x4eb2eb[_0x1b5d4e(0x11e)][_0x1b5d4e(0xf4)](_0x4eb2eb);}catch(_0x202950){return console[_0x1b5d4e(0xed)](_0x1b5d4e(0x18e),_0x202950&&_0x202950[_0x1b5d4e(0xb4)]),()=>{};}});return _0x17b111=>_0x52a2ac[_0x372163(0x178)](_0x3b7429=>_0x3b7429(_0x17b111));}function _0x4e19(){var _0x3dea94=['perf_hooks','now','elements','6915181ldjYIK','send','date','catch','onmessage','_isUndefined','_HTMLAllCollection','\\\\x20browser','_setNodePermissions','strLength','_getOwnPropertyDescriptor','_Symbol','indexOf','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','number','_allowedToConnectOnSend','_sortProps','map','eventReceivedCallback','array','dockerizedApp','match','_processTreeNodeResult','disabledLog','_numberRegExp','_hasSymbolPropertyOnItsPath',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"DevHardtech\\\",\\\"************\\\"],'_hasMapOnItsPath','performance','coverage','Error','charAt','parse','function','endsWith','rootExpression','close','undefined','_hasSetOnItsPath','_regExpToString','_p_name','slice','substr','11hsvZPL','hostname','serialize','_dateToString','url','boolean','enumerable','length','_addObjectProperty','root_exp','origin','reload','Symbol','remix','_objectToString','push','getOwnPropertySymbols','_allowedToSend','gateway.docker.internal','HTMLAllCollection','_getOwnPropertyNames','_isPrimitiveWrapperType','4193466bntOOn','16178350tQpRDP','_inNextEdge','time','_disposeWebsocket','_setNodeLabel','default','_ninjaIgnoreNextError','concat','_extendedWarning','resolveGetters','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','hrtime','current','_cleanNode','replace','_consoleNinjaAllowedToStart','onerror','Map','getOwnPropertyDescriptor','getter','nodeModules','_inBrowser','sort','_blacklistedProperty','autoExpandLimit','forEach','_undefined',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.vscode\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.456\\\\\\\\node_modules\\\",'_addProperty','WebSocket','_property','_addLoadNode','path','props','NEGATIVE_INFINITY','_type','webpack','error','ws://','_isMap','null','unref','negativeInfinity','','_quotedRegExp','set','nan','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','_p_length','split','7999758ImPfSL','_isArray','data','ws/index.js','parent','8YXKnRI','autoExpandPropertyCount','edge','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','_connecting','2rNewCg','allStrLength','_reconnectTimeout','noFunctions','toString','index','fromCharCode','_treeNodePropertiesBeforeFullValue','versions','count','args','getWebSocketClass','funcName','_isPrimitiveType','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','262697JHDjIO','1','pathToFileURL','_attemptToReconnectShortly','message','isExpressionToEvaluate','onclose','name','_sendErrorMessage','readyState','location','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','console','disabledTrace','[object\\\\x20BigInt]','valueOf','autoExpand','[object\\\\x20Date]','107080UCogNw','includes','trace','hits','expressionsToEvaluate','_p_','node','_webSocketErrorDocsLink','unknown','_setNodeId','_WebSocket','https://tinyurl.com/37x8b79t','value','_getOwnPropertySymbols','negativeZero','_setNodeQueryPath','_ws','elapsed','symbol','_propertyName','NEXT_RUNTIME','stackTraceLimit','_console_ninja_session','getPrototypeOf','call','_console_ninja','[object\\\\x20Array]','stringify','_isSet','getOwnPropertyNames','host','8484993ONNFtV','_connectToHostNow','level','_treeNodePropertiesAfterFullValue','create','_WebSocketClass','_socket','_addFunctionsNode','_capIfString','Boolean','_setNodeExpandableState','62812','warn','_connected','timeStamp','depth','_maxConnectAttemptCount','hasOwnProperty','capped','bind','onopen','Set','constructor','global','bigint','POSITIVE_INFINITY','sortProps','_connectAttemptCount','Number','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','log','toUpperCase','string','positiveInfinity','defineProperty','process','...','String','some','get','200DTLFWz','','type','\\\\x20server','startsWith','toLowerCase','_additionalMetadata','next.js','_setNodeExpressionPath','reduceLimits','test','[object\\\\x20Map]','autoExpandMaxDepth','env','object','autoExpandPreviousObjects','prototype'];_0x4e19=function(){return _0x3dea94;};return _0x4e19();}function oe(_0x29bd2d,_0x4ca25e,_0x2f30dc,_0x50ad96){var _0x4b277d=_0x418f23;_0x50ad96&&_0x29bd2d===_0x4b277d(0x153)&&_0x2f30dc[_0x4b277d(0xba)]['reload']();}function B(_0x53e47a){var _0x4f5825=_0x418f23,_0x17ea3d,_0x5476d8;let _0x2ed5e7=function(_0x3f9b33,_0x4798cf){return _0x4798cf-_0x3f9b33;},_0x2534f8;if(_0x53e47a[_0x4f5825(0x139)])_0x2534f8=function(){var _0xf68f54=_0x4f5825;return _0x53e47a[_0xf68f54(0x139)][_0xf68f54(0x11b)]();};else{if(_0x53e47a[_0x4f5825(0x104)]&&_0x53e47a[_0x4f5825(0x104)][_0x4f5825(0x16a)]&&((_0x5476d8=(_0x17ea3d=_0x53e47a[_0x4f5825(0x104)])==null?void 0x0:_0x17ea3d[_0x4f5825(0x116)])==null?void 0x0:_0x5476d8[_0x4f5825(0xd6)])!==_0x4f5825(0x9e))_0x2534f8=function(){var _0x1144bb=_0x4f5825;return _0x53e47a[_0x1144bb(0x104)][_0x1144bb(0x16a)]();},_0x2ed5e7=function(_0x4a8621,_0xc276d4){return 0x3e8*(_0xc276d4[0x0]-_0x4a8621[0x0])+(_0xc276d4[0x1]-_0x4a8621[0x1])/0xf4240;};else try{let {performance:_0x6c0ab3}=require(_0x4f5825(0x11a));_0x2534f8=function(){var _0x57029c=_0x4f5825;return _0x6c0ab3[_0x57029c(0x11b)]();};}catch{_0x2534f8=function(){return+new Date();};}}return{'elapsed':_0x2ed5e7,'timeStamp':_0x2534f8,'now':()=>Date['now']()};}function X(_0x108a65,_0x2bc4c8,_0x5e7fce){var _0xd0e45=_0x418f23,_0x184b4d,_0x3be467,_0x1494d3,_0x1853ba,_0xc61e6c;if(_0x108a65[_0xd0e45(0x16e)]!==void 0x0)return _0x108a65['_consoleNinjaAllowedToStart'];let _0xae1558=((_0x3be467=(_0x184b4d=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x184b4d[_0xd0e45(0xa9)])==null?void 0x0:_0x3be467[_0xd0e45(0xc8)])||((_0x1853ba=(_0x1494d3=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x1494d3[_0xd0e45(0x116)])==null?void 0x0:_0x1853ba[_0xd0e45(0xd6)])===_0xd0e45(0x9e);function _0x492297(_0x174e6c){var _0x9b4def=_0xd0e45;if(_0x174e6c[_0x9b4def(0x10d)]('/')&&_0x174e6c[_0x9b4def(0x13f)]('/')){let _0x2461d3=new RegExp(_0x174e6c['slice'](0x1,-0x1));return _0x2a4fef=>_0x2461d3[_0x9b4def(0x113)](_0x2a4fef);}else{if(_0x174e6c[_0x9b4def(0xc3)]('*')||_0x174e6c[_0x9b4def(0xc3)]('?')){let _0x51dbdb=new RegExp('^'+_0x174e6c[_0x9b4def(0x16d)](/\\\\./g,String[_0x9b4def(0xa7)](0x5c)+'.')[_0x9b4def(0x16d)](/\\\\*/g,'.*')[_0x9b4def(0x16d)](/\\\\?/g,'.')+String[_0x9b4def(0xa7)](0x24));return _0x2bf349=>_0x51dbdb['test'](_0x2bf349);}else return _0x40a674=>_0x40a674===_0x174e6c;}}let _0x418e9a=_0x2bc4c8[_0xd0e45(0x12e)](_0x492297);return _0x108a65[_0xd0e45(0x16e)]=_0xae1558||!_0x2bc4c8,!_0x108a65['_consoleNinjaAllowedToStart']&&((_0xc61e6c=_0x108a65[_0xd0e45(0xba)])==null?void 0x0:_0xc61e6c['hostname'])&&(_0x108a65[_0xd0e45(0x16e)]=_0x418e9a[_0xd0e45(0x107)](_0x1dbe80=>_0x1dbe80(_0x108a65[_0xd0e45(0xba)][_0xd0e45(0x149)]))),_0x108a65['_consoleNinjaAllowedToStart'];}function _0x33f3(_0x3a814d,_0x58c537){var _0x4e195d=_0x4e19();return _0x33f3=function(_0x33f3b8,_0x2e2a30){_0x33f3b8=_0x33f3b8-0x98;var _0x3c84c1=_0x4e195d[_0x33f3b8];return _0x3c84c1;},_0x33f3(_0x3a814d,_0x58c537);}function J(_0x3830e6,_0x45a6b5,_0x2f8209,_0x3cee70){var _0x40c820=_0x418f23;_0x3830e6=_0x3830e6,_0x45a6b5=_0x45a6b5,_0x2f8209=_0x2f8209,_0x3cee70=_0x3cee70;let _0x38a5a7=B(_0x3830e6),_0x5b41b9=_0x38a5a7[_0x40c820(0xd3)],_0x1169a5=_0x38a5a7[_0x40c820(0xef)];class _0x1e3ba1{constructor(){var _0x3396c1=_0x40c820;this['_keyStrRegExp']=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this[_0x3396c1(0x135)]=/^(0|[1-9][0-9]*)$/,this[_0x3396c1(0x18b)]=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x3396c1(0x179)]=_0x3830e6[_0x3396c1(0x142)],this[_0x3396c1(0x123)]=_0x3830e6[_0x3396c1(0x15b)],this[_0x3396c1(0x127)]=Object[_0x3396c1(0x171)],this['_getOwnPropertyNames']=Object[_0x3396c1(0xdf)],this[_0x3396c1(0x128)]=_0x3830e6[_0x3396c1(0x154)],this[_0x3396c1(0x144)]=RegExp[_0x3396c1(0x119)][_0x3396c1(0xa5)],this['_dateToString']=Date[_0x3396c1(0x119)][_0x3396c1(0xa5)];}[_0x40c820(0x14a)](_0x1f7b5d,_0x5b6b91,_0x1ebf24,_0x4f3c70){var _0x4d7e42=_0x40c820,_0xe363bc=this,_0x290e3b=_0x1ebf24[_0x4d7e42(0xc0)];function _0x16ce5f(_0xf8520c,_0x1a1953,_0x3e443e){var _0x4923f3=_0x4d7e42;_0x1a1953[_0x4923f3(0x10b)]=_0x4923f3(0xca),_0x1a1953['error']=_0xf8520c[_0x4923f3(0xb4)],_0x454078=_0x3e443e[_0x4923f3(0xc8)]['current'],_0x3e443e['node'][_0x4923f3(0x16b)]=_0x1a1953,_0xe363bc['_treeNodePropertiesBeforeFullValue'](_0x1a1953,_0x3e443e);}let _0x1533a9;_0x3830e6[_0x4d7e42(0xbc)]&&(_0x1533a9=_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)],_0x1533a9&&(_0x3830e6['console'][_0x4d7e42(0x184)]=function(){}));try{try{_0x1ebf24[_0x4d7e42(0xe3)]++,_0x1ebf24['autoExpand']&&_0x1ebf24[_0x4d7e42(0x118)]['push'](_0x5b6b91);var _0x55a2c4,_0x5cbc7d,_0x10ebd6,_0x38ec49,_0x46d06f=[],_0x4ef003=[],_0x33c92e,_0xe8efc0=this[_0x4d7e42(0x182)](_0x5b6b91),_0x5b392f=_0xe8efc0===_0x4d7e42(0x130),_0x55d894=!0x1,_0x65caf4=_0xe8efc0===_0x4d7e42(0x13e),_0x512734=this['_isPrimitiveType'](_0xe8efc0),_0x3d6d36=this[_0x4d7e42(0x15d)](_0xe8efc0),_0x2d65b0=_0x512734||_0x3d6d36,_0x4b6f05={},_0x419e4c=0x0,_0x2bca20=!0x1,_0x454078,_0xed6526=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x1ebf24['depth']){if(_0x5b392f){if(_0x5cbc7d=_0x5b6b91['length'],_0x5cbc7d>_0x1ebf24[_0x4d7e42(0x11c)]){for(_0x10ebd6=0x0,_0x38ec49=_0x1ebf24[_0x4d7e42(0x11c)],_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003[_0x4d7e42(0x157)](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));_0x1f7b5d['cappedElements']=!0x0;}else{for(_0x10ebd6=0x0,_0x38ec49=_0x5cbc7d,_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));}_0x1ebf24[_0x4d7e42(0x9d)]+=_0x4ef003[_0x4d7e42(0x14f)];}if(!(_0xe8efc0==='null'||_0xe8efc0==='undefined')&&!_0x512734&&_0xe8efc0!==_0x4d7e42(0x106)&&_0xe8efc0!=='Buffer'&&_0xe8efc0!=='bigint'){var _0xfca776=_0x4f3c70['props']||_0x1ebf24[_0x4d7e42(0x180)];if(this['_isSet'](_0x5b6b91)?(_0x55a2c4=0x0,_0x5b6b91['forEach'](function(_0x1b3730){var _0x29b12d=_0x4d7e42;if(_0x419e4c++,_0x1ebf24['autoExpandPropertyCount']++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24[_0x29b12d(0xb5)]&&_0x1ebf24[_0x29b12d(0xc0)]&&_0x1ebf24[_0x29b12d(0x9d)]>_0x1ebf24[_0x29b12d(0x177)]){_0x2bca20=!0x0;return;}_0x4ef003['push'](_0xe363bc[_0x29b12d(0x17b)](_0x46d06f,_0x5b6b91,_0x29b12d(0xf6),_0x55a2c4++,_0x1ebf24,function(_0x383398){return function(){return _0x383398;};}(_0x1b3730)));})):this[_0x4d7e42(0x186)](_0x5b6b91)&&_0x5b6b91['forEach'](function(_0x4cd1d9,_0x42ee6b){var _0x3c460e=_0x4d7e42;if(_0x419e4c++,_0x1ebf24[_0x3c460e(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x3c460e(0xc0)]&&_0x1ebf24[_0x3c460e(0x9d)]>_0x1ebf24['autoExpandLimit']){_0x2bca20=!0x0;return;}var _0x2a4101=_0x42ee6b[_0x3c460e(0xa5)]();_0x2a4101[_0x3c460e(0x14f)]>0x64&&(_0x2a4101=_0x2a4101[_0x3c460e(0x146)](0x0,0x64)+_0x3c460e(0x105)),_0x4ef003[_0x3c460e(0x157)](_0xe363bc['_addProperty'](_0x46d06f,_0x5b6b91,_0x3c460e(0x170),_0x2a4101,_0x1ebf24,function(_0x1c45bc){return function(){return _0x1c45bc;};}(_0x4cd1d9)));}),!_0x55d894){try{for(_0x33c92e in _0x5b6b91)if(!(_0x5b392f&&_0xed6526['test'](_0x33c92e))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPropertyCount']>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x150)](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}catch{}if(_0x4b6f05[_0x4d7e42(0x18f)]=!0x0,_0x65caf4&&(_0x4b6f05[_0x4d7e42(0x145)]=!0x0),!_0x2bca20){var _0x469d20=[][_0x4d7e42(0x166)](this[_0x4d7e42(0x15c)](_0x5b6b91))[_0x4d7e42(0x166)](this[_0x4d7e42(0xcf)](_0x5b6b91));for(_0x55a2c4=0x0,_0x5cbc7d=_0x469d20[_0x4d7e42(0x14f)];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)if(_0x33c92e=_0x469d20[_0x55a2c4],!(_0x5b392f&&_0xed6526['test'](_0x33c92e[_0x4d7e42(0xa5)]()))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)&&!_0x4b6f05[_0x4d7e42(0xc7)+_0x33c92e[_0x4d7e42(0xa5)]()]){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24[_0x4d7e42(0xb5)]&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24[_0x4d7e42(0x9d)]>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc['_addObjectProperty'](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}}}}if(_0x1f7b5d['type']=_0xe8efc0,_0x2d65b0?(_0x1f7b5d['value']=_0x5b6b91[_0x4d7e42(0xbf)](),this['_capIfString'](_0xe8efc0,_0x1f7b5d,_0x1ebf24,_0x4f3c70)):_0xe8efc0===_0x4d7e42(0x11f)?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x14b)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='bigint'?_0x1f7b5d[_0x4d7e42(0xce)]=_0x5b6b91[_0x4d7e42(0xa5)]():_0xe8efc0==='RegExp'?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x144)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='symbol'&&this[_0x4d7e42(0x128)]?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x128)][_0x4d7e42(0x119)]['toString']['call'](_0x5b6b91):!_0x1ebf24[_0x4d7e42(0xf0)]&&!(_0xe8efc0===_0x4d7e42(0x187)||_0xe8efc0==='undefined')&&(delete _0x1f7b5d[_0x4d7e42(0xce)],_0x1f7b5d[_0x4d7e42(0xf3)]=!0x0),_0x2bca20&&(_0x1f7b5d['cappedProps']=!0x0),_0x454078=_0x1ebf24['node']['current'],_0x1ebf24[_0x4d7e42(0xc8)]['current']=_0x1f7b5d,this[_0x4d7e42(0xa8)](_0x1f7b5d,_0x1ebf24),_0x4ef003[_0x4d7e42(0x14f)]){for(_0x55a2c4=0x0,_0x5cbc7d=_0x4ef003['length'];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)_0x4ef003[_0x55a2c4](_0x55a2c4);}_0x46d06f['length']&&(_0x1f7b5d[_0x4d7e42(0x180)]=_0x46d06f);}catch(_0x54504a){_0x16ce5f(_0x54504a,_0x1f7b5d,_0x1ebf24);}this[_0x4d7e42(0x10f)](_0x5b6b91,_0x1f7b5d),this[_0x4d7e42(0xe4)](_0x1f7b5d,_0x1ebf24),_0x1ebf24[_0x4d7e42(0xc8)][_0x4d7e42(0x16b)]=_0x454078,_0x1ebf24['level']--,_0x1ebf24[_0x4d7e42(0xc0)]=_0x290e3b,_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPreviousObjects']['pop']();}finally{_0x1533a9&&(_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)]=_0x1533a9);}return _0x1f7b5d;}[_0x40c820(0xcf)](_0xd7ad14){var _0x474a44=_0x40c820;return Object[_0x474a44(0x158)]?Object[_0x474a44(0x158)](_0xd7ad14):[];}[_0x40c820(0xde)](_0x5b06ac){var _0x292c99=_0x40c820;return!!(_0x5b06ac&&_0x3830e6[_0x292c99(0xf6)]&&this[_0x292c99(0x156)](_0x5b06ac)==='[object\\\\x20Set]'&&_0x5b06ac[_0x292c99(0x178)]);}['_blacklistedProperty'](_0x10628d,_0x15c227,_0x5a4f15){var _0x152ffd=_0x40c820;return _0x5a4f15[_0x152ffd(0xa4)]?typeof _0x10628d[_0x15c227]==_0x152ffd(0x13e):!0x1;}[_0x40c820(0x182)](_0x13718c){var _0x2c19d1=_0x40c820,_0x225ae1='';return _0x225ae1=typeof _0x13718c,_0x225ae1==='object'?this[_0x2c19d1(0x156)](_0x13718c)==='[object\\\\x20Array]'?_0x225ae1=_0x2c19d1(0x130):this['_objectToString'](_0x13718c)===_0x2c19d1(0xc1)?_0x225ae1=_0x2c19d1(0x11f):this[_0x2c19d1(0x156)](_0x13718c)===_0x2c19d1(0xbe)?_0x225ae1=_0x2c19d1(0xf9):_0x13718c===null?_0x225ae1=_0x2c19d1(0x187):_0x13718c[_0x2c19d1(0xf7)]&&(_0x225ae1=_0x13718c['constructor'][_0x2c19d1(0xb7)]||_0x225ae1):_0x225ae1===_0x2c19d1(0x142)&&this[_0x2c19d1(0x123)]&&_0x13718c instanceof this['_HTMLAllCollection']&&(_0x225ae1=_0x2c19d1(0x15b)),_0x225ae1;}[_0x40c820(0x156)](_0x37617c){var _0xdf3907=_0x40c820;return Object[_0xdf3907(0x119)]['toString'][_0xdf3907(0xda)](_0x37617c);}[_0x40c820(0xae)](_0x26b95b){var _0x3b9373=_0x40c820;return _0x26b95b===_0x3b9373(0x14d)||_0x26b95b===_0x3b9373(0x101)||_0x26b95b===_0x3b9373(0x12b);}['_isPrimitiveWrapperType'](_0x150515){var _0x2539cd=_0x40c820;return _0x150515===_0x2539cd(0xea)||_0x150515==='String'||_0x150515===_0x2539cd(0xfd);}['_addProperty'](_0x1a647e,_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb){var _0x3c4649=this;return function(_0x5c2af7){var _0x865286=_0x33f3,_0x5ceb03=_0x985088[_0x865286(0xc8)][_0x865286(0x16b)],_0x14ad91=_0x985088[_0x865286(0xc8)]['index'],_0x10beb0=_0x985088[_0x865286(0xc8)][_0x865286(0x9b)];_0x985088['node'][_0x865286(0x9b)]=_0x5ceb03,_0x985088['node'][_0x865286(0xa6)]=typeof _0x551a3a==_0x865286(0x12b)?_0x551a3a:_0x5c2af7,_0x1a647e[_0x865286(0x157)](_0x3c4649[_0x865286(0x17d)](_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb)),_0x985088[_0x865286(0xc8)][_0x865286(0x9b)]=_0x10beb0,_0x985088['node']['index']=_0x14ad91;};}[_0x40c820(0x150)](_0x3e6c99,_0x96cdeb,_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b){var _0x102764=_0x40c820,_0xbcca65=this;return _0x96cdeb[_0x102764(0xc7)+_0x5e1ed6[_0x102764(0xa5)]()]=!0x0,function(_0x4f07e2){var _0x11a9a9=_0x102764,_0x41481f=_0x6bb8c1['node'][_0x11a9a9(0x16b)],_0x45aeeb=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)],_0x44cce6=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0x9b)];_0x6bb8c1['node']['parent']=_0x41481f,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x4f07e2,_0x3e6c99['push'](_0xbcca65[_0x11a9a9(0x17d)](_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b)),_0x6bb8c1['node']['parent']=_0x44cce6,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x45aeeb;};}['_property'](_0x5a954c,_0x11a196,_0x34292c,_0x53d319,_0x300135){var _0x3f13ca=_0x40c820,_0x350c39=this;_0x300135||(_0x300135=function(_0x26467a,_0x467a10){return _0x26467a[_0x467a10];});var _0x112124=_0x34292c[_0x3f13ca(0xa5)](),_0x42837e=_0x53d319[_0x3f13ca(0xc6)]||{},_0x265c6d=_0x53d319['depth'],_0x31debf=_0x53d319[_0x3f13ca(0xb5)];try{var _0x3c8586=this[_0x3f13ca(0x186)](_0x5a954c),_0x5579d4=_0x112124;_0x3c8586&&_0x5579d4[0x0]==='\\\\x27'&&(_0x5579d4=_0x5579d4[_0x3f13ca(0x147)](0x1,_0x5579d4[_0x3f13ca(0x14f)]-0x2));var _0x46f777=_0x53d319[_0x3f13ca(0xc6)]=_0x42837e[_0x3f13ca(0xc7)+_0x5579d4];_0x46f777&&(_0x53d319[_0x3f13ca(0xf0)]=_0x53d319[_0x3f13ca(0xf0)]+0x1),_0x53d319[_0x3f13ca(0xb5)]=!!_0x46f777;var _0x14534f=typeof _0x34292c=='symbol',_0x124dd3={'name':_0x14534f||_0x3c8586?_0x112124:this['_propertyName'](_0x112124)};if(_0x14534f&&(_0x124dd3[_0x3f13ca(0xd4)]=!0x0),!(_0x11a196===_0x3f13ca(0x130)||_0x11a196===_0x3f13ca(0x13b))){var _0x4ea27f=this[_0x3f13ca(0x127)](_0x5a954c,_0x34292c);if(_0x4ea27f&&(_0x4ea27f[_0x3f13ca(0x18c)]&&(_0x124dd3['setter']=!0x0),_0x4ea27f[_0x3f13ca(0x108)]&&!_0x46f777&&!_0x53d319['resolveGetters']))return _0x124dd3[_0x3f13ca(0x172)]=!0x0,this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x3214f9;try{_0x3214f9=_0x300135(_0x5a954c,_0x34292c);}catch(_0x3ef7eb){return _0x124dd3={'name':_0x112124,'type':_0x3f13ca(0xca),'error':_0x3ef7eb['message']},this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x4c3356=this[_0x3f13ca(0x182)](_0x3214f9),_0x18e3f8=this[_0x3f13ca(0xae)](_0x4c3356);if(_0x124dd3[_0x3f13ca(0x10b)]=_0x4c3356,_0x18e3f8)this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x7d7701=_0x3f13ca;_0x124dd3['value']=_0x3214f9[_0x7d7701(0xbf)](),!_0x46f777&&_0x350c39[_0x7d7701(0xe9)](_0x4c3356,_0x124dd3,_0x53d319,{});});else{var _0x275cea=_0x53d319[_0x3f13ca(0xc0)]&&_0x53d319['level']<_0x53d319['autoExpandMaxDepth']&&_0x53d319[_0x3f13ca(0x118)][_0x3f13ca(0x129)](_0x3214f9)<0x0&&_0x4c3356!==_0x3f13ca(0x13e)&&_0x53d319['autoExpandPropertyCount']<_0x53d319['autoExpandLimit'];_0x275cea||_0x53d319[_0x3f13ca(0xe3)]<_0x265c6d||_0x46f777?(this['serialize'](_0x124dd3,_0x3214f9,_0x53d319,_0x46f777||{}),this[_0x3f13ca(0x10f)](_0x3214f9,_0x124dd3)):this['_processTreeNodeResult'](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x2b8765=_0x3f13ca;_0x4c3356===_0x2b8765(0x187)||_0x4c3356===_0x2b8765(0x142)||(delete _0x124dd3[_0x2b8765(0xce)],_0x124dd3[_0x2b8765(0xf3)]=!0x0);});}return _0x124dd3;}finally{_0x53d319['expressionsToEvaluate']=_0x42837e,_0x53d319[_0x3f13ca(0xf0)]=_0x265c6d,_0x53d319['isExpressionToEvaluate']=_0x31debf;}}[_0x40c820(0xe9)](_0x3711dd,_0x3273d6,_0x30712a,_0x2bfd2c){var _0x22c791=_0x40c820,_0x3ed3d6=_0x2bfd2c['strLength']||_0x30712a[_0x22c791(0x126)];if((_0x3711dd==='string'||_0x3711dd==='String')&&_0x3273d6[_0x22c791(0xce)]){let _0xffdbb0=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x14f)];_0x30712a['allStrLength']+=_0xffdbb0,_0x30712a[_0x22c791(0xa2)]>_0x30712a['totalStrLength']?(_0x3273d6[_0x22c791(0xf3)]='',delete _0x3273d6[_0x22c791(0xce)]):_0xffdbb0>_0x3ed3d6&&(_0x3273d6[_0x22c791(0xf3)]=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x147)](0x0,_0x3ed3d6),delete _0x3273d6[_0x22c791(0xce)]);}}['_isMap'](_0x261c40){var _0xd2ec63=_0x40c820;return!!(_0x261c40&&_0x3830e6['Map']&&this[_0xd2ec63(0x156)](_0x261c40)===_0xd2ec63(0x114)&&_0x261c40[_0xd2ec63(0x178)]);}[_0x40c820(0xd5)](_0x24e250){var _0x49be73=_0x40c820;if(_0x24e250[_0x49be73(0x132)](/^\\\\d+$/))return _0x24e250;var _0x1d38d;try{_0x1d38d=JSON['stringify'](''+_0x24e250);}catch{_0x1d38d='\\\\x22'+this['_objectToString'](_0x24e250)+'\\\\x22';}return _0x1d38d['match'](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x1d38d=_0x1d38d[_0x49be73(0x147)](0x1,_0x1d38d[_0x49be73(0x14f)]-0x2):_0x1d38d=_0x1d38d[_0x49be73(0x16d)](/'/g,'\\\\x5c\\\\x27')[_0x49be73(0x16d)](/\\\\\\\\\\\"/g,'\\\\x22')[_0x49be73(0x16d)](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x1d38d;}[_0x40c820(0x133)](_0x1b7571,_0x8f439b,_0x2c2981,_0x188fd5){var _0xf97f10=_0x40c820;this[_0xf97f10(0xa8)](_0x1b7571,_0x8f439b),_0x188fd5&&_0x188fd5(),this[_0xf97f10(0x10f)](_0x2c2981,_0x1b7571),this['_treeNodePropertiesAfterFullValue'](_0x1b7571,_0x8f439b);}[_0x40c820(0xa8)](_0x231eb3,_0x5ea482){var _0x31f690=_0x40c820;this['_setNodeId'](_0x231eb3,_0x5ea482),this[_0x31f690(0xd1)](_0x231eb3,_0x5ea482),this[_0x31f690(0x111)](_0x231eb3,_0x5ea482),this[_0x31f690(0x125)](_0x231eb3,_0x5ea482);}[_0x40c820(0xcb)](_0x10d2e1,_0x3c8083){}[_0x40c820(0xd1)](_0x53d949,_0x188c67){}[_0x40c820(0x163)](_0x3f8259,_0x16e80a){}[_0x40c820(0x122)](_0x25a3a3){return _0x25a3a3===this['_undefined'];}[_0x40c820(0xe4)](_0x44987b,_0x4ed592){var _0x37fb3e=_0x40c820;this[_0x37fb3e(0x163)](_0x44987b,_0x4ed592),this[_0x37fb3e(0xeb)](_0x44987b),_0x4ed592[_0x37fb3e(0xfb)]&&this[_0x37fb3e(0x12d)](_0x44987b),this['_addFunctionsNode'](_0x44987b,_0x4ed592),this[_0x37fb3e(0x17e)](_0x44987b,_0x4ed592),this[_0x37fb3e(0x16c)](_0x44987b);}['_additionalMetadata'](_0x1c2784,_0x4c1dde){var _0x4f21c1=_0x40c820;try{_0x1c2784&&typeof _0x1c2784[_0x4f21c1(0x14f)]==_0x4f21c1(0x12b)&&(_0x4c1dde[_0x4f21c1(0x14f)]=_0x1c2784[_0x4f21c1(0x14f)]);}catch{}if(_0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x12b)||_0x4c1dde[_0x4f21c1(0x10b)]==='Number'){if(isNaN(_0x4c1dde[_0x4f21c1(0xce)]))_0x4c1dde[_0x4f21c1(0x18d)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];else switch(_0x4c1dde[_0x4f21c1(0xce)]){case Number[_0x4f21c1(0xfa)]:_0x4c1dde[_0x4f21c1(0x102)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case Number['NEGATIVE_INFINITY']:_0x4c1dde[_0x4f21c1(0x189)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case 0x0:this['_isNegativeZero'](_0x4c1dde[_0x4f21c1(0xce)])&&(_0x4c1dde[_0x4f21c1(0xd0)]=!0x0);break;}}else _0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x13e)&&typeof _0x1c2784['name']==_0x4f21c1(0x101)&&_0x1c2784[_0x4f21c1(0xb7)]&&_0x4c1dde['name']&&_0x1c2784[_0x4f21c1(0xb7)]!==_0x4c1dde[_0x4f21c1(0xb7)]&&(_0x4c1dde[_0x4f21c1(0xad)]=_0x1c2784[_0x4f21c1(0xb7)]);}['_isNegativeZero'](_0x289882){var _0x1b66c9=_0x40c820;return 0x1/_0x289882===Number[_0x1b66c9(0x181)];}['_sortProps'](_0x3992ee){var _0x3db550=_0x40c820;!_0x3992ee[_0x3db550(0x180)]||!_0x3992ee[_0x3db550(0x180)][_0x3db550(0x14f)]||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0x130)||_0x3992ee[_0x3db550(0x10b)]==='Map'||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0xf6)||_0x3992ee[_0x3db550(0x180)][_0x3db550(0x175)](function(_0x57a739,_0x31b40b){var _0x5dcaae=_0x3db550,_0x3d0d50=_0x57a739[_0x5dcaae(0xb7)][_0x5dcaae(0x10e)](),_0xd6d4fc=_0x31b40b[_0x5dcaae(0xb7)]['toLowerCase']();return _0x3d0d50<_0xd6d4fc?-0x1:_0x3d0d50>_0xd6d4fc?0x1:0x0;});}[_0x40c820(0xe8)](_0x12537a,_0x57f3dc){var _0x2884a4=_0x40c820;if(!(_0x57f3dc[_0x2884a4(0xa4)]||!_0x12537a['props']||!_0x12537a[_0x2884a4(0x180)][_0x2884a4(0x14f)])){for(var _0x53c006=[],_0x347d6e=[],_0x52e85a=0x0,_0x273297=_0x12537a['props']['length'];_0x52e85a<_0x273297;_0x52e85a++){var _0x1ee5b3=_0x12537a[_0x2884a4(0x180)][_0x52e85a];_0x1ee5b3[_0x2884a4(0x10b)]==='function'?_0x53c006[_0x2884a4(0x157)](_0x1ee5b3):_0x347d6e['push'](_0x1ee5b3);}if(!(!_0x347d6e[_0x2884a4(0x14f)]||_0x53c006[_0x2884a4(0x14f)]<=0x1)){_0x12537a[_0x2884a4(0x180)]=_0x347d6e;var _0x15f515={'functionsNode':!0x0,'props':_0x53c006};this[_0x2884a4(0xcb)](_0x15f515,_0x57f3dc),this[_0x2884a4(0x163)](_0x15f515,_0x57f3dc),this[_0x2884a4(0xeb)](_0x15f515),this[_0x2884a4(0x125)](_0x15f515,_0x57f3dc),_0x15f515['id']+='\\\\x20f',_0x12537a['props']['unshift'](_0x15f515);}}}['_addLoadNode'](_0x5bea6e,_0x14049e){}[_0x40c820(0xeb)](_0x199084){}[_0x40c820(0x98)](_0xf50c17){var _0x35cb98=_0x40c820;return Array['isArray'](_0xf50c17)||typeof _0xf50c17==_0x35cb98(0x117)&&this['_objectToString'](_0xf50c17)===_0x35cb98(0xdc);}[_0x40c820(0x125)](_0x3ea390,_0x54c209){}[_0x40c820(0x16c)](_0x25cdb9){var _0x1aa0a5=_0x40c820;delete _0x25cdb9[_0x1aa0a5(0x136)],delete _0x25cdb9[_0x1aa0a5(0x143)],delete _0x25cdb9[_0x1aa0a5(0x138)];}['_setNodeExpressionPath'](_0x17f351,_0x40c77e){}}let _0x459cb0=new _0x1e3ba1(),_0x218fe5={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x11fc4c={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x482c8e(_0x50675f,_0x2f7559,_0x19c481,_0x2c8a95,_0x245f16,_0x24484e){var _0x4c67af=_0x40c820;let _0x3ce9b8,_0x1af844;try{_0x1af844=_0x1169a5(),_0x3ce9b8=_0x2f8209[_0x2f7559],!_0x3ce9b8||_0x1af844-_0x3ce9b8['ts']>0x1f4&&_0x3ce9b8[_0x4c67af(0xaa)]&&_0x3ce9b8['time']/_0x3ce9b8[_0x4c67af(0xaa)]<0x64?(_0x2f8209[_0x2f7559]=_0x3ce9b8={'count':0x0,'time':0x0,'ts':_0x1af844},_0x2f8209[_0x4c67af(0xc5)]={}):_0x1af844-_0x2f8209[_0x4c67af(0xc5)]['ts']>0x32&&_0x2f8209['hits']['count']&&_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x161)]/_0x2f8209[_0x4c67af(0xc5)]['count']<0x64&&(_0x2f8209[_0x4c67af(0xc5)]={});let _0x157126=[],_0x1464d6=_0x3ce9b8[_0x4c67af(0x112)]||_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]?_0x11fc4c:_0x218fe5,_0x553948=_0x2ff7b4=>{var _0x3a1316=_0x4c67af;let _0x5d676c={};return _0x5d676c[_0x3a1316(0x180)]=_0x2ff7b4[_0x3a1316(0x180)],_0x5d676c['elements']=_0x2ff7b4['elements'],_0x5d676c[_0x3a1316(0x126)]=_0x2ff7b4[_0x3a1316(0x126)],_0x5d676c['totalStrLength']=_0x2ff7b4['totalStrLength'],_0x5d676c['autoExpandLimit']=_0x2ff7b4[_0x3a1316(0x177)],_0x5d676c[_0x3a1316(0x115)]=_0x2ff7b4['autoExpandMaxDepth'],_0x5d676c['sortProps']=!0x1,_0x5d676c['noFunctions']=!_0x45a6b5,_0x5d676c['depth']=0x1,_0x5d676c[_0x3a1316(0xe3)]=0x0,_0x5d676c['expId']='root_exp_id',_0x5d676c[_0x3a1316(0x140)]=_0x3a1316(0x151),_0x5d676c['autoExpand']=!0x0,_0x5d676c[_0x3a1316(0x118)]=[],_0x5d676c[_0x3a1316(0x9d)]=0x0,_0x5d676c[_0x3a1316(0x168)]=!0x0,_0x5d676c['allStrLength']=0x0,_0x5d676c[_0x3a1316(0xc8)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0x5d676c;};for(var _0x33de8f=0x0;_0x33de8f<_0x245f16[_0x4c67af(0x14f)];_0x33de8f++)_0x157126[_0x4c67af(0x157)](_0x459cb0[_0x4c67af(0x14a)]({'timeNode':_0x50675f===_0x4c67af(0x161)||void 0x0},_0x245f16[_0x33de8f],_0x553948(_0x1464d6),{}));if(_0x50675f==='trace'||_0x50675f===_0x4c67af(0x184)){let _0x1d9735=Error[_0x4c67af(0xd7)];try{Error[_0x4c67af(0xd7)]=0x1/0x0,_0x157126[_0x4c67af(0x157)](_0x459cb0['serialize']({'stackNode':!0x0},new Error()['stack'],_0x553948(_0x1464d6),{'strLength':0x1/0x0}));}finally{Error[_0x4c67af(0xd7)]=_0x1d9735;}}return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':_0x157126,'id':_0x2f7559,'context':_0x24484e}]};}catch(_0x2d5a77){return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':[{'type':_0x4c67af(0xca),'error':_0x2d5a77&&_0x2d5a77[_0x4c67af(0xb4)]}],'id':_0x2f7559,'context':_0x24484e}]};}finally{try{if(_0x3ce9b8&&_0x1af844){let _0xff386f=_0x1169a5();_0x3ce9b8[_0x4c67af(0xaa)]++,_0x3ce9b8['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x3ce9b8['ts']=_0xff386f,_0x2f8209[_0x4c67af(0xc5)]['count']++,_0x2f8209[_0x4c67af(0xc5)]['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x2f8209['hits']['ts']=_0xff386f,(_0x3ce9b8[_0x4c67af(0xaa)]>0x32||_0x3ce9b8[_0x4c67af(0x161)]>0x64)&&(_0x3ce9b8['reduceLimits']=!0x0),(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0xaa)]>0x3e8||_0x2f8209[_0x4c67af(0xc5)]['time']>0x12c)&&(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]=!0x0);}}catch{}}}return _0x482c8e;}((_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x296e29,_0x567fe9,_0x14adfa,_0x6b3989,_0x593945,_0x42f609)=>{var _0x543ef9=_0x418f23;if(_0x12a02f[_0x543ef9(0xdb)])return _0x12a02f[_0x543ef9(0xdb)];if(!X(_0x12a02f,_0x14adfa,_0x1164b7))return _0x12a02f[_0x543ef9(0xdb)]={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x12a02f[_0x543ef9(0xdb)];let _0x5a7d78=B(_0x12a02f),_0x236b4f=_0x5a7d78[_0x543ef9(0xd3)],_0x57b9d9=_0x5a7d78['timeStamp'],_0x163b61=_0x5a7d78[_0x543ef9(0x11b)],_0x384cd9={'hits':{},'ts':{}},_0x9c7997=J(_0x12a02f,_0x6b3989,_0x384cd9,_0x296e29),_0x3ffb36=_0xa1ec34=>{_0x384cd9['ts'][_0xa1ec34]=_0x57b9d9();},_0x4ce4d2=(_0x173258,_0x2e0c6d)=>{var _0x3b6e53=_0x543ef9;let _0x2b64c1=_0x384cd9['ts'][_0x2e0c6d];if(delete _0x384cd9['ts'][_0x2e0c6d],_0x2b64c1){let _0x557981=_0x236b4f(_0x2b64c1,_0x57b9d9());_0x593a90(_0x9c7997(_0x3b6e53(0x161),_0x173258,_0x163b61(),_0x50e896,[_0x557981],_0x2e0c6d));}},_0x46c5f3=_0x1f105e=>{var _0x1152c8=_0x543ef9,_0x4a2783;return _0x1164b7===_0x1152c8(0x110)&&_0x12a02f[_0x1152c8(0x152)]&&((_0x4a2783=_0x1f105e==null?void 0x0:_0x1f105e[_0x1152c8(0xab)])==null?void 0x0:_0x4a2783[_0x1152c8(0x14f)])&&(_0x1f105e[_0x1152c8(0xab)][0x0][_0x1152c8(0x152)]=_0x12a02f[_0x1152c8(0x152)]),_0x1f105e;};_0x12a02f[_0x543ef9(0xdb)]={'consoleLog':(_0x204f4b,_0x3e1804)=>{var _0x309615=_0x543ef9;_0x12a02f['console'][_0x309615(0xff)][_0x309615(0xb7)]!==_0x309615(0x134)&&_0x593a90(_0x9c7997(_0x309615(0xff),_0x204f4b,_0x163b61(),_0x50e896,_0x3e1804));},'consoleTrace':(_0x267a3f,_0x51c339)=>{var _0x4c4943=_0x543ef9,_0x33fd8c,_0x24e61f;_0x12a02f[_0x4c4943(0xbc)][_0x4c4943(0xff)]['name']!==_0x4c4943(0xbd)&&((_0x24e61f=(_0x33fd8c=_0x12a02f[_0x4c4943(0x104)])==null?void 0x0:_0x33fd8c['versions'])!=null&&_0x24e61f[_0x4c4943(0xc8)]&&(_0x12a02f[_0x4c4943(0x165)]=!0x0),_0x593a90(_0x46c5f3(_0x9c7997(_0x4c4943(0xc4),_0x267a3f,_0x163b61(),_0x50e896,_0x51c339))));},'consoleError':(_0xf7f1fc,_0x1384d7)=>{var _0x28d83c=_0x543ef9;_0x12a02f['_ninjaIgnoreNextError']=!0x0,_0x593a90(_0x46c5f3(_0x9c7997(_0x28d83c(0x184),_0xf7f1fc,_0x163b61(),_0x50e896,_0x1384d7)));},'consoleTime':_0x2ad865=>{_0x3ffb36(_0x2ad865);},'consoleTimeEnd':(_0x3c91cf,_0x308c8b)=>{_0x4ce4d2(_0x308c8b,_0x3c91cf);},'autoLog':(_0x4bbc9f,_0x3599a3)=>{var _0x598cfa=_0x543ef9;_0x593a90(_0x9c7997(_0x598cfa(0xff),_0x3599a3,_0x163b61(),_0x50e896,[_0x4bbc9f]));},'autoLogMany':(_0x158592,_0x29b77d)=>{var _0x425f64=_0x543ef9;_0x593a90(_0x9c7997(_0x425f64(0xff),_0x158592,_0x163b61(),_0x50e896,_0x29b77d));},'autoTrace':(_0x3f5f9d,_0xc378ab)=>{var _0x377a7d=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x377a7d(0xc4),_0xc378ab,_0x163b61(),_0x50e896,[_0x3f5f9d])));},'autoTraceMany':(_0x2c6f73,_0x35405b)=>{var _0x4f4e7f=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x4f4e7f(0xc4),_0x2c6f73,_0x163b61(),_0x50e896,_0x35405b)));},'autoTime':(_0x4915d6,_0xaaf0db,_0x4c1f1e)=>{_0x3ffb36(_0x4c1f1e);},'autoTimeEnd':(_0x397624,_0x436d57,_0x47b9b8)=>{_0x4ce4d2(_0x436d57,_0x47b9b8);},'coverage':_0x45a646=>{var _0x1e9860=_0x543ef9;_0x593a90({'method':_0x1e9860(0x13a),'version':_0x296e29,'args':[{'id':_0x45a646}]});}};let _0x593a90=H(_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x593945,_0x42f609),_0x50e896=_0x12a02f[_0x543ef9(0xd8)];return _0x12a02f[_0x543ef9(0xdb)];})(globalThis,'127.0.0.1',_0x418f23(0xec),_0x418f23(0x17a),_0x418f23(0x183),'1.0.0','1751470503250',_0x418f23(0x137),_0x418f23(0x18a),_0x418f23(0x10a),_0x418f23(0xb1));\");\n  } catch (e) {}\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_oo(\n/**@type{any}**/\ni) {\n  for (var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    v[_key - 1] = arguments[_key];\n  }\n\n  try {\n    oo_cm().consoleLog(i, v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_tr(\n/**@type{any}**/\ni) {\n  for (var _len2 = arguments.length, v = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    v[_key2 - 1] = arguments[_key2];\n  }\n\n  try {\n    oo_cm().consoleTrace(i, v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_tx(\n/**@type{any}**/\ni) {\n  for (var _len3 = arguments.length, v = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n    v[_key3 - 1] = arguments[_key3];\n  }\n\n  try {\n    oo_cm().consoleError(i, v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_ts(\n/**@type{any}**/\nv) {\n  try {\n    oo_cm().consoleTime(v);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/* istanbul ignore next */\n\nfunction oo_te(\n/**@type{any}**/\nv,\n/**@type{any}**/\ni) {\n  try {\n    oo_cm().consoleTimeEnd(v, i);\n  } catch (e) {}\n\n  return v;\n}\n\n;\n/*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/\n\nvar _c, _c2;\n\n$RefreshReg$(_c, \"ThinkingLoader\");\n$RefreshReg$(_c2, \"PredictionModal\");", "map": {"version": 3, "sources": ["D:/www/SWF/sian-web-frontend/src/views/commercial/salesDashboard/components/PredictionModal.jsx"], "names": ["React", "useState", "useRef", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TextField", "Box", "Typography", "IconButton", "Fade", "useTheme", "<PERSON><PERSON>", "CircularProgress", "alpha", "CloseIcon", "SmartToyIcon", "SendIcon", "ReactMarkdown", "styled", "ChatMessage", "theme", "isUser", "display", "flexDirection", "alignItems", "marginBottom", "spacing", "max<PERSON><PERSON><PERSON>", "alignSelf", "padding", "borderRadius", "background", "palette", "primary", "dark", "main", "color", "text", "boxShadow", "transition", "transform", "margin", "backgroundColor", "overflowX", "fontFamily", "<PERSON><PERSON><PERSON><PERSON>", "dotCount", "setDotCount", "currentThought", "setCurrentThought", "thoughts", "dotInterval", "setInterval", "prev", "thoughtInterval", "length", "clearInterval", "dots", "repeat", "mt", "mb", "p", "secondary", "light", "border", "<PERSON><PERSON>ilter", "justifyContent", "position", "height", "width", "animation", "content", "filter", "opacity", "zIndex", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "fontWeight", "min<PERSON><PERSON><PERSON>", "textAlign", "minHeight", "fontSize", "letterSpacing", "gap", "map", "i", "animationDelay", "PredictionModal", "open", "onClose", "storeName", "storeData", "selectedTab", "userInput", "setUserInput", "chatHistory", "setChatHistory", "isLoading", "setIsLoading", "chatEndRef", "prevOpenRef", "<PERSON><PERSON><PERSON><PERSON>", "process", "env", "REACT_APP_OPENAI_API_KEY", "model", "temperature", "apiEndpoint", "REACT_APP_OPENAI_URL_BASE", "current", "scrollIntoView", "behavior", "setTimeout", "generateInitialAnalysis", "isTotal", "storeSummary", "today_sales", "today_goal", "parseFloat", "toFixed", "Date", "query_date", "toLocaleDateString", "progress_sales", "period_goal", "toLocaleString", "month", "year", "elapsed_days", "date_goal", "additionalContext", "systemPrompt", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "messages", "role", "ok", "Error", "data", "json", "aiResponse", "choices", "message", "error", "console", "oo_tx", "handleInputChange", "event", "target", "value", "handleKeyPress", "key", "shift<PERSON>ey", "preventDefault", "handleSend", "trim", "newMessage", "storeContext", "timeout", "sx", "overflow", "paper", "borderBottom", "mr", "flexGrow", "overflowY", "index", "from", "to", "borderTop", "top", "left", "borderColor", "borderWidth", "ml", "action", "disabledBackground", "oo_cm", "eval", "e", "oo_oo", "v", "consoleLog", "oo_tr", "consoleTrace", "consoleError", "oo_ts", "consoleTime", "oo_te", "consoleTimeEnd"], "mappings": ";;;;AAAA;AACA,OAAOA,KAAP,IAAgBC,QAAhB,EAA0BC,MAA1B,EAAkCC,SAAlC,QAAmD,OAAnD;AACA,SACEC,MADF,EAEEC,WAFF,EAGEC,aAHF,EAIEC,SAJF,EAKEC,GALF,EAMEC,UANF,EAOEC,UAPF,EAQEC,IARF,EASEC,QATF,EAUEC,MAVF,EAWEC,gBAXF,EAYEC,KAZF,QAaO,eAbP;AAcA,OAAOC,SAAP,MAAsB,2BAAtB;AACA,OAAOC,YAAP,MAAyB,8BAAzB;AACA,OAAOC,QAAP,MAAqB,0BAArB;AACA,OAAOC,aAAP,MAA0B,gBAA1B;AACA,SAASC,MAAT,QAAuB,sBAAvB,C,CAEA;;;AACA,MAAMC,WAAW,GAAGD,MAAM,CAACZ,GAAD,CAAN,CAAY;AAAA,MAAC;AAAEc,IAAAA,KAAF;AAASC,IAAAA;AAAT,GAAD;AAAA,SAAwB;AACtDC,IAAAA,OAAO,EAAE,MAD6C;AAEtDC,IAAAA,aAAa,EAAE,QAFuC;AAGtDC,IAAAA,UAAU,EAAEH,MAAM,GAAG,UAAH,GAAgB,YAHoB;AAItDI,IAAAA,YAAY,EAAEL,KAAK,CAACM,OAAN,CAAc,CAAd,CAJwC;AAKtDC,IAAAA,QAAQ,EAAE,KAL4C;AAMtDC,IAAAA,SAAS,EAAEP,MAAM,GAAG,UAAH,GAAgB,YANqB;AAOtD,eAAW;AACTQ,MAAAA,OAAO,EAAET,KAAK,CAACM,OAAN,CAAc,CAAd,CADA;AAETI,MAAAA,YAAY,EAAET,MAAM,GAAG,kBAAH,GAAwB,kBAFnC;AAGTU,MAAAA,UAAU,EAAEV,MAAM,GACb,2BAA0BD,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBC,IAAK,QAAOd,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAK,QAD1E,GAEb,mDALI;AAMTC,MAAAA,KAAK,EAAEf,MAAM,GAAG,MAAH,GAAYD,KAAK,CAACY,OAAN,CAAcK,IAAd,CAAmBJ,OANnC;AAOTK,MAAAA,SAAS,EAAEjB,MAAM,GACb,qCADa,GAEb,6BATK;AAUTkB,MAAAA,UAAU,EAAE,sBAVH;AAWT,iBAAW;AACTC,QAAAA,SAAS,EAAE,kBADF;AAETF,QAAAA,SAAS,EAAEjB,MAAM,GACb,oCADa,GAEb;AAJK,OAXF;AAiBT,aAAO;AACLoB,QAAAA,MAAM,EAAE,CADH;AAELhB,QAAAA,YAAY,EAAE,OAFT;AAGL,wBAAgB;AACdA,UAAAA,YAAY,EAAE;AADA;AAHX,OAjBE;AAwBT,eAAS;AACPiB,QAAAA,eAAe,EAAErB,MAAM,GAAG,iBAAH,GAAuB,SADvC;AAEPQ,QAAAA,OAAO,EAAET,KAAK,CAACM,OAAN,CAAc,CAAd,CAFF;AAGPI,QAAAA,YAAY,EAAEV,KAAK,CAACM,OAAN,CAAc,CAAd,CAHP;AAIPiB,QAAAA,SAAS,EAAE,MAJJ;AAKP,kBAAU;AACRC,UAAAA,UAAU,EAAE;AADJ;AALH;AAxBA;AAP2C,GAAxB;AAAA,CAAZ,CAApB;;AA2CA,MAAMC,cAAc,GAAG,MAAM;AAAA;;AAC3B,QAAMzB,KAAK,GAAGV,QAAQ,EAAtB;AACA,QAAM,CAACoC,QAAD,EAAWC,WAAX,IAA0BhD,QAAQ,CAAC,CAAD,CAAxC;AACA,QAAM,CAACiD,cAAD,EAAiBC,iBAAjB,IAAsClD,QAAQ,CAAC,CAAD,CAApD;AAEA,QAAMmD,QAAQ,GAAG,CACf,4BADe,EAEf,wBAFe,EAGf,uBAHe,EAIf,sBAJe,EAKf,sBALe,CAAjB;AAQAjD,EAAAA,SAAS,CAAC,MAAM;AACd,UAAMkD,WAAW,GAAGC,WAAW,CAAC,MAAM;AACpCL,MAAAA,WAAW,CAAEM,IAAD,IAAU,CAACA,IAAI,GAAG,CAAR,IAAa,CAAxB,CAAX;AACD,KAF8B,EAE5B,GAF4B,CAA/B;AAIA,UAAMC,eAAe,GAAGF,WAAW,CAAC,MAAM;AACxCH,MAAAA,iBAAiB,CAAEI,IAAD,IAAU,CAACA,IAAI,GAAG,CAAR,IAAaH,QAAQ,CAACK,MAAjC,CAAjB;AACD,KAFkC,EAEhC,IAFgC,CAAnC;AAIA,WAAO,MAAM;AACXC,MAAAA,aAAa,CAACL,WAAD,CAAb;AACAK,MAAAA,aAAa,CAACF,eAAD,CAAb;AACD,KAHD;AAID,GAbQ,EAaN,EAbM,CAAT;AAeA,QAAMG,IAAI,GAAG,IAAIC,MAAJ,CAAWZ,QAAX,CAAb;AAEA,sBACE,QAAC,GAAD;AAAK,IAAA,EAAE,EAAE;AACPxB,MAAAA,OAAO,EAAE,MADF;AAEPC,MAAAA,aAAa,EAAE,QAFR;AAGPC,MAAAA,UAAU,EAAE,QAHL;AAIPmC,MAAAA,EAAE,EAAE,CAJG;AAKPC,MAAAA,EAAE,EAAE,CALG;AAMPC,MAAAA,CAAC,EAAE,CANI;AAOP9B,MAAAA,UAAU,EAAG,2BAA0BlB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAc8B,SAAd,CAAwBC,KAAzB,EAAgC,GAAhC,CAAqC,QAAOlD,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsB8B,KAAvB,EAA8B,IAA9B,CAAoC,QAP1H;AAQPjC,MAAAA,YAAY,EAAE,CARP;AASPkC,MAAAA,MAAM,EAAG,aAAYnD,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,IAA7B,CAAmC,EATtD;AAUP8B,MAAAA,cAAc,EAAE,WAVT;AAWP3B,MAAAA,SAAS,EAAG,gBAAezB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B,CAAkC;AAX3D,KAAT;AAAA,4BAaE,QAAC,GAAD;AAAK,MAAA,EAAE,EAAE;AACPb,QAAAA,OAAO,EAAE,MADF;AAEP4C,QAAAA,cAAc,EAAE,QAFT;AAGPC,QAAAA,QAAQ,EAAE,UAHH;AAIPP,QAAAA,EAAE,EAAE,CAJG;AAKPQ,QAAAA,MAAM,EAAE;AALD,OAAT;AAAA,6BAOE,QAAC,GAAD;AAAK,QAAA,EAAE,EAAE;AACPD,UAAAA,QAAQ,EAAE,UADH;AAEPE,UAAAA,KAAK,EAAE,EAFA;AAGPD,UAAAA,MAAM,EAAE,EAHD;AAIPtC,UAAAA,YAAY,EAAE,KAJP;AAKPR,UAAAA,OAAO,EAAE,MALF;AAMPE,UAAAA,UAAU,EAAE,QANL;AAOP0C,UAAAA,cAAc,EAAE,QAPT;AAQPnC,UAAAA,UAAU,EAAG,2BAA0BX,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAK,QAAOf,KAAK,CAACY,OAAN,CAAc8B,SAAd,CAAwB3B,IAAK,QAR/F;AASPC,UAAAA,KAAK,EAAE,OATA;AAUPkC,UAAAA,SAAS,EAAE,qBAVJ;AAWP,8BAAoB;AAClB,kBAAM;AAAE9B,cAAAA,SAAS,EAAE,UAAb;AAAyBF,cAAAA,SAAS,EAAG,WAAUzB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B,CAAkC;AAAtF,aADY;AAElB,mBAAO;AAAEK,cAAAA,SAAS,EAAE,aAAb;AAA4BF,cAAAA,SAAS,EAAG,cAAazB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,CAA7B,CAAgC;AAA1F,aAFW;AAGlB,oBAAQ;AAAEK,cAAAA,SAAS,EAAE,UAAb;AAAyBF,cAAAA,SAAS,EAAG,WAAUzB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,CAA7B,CAAgC;AAApF;AAHU,WAXb;AAgBP,sBAAY;AACVoC,YAAAA,OAAO,EAAE,IADC;AAEVJ,YAAAA,QAAQ,EAAE,UAFA;AAGVE,YAAAA,KAAK,EAAE,MAHG;AAIVD,YAAAA,MAAM,EAAE,MAJE;AAKVtC,YAAAA,YAAY,EAAE,KALJ;AAMVC,YAAAA,UAAU,EAAG,2BAA0BX,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAK,QAAOf,KAAK,CAACY,OAAN,CAAc8B,SAAd,CAAwB3B,IAAK,QAN5F;AAOVqC,YAAAA,MAAM,EAAE,YAPE;AAQVC,YAAAA,OAAO,EAAE,GARC;AASVC,YAAAA,MAAM,EAAE,CAAC;AATC;AAhBL,SAAT;AAAA,+BA4BE,QAAC,YAAD;AAAc,UAAA,QAAQ,EAAC;AAAvB;AAAA;AAAA;AAAA;AAAA;AA5BF;AAAA;AAAA;AAAA;AAAA;AAPF;AAAA;AAAA;AAAA;AAAA,YAbF,eAoDE,QAAC,UAAD;AACE,MAAA,OAAO,EAAC,OADV;AAEE,MAAA,EAAE,EAAE;AACF3C,QAAAA,UAAU,EAAG,0BAAyBX,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAK,KAAIf,KAAK,CAACY,OAAN,CAAc8B,SAAd,CAAwB3B,IAAK,GADhG;AAEFwC,QAAAA,oBAAoB,EAAE,MAFpB;AAGFC,QAAAA,mBAAmB,EAAE,aAHnB;AAIFC,QAAAA,cAAc,EAAE,MAJd;AAKFC,QAAAA,UAAU,EAAE,MALV;AAMFC,QAAAA,QAAQ,EAAE,GANR;AAOFC,QAAAA,SAAS,EAAE,QAPT;AAQFC,QAAAA,SAAS,EAAE,EART;AASFC,QAAAA,QAAQ,EAAE,QATR;AAUFC,QAAAA,aAAa,EAAE;AAVb,OAFN;AAAA,iBAeGjC,QAAQ,CAACF,cAAD,CAfX,EAe6BS,IAf7B;AAAA;AAAA;AAAA;AAAA;AAAA,YApDF,eAsEE,QAAC,GAAD;AAAK,MAAA,EAAE,EAAE;AACPnC,QAAAA,OAAO,EAAE,MADF;AAEPqC,QAAAA,EAAE,EAAE,CAFG;AAGPO,QAAAA,cAAc,EAAE,QAHT;AAIPkB,QAAAA,GAAG,EAAE;AAJE,OAAT;AAAA,gBAMG,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgBC,GAAhB,CAAqBC,CAAD,iBACnB,QAAC,GAAD;AAEE,QAAA,EAAE,EAAE;AACFjB,UAAAA,KAAK,EAAE,EADL;AAEFD,UAAAA,MAAM,EAAE,EAFN;AAGFtC,UAAAA,YAAY,EAAE,KAHZ;AAIFC,UAAAA,UAAU,EAAG,2BAA0BX,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAK,QAAOf,KAAK,CAACY,OAAN,CAAc8B,SAAd,CAAwB3B,IAAK,QAJpG;AAKFsC,UAAAA,OAAO,EAAEa,CAAC,IAAIxC,QAAL,GAAgB,CAAhB,GAAoB,GAL3B;AAMFP,UAAAA,UAAU,EAAE,eANV;AAOF+B,UAAAA,SAAS,EAAG,uCAPV;AAQFiB,UAAAA,cAAc,EAAG,GAAED,CAAC,GAAG,GAAI,GARzB;AASFhD,UAAAA,SAAS,EAAG,cAAazB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B,CAAkC,EAT9D;AAUF,+BAAqB;AACnB,6BAAiB;AAAEK,cAAAA,SAAS,EAAE;AAAb,aADE;AAEnB,mBAAO;AAAEA,cAAAA,SAAS,EAAE;AAAb;AAFY;AAVnB;AAFN,SACO8C,CADP;AAAA;AAAA;AAAA;AAAA,cADD;AANH;AAAA;AAAA;AAAA;AAAA,YAtEF;AAAA;AAAA;AAAA;AAAA;AAAA,UADF;AAoGD,CAlID;;GAAMzC,c;UACUnC,Q;;;KADVmC,c;;AAoIN,MAAM2C,eAAe,GAAG,SAMlB;AAAA;;AAAA,MANmB;AACvBC,IAAAA,IADuB;AAEvBC,IAAAA,OAFuB;AAGvBC,IAAAA,SAHuB;AAIvBC,IAAAA,SAJuB;AAKvBC,IAAAA;AALuB,GAMnB;AACJ,QAAMzE,KAAK,GAAGV,QAAQ,EAAtB;AACA,QAAM,CAACoF,SAAD,EAAYC,YAAZ,IAA4BhG,QAAQ,CAAC,EAAD,CAA1C;AACA,QAAM,CAACiG,WAAD,EAAcC,cAAd,IAAgClG,QAAQ,CAAC,EAAD,CAA9C;AACA,QAAM,CAACmG,SAAD,EAAYC,YAAZ,IAA4BpG,QAAQ,CAAC,KAAD,CAA1C;AACA,QAAMqG,UAAU,GAAGpG,MAAM,CAAC,IAAD,CAAzB;AACA,QAAMqG,WAAW,GAAGrG,MAAM,CAAC,KAAD,CAA1B,CANI,CAQJ;;AACA,QAAMsG,MAAM,GAAGC,OAAO,CAACC,GAAR,CAAYC,wBAA3B;AACA,QAAMC,KAAK,GAAG,aAAd;AACA,QAAMC,WAAW,GAAG,GAApB;AACA,QAAMC,WAAW,GAAGL,OAAO,CAACC,GAAR,CAAYK,yBAAhC,CAZI,CAcJ;;AACA5G,EAAAA,SAAS,CAAC,MAAM;AAAA;;AACd,2BAAAmG,UAAU,CAACU,OAAX,4EAAoBC,cAApB,CAAmC;AAAEC,MAAAA,QAAQ,EAAE;AAAZ,KAAnC;AACD,GAFQ,EAEN,CAAChB,WAAD,CAFM,CAAT,CAfI,CAmBJ;;AACA/F,EAAAA,SAAS,CAAC,MAAM;AACd;AACA,QAAIwF,IAAI,IAAI,CAACY,WAAW,CAACS,OAArB,IAAgClB,SAApC,EAA+C;AAC7C;AACAK,MAAAA,cAAc,CAAC,EAAD,CAAd,CAF6C,CAG7C;;AACAgB,MAAAA,UAAU,CAAC,MAAM;AACfC,QAAAA,uBAAuB;AACxB,OAFS,EAEP,GAFO,CAAV,CAJ6C,CAMpC;AACV,KATa,CAUd;;;AACAb,IAAAA,WAAW,CAACS,OAAZ,GAAsBrB,IAAtB;AACD,GAZQ,EAYN,CAACA,IAAD,EAAOG,SAAP,EAAkBC,WAAlB,EAA+BF,SAA/B,CAZM,CAAT;;AAcA,QAAMuB,uBAAuB,GAAG,YAAY;AAC1Cf,IAAAA,YAAY,CAAC,IAAD,CAAZ,CAD0C,CAG1C;;AACA,UAAMgB,OAAO,GAAGxB,SAAS,KAAK,WAA9B,CAJ0C,CAM1C;;AACA,QAAIyB,YAAY,GAAG,EAAnB;;AACA,QAAIvB,WAAW,KAAK,KAApB,EAA2B;AACzBuB,MAAAA,YAAY,GAAI;AACtB,UAAUD,OAAO,GAAG,WAAH,GAAkB,WAAUxB,SAAU,EAAE;AACzD,wBAAwBC,SAAS,CAACyB,WAAY;AAC9C,uBAAuBzB,SAAS,CAAC0B,UAAW;AAC5C,gCAAgC,CAACC,UAAU,CAAC3B,SAAS,CAACyB,WAAX,CAAV,GAAoCE,UAAU,CAAC3B,SAAS,CAAC0B,UAAX,CAA9C,GAAuE,GAAxE,EAA6EE,OAA7E,CAAqF,CAArF,CAAwF;AACxH,8BAA8BD,UAAU,CAAC3B,SAAS,CAAC0B,UAAX,CAAV,GAAmCC,UAAU,CAAC3B,SAAS,CAACyB,WAAX,CAAwB;AACnG,iBAAiB,IAAII,IAAJ,CAAS7B,SAAS,CAAC8B,UAAnB,EAA+BC,kBAA/B,EAAoD;AACrE,OAPM;AAQD,KATD,MASO,IAAI9B,WAAW,KAAK,OAApB,EAA6B;AAClCuB,MAAAA,YAAY,GAAI;AACtB,UAAUD,OAAO,GAAG,WAAH,GAAkB,WAAUxB,SAAU,EAAE;AACzD,2BAA2BC,SAAS,CAACgC,cAAe;AACpD,wBAAwBhC,SAAS,CAACiC,WAAY;AAC9C,gCAAgC,CAACN,UAAU,CAAC3B,SAAS,CAACgC,cAAX,CAAV,GAAuCL,UAAU,CAAC3B,SAAS,CAACiC,WAAX,CAAjD,GAA2E,GAA5E,EAAiFL,OAAjF,CAAyF,CAAzF,CAA4F;AAC5H,8BAA8BD,UAAU,CAAC3B,SAAS,CAACiC,WAAX,CAAV,GAAoCN,UAAU,CAAC3B,SAAS,CAACgC,cAAX,CAA2B;AACvG,eAAe,IAAIH,IAAJ,CAAS7B,SAAS,CAAC8B,UAAnB,EAA+BI,cAA/B,CAA8C,SAA9C,EAAyD;AAAEC,QAAAA,KAAK,EAAE,MAAT;AAAiBC,QAAAA,IAAI,EAAE;AAAvB,OAAzD,CAA6F;AAC5G,OAPM;AAQD,KATM,MASA;AAAE;AACPZ,MAAAA,YAAY,GAAI;AACtB,UAAUD,OAAO,GAAG,WAAH,GAAkB,WAAUxB,SAAU,EAAE;AACzD,2BAA2BC,SAAS,CAACgC,cAAe;AACpD,yBAAyBhC,SAAS,CAACqC,YAAa,WAAUrC,SAAS,CAACsC,SAAU;AAC9E,gCAAgC,CAACX,UAAU,CAAC3B,SAAS,CAACgC,cAAX,CAAV,GAAuCL,UAAU,CAAC3B,SAAS,CAACsC,SAAX,CAAjD,GAAyE,GAA1E,EAA+EV,OAA/E,CAAuF,CAAvF,CAA0F;AAC1H,8BAA8BD,UAAU,CAAC3B,SAAS,CAACsC,SAAX,CAAV,GAAkCX,UAAU,CAAC3B,SAAS,CAACgC,cAAX,CAA2B;AACrG,8BAA8BhC,SAAS,CAACqC,YAAa;AACrD,iBAAiB,IAAIR,IAAJ,CAAS7B,SAAS,CAAC8B,UAAnB,EAA+BC,kBAA/B,EAAoD;AACrE,OARM;AASD,KApCyC,CAsC1C;;;AACA,QAAIQ,iBAAiB,GAAG,EAAxB;;AACA,QAAIhB,OAAJ,EAAa;AACXgB,MAAAA,iBAAiB,GAAG,0EAApB;AACD,KA1CyC,CA4C1C;;;AACA,UAAMC,YAAY,GAAI,4FAA2FjB,OAAO,GAAG,uCAAH,GAA8C,gBAAexB,SAAU,EAAE;AACrM;AACA;AACA,6BAA6BwB,OAAO,GAAG,yBAAH,GAA+B,WAAY,sBAAqBtB,WAAW,KAAK,KAAhB,GAAwB,QAAxB,GAAmCA,WAAW,KAAK,OAAhB,GAA0B,SAA1B,GAAsC,aAAc;AAC3L;AACA;AACA;AACA;AACA,YAAYsB,OAAO,GAAG,cAAH,GAAoB,cAAe;AACtD,EAAEC,YAAa;AACf;AACA,EAAEe,iBAAkB;AACpB;AACA,4FAbI;;AAeA,QAAI;AACF,YAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC1B,WAAD,EAAc;AACxC2B,QAAAA,MAAM,EAAE,MADgC;AAExCC,QAAAA,OAAO,EAAE;AACP,0BAAgB,kBADT;AAEP,2BAAkB,UAASlC,MAAO;AAF3B,SAF+B;AAMxCmC,QAAAA,IAAI,EAAEC,IAAI,CAACC,SAAL,CAAe;AACnBjC,UAAAA,KADmB;AAEnBkC,UAAAA,QAAQ,EAAE,CAAC;AAAEC,YAAAA,IAAI,EAAE,QAAR;AAAkBtE,YAAAA,OAAO,EAAE6D;AAA3B,WAAD,CAFS;AAGnBzB,UAAAA;AAHmB,SAAf;AANkC,OAAd,CAA5B;;AAaA,UAAI,CAAC0B,QAAQ,CAACS,EAAd,EAAkB;AAChB,cAAM,IAAIC,KAAJ,CAAU,sCAAV,CAAN;AACD;;AAED,YAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACY,IAAT,EAAnB;AACA,YAAMC,UAAU,GAAGF,IAAI,CAACG,OAAL,CAAa,CAAb,EAAgBC,OAAnC;AAEAnD,MAAAA,cAAc,CAAC,CAACiD,UAAD,CAAD,CAAd;AACD,KAtBD,CAsBE,OAAOG,KAAP,EAAc;AACd;AAAoBC,MAAAA,OAAO,CAACD,KAAR,CAAc,GAAGE,KAAK,CAAE,4BAAF,EAA8B,QAA9B,EAAwCF,KAAxC,CAAtB;AACpBpD,MAAAA,cAAc,CAAC,CACb;AAAE4C,QAAAA,IAAI,EAAE,WAAR;AAAqBtE,QAAAA,OAAO,EAAE;AAA9B,OADa,CAAD,CAAd;AAGD,KA3BD,SA2BU;AACR4B,MAAAA,YAAY,CAAC,KAAD,CAAZ;AACD;AACF,GA1FD;;AA4FA,QAAMqD,iBAAiB,GAAIC,KAAD,IAAW;AACnC1D,IAAAA,YAAY,CAAC0D,KAAK,CAACC,MAAN,CAAaC,KAAd,CAAZ;AACD,GAFD;;AAIA,QAAMC,cAAc,GAAIH,KAAD,IAAW;AAChC,QAAIA,KAAK,CAACI,GAAN,KAAc,OAAd,IAAyB,CAACJ,KAAK,CAACK,QAApC,EAA8C;AAC5CL,MAAAA,KAAK,CAACM,cAAN;AACAC,MAAAA,UAAU;AACX;AACF,GALD;;AAOA,QAAMA,UAAU,GAAG,YAAY;AAC7B,QAAI,CAAClE,SAAS,CAACmE,IAAV,EAAL,EAAuB;AAEvB,UAAMC,UAAU,GAAG;AAAErB,MAAAA,IAAI,EAAE,MAAR;AAAgBtE,MAAAA,OAAO,EAAEuB;AAAzB,KAAnB;AACAG,IAAAA,cAAc,CAAC5C,IAAI,IAAI,CAAC,GAAGA,IAAJ,EAAU6G,UAAV,CAAT,CAAd;AACAnE,IAAAA,YAAY,CAAC,EAAD,CAAZ;AACAI,IAAAA,YAAY,CAAC,IAAD,CAAZ;;AAEA,QAAI;AACF;AACA,YAAMgB,OAAO,GAAGxB,SAAS,KAAK,WAA9B,CAFE,CAIF;;AACA,UAAIwE,YAAY,GAAG,EAAnB;;AACA,UAAItE,WAAW,KAAK,KAApB,EAA2B;AACzBsE,QAAAA,YAAY,GAAI;AACxB,YAAYhD,OAAO,GAAG,WAAH,GAAkB,WAAUxB,SAAU,EAAE;AAC3D,0BAA0BC,SAAS,CAACyB,WAAY;AAChD,yBAAyBzB,SAAS,CAAC0B,UAAW;AAC9C,kCAAkC,CAACC,UAAU,CAAC3B,SAAS,CAACyB,WAAX,CAAV,GAAoCE,UAAU,CAAC3B,SAAS,CAAC0B,UAAX,CAA9C,GAAuE,GAAxE,EAA6EE,OAA7E,CAAqF,CAArF,CAAwF;AAC1H,gCAAgCD,UAAU,CAAC3B,SAAS,CAAC0B,UAAX,CAAV,GAAmCC,UAAU,CAAC3B,SAAS,CAACyB,WAAX,CAAwB;AACrG,mBAAmB,IAAII,IAAJ,CAAS7B,SAAS,CAAC8B,UAAnB,EAA+BC,kBAA/B,EAAoD;AACvE,SAPQ;AAQD,OATD,MASO,IAAI9B,WAAW,KAAK,OAApB,EAA6B;AAClCsE,QAAAA,YAAY,GAAI;AACxB,YAAYhD,OAAO,GAAG,WAAH,GAAkB,WAAUxB,SAAU,EAAE;AAC3D,6BAA6BC,SAAS,CAACgC,cAAe;AACtD,0BAA0BhC,SAAS,CAACiC,WAAY;AAChD,kCAAkC,CAACN,UAAU,CAAC3B,SAAS,CAACgC,cAAX,CAAV,GAAuCL,UAAU,CAAC3B,SAAS,CAACiC,WAAX,CAAjD,GAA2E,GAA5E,EAAiFL,OAAjF,CAAyF,CAAzF,CAA4F;AAC9H,gCAAgCD,UAAU,CAAC3B,SAAS,CAACiC,WAAX,CAAV,GAAoCN,UAAU,CAAC3B,SAAS,CAACgC,cAAX,CAA2B;AACzG,iBAAiB,IAAIH,IAAJ,CAAS7B,SAAS,CAAC8B,UAAnB,EAA+BI,cAA/B,CAA8C,SAA9C,EAAyD;AAAEC,UAAAA,KAAK,EAAE,MAAT;AAAiBC,UAAAA,IAAI,EAAE;AAAvB,SAAzD,CAA6F;AAC9G,SAPQ;AAQD,OATM,MASA;AAAE;AACPmC,QAAAA,YAAY,GAAI;AACxB,YAAYhD,OAAO,GAAG,WAAH,GAAkB,WAAUxB,SAAU,EAAE;AAC3D,6BAA6BC,SAAS,CAACgC,cAAe;AACtD,2BAA2BhC,SAAS,CAACqC,YAAa,WAAUrC,SAAS,CAACsC,SAAU;AAChF,kCAAkC,CAACX,UAAU,CAAC3B,SAAS,CAACgC,cAAX,CAAV,GAAuCL,UAAU,CAAC3B,SAAS,CAACsC,SAAX,CAAjD,GAAyE,GAA1E,EAA+EV,OAA/E,CAAuF,CAAvF,CAA0F;AAC5H,gCAAgCD,UAAU,CAAC3B,SAAS,CAACsC,SAAX,CAAV,GAAkCX,UAAU,CAAC3B,SAAS,CAACgC,cAAX,CAA2B;AACvG,gCAAgChC,SAAS,CAACqC,YAAa;AACvD,mBAAmB,IAAIR,IAAJ,CAAS7B,SAAS,CAAC8B,UAAnB,EAA+BC,kBAA/B,EAAoD;AACvE,SARQ;AASD,OAlCC,CAoCF;;;AACA,UAAIQ,iBAAiB,GAAG,EAAxB;;AACA,UAAIhB,OAAJ,EAAa;AACXgB,QAAAA,iBAAiB,GAAG,0EAApB;AACD,OAxCC,CA0CF;;;AACA,YAAMC,YAAY,GAAI,sFAAqFjB,OAAO,GAAG,uCAAH,GAA8C,gBAAexB,SAAU,EAAE;AACjM;AACA,EAAEwE,YAAa;AACf;AACA,EAAEhC,iBAAkB;AACpB;AACA,wFANM,CA3CE,CAmDF;;AACA,YAAMS,QAAQ,GAAG,CACf;AAAEC,QAAAA,IAAI,EAAE,QAAR;AAAkBtE,QAAAA,OAAO,EAAE6D;AAA3B,OADe,EAEf,GAAGpC,WAFY,EAGfkE,UAHe,CAAjB;AAMA,YAAM7B,QAAQ,GAAG,MAAMC,KAAK,CAAC1B,WAAD,EAAc;AACxC2B,QAAAA,MAAM,EAAE,MADgC;AAExCC,QAAAA,OAAO,EAAE;AACP,0BAAgB,kBADT;AAEP,2BAAkB,UAASlC,MAAO;AAF3B,SAF+B;AAMxCmC,QAAAA,IAAI,EAAEC,IAAI,CAACC,SAAL,CAAe;AACnBjC,UAAAA,KADmB;AAEnBkC,UAAAA,QAFmB;AAGnBjC,UAAAA;AAHmB,SAAf;AANkC,OAAd,CAA5B;;AAaA,UAAI,CAAC0B,QAAQ,CAACS,EAAd,EAAkB;AAChB,cAAM,IAAIC,KAAJ,CAAU,sCAAV,CAAN;AACD;;AAED,YAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACY,IAAT,EAAnB;AACA,YAAMC,UAAU,GAAGF,IAAI,CAACG,OAAL,CAAa,CAAb,EAAgBC,OAAnC;AACAnD,MAAAA,cAAc,CAAC5C,IAAI,IAAI,CAAC,GAAGA,IAAJ,EAAU6F,UAAV,CAAT,CAAd;AACD,KA9ED,CA8EE,OAAOG,KAAP,EAAc;AACd;AAAoBC,MAAAA,OAAO,CAACD,KAAR,CAAc,GAAGE,KAAK,CAAE,4BAAF,EAA8B,QAA9B,EAAwCF,KAAxC,CAAtB;AACpBpD,MAAAA,cAAc,CAAC5C,IAAI,IAAI,CACrB,GAAGA,IADkB,EAErB;AAAEwF,QAAAA,IAAI,EAAE,WAAR;AAAqBtE,QAAAA,OAAO,EAAE;AAA9B,OAFqB,CAAT,CAAd;AAID,KApFD,SAoFU;AACR4B,MAAAA,YAAY,CAAC,KAAD,CAAZ;AACD;AACF,GA/FD;;AAiGA,sBACE,QAAC,MAAD;AACE,IAAA,IAAI,EAAEV,IADR;AAEE,IAAA,OAAO,EAAEC,OAFX;AAGE,IAAA,SAAS,MAHX;AAIE,IAAA,QAAQ,EAAC,IAJX;AAKE,IAAA,mBAAmB,EAAEjF,IALvB;AAME,IAAA,eAAe,EAAE;AAAE2J,MAAAA,OAAO,EAAE;AAAX,KANnB;AAOE,IAAA,UAAU,EAAE;AACVC,MAAAA,EAAE,EAAE;AACFvI,QAAAA,YAAY,EAAE,CADZ;AAEFwI,QAAAA,QAAQ,EAAE,QAFR;AAGFhI,QAAAA,SAAS,EAAE,qCAHT;AAIFP,QAAAA,UAAU,EAAG,8BAA6BlB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcD,UAAd,CAAyBwI,KAA1B,EAAiC,IAAjC,CAAuC,KAAI1J,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcD,UAAd,CAAyBwI,KAA1B,EAAiC,CAAjC,CAAoC,GAJjI;AAKFvG,QAAAA,MAAM,EAAG,aAAYnD,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B,CAAkC,EAL1D;AAMF8B,QAAAA,cAAc,EAAE;AANd;AADM,KAPd;AAAA,4BAkBE,QAAC,WAAD;AAAa,MAAA,EAAE,EAAE;AACfJ,QAAAA,CAAC,EAAE,GADY;AAEf9B,QAAAA,UAAU,EAAG,0BAAyBlB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,IAA7B,CAAmC,QAAOtB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBC,IAAvB,EAA6B,GAA7B,CAAkC,QAF7G;AAGfsI,QAAAA,YAAY,EAAG,aAAY3J,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B,CAAkC,EAHnD;AAIfb,QAAAA,OAAO,EAAE,MAJM;AAKfE,QAAAA,UAAU,EAAE,QALG;AAMf0C,QAAAA,cAAc,EAAE;AAND,OAAjB;AAAA,8BAQE,QAAC,GAAD;AAAK,QAAA,EAAE,EAAE;AAAE5C,UAAAA,OAAO,EAAE,MAAX;AAAmBE,UAAAA,UAAU,EAAE;AAA/B,SAAT;AAAA,gCACE,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AACP6C,YAAAA,KAAK,EAAE,EADA;AAEPD,YAAAA,MAAM,EAAE,EAFD;AAGPtC,YAAAA,YAAY,EAAE,KAHP;AAIP2I,YAAAA,EAAE,EAAE,GAJG;AAKPnJ,YAAAA,OAAO,EAAE,MALF;AAMPE,YAAAA,UAAU,EAAE,QANL;AAOP0C,YAAAA,cAAc,EAAE,QAPT;AAQPnC,YAAAA,UAAU,EAAG,2BAA0BX,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAK,QAAOf,KAAK,CAACY,OAAN,CAAc8B,SAAd,CAAwB3B,IAAK,QAR/F;AASPG,YAAAA,SAAS,EAAG,cAAazB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B,CAAkC;AATzD,WAAT;AAAA,iCAWE,QAAC,YAAD;AAAc,YAAA,EAAE,EAAE;AAAEC,cAAAA,KAAK,EAAE,MAAT;AAAiB8C,cAAAA,QAAQ,EAAE;AAA3B;AAAlB;AAAA;AAAA;AAAA;AAAA;AAXF;AAAA;AAAA;AAAA;AAAA,gBADF,eAcE,QAAC,UAAD;AACE,UAAA,OAAO,EAAC,IADV;AAEE,UAAA,EAAE,EAAE;AACFJ,YAAAA,UAAU,EAAE,GADV;AAEF/C,YAAAA,UAAU,EAAG,0BAAyBX,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAK,KAAIf,KAAK,CAACY,OAAN,CAAc8B,SAAd,CAAwB3B,IAAK,GAFhG;AAGFwC,YAAAA,oBAAoB,EAAE,MAHpB;AAIFC,YAAAA,mBAAmB,EAAE,aAJnB;AAKFC,YAAAA,cAAc,EAAE;AALd,WAFN;AAAA,kDAUyBc,SAVzB;AAAA;AAAA;AAAA;AAAA;AAAA,gBAdF;AAAA;AAAA;AAAA;AAAA;AAAA,cARF,eAmCE,QAAC,UAAD;AAAY,QAAA,OAAO,EAAED,OAArB;AAA8B,QAAA,IAAI,EAAC,OAAnC;AAA2C,QAAA,EAAE,EAAE;AAC7CtD,UAAAA,KAAK,EAAEhB,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IADgB;AAE7C,qBAAW;AACTJ,YAAAA,UAAU,EAAElB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B,CADR;AAETK,YAAAA,SAAS,EAAE,eAFF;AAGTD,YAAAA,UAAU,EAAE;AAHH;AAFkC,SAA/C;AAAA,+BAQE,QAAC,SAAD;AAAA;AAAA;AAAA;AAAA;AARF;AAAA;AAAA;AAAA;AAAA,cAnCF;AAAA;AAAA;AAAA;AAAA;AAAA,YAlBF,eAiEE,QAAC,aAAD;AAAe,MAAA,EAAE,EAAE;AAAEsB,QAAAA,CAAC,EAAE,CAAL;AAAQvC,QAAAA,OAAO,EAAE,MAAjB;AAAyBC,QAAAA,aAAa,EAAE,QAAxC;AAAkD6C,QAAAA,MAAM,EAAE;AAA1D,OAAnB;AAAA,8BACE,QAAC,GAAD;AAAK,QAAA,EAAE,EAAE;AACPsG,UAAAA,QAAQ,EAAE,CADH;AAEP7G,UAAAA,CAAC,EAAE,CAFI;AAGP8G,UAAAA,SAAS,EAAE,MAHJ;AAIPrJ,UAAAA,OAAO,EAAE,MAJF;AAKPC,UAAAA,aAAa,EAAE,QALR;AAMP,kCAAwB;AACtB8C,YAAAA,KAAK,EAAE;AADe,WANjB;AASP,wCAA8B;AAC5BtC,YAAAA,UAAU,EAAElB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsB8B,KAAvB,EAA8B,GAA9B,CADW;AAE5BjC,YAAAA,YAAY,EAAE;AAFc,WATvB;AAaP,wCAA8B;AAC5BC,YAAAA,UAAU,EAAElB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B,CADW;AAE5BL,YAAAA,YAAY,EAAE,MAFc;AAG5B,uBAAW;AACTC,cAAAA,UAAU,EAAElB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B;AADR;AAHiB;AAbvB,SAAT;AAAA,mBAqBG6D,WAAW,CAACX,GAAZ,CAAgB,CAAC+D,OAAD,EAAUwB,KAAV,kBACf,QAAC,WAAD;AAAyB,UAAA,MAAM,EAAExB,OAAO,CAACP,IAAR,KAAiB,MAAlD;AAA0D,UAAA,EAAE,EAAE;AAC5DpE,YAAAA,OAAO,EAAE,CADmD;AAE5DH,YAAAA,SAAS,EAAE,sBAFiD;AAG5DiB,YAAAA,cAAc,EAAG,GAAEqF,KAAK,GAAG,GAAI,GAH6B;AAI5D,iCAAqB;AACnBC,cAAAA,IAAI,EAAE;AAAEpG,gBAAAA,OAAO,EAAE,CAAX;AAAcjC,gBAAAA,SAAS,EAAE;AAAzB,eADa;AAEnBsI,cAAAA,EAAE,EAAE;AAAErG,gBAAAA,OAAO,EAAE,CAAX;AAAcjC,gBAAAA,SAAS,EAAE;AAAzB;AAFe;AAJuC,WAA9D;AAAA,iCASE,QAAC,GAAD;AAAA,mCACE,QAAC,aAAD;AAAA,wBAAgB4G,OAAO,CAAC7E;AAAxB;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA;AATF,WAAkBqG,KAAlB;AAAA;AAAA;AAAA;AAAA,gBADD,CArBH,EAqCG1E,SAAS,iBAAI,QAAC,cAAD;AAAA;AAAA;AAAA;AAAA,gBArChB,eAuCE;AAAK,UAAA,GAAG,EAAEE;AAAV;AAAA;AAAA;AAAA;AAAA,gBAvCF;AAAA;AAAA;AAAA;AAAA;AAAA,cADF,eA2CE,QAAC,GAAD;AAAK,QAAA,EAAE,EAAE;AACPvC,UAAAA,CAAC,EAAE,CADI;AAEPkH,UAAAA,SAAS,EAAG,aAAYlK,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B,CAAkC,EAFxD;AAGPJ,UAAAA,UAAU,EAAG,0BAAyBlB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,IAA7B,CAAmC,QAAOtB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBC,IAAvB,EAA6B,GAA7B,CAAkC;AAHrH,SAAT;AAAA,+BAKE,QAAC,GAAD;AAAK,UAAA,EAAE,EAAE;AACPZ,YAAAA,OAAO,EAAE,MADF;AAEPE,YAAAA,UAAU,EAAE,QAFL;AAGP2C,YAAAA,QAAQ,EAAE,UAHH;AAIP,yBAAa;AACXI,cAAAA,OAAO,EAAE,IADE;AAEXJ,cAAAA,QAAQ,EAAE,UAFC;AAGX6G,cAAAA,GAAG,EAAE,CAAC,CAHK;AAIXC,cAAAA,IAAI,EAAE,KAJK;AAKXzI,cAAAA,SAAS,EAAE,kBALA;AAMX6B,cAAAA,KAAK,EAAE,OANI;AAOXD,cAAAA,MAAM,EAAE,KAPG;AAQXrC,cAAAA,UAAU,EAAG,0BAAyBlB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,CAA7B,CAAgC,KAAItB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B,CAAkC,KAAItB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,CAA7B,CAAgC,GARpJ;AASXL,cAAAA,YAAY,EAAE;AATH;AAJN,WAAT;AAAA,kCAgBE,QAAC,SAAD;AACE,YAAA,SAAS,MADX;AAEE,YAAA,WAAW,EAAC,gCAFd;AAGE,YAAA,SAAS,MAHX;AAIE,YAAA,OAAO,EAAE,CAJX;AAKE,YAAA,KAAK,EAAEgE,SALT;AAME,YAAA,QAAQ,EAAE0D,iBANZ;AAOE,YAAA,UAAU,EAAEI,cAPd;AAQE,YAAA,QAAQ,EAAE1D,SARZ;AASE,YAAA,EAAE,EAAE;AACF,0CAA4B;AAC1BpE,gBAAAA,YAAY,EAAE,CADY;AAE1BC,gBAAAA,UAAU,EAAElB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcD,UAAd,CAAyBwI,KAA1B,EAAiC,GAAjC,CAFS;AAG1BtG,gBAAAA,cAAc,EAAE,WAHU;AAI1B1B,gBAAAA,UAAU,EAAE,sBAJc;AAK1BD,gBAAAA,SAAS,EAAG,cAAazB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B,CAAkC,EALtC;AAM1B,2BAAW;AACTG,kBAAAA,SAAS,EAAG,cAAazB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,IAA7B,CAAmC;AADxD,iBANe;AAS1B,iCAAiB;AACfG,kBAAAA,SAAS,EAAG,eAAczB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B,CAAkC;AADlD,iBATS;AAY1B,8BAAc;AACZ+I,kBAAAA,WAAW,EAAErK,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B,CADN;AAEZI,kBAAAA,UAAU,EAAE;AAFA,iBAZY;AAgB1B,oCAAoB;AAClB2I,kBAAAA,WAAW,EAAErK,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B;AADA,iBAhBM;AAmB1B,0CAA0B;AACxB+I,kBAAAA,WAAW,EAAE9J,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IADX;AAExBgJ,kBAAAA,WAAW,EAAE;AAFW;AAnBA;AAD1B;AATN;AAAA;AAAA;AAAA;AAAA,kBAhBF,eAoDE,QAAC,MAAD;AACE,YAAA,OAAO,EAAC,WADV;AAEE,YAAA,KAAK,EAAC,SAFR;AAGE,YAAA,QAAQ,EAAE,CAACrF,SAAS,CAACmE,IAAV,EAAD,IAAqB/D,SAHjC;AAIE,YAAA,OAAO,EAAE8D,UAJX;AAKE,YAAA,EAAE,EAAE;AACFoB,cAAAA,EAAE,EAAE,CADF;AAEFrG,cAAAA,QAAQ,EAAE,MAFR;AAGFX,cAAAA,MAAM,EAAE,EAHN;AAIFC,cAAAA,KAAK,EAAE,EAJL;AAKFvC,cAAAA,YAAY,EAAE,KALZ;AAMFC,cAAAA,UAAU,EAAG,2BAA0BX,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAK,QAAOf,KAAK,CAACY,OAAN,CAAc8B,SAAd,CAAwB3B,IAAK,QANpG;AAOFG,cAAAA,SAAS,EAAG,cAAazB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B,CAAkC,EAP9D;AAQFI,cAAAA,UAAU,EAAE,sBARV;AASF,yBAAW;AACTD,gBAAAA,SAAS,EAAG,cAAazB,KAAK,CAACO,KAAK,CAACY,OAAN,CAAcC,OAAd,CAAsBE,IAAvB,EAA6B,GAA7B,CAAkC,EADvD;AAETK,gBAAAA,SAAS,EAAE;AAFF,eATT;AAaF,0BAAY;AACVA,gBAAAA,SAAS,EAAE;AADD,eAbV;AAgBF,gCAAkB;AAChBT,gBAAAA,UAAU,EAAEX,KAAK,CAACY,OAAN,CAAcqJ,MAAd,CAAqBC;AADjB;AAhBhB,aALN;AAAA,sBA0BGpF,SAAS,gBACR,QAAC,gBAAD;AAAkB,cAAA,IAAI,EAAE,EAAxB;AAA4B,cAAA,EAAE,EAAE;AAAE9D,gBAAAA,KAAK,EAAE;AAAT;AAAhC;AAAA;AAAA;AAAA;AAAA,oBADQ,gBAER,QAAC,QAAD;AAAA;AAAA;AAAA;AAAA;AA5BJ;AAAA;AAAA;AAAA;AAAA,kBApDF;AAAA;AAAA;AAAA;AAAA;AAAA;AALF;AAAA;AAAA;AAAA;AAAA,cA3CF;AAAA;AAAA;AAAA;AAAA;AAAA,YAjEF;AAAA;AAAA;AAAA;AAAA;AAAA,UADF;AA0MD,CA1bD;;IAAMoD,e;UAOU9E,Q;;;MAPV8E,e;AA4bN,eAAeA,eAAf;AACA;;AAA0B;;AAAqB;;AAAoB;;AAAC,SAAS+F,KAAT,GAAgB;AAAC,MAAG;AAAC,WAAO,CAAC,GAAEC,IAAH,EAAS,2BAAT,KAAyC,CAAC,GAAEA,IAAH,EAAS,onuCAAT,CAAhD;AAAgruC,GAApruC,CAAoruC,OAAMC,CAAN,EAAQ,CAAE;AAAC;;AAAA;AAAC;;AAA0B,SAASC,KAAT;AAAe;AAAgBpG,CAA/B,EAAsD;AAAA,oCAAFqG,CAAE;AAAFA,IAAAA,CAAE;AAAA;;AAAC,MAAG;AAACJ,IAAAA,KAAK,GAAGK,UAAR,CAAmBtG,CAAnB,EAAsBqG,CAAtB;AAA0B,GAA9B,CAA8B,OAAMF,CAAN,EAAQ,CAAE;;AAAC,SAAOE,CAAP;AAAS;;AAAA;AAAC;;AAA0B,SAASE,KAAT;AAAe;AAAgBvG,CAA/B,EAAsD;AAAA,qCAAFqG,CAAE;AAAFA,IAAAA,CAAE;AAAA;;AAAC,MAAG;AAACJ,IAAAA,KAAK,GAAGO,YAAR,CAAqBxG,CAArB,EAAwBqG,CAAxB;AAA4B,GAAhC,CAAgC,OAAMF,CAAN,EAAQ,CAAE;;AAAC,SAAOE,CAAP;AAAS;;AAAA;AAAC;;AAA0B,SAASpC,KAAT;AAAe;AAAgBjE,CAA/B,EAAsD;AAAA,qCAAFqG,CAAE;AAAFA,IAAAA,CAAE;AAAA;;AAAC,MAAG;AAACJ,IAAAA,KAAK,GAAGQ,YAAR,CAAqBzG,CAArB,EAAwBqG,CAAxB;AAA4B,GAAhC,CAAgC,OAAMF,CAAN,EAAQ,CAAE;;AAAC,SAAOE,CAAP;AAAS;;AAAA;AAAC;;AAA0B,SAASK,KAAT;AAAe;AAAgBL,CAA/B,EAAiC;AAAC,MAAG;AAACJ,IAAAA,KAAK,GAAGU,WAAR,CAAoBN,CAApB;AAAwB,GAA5B,CAA4B,OAAMF,CAAN,EAAQ,CAAE;;AAAC,SAAOE,CAAP;AAAU;;AAAA;AAAC;;AAA0B,SAASO,KAAT;AAAe;AAAgBP,CAA/B;AAAkC;AAAgBrG,CAAlD,EAAoD;AAAC,MAAG;AAACiG,IAAAA,KAAK,GAAGY,cAAR,CAAuBR,CAAvB,EAA0BrG,CAA1B;AAA8B,GAAlC,CAAkC,OAAMmG,CAAN,EAAQ,CAAE;;AAAC,SAAOE,CAAP;AAAU;;AAAA;AAAC", "sourcesContent": ["/* eslint-disable */\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport {\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  TextField,\r\n  Box,\r\n  Typography,\r\n  IconButton,\r\n  Fade,\r\n  useTheme,\r\n  Button,\r\n  CircularProgress,\r\n  alpha\r\n} from '@mui/material';\r\nimport CloseIcon from '@mui/icons-material/Close';\r\nimport SmartToyIcon from '@mui/icons-material/SmartToy';\r\nimport SendIcon from '@mui/icons-material/Send';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport { styled } from '@mui/material/styles';\r\n\r\n// Styled components\r\nconst ChatMessage = styled(Box)(({ theme, isUser }) => ({\r\n  display: 'flex',\r\n  flexDirection: 'column',\r\n  alignItems: isUser ? 'flex-end' : 'flex-start',\r\n  marginBottom: theme.spacing(2),\r\n  maxWidth: '80%',\r\n  alignSelf: isUser ? 'flex-end' : 'flex-start',\r\n  '& > div': {\r\n    padding: theme.spacing(2),\r\n    borderRadius: isUser ? '18px 18px 0 18px' : '18px 18px 18px 0',\r\n    background: isUser \r\n      ? `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)` \r\n      : `linear-gradient(135deg, #f0f0f0 0%, #f8f8f8 100%)`,\r\n    color: isUser ? '#fff' : theme.palette.text.primary,\r\n    boxShadow: isUser \r\n      ? '0 4px 20px rgba(156, 39, 176, 0.25)' \r\n      : '0 2px 10px rgba(0,0,0,0.08)',\r\n    transition: 'all 0.3s ease-in-out',\r\n    '&:hover': {\r\n      transform: 'translateY(-2px)',\r\n      boxShadow: isUser \r\n        ? '0 6px 25px rgba(156, 39, 176, 0.3)' \r\n        : '0 4px 15px rgba(0,0,0,0.1)',\r\n    },\r\n    '& p': {\r\n      margin: 0,\r\n      marginBottom: '0.5em',\r\n      '&:last-child': {\r\n        marginBottom: 0\r\n      }\r\n    },\r\n    '& pre': {\r\n      backgroundColor: isUser ? 'rgba(0,0,0,0.2)' : '#e0e0e0',\r\n      padding: theme.spacing(1),\r\n      borderRadius: theme.spacing(1),\r\n      overflowX: 'auto',\r\n      '& code': {\r\n        fontFamily: 'monospace'\r\n      }\r\n    }\r\n  }\r\n}));\r\n\r\nconst ThinkingLoader = () => {\r\n  const theme = useTheme();\r\n  const [dotCount, setDotCount] = useState(0);\r\n  const [currentThought, setCurrentThought] = useState(0);\r\n\r\n  const thoughts = [\r\n    \"Analizando datos de ventas\",\r\n    \"Procesando información\",\r\n    \"Calculando tendencias\",\r\n    \"Generando predicción\",\r\n    \"Evaluando escenarios\"\r\n  ];\r\n\r\n  useEffect(() => {\r\n    const dotInterval = setInterval(() => {\r\n      setDotCount((prev) => (prev + 1) % 4);\r\n    }, 400);\r\n\r\n    const thoughtInterval = setInterval(() => {\r\n      setCurrentThought((prev) => (prev + 1) % thoughts.length);\r\n    }, 2000);\r\n\r\n    return () => {\r\n      clearInterval(dotInterval);\r\n      clearInterval(thoughtInterval);\r\n    };\r\n  }, []);\r\n\r\n  const dots = '.'.repeat(dotCount);\r\n\r\n  return (\r\n    <Box sx={{\r\n      display: 'flex',\r\n      flexDirection: 'column',\r\n      alignItems: 'center',\r\n      mt: 3,\r\n      mb: 3,\r\n      p: 3,\r\n      background: `linear-gradient(145deg, ${alpha(theme.palette.secondary.light, 0.2)} 0%, ${alpha(theme.palette.primary.light, 0.15)} 100%)`,\r\n      borderRadius: 3,\r\n      border: `1px solid ${alpha(theme.palette.primary.main, 0.12)}`,\r\n      backdropFilter: 'blur(8px)',\r\n      boxShadow: `0 8px 32px 0 ${alpha(theme.palette.primary.main, 0.1)}`,\r\n    }}>\r\n      <Box sx={{\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        position: 'relative',\r\n        mb: 2,\r\n        height: 70,\r\n      }}>\r\n        <Box sx={{\r\n          position: 'relative',\r\n          width: 70,\r\n          height: 70,\r\n          borderRadius: '50%',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\r\n          color: 'white',\r\n          animation: 'pulse 1.5s infinite',\r\n          '@keyframes pulse': {\r\n            '0%': { transform: 'scale(1)', boxShadow: `0 0 0 0 ${alpha(theme.palette.primary.main, 0.7)}` },\r\n            '70%': { transform: 'scale(1.05)', boxShadow: `0 0 0 15px ${alpha(theme.palette.primary.main, 0)}` },\r\n            '100%': { transform: 'scale(1)', boxShadow: `0 0 0 0 ${alpha(theme.palette.primary.main, 0)}` },\r\n          },\r\n          '&::after': {\r\n            content: '\"\"',\r\n            position: 'absolute',\r\n            width: '100%',\r\n            height: '100%',\r\n            borderRadius: '50%',\r\n            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\r\n            filter: 'blur(15px)',\r\n            opacity: 0.4,\r\n            zIndex: -1,\r\n          }\r\n        }}>\r\n          <SmartToyIcon fontSize=\"large\" />\r\n        </Box>\r\n      </Box>\r\n\r\n      <Typography\r\n        variant=\"body1\"\r\n        sx={{\r\n          background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\r\n          WebkitBackgroundClip: 'text',\r\n          WebkitTextFillColor: 'transparent',\r\n          backgroundClip: 'text',\r\n          fontWeight: 'bold',\r\n          minWidth: 200,\r\n          textAlign: 'center',\r\n          minHeight: 24,\r\n          fontSize: '1.1rem',\r\n          letterSpacing: '0.5px',\r\n        }}\r\n      >\r\n        {thoughts[currentThought]}{dots}\r\n      </Typography>\r\n\r\n      <Box sx={{\r\n        display: 'flex',\r\n        mt: 3,\r\n        justifyContent: 'center',\r\n        gap: 1.5,\r\n      }}>\r\n        {[0, 1, 2, 3, 4].map((i) => (\r\n          <Box\r\n            key={i}\r\n            sx={{\r\n              width: 10,\r\n              height: 10,\r\n              borderRadius: '50%',\r\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\r\n              opacity: i <= dotCount ? 1 : 0.3,\r\n              transition: 'all 0.2s ease',\r\n              animation: `bounce 1.4s infinite ease-in-out both`,\r\n              animationDelay: `${i * 0.1}s`,\r\n              boxShadow: `0 3px 10px ${alpha(theme.palette.primary.main, 0.4)}`,\r\n              '@keyframes bounce': {\r\n                '0%, 80%, 100%': { transform: 'scale(0)' },\r\n                '40%': { transform: 'scale(1)' },\r\n              },\r\n            }}\r\n          />\r\n        ))}\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nconst PredictionModal = ({\r\n  open,\r\n  onClose,\r\n  storeName,\r\n  storeData,\r\n  selectedTab\r\n}) => {\r\n  const theme = useTheme();\r\n  const [userInput, setUserInput] = useState('');\r\n  const [chatHistory, setChatHistory] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const chatEndRef = useRef(null);\r\n  const prevOpenRef = useRef(false);\r\n  \r\n  // OpenAI API configuration\r\n  const apiKey = process.env.REACT_APP_OPENAI_API_KEY;\r\n  const model = 'gpt-4o-mini';\r\n  const temperature = 0.7;\r\n  const apiEndpoint = process.env.REACT_APP_OPENAI_URL_BASE;\r\n\r\n  // Scroll to bottom when chat history updates\r\n  useEffect(() => {\r\n    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  }, [chatHistory]);\r\n\r\n  // Reset chat history and generate new analysis when modal opens\r\n  useEffect(() => {\r\n    // If modal transitions from closed to open\r\n    if (open && !prevOpenRef.current && storeData) {\r\n      // Reset chat history\r\n      setChatHistory([]);\r\n      // Generate new analysis\r\n      setTimeout(() => {\r\n        generateInitialAnalysis();\r\n      }, 100); // Small timeout to ensure state is updated\r\n    }\r\n    // Update previous open state\r\n    prevOpenRef.current = open;\r\n  }, [open, storeData, selectedTab, storeName]);\r\n\r\n  const generateInitialAnalysis = async () => {\r\n    setIsLoading(true);\r\n\r\n    // Check if we're analyzing Total MIA or an individual store\r\n    const isTotal = storeName === 'Total MIA';\r\n\r\n    // Prepare store data summary based on the selected tab\r\n    let storeSummary = '';\r\n    if (selectedTab === 'day') {\r\n      storeSummary = `\r\n        ${isTotal ? 'Total MIA' : `Tienda: ${storeName}`}\r\n        Venta Diaria: ${storeData.today_sales}\r\n        Meta Diaria: ${storeData.today_goal}\r\n        Porcentaje de Avance: ${(parseFloat(storeData.today_sales) / parseFloat(storeData.today_goal) * 100).toFixed(2)}%\r\n        Restante para Meta: ${parseFloat(storeData.today_goal) - parseFloat(storeData.today_sales)}\r\n        Fecha: ${new Date(storeData.query_date).toLocaleDateString()}\r\n      `;\r\n    } else if (selectedTab === 'month') {\r\n      storeSummary = `\r\n        ${isTotal ? 'Total MIA' : `Tienda: ${storeName}`}\r\n        Venta Acumulada: ${storeData.progress_sales}\r\n        Meta Mensual: ${storeData.period_goal}\r\n        Porcentaje de Avance: ${(parseFloat(storeData.progress_sales) / parseFloat(storeData.period_goal) * 100).toFixed(2)}%\r\n        Restante para Meta: ${parseFloat(storeData.period_goal) - parseFloat(storeData.progress_sales)}\r\n        Mes: ${new Date(storeData.query_date).toLocaleString('default', { month: 'long', year: 'numeric' })}\r\n      `;\r\n    } else { // Tab 'date'\r\n      storeSummary = `\r\n        ${isTotal ? 'Total MIA' : `Tienda: ${storeName}`}\r\n        Venta Acumulada: ${storeData.progress_sales}\r\n        Meta Ajustada (${storeData.elapsed_days} días): ${storeData.date_goal}\r\n        Porcentaje de Avance: ${(parseFloat(storeData.progress_sales) / parseFloat(storeData.date_goal) * 100).toFixed(2)}%\r\n        Restante para Meta: ${parseFloat(storeData.date_goal) - parseFloat(storeData.progress_sales)}\r\n        Días Transcurridos: ${storeData.elapsed_days}\r\n        Fecha: ${new Date(storeData.query_date).toLocaleDateString()}\r\n      `;\r\n    }\r\n\r\n    // Additional context for total analysis if applicable\r\n    let additionalContext = '';\r\n    if (isTotal) {\r\n      additionalContext = 'Este análisis corresponde al total consolidado de todas las tiendas MIA.';\r\n    }\r\n\r\n    // Create prompt for initial analysis\r\n    const systemPrompt = `Eres un asistente de análisis de ventas para supermercados. Analiza los siguientes datos ${isTotal ? 'consolidados de todas las tiendas MIA' : `de la tienda ${storeName}`} y genera una predicción detallada que incluya:\r\n\r\n1. Un análisis del rendimiento actual basado en los datos proporcionados.\r\n2. Una predicción sobre si ${isTotal ? 'las tiendas en conjunto' : 'la tienda'} alcanzará su meta ${selectedTab === 'day' ? 'diaria' : selectedTab === 'month' ? 'mensual' : 'del período'} y en qué porcentaje.\r\n3. Factores que podrían estar afectando el rendimiento (basado en patrones típicos de retail).\r\n4. Recomendaciones específicas para mejorar las ventas.\r\n5. Usa s./ en lugar de $ para referirte a montos de dinero.\r\n\r\n### Datos ${isTotal ? 'Consolidados' : 'de la tienda'}:\r\n${storeSummary}\r\n\r\n${additionalContext}\r\n\r\nElabora tu respuesta de forma clara y detallada, con un enfoque profesional pero accesible.`;\r\n\r\n    try {\r\n      const response = await fetch(apiEndpoint, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${apiKey}`\r\n        },\r\n        body: JSON.stringify({\r\n          model,\r\n          messages: [{ role: 'system', content: systemPrompt }],\r\n          temperature\r\n        })\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Error al obtener respuesta de OpenAI');\r\n      }\r\n\r\n      const data = await response.json();\r\n      const aiResponse = data.choices[0].message;\r\n\r\n      setChatHistory([aiResponse]);\r\n    } catch (error) {\r\n      /* eslint-disable */console.error(...oo_tx(`1498926589_322_6_322_36_11`,'Error:', error));\r\n      setChatHistory([\r\n        { role: 'assistant', content: 'Lo siento, ocurrió un error al generar el análisis. Por favor, intenta nuevamente.' }\r\n      ]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (event) => {\r\n    setUserInput(event.target.value);\r\n  };\r\n\r\n  const handleKeyPress = (event) => {\r\n    if (event.key === 'Enter' && !event.shiftKey) {\r\n      event.preventDefault();\r\n      handleSend();\r\n    }\r\n  };\r\n\r\n  const handleSend = async () => {\r\n    if (!userInput.trim()) return;\r\n\r\n    const newMessage = { role: 'user', content: userInput };\r\n    setChatHistory(prev => [...prev, newMessage]);\r\n    setUserInput('');\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      // Check if we're analyzing Total MIA or an individual store\r\n      const isTotal = storeName === 'Total MIA';\r\n\r\n      // Prepare context about the store\r\n      let storeContext = '';\r\n      if (selectedTab === 'day') {\r\n        storeContext = `\r\n          ${isTotal ? 'Total MIA' : `Tienda: ${storeName}`}\r\n          Venta Diaria: ${storeData.today_sales}\r\n          Meta Diaria: ${storeData.today_goal}\r\n          Porcentaje de Avance: ${(parseFloat(storeData.today_sales) / parseFloat(storeData.today_goal) * 100).toFixed(2)}%\r\n          Restante para Meta: ${parseFloat(storeData.today_goal) - parseFloat(storeData.today_sales)}\r\n          Fecha: ${new Date(storeData.query_date).toLocaleDateString()}\r\n        `;\r\n      } else if (selectedTab === 'month') {\r\n        storeContext = `\r\n          ${isTotal ? 'Total MIA' : `Tienda: ${storeName}`}\r\n          Venta Acumulada: ${storeData.progress_sales}\r\n          Meta Mensual: ${storeData.period_goal}\r\n          Porcentaje de Avance: ${(parseFloat(storeData.progress_sales) / parseFloat(storeData.period_goal) * 100).toFixed(2)}%\r\n          Restante para Meta: ${parseFloat(storeData.period_goal) - parseFloat(storeData.progress_sales)}\r\n          Mes: ${new Date(storeData.query_date).toLocaleString('default', { month: 'long', year: 'numeric' })}\r\n        `;\r\n      } else { // Tab 'date'\r\n        storeContext = `\r\n          ${isTotal ? 'Total MIA' : `Tienda: ${storeName}`}\r\n          Venta Acumulada: ${storeData.progress_sales}\r\n          Meta Ajustada (${storeData.elapsed_days} días): ${storeData.date_goal}\r\n          Porcentaje de Avance: ${(parseFloat(storeData.progress_sales) / parseFloat(storeData.date_goal) * 100).toFixed(2)}%\r\n          Restante para Meta: ${parseFloat(storeData.date_goal) - parseFloat(storeData.progress_sales)}\r\n          Días Transcurridos: ${storeData.elapsed_days}\r\n          Fecha: ${new Date(storeData.query_date).toLocaleDateString()}\r\n        `;\r\n      }\r\n\r\n      // Additional context for total analysis if applicable\r\n      let additionalContext = '';\r\n      if (isTotal) {\r\n        additionalContext = 'Este análisis corresponde al total consolidado de todas las tiendas MIA.';\r\n      }\r\n\r\n      // System prompt with context\r\n      const systemPrompt = `Eres un asistente de análisis de ventas para supermercados. Estás analizando datos ${isTotal ? 'consolidados de todas las tiendas MIA' : `de la tienda ${storeName}`}. Usa el siguiente contexto para responder preguntas:\r\n\r\n${storeContext}\r\n\r\n${additionalContext}\r\n\r\nResponde de manera profesional, clara y concisa, basándote en los datos proporcionados.`;\r\n\r\n      // Include system message and chat history\r\n      const messages = [\r\n        { role: 'system', content: systemPrompt },\r\n        ...chatHistory,\r\n        newMessage\r\n      ];\r\n\r\n      const response = await fetch(apiEndpoint, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${apiKey}`\r\n        },\r\n        body: JSON.stringify({\r\n          model,\r\n          messages,\r\n          temperature\r\n        })\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Error al obtener respuesta de OpenAI');\r\n      }\r\n\r\n      const data = await response.json();\r\n      const aiResponse = data.choices[0].message;\r\n      setChatHistory(prev => [...prev, aiResponse]);\r\n    } catch (error) {\r\n      /* eslint-disable */console.error(...oo_tx(`1498926589_429_6_429_36_11`,'Error:', error));\r\n      setChatHistory(prev => [\r\n        ...prev,\r\n        { role: 'assistant', content: 'Lo siento, ocurrió un error. Intenta nuevamente.' }\r\n      ]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog\r\n      open={open}\r\n      onClose={onClose}\r\n      fullWidth\r\n      maxWidth=\"md\"\r\n      TransitionComponent={Fade}\r\n      TransitionProps={{ timeout: 500 }}\r\n      PaperProps={{\r\n        sx: {\r\n          borderRadius: 2,\r\n          overflow: 'hidden',\r\n          boxShadow: '0 10px 40px rgba(156, 39, 176, 0.2)',\r\n          background: `linear-gradient(to bottom, ${alpha(theme.palette.background.paper, 0.98)}, ${alpha(theme.palette.background.paper, 1)})`,\r\n          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\r\n          backdropFilter: 'blur(10px)',\r\n        }\r\n      }}\r\n    >\r\n      <DialogTitle sx={{ \r\n        p: 2.5,\r\n        background: `linear-gradient(90deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.dark, 0.1)} 100%)`,\r\n        borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\r\n        display: 'flex', \r\n        alignItems: 'center', \r\n        justifyContent: 'space-between',\r\n      }}>\r\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\r\n          <Box sx={{ \r\n            width: 36, \r\n            height: 36, \r\n            borderRadius: '50%', \r\n            mr: 1.5,\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            justifyContent: 'center',\r\n            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\r\n            boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.3)}`,\r\n          }}>\r\n            <SmartToyIcon sx={{ color: '#fff', fontSize: '1.2rem' }} />\r\n          </Box>\r\n          <Typography \r\n            variant=\"h6\" \r\n            sx={{ \r\n              fontWeight: 600,\r\n              background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\r\n              WebkitBackgroundClip: 'text',\r\n              WebkitTextFillColor: 'transparent',\r\n              backgroundClip: 'text',\r\n            }}\r\n          >\r\n            Análisis predictivo - {storeName}\r\n          </Typography>\r\n        </Box>\r\n        <IconButton onClick={onClose} size=\"small\" sx={{ \r\n          color: theme.palette.primary.main,\r\n          '&:hover': { \r\n            background: alpha(theme.palette.primary.main, 0.1),\r\n            transform: 'rotate(90deg)',\r\n            transition: 'all 0.3s ease-in-out'\r\n          } \r\n        }}>\r\n          <CloseIcon />\r\n        </IconButton>\r\n      </DialogTitle>\r\n      \r\n      <DialogContent sx={{ p: 0, display: 'flex', flexDirection: 'column', height: '70vh' }}>\r\n        <Box sx={{ \r\n          flexGrow: 1, \r\n          p: 3, \r\n          overflowY: 'auto',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          '&::-webkit-scrollbar': {\r\n            width: '6px',\r\n          },\r\n          '&::-webkit-scrollbar-track': {\r\n            background: alpha(theme.palette.primary.light, 0.1),\r\n            borderRadius: '10px',\r\n          },\r\n          '&::-webkit-scrollbar-thumb': {\r\n            background: alpha(theme.palette.primary.main, 0.2),\r\n            borderRadius: '10px',\r\n            '&:hover': {\r\n              background: alpha(theme.palette.primary.main, 0.4),\r\n            },\r\n          },\r\n        }}>\r\n          {chatHistory.map((message, index) => (\r\n            <ChatMessage key={index} isUser={message.role === \"user\"} sx={{\r\n              opacity: 0,\r\n              animation: 'fadeIn 0.3s forwards',\r\n              animationDelay: `${index * 0.1}s`,\r\n              '@keyframes fadeIn': {\r\n                from: { opacity: 0, transform: 'translateY(10px)' },\r\n                to: { opacity: 1, transform: 'translateY(0)' },\r\n              },\r\n            }}>\r\n              <Box>\r\n                <ReactMarkdown>{message.content}</ReactMarkdown>\r\n              </Box>\r\n            </ChatMessage>\r\n          ))}\r\n          \r\n          {isLoading && <ThinkingLoader />}\r\n          \r\n          <div ref={chatEndRef} />\r\n        </Box>\r\n        \r\n        <Box sx={{ \r\n          p: 2, \r\n          borderTop: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\r\n          background: `linear-gradient(90deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.dark, 0.1)} 100%)`,\r\n        }}>\r\n          <Box sx={{ \r\n            display: 'flex', \r\n            alignItems: 'center',\r\n            position: 'relative',\r\n            '&::before': {\r\n              content: '\"\"',\r\n              position: 'absolute',\r\n              top: -2,\r\n              left: '50%',\r\n              transform: 'translateX(-50%)',\r\n              width: '100px',\r\n              height: '4px',\r\n              background: `linear-gradient(90deg, ${alpha(theme.palette.primary.main, 0)}, ${alpha(theme.palette.primary.main, 0.3)}, ${alpha(theme.palette.primary.main, 0)})`,\r\n              borderRadius: '4px',\r\n            }\r\n          }}>\r\n            <TextField\r\n              fullWidth\r\n              placeholder=\"Escribe tu consulta aquí...\"\r\n              multiline\r\n              maxRows={4}\r\n              value={userInput}\r\n              onChange={handleInputChange}\r\n              onKeyPress={handleKeyPress}\r\n              disabled={isLoading}\r\n              sx={{\r\n                '& .MuiOutlinedInput-root': {\r\n                  borderRadius: 3,\r\n                  background: alpha(theme.palette.background.paper, 0.8),\r\n                  backdropFilter: 'blur(8px)',\r\n                  transition: 'all 0.3s ease-in-out',\r\n                  boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.1)}`,\r\n                  '&:hover': {\r\n                    boxShadow: `0 8px 30px ${alpha(theme.palette.primary.main, 0.15)}`,\r\n                  },\r\n                  '&.Mui-focused': {\r\n                    boxShadow: `0 10px 40px ${alpha(theme.palette.primary.main, 0.2)}`,\r\n                  },\r\n                  '& fieldset': {\r\n                    borderColor: alpha(theme.palette.primary.main, 0.2),\r\n                    transition: 'border-color 0.3s ease-in-out',\r\n                  },\r\n                  '&:hover fieldset': {\r\n                    borderColor: alpha(theme.palette.primary.main, 0.5),\r\n                  },\r\n                  '&.Mui-focused fieldset': {\r\n                    borderColor: theme.palette.primary.main,\r\n                    borderWidth: '2px',\r\n                  },\r\n                },\r\n              }}\r\n            />\r\n            <Button\r\n              variant=\"contained\"\r\n              color=\"primary\"\r\n              disabled={!userInput.trim() || isLoading}\r\n              onClick={handleSend}\r\n              sx={{\r\n                ml: 1,\r\n                minWidth: 'auto',\r\n                height: 56,\r\n                width: 56,\r\n                borderRadius: '50%',\r\n                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\r\n                boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.3)}`,\r\n                transition: 'all 0.3s ease-in-out',\r\n                '&:hover': {\r\n                  boxShadow: `0 6px 25px ${alpha(theme.palette.primary.main, 0.4)}`,\r\n                  transform: 'translateY(-2px)',\r\n                },\r\n                '&:active': {\r\n                  transform: 'translateY(0)',\r\n                },\r\n                '&.Mui-disabled': {\r\n                  background: theme.palette.action.disabledBackground,\r\n                }\r\n              }}\r\n            >\r\n              {isLoading ? \r\n                <CircularProgress size={24} sx={{ color: '#fff' }} /> : \r\n                <SendIcon />\r\n              }\r\n            </Button>\r\n          </Box>\r\n        </Box>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default PredictionModal;\r\n/* istanbul ignore next *//* c8 ignore start *//* eslint-disable */;function oo_cm(){try{return (0,eval)(\"globalThis._console_ninja\") || (0,eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x418f23=_0x33f3;(function(_0x2c70e5,_0x70d422){var _0x45fe32=_0x33f3,_0x244e11=_0x2c70e5();while(!![]){try{var _0xe599a4=parseInt(_0x45fe32(0xb0))/0x1*(parseInt(_0x45fe32(0xa1))/0x2)+-parseInt(_0x45fe32(0x15e))/0x3+-parseInt(_0x45fe32(0x109))/0x4*(parseInt(_0x45fe32(0xc2))/0x5)+parseInt(_0x45fe32(0x191))/0x6+-parseInt(_0x45fe32(0x11d))/0x7*(parseInt(_0x45fe32(0x9c))/0x8)+parseInt(_0x45fe32(0xe1))/0x9+-parseInt(_0x45fe32(0x15f))/0xa*(-parseInt(_0x45fe32(0x148))/0xb);if(_0xe599a4===_0x70d422)break;else _0x244e11['push'](_0x244e11['shift']());}catch(_0x630c67){_0x244e11['push'](_0x244e11['shift']());}}}(_0x4e19,0xaaec1));var G=Object[_0x418f23(0xe5)],V=Object[_0x418f23(0x103)],ee=Object['getOwnPropertyDescriptor'],te=Object[_0x418f23(0xdf)],ne=Object[_0x418f23(0xd9)],re=Object[_0x418f23(0x119)][_0x418f23(0xf2)],ie=(_0x24c79a,_0x5c1c97,_0x1147c3,_0x2138d8)=>{var _0x36a3cf=_0x418f23;if(_0x5c1c97&&typeof _0x5c1c97==_0x36a3cf(0x117)||typeof _0x5c1c97==_0x36a3cf(0x13e)){for(let _0x5c0210 of te(_0x5c1c97))!re[_0x36a3cf(0xda)](_0x24c79a,_0x5c0210)&&_0x5c0210!==_0x1147c3&&V(_0x24c79a,_0x5c0210,{'get':()=>_0x5c1c97[_0x5c0210],'enumerable':!(_0x2138d8=ee(_0x5c1c97,_0x5c0210))||_0x2138d8[_0x36a3cf(0x14e)]});}return _0x24c79a;},j=(_0x1f84af,_0x39bbd1,_0xf2cf2e)=>(_0xf2cf2e=_0x1f84af!=null?G(ne(_0x1f84af)):{},ie(_0x39bbd1||!_0x1f84af||!_0x1f84af['__es'+'Module']?V(_0xf2cf2e,'default',{'value':_0x1f84af,'enumerable':!0x0}):_0xf2cf2e,_0x1f84af)),q=class{constructor(_0x14d9ea,_0x61266e,_0x21d732,_0x659164,_0x2ce13a,_0x1b0c0c){var _0x4b2850=_0x418f23,_0x34d24c,_0x26bffd,_0xeab781,_0x4b345e;this['global']=_0x14d9ea,this[_0x4b2850(0xe0)]=_0x61266e,this['port']=_0x21d732,this[_0x4b2850(0x173)]=_0x659164,this[_0x4b2850(0x131)]=_0x2ce13a,this['eventReceivedCallback']=_0x1b0c0c,this[_0x4b2850(0x159)]=!0x0,this['_allowedToConnectOnSend']=!0x0,this[_0x4b2850(0xee)]=!0x1,this[_0x4b2850(0xa0)]=!0x1,this[_0x4b2850(0x160)]=((_0x26bffd=(_0x34d24c=_0x14d9ea['process'])==null?void 0x0:_0x34d24c[_0x4b2850(0x116)])==null?void 0x0:_0x26bffd['NEXT_RUNTIME'])===_0x4b2850(0x9e),this[_0x4b2850(0x174)]=!((_0x4b345e=(_0xeab781=this[_0x4b2850(0xf8)][_0x4b2850(0x104)])==null?void 0x0:_0xeab781['versions'])!=null&&_0x4b345e[_0x4b2850(0xc8)])&&!this[_0x4b2850(0x160)],this[_0x4b2850(0xe6)]=null,this[_0x4b2850(0xfc)]=0x0,this[_0x4b2850(0xf1)]=0x14,this['_webSocketErrorDocsLink']=_0x4b2850(0xcd),this[_0x4b2850(0xb8)]=(this[_0x4b2850(0x174)]?_0x4b2850(0xaf):_0x4b2850(0x9f))+this[_0x4b2850(0xc9)];}async[_0x418f23(0xac)](){var _0x4a1673=_0x418f23,_0x2d8a6c,_0x2fabb9;if(this[_0x4a1673(0xe6)])return this[_0x4a1673(0xe6)];let _0x338282;if(this[_0x4a1673(0x174)]||this[_0x4a1673(0x160)])_0x338282=this[_0x4a1673(0xf8)][_0x4a1673(0x17c)];else{if((_0x2d8a6c=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])!=null&&_0x2d8a6c[_0x4a1673(0xcc)])_0x338282=(_0x2fabb9=this[_0x4a1673(0xf8)][_0x4a1673(0x104)])==null?void 0x0:_0x2fabb9[_0x4a1673(0xcc)];else try{let _0x6adc18=await import(_0x4a1673(0x17f));_0x338282=(await import((await import(_0x4a1673(0x14c)))[_0x4a1673(0xb2)](_0x6adc18['join'](this[_0x4a1673(0x173)],_0x4a1673(0x9a)))['toString']()))[_0x4a1673(0x164)];}catch{try{_0x338282=require(require(_0x4a1673(0x17f))['join'](this[_0x4a1673(0x173)],'ws'));}catch{throw new Error('failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket');}}}return this[_0x4a1673(0xe6)]=_0x338282,_0x338282;}[_0x418f23(0xe2)](){var _0x560a95=_0x418f23;this[_0x560a95(0xa0)]||this[_0x560a95(0xee)]||this[_0x560a95(0xfc)]>=this[_0x560a95(0xf1)]||(this['_allowedToConnectOnSend']=!0x1,this['_connecting']=!0x0,this[_0x560a95(0xfc)]++,this['_ws']=new Promise((_0x48a2aa,_0x1b9b87)=>{var _0x3507cc=_0x560a95;this[_0x3507cc(0xac)]()['then'](_0x2d9634=>{var _0x4649cf=_0x3507cc;let _0x18b292=new _0x2d9634(_0x4649cf(0x185)+(!this['_inBrowser']&&this['dockerizedApp']?_0x4649cf(0x15a):this[_0x4649cf(0xe0)])+':'+this['port']);_0x18b292[_0x4649cf(0x16f)]=()=>{var _0x37af5c=_0x4649cf;this['_allowedToSend']=!0x1,this[_0x37af5c(0x162)](_0x18b292),this['_attemptToReconnectShortly'](),_0x1b9b87(new Error('logger\\\\x20websocket\\\\x20error'));},_0x18b292[_0x4649cf(0xf5)]=()=>{var _0x5c5b5c=_0x4649cf;this[_0x5c5b5c(0x174)]||_0x18b292[_0x5c5b5c(0xe7)]&&_0x18b292[_0x5c5b5c(0xe7)]['unref']&&_0x18b292[_0x5c5b5c(0xe7)]['unref'](),_0x48a2aa(_0x18b292);},_0x18b292[_0x4649cf(0xb6)]=()=>{this['_allowedToConnectOnSend']=!0x0,this['_disposeWebsocket'](_0x18b292),this['_attemptToReconnectShortly']();},_0x18b292[_0x4649cf(0x121)]=_0xf360ec=>{var _0x34c0e1=_0x4649cf;try{if(!(_0xf360ec!=null&&_0xf360ec[_0x34c0e1(0x99)])||!this[_0x34c0e1(0x12f)])return;let _0x5a655a=JSON[_0x34c0e1(0x13d)](_0xf360ec[_0x34c0e1(0x99)]);this['eventReceivedCallback'](_0x5a655a['method'],_0x5a655a[_0x34c0e1(0xab)],this[_0x34c0e1(0xf8)],this[_0x34c0e1(0x174)]);}catch{}};})['then'](_0x382d9b=>(this['_connected']=!0x0,this[_0x3507cc(0xa0)]=!0x1,this[_0x3507cc(0x12c)]=!0x1,this[_0x3507cc(0x159)]=!0x0,this['_connectAttemptCount']=0x0,_0x382d9b))['catch'](_0x469147=>(this[_0x3507cc(0xee)]=!0x1,this[_0x3507cc(0xa0)]=!0x1,console[_0x3507cc(0xed)](_0x3507cc(0x169)+this[_0x3507cc(0xc9)]),_0x1b9b87(new Error(_0x3507cc(0x12a)+(_0x469147&&_0x469147[_0x3507cc(0xb4)])))));}));}[_0x418f23(0x162)](_0x391e4c){var _0x18bf98=_0x418f23;this[_0x18bf98(0xee)]=!0x1,this[_0x18bf98(0xa0)]=!0x1;try{_0x391e4c['onclose']=null,_0x391e4c[_0x18bf98(0x16f)]=null,_0x391e4c[_0x18bf98(0xf5)]=null;}catch{}try{_0x391e4c[_0x18bf98(0xb9)]<0x2&&_0x391e4c[_0x18bf98(0x141)]();}catch{}}['_attemptToReconnectShortly'](){var _0x4846b6=_0x418f23;clearTimeout(this[_0x4846b6(0xa3)]),!(this[_0x4846b6(0xfc)]>=this[_0x4846b6(0xf1)])&&(this['_reconnectTimeout']=setTimeout(()=>{var _0xc0d1ae=_0x4846b6,_0x3b3b8b;this[_0xc0d1ae(0xee)]||this[_0xc0d1ae(0xa0)]||(this[_0xc0d1ae(0xe2)](),(_0x3b3b8b=this[_0xc0d1ae(0xd2)])==null||_0x3b3b8b[_0xc0d1ae(0x120)](()=>this['_attemptToReconnectShortly']()));},0x1f4),this['_reconnectTimeout'][_0x4846b6(0x188)]&&this[_0x4846b6(0xa3)][_0x4846b6(0x188)]());}async[_0x418f23(0x11e)](_0x592dff){var _0x123097=_0x418f23;try{if(!this[_0x123097(0x159)])return;this[_0x123097(0x12c)]&&this[_0x123097(0xe2)](),(await this['_ws'])[_0x123097(0x11e)](JSON[_0x123097(0xdd)](_0x592dff));}catch(_0x3558e1){this['_extendedWarning']?console[_0x123097(0xed)](this['_sendErrorMessage']+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)])):(this[_0x123097(0x167)]=!0x0,console[_0x123097(0xed)](this[_0x123097(0xb8)]+':\\\\x20'+(_0x3558e1&&_0x3558e1[_0x123097(0xb4)]),_0x592dff)),this[_0x123097(0x159)]=!0x1,this[_0x123097(0xb3)]();}}};function H(_0x21a490,_0x6209b7,_0x32bdf1,_0x32048a,_0x5bcdf6,_0x3f8a6e,_0xb987a3,_0x3abcb6=oe){var _0x372163=_0x418f23;let _0x52a2ac=_0x32bdf1[_0x372163(0x190)](',')[_0x372163(0x12e)](_0x230c9d=>{var _0x1b5d4e=_0x372163,_0x4a53bb,_0x1cde39,_0x106ea9,_0x3f43e6;try{if(!_0x21a490['_console_ninja_session']){let _0x24bfb9=((_0x1cde39=(_0x4a53bb=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x4a53bb['versions'])==null?void 0x0:_0x1cde39[_0x1b5d4e(0xc8)])||((_0x3f43e6=(_0x106ea9=_0x21a490[_0x1b5d4e(0x104)])==null?void 0x0:_0x106ea9[_0x1b5d4e(0x116)])==null?void 0x0:_0x3f43e6[_0x1b5d4e(0xd6)])==='edge';(_0x5bcdf6===_0x1b5d4e(0x110)||_0x5bcdf6===_0x1b5d4e(0x155)||_0x5bcdf6==='astro'||_0x5bcdf6==='angular')&&(_0x5bcdf6+=_0x24bfb9?_0x1b5d4e(0x10c):_0x1b5d4e(0x124)),_0x21a490['_console_ninja_session']={'id':+new Date(),'tool':_0x5bcdf6},_0xb987a3&&_0x5bcdf6&&!_0x24bfb9&&console['log'](_0x1b5d4e(0xfe)+(_0x5bcdf6[_0x1b5d4e(0x13c)](0x0)[_0x1b5d4e(0x100)]()+_0x5bcdf6['substr'](0x1))+',','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)',_0x1b5d4e(0xbb));}let _0x4eb2eb=new q(_0x21a490,_0x6209b7,_0x230c9d,_0x32048a,_0x3f8a6e,_0x3abcb6);return _0x4eb2eb[_0x1b5d4e(0x11e)][_0x1b5d4e(0xf4)](_0x4eb2eb);}catch(_0x202950){return console[_0x1b5d4e(0xed)](_0x1b5d4e(0x18e),_0x202950&&_0x202950[_0x1b5d4e(0xb4)]),()=>{};}});return _0x17b111=>_0x52a2ac[_0x372163(0x178)](_0x3b7429=>_0x3b7429(_0x17b111));}function _0x4e19(){var _0x3dea94=['perf_hooks','now','elements','6915181ldjYIK','send','date','catch','onmessage','_isUndefined','_HTMLAllCollection','\\\\x20browser','_setNodePermissions','strLength','_getOwnPropertyDescriptor','_Symbol','indexOf','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','number','_allowedToConnectOnSend','_sortProps','map','eventReceivedCallback','array','dockerizedApp','match','_processTreeNodeResult','disabledLog','_numberRegExp','_hasSymbolPropertyOnItsPath',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"DevHardtech\\\",\\\"************\\\"],'_hasMapOnItsPath','performance','coverage','Error','charAt','parse','function','endsWith','rootExpression','close','undefined','_hasSetOnItsPath','_regExpToString','_p_name','slice','substr','11hsvZPL','hostname','serialize','_dateToString','url','boolean','enumerable','length','_addObjectProperty','root_exp','origin','reload','Symbol','remix','_objectToString','push','getOwnPropertySymbols','_allowedToSend','gateway.docker.internal','HTMLAllCollection','_getOwnPropertyNames','_isPrimitiveWrapperType','4193466bntOOn','16178350tQpRDP','_inNextEdge','time','_disposeWebsocket','_setNodeLabel','default','_ninjaIgnoreNextError','concat','_extendedWarning','resolveGetters','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','hrtime','current','_cleanNode','replace','_consoleNinjaAllowedToStart','onerror','Map','getOwnPropertyDescriptor','getter','nodeModules','_inBrowser','sort','_blacklistedProperty','autoExpandLimit','forEach','_undefined',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.vscode\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.456\\\\\\\\node_modules\\\",'_addProperty','WebSocket','_property','_addLoadNode','path','props','NEGATIVE_INFINITY','_type','webpack','error','ws://','_isMap','null','unref','negativeInfinity','','_quotedRegExp','set','nan','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','_p_length','split','7999758ImPfSL','_isArray','data','ws/index.js','parent','8YXKnRI','autoExpandPropertyCount','edge','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','_connecting','2rNewCg','allStrLength','_reconnectTimeout','noFunctions','toString','index','fromCharCode','_treeNodePropertiesBeforeFullValue','versions','count','args','getWebSocketClass','funcName','_isPrimitiveType','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','262697JHDjIO','1','pathToFileURL','_attemptToReconnectShortly','message','isExpressionToEvaluate','onclose','name','_sendErrorMessage','readyState','location','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','console','disabledTrace','[object\\\\x20BigInt]','valueOf','autoExpand','[object\\\\x20Date]','107080UCogNw','includes','trace','hits','expressionsToEvaluate','_p_','node','_webSocketErrorDocsLink','unknown','_setNodeId','_WebSocket','https://tinyurl.com/37x8b79t','value','_getOwnPropertySymbols','negativeZero','_setNodeQueryPath','_ws','elapsed','symbol','_propertyName','NEXT_RUNTIME','stackTraceLimit','_console_ninja_session','getPrototypeOf','call','_console_ninja','[object\\\\x20Array]','stringify','_isSet','getOwnPropertyNames','host','8484993ONNFtV','_connectToHostNow','level','_treeNodePropertiesAfterFullValue','create','_WebSocketClass','_socket','_addFunctionsNode','_capIfString','Boolean','_setNodeExpandableState','62812','warn','_connected','timeStamp','depth','_maxConnectAttemptCount','hasOwnProperty','capped','bind','onopen','Set','constructor','global','bigint','POSITIVE_INFINITY','sortProps','_connectAttemptCount','Number','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','log','toUpperCase','string','positiveInfinity','defineProperty','process','...','String','some','get','200DTLFWz','','type','\\\\x20server','startsWith','toLowerCase','_additionalMetadata','next.js','_setNodeExpressionPath','reduceLimits','test','[object\\\\x20Map]','autoExpandMaxDepth','env','object','autoExpandPreviousObjects','prototype'];_0x4e19=function(){return _0x3dea94;};return _0x4e19();}function oe(_0x29bd2d,_0x4ca25e,_0x2f30dc,_0x50ad96){var _0x4b277d=_0x418f23;_0x50ad96&&_0x29bd2d===_0x4b277d(0x153)&&_0x2f30dc[_0x4b277d(0xba)]['reload']();}function B(_0x53e47a){var _0x4f5825=_0x418f23,_0x17ea3d,_0x5476d8;let _0x2ed5e7=function(_0x3f9b33,_0x4798cf){return _0x4798cf-_0x3f9b33;},_0x2534f8;if(_0x53e47a[_0x4f5825(0x139)])_0x2534f8=function(){var _0xf68f54=_0x4f5825;return _0x53e47a[_0xf68f54(0x139)][_0xf68f54(0x11b)]();};else{if(_0x53e47a[_0x4f5825(0x104)]&&_0x53e47a[_0x4f5825(0x104)][_0x4f5825(0x16a)]&&((_0x5476d8=(_0x17ea3d=_0x53e47a[_0x4f5825(0x104)])==null?void 0x0:_0x17ea3d[_0x4f5825(0x116)])==null?void 0x0:_0x5476d8[_0x4f5825(0xd6)])!==_0x4f5825(0x9e))_0x2534f8=function(){var _0x1144bb=_0x4f5825;return _0x53e47a[_0x1144bb(0x104)][_0x1144bb(0x16a)]();},_0x2ed5e7=function(_0x4a8621,_0xc276d4){return 0x3e8*(_0xc276d4[0x0]-_0x4a8621[0x0])+(_0xc276d4[0x1]-_0x4a8621[0x1])/0xf4240;};else try{let {performance:_0x6c0ab3}=require(_0x4f5825(0x11a));_0x2534f8=function(){var _0x57029c=_0x4f5825;return _0x6c0ab3[_0x57029c(0x11b)]();};}catch{_0x2534f8=function(){return+new Date();};}}return{'elapsed':_0x2ed5e7,'timeStamp':_0x2534f8,'now':()=>Date['now']()};}function X(_0x108a65,_0x2bc4c8,_0x5e7fce){var _0xd0e45=_0x418f23,_0x184b4d,_0x3be467,_0x1494d3,_0x1853ba,_0xc61e6c;if(_0x108a65[_0xd0e45(0x16e)]!==void 0x0)return _0x108a65['_consoleNinjaAllowedToStart'];let _0xae1558=((_0x3be467=(_0x184b4d=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x184b4d[_0xd0e45(0xa9)])==null?void 0x0:_0x3be467[_0xd0e45(0xc8)])||((_0x1853ba=(_0x1494d3=_0x108a65[_0xd0e45(0x104)])==null?void 0x0:_0x1494d3[_0xd0e45(0x116)])==null?void 0x0:_0x1853ba[_0xd0e45(0xd6)])===_0xd0e45(0x9e);function _0x492297(_0x174e6c){var _0x9b4def=_0xd0e45;if(_0x174e6c[_0x9b4def(0x10d)]('/')&&_0x174e6c[_0x9b4def(0x13f)]('/')){let _0x2461d3=new RegExp(_0x174e6c['slice'](0x1,-0x1));return _0x2a4fef=>_0x2461d3[_0x9b4def(0x113)](_0x2a4fef);}else{if(_0x174e6c[_0x9b4def(0xc3)]('*')||_0x174e6c[_0x9b4def(0xc3)]('?')){let _0x51dbdb=new RegExp('^'+_0x174e6c[_0x9b4def(0x16d)](/\\\\./g,String[_0x9b4def(0xa7)](0x5c)+'.')[_0x9b4def(0x16d)](/\\\\*/g,'.*')[_0x9b4def(0x16d)](/\\\\?/g,'.')+String[_0x9b4def(0xa7)](0x24));return _0x2bf349=>_0x51dbdb['test'](_0x2bf349);}else return _0x40a674=>_0x40a674===_0x174e6c;}}let _0x418e9a=_0x2bc4c8[_0xd0e45(0x12e)](_0x492297);return _0x108a65[_0xd0e45(0x16e)]=_0xae1558||!_0x2bc4c8,!_0x108a65['_consoleNinjaAllowedToStart']&&((_0xc61e6c=_0x108a65[_0xd0e45(0xba)])==null?void 0x0:_0xc61e6c['hostname'])&&(_0x108a65[_0xd0e45(0x16e)]=_0x418e9a[_0xd0e45(0x107)](_0x1dbe80=>_0x1dbe80(_0x108a65[_0xd0e45(0xba)][_0xd0e45(0x149)]))),_0x108a65['_consoleNinjaAllowedToStart'];}function _0x33f3(_0x3a814d,_0x58c537){var _0x4e195d=_0x4e19();return _0x33f3=function(_0x33f3b8,_0x2e2a30){_0x33f3b8=_0x33f3b8-0x98;var _0x3c84c1=_0x4e195d[_0x33f3b8];return _0x3c84c1;},_0x33f3(_0x3a814d,_0x58c537);}function J(_0x3830e6,_0x45a6b5,_0x2f8209,_0x3cee70){var _0x40c820=_0x418f23;_0x3830e6=_0x3830e6,_0x45a6b5=_0x45a6b5,_0x2f8209=_0x2f8209,_0x3cee70=_0x3cee70;let _0x38a5a7=B(_0x3830e6),_0x5b41b9=_0x38a5a7[_0x40c820(0xd3)],_0x1169a5=_0x38a5a7[_0x40c820(0xef)];class _0x1e3ba1{constructor(){var _0x3396c1=_0x40c820;this['_keyStrRegExp']=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this[_0x3396c1(0x135)]=/^(0|[1-9][0-9]*)$/,this[_0x3396c1(0x18b)]=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x3396c1(0x179)]=_0x3830e6[_0x3396c1(0x142)],this[_0x3396c1(0x123)]=_0x3830e6[_0x3396c1(0x15b)],this[_0x3396c1(0x127)]=Object[_0x3396c1(0x171)],this['_getOwnPropertyNames']=Object[_0x3396c1(0xdf)],this[_0x3396c1(0x128)]=_0x3830e6[_0x3396c1(0x154)],this[_0x3396c1(0x144)]=RegExp[_0x3396c1(0x119)][_0x3396c1(0xa5)],this['_dateToString']=Date[_0x3396c1(0x119)][_0x3396c1(0xa5)];}[_0x40c820(0x14a)](_0x1f7b5d,_0x5b6b91,_0x1ebf24,_0x4f3c70){var _0x4d7e42=_0x40c820,_0xe363bc=this,_0x290e3b=_0x1ebf24[_0x4d7e42(0xc0)];function _0x16ce5f(_0xf8520c,_0x1a1953,_0x3e443e){var _0x4923f3=_0x4d7e42;_0x1a1953[_0x4923f3(0x10b)]=_0x4923f3(0xca),_0x1a1953['error']=_0xf8520c[_0x4923f3(0xb4)],_0x454078=_0x3e443e[_0x4923f3(0xc8)]['current'],_0x3e443e['node'][_0x4923f3(0x16b)]=_0x1a1953,_0xe363bc['_treeNodePropertiesBeforeFullValue'](_0x1a1953,_0x3e443e);}let _0x1533a9;_0x3830e6[_0x4d7e42(0xbc)]&&(_0x1533a9=_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)],_0x1533a9&&(_0x3830e6['console'][_0x4d7e42(0x184)]=function(){}));try{try{_0x1ebf24[_0x4d7e42(0xe3)]++,_0x1ebf24['autoExpand']&&_0x1ebf24[_0x4d7e42(0x118)]['push'](_0x5b6b91);var _0x55a2c4,_0x5cbc7d,_0x10ebd6,_0x38ec49,_0x46d06f=[],_0x4ef003=[],_0x33c92e,_0xe8efc0=this[_0x4d7e42(0x182)](_0x5b6b91),_0x5b392f=_0xe8efc0===_0x4d7e42(0x130),_0x55d894=!0x1,_0x65caf4=_0xe8efc0===_0x4d7e42(0x13e),_0x512734=this['_isPrimitiveType'](_0xe8efc0),_0x3d6d36=this[_0x4d7e42(0x15d)](_0xe8efc0),_0x2d65b0=_0x512734||_0x3d6d36,_0x4b6f05={},_0x419e4c=0x0,_0x2bca20=!0x1,_0x454078,_0xed6526=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x1ebf24['depth']){if(_0x5b392f){if(_0x5cbc7d=_0x5b6b91['length'],_0x5cbc7d>_0x1ebf24[_0x4d7e42(0x11c)]){for(_0x10ebd6=0x0,_0x38ec49=_0x1ebf24[_0x4d7e42(0x11c)],_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003[_0x4d7e42(0x157)](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));_0x1f7b5d['cappedElements']=!0x0;}else{for(_0x10ebd6=0x0,_0x38ec49=_0x5cbc7d,_0x55a2c4=_0x10ebd6;_0x55a2c4<_0x38ec49;_0x55a2c4++)_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x17b)](_0x46d06f,_0x5b6b91,_0xe8efc0,_0x55a2c4,_0x1ebf24));}_0x1ebf24[_0x4d7e42(0x9d)]+=_0x4ef003[_0x4d7e42(0x14f)];}if(!(_0xe8efc0==='null'||_0xe8efc0==='undefined')&&!_0x512734&&_0xe8efc0!==_0x4d7e42(0x106)&&_0xe8efc0!=='Buffer'&&_0xe8efc0!=='bigint'){var _0xfca776=_0x4f3c70['props']||_0x1ebf24[_0x4d7e42(0x180)];if(this['_isSet'](_0x5b6b91)?(_0x55a2c4=0x0,_0x5b6b91['forEach'](function(_0x1b3730){var _0x29b12d=_0x4d7e42;if(_0x419e4c++,_0x1ebf24['autoExpandPropertyCount']++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24[_0x29b12d(0xb5)]&&_0x1ebf24[_0x29b12d(0xc0)]&&_0x1ebf24[_0x29b12d(0x9d)]>_0x1ebf24[_0x29b12d(0x177)]){_0x2bca20=!0x0;return;}_0x4ef003['push'](_0xe363bc[_0x29b12d(0x17b)](_0x46d06f,_0x5b6b91,_0x29b12d(0xf6),_0x55a2c4++,_0x1ebf24,function(_0x383398){return function(){return _0x383398;};}(_0x1b3730)));})):this[_0x4d7e42(0x186)](_0x5b6b91)&&_0x5b6b91['forEach'](function(_0x4cd1d9,_0x42ee6b){var _0x3c460e=_0x4d7e42;if(_0x419e4c++,_0x1ebf24[_0x3c460e(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;return;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x3c460e(0xc0)]&&_0x1ebf24[_0x3c460e(0x9d)]>_0x1ebf24['autoExpandLimit']){_0x2bca20=!0x0;return;}var _0x2a4101=_0x42ee6b[_0x3c460e(0xa5)]();_0x2a4101[_0x3c460e(0x14f)]>0x64&&(_0x2a4101=_0x2a4101[_0x3c460e(0x146)](0x0,0x64)+_0x3c460e(0x105)),_0x4ef003[_0x3c460e(0x157)](_0xe363bc['_addProperty'](_0x46d06f,_0x5b6b91,_0x3c460e(0x170),_0x2a4101,_0x1ebf24,function(_0x1c45bc){return function(){return _0x1c45bc;};}(_0x4cd1d9)));}),!_0x55d894){try{for(_0x33c92e in _0x5b6b91)if(!(_0x5b392f&&_0xed6526['test'](_0x33c92e))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24['isExpressionToEvaluate']&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPropertyCount']>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc[_0x4d7e42(0x150)](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}catch{}if(_0x4b6f05[_0x4d7e42(0x18f)]=!0x0,_0x65caf4&&(_0x4b6f05[_0x4d7e42(0x145)]=!0x0),!_0x2bca20){var _0x469d20=[][_0x4d7e42(0x166)](this[_0x4d7e42(0x15c)](_0x5b6b91))[_0x4d7e42(0x166)](this[_0x4d7e42(0xcf)](_0x5b6b91));for(_0x55a2c4=0x0,_0x5cbc7d=_0x469d20[_0x4d7e42(0x14f)];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)if(_0x33c92e=_0x469d20[_0x55a2c4],!(_0x5b392f&&_0xed6526['test'](_0x33c92e[_0x4d7e42(0xa5)]()))&&!this[_0x4d7e42(0x176)](_0x5b6b91,_0x33c92e,_0x1ebf24)&&!_0x4b6f05[_0x4d7e42(0xc7)+_0x33c92e[_0x4d7e42(0xa5)]()]){if(_0x419e4c++,_0x1ebf24[_0x4d7e42(0x9d)]++,_0x419e4c>_0xfca776){_0x2bca20=!0x0;break;}if(!_0x1ebf24[_0x4d7e42(0xb5)]&&_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24[_0x4d7e42(0x9d)]>_0x1ebf24[_0x4d7e42(0x177)]){_0x2bca20=!0x0;break;}_0x4ef003['push'](_0xe363bc['_addObjectProperty'](_0x46d06f,_0x4b6f05,_0x5b6b91,_0xe8efc0,_0x33c92e,_0x1ebf24));}}}}}if(_0x1f7b5d['type']=_0xe8efc0,_0x2d65b0?(_0x1f7b5d['value']=_0x5b6b91[_0x4d7e42(0xbf)](),this['_capIfString'](_0xe8efc0,_0x1f7b5d,_0x1ebf24,_0x4f3c70)):_0xe8efc0===_0x4d7e42(0x11f)?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x14b)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='bigint'?_0x1f7b5d[_0x4d7e42(0xce)]=_0x5b6b91[_0x4d7e42(0xa5)]():_0xe8efc0==='RegExp'?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x144)][_0x4d7e42(0xda)](_0x5b6b91):_0xe8efc0==='symbol'&&this[_0x4d7e42(0x128)]?_0x1f7b5d[_0x4d7e42(0xce)]=this[_0x4d7e42(0x128)][_0x4d7e42(0x119)]['toString']['call'](_0x5b6b91):!_0x1ebf24[_0x4d7e42(0xf0)]&&!(_0xe8efc0===_0x4d7e42(0x187)||_0xe8efc0==='undefined')&&(delete _0x1f7b5d[_0x4d7e42(0xce)],_0x1f7b5d[_0x4d7e42(0xf3)]=!0x0),_0x2bca20&&(_0x1f7b5d['cappedProps']=!0x0),_0x454078=_0x1ebf24['node']['current'],_0x1ebf24[_0x4d7e42(0xc8)]['current']=_0x1f7b5d,this[_0x4d7e42(0xa8)](_0x1f7b5d,_0x1ebf24),_0x4ef003[_0x4d7e42(0x14f)]){for(_0x55a2c4=0x0,_0x5cbc7d=_0x4ef003['length'];_0x55a2c4<_0x5cbc7d;_0x55a2c4++)_0x4ef003[_0x55a2c4](_0x55a2c4);}_0x46d06f['length']&&(_0x1f7b5d[_0x4d7e42(0x180)]=_0x46d06f);}catch(_0x54504a){_0x16ce5f(_0x54504a,_0x1f7b5d,_0x1ebf24);}this[_0x4d7e42(0x10f)](_0x5b6b91,_0x1f7b5d),this[_0x4d7e42(0xe4)](_0x1f7b5d,_0x1ebf24),_0x1ebf24[_0x4d7e42(0xc8)][_0x4d7e42(0x16b)]=_0x454078,_0x1ebf24['level']--,_0x1ebf24[_0x4d7e42(0xc0)]=_0x290e3b,_0x1ebf24[_0x4d7e42(0xc0)]&&_0x1ebf24['autoExpandPreviousObjects']['pop']();}finally{_0x1533a9&&(_0x3830e6[_0x4d7e42(0xbc)][_0x4d7e42(0x184)]=_0x1533a9);}return _0x1f7b5d;}[_0x40c820(0xcf)](_0xd7ad14){var _0x474a44=_0x40c820;return Object[_0x474a44(0x158)]?Object[_0x474a44(0x158)](_0xd7ad14):[];}[_0x40c820(0xde)](_0x5b06ac){var _0x292c99=_0x40c820;return!!(_0x5b06ac&&_0x3830e6[_0x292c99(0xf6)]&&this[_0x292c99(0x156)](_0x5b06ac)==='[object\\\\x20Set]'&&_0x5b06ac[_0x292c99(0x178)]);}['_blacklistedProperty'](_0x10628d,_0x15c227,_0x5a4f15){var _0x152ffd=_0x40c820;return _0x5a4f15[_0x152ffd(0xa4)]?typeof _0x10628d[_0x15c227]==_0x152ffd(0x13e):!0x1;}[_0x40c820(0x182)](_0x13718c){var _0x2c19d1=_0x40c820,_0x225ae1='';return _0x225ae1=typeof _0x13718c,_0x225ae1==='object'?this[_0x2c19d1(0x156)](_0x13718c)==='[object\\\\x20Array]'?_0x225ae1=_0x2c19d1(0x130):this['_objectToString'](_0x13718c)===_0x2c19d1(0xc1)?_0x225ae1=_0x2c19d1(0x11f):this[_0x2c19d1(0x156)](_0x13718c)===_0x2c19d1(0xbe)?_0x225ae1=_0x2c19d1(0xf9):_0x13718c===null?_0x225ae1=_0x2c19d1(0x187):_0x13718c[_0x2c19d1(0xf7)]&&(_0x225ae1=_0x13718c['constructor'][_0x2c19d1(0xb7)]||_0x225ae1):_0x225ae1===_0x2c19d1(0x142)&&this[_0x2c19d1(0x123)]&&_0x13718c instanceof this['_HTMLAllCollection']&&(_0x225ae1=_0x2c19d1(0x15b)),_0x225ae1;}[_0x40c820(0x156)](_0x37617c){var _0xdf3907=_0x40c820;return Object[_0xdf3907(0x119)]['toString'][_0xdf3907(0xda)](_0x37617c);}[_0x40c820(0xae)](_0x26b95b){var _0x3b9373=_0x40c820;return _0x26b95b===_0x3b9373(0x14d)||_0x26b95b===_0x3b9373(0x101)||_0x26b95b===_0x3b9373(0x12b);}['_isPrimitiveWrapperType'](_0x150515){var _0x2539cd=_0x40c820;return _0x150515===_0x2539cd(0xea)||_0x150515==='String'||_0x150515===_0x2539cd(0xfd);}['_addProperty'](_0x1a647e,_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb){var _0x3c4649=this;return function(_0x5c2af7){var _0x865286=_0x33f3,_0x5ceb03=_0x985088[_0x865286(0xc8)][_0x865286(0x16b)],_0x14ad91=_0x985088[_0x865286(0xc8)]['index'],_0x10beb0=_0x985088[_0x865286(0xc8)][_0x865286(0x9b)];_0x985088['node'][_0x865286(0x9b)]=_0x5ceb03,_0x985088['node'][_0x865286(0xa6)]=typeof _0x551a3a==_0x865286(0x12b)?_0x551a3a:_0x5c2af7,_0x1a647e[_0x865286(0x157)](_0x3c4649[_0x865286(0x17d)](_0x588bda,_0x30f2fe,_0x551a3a,_0x985088,_0x148adb)),_0x985088[_0x865286(0xc8)][_0x865286(0x9b)]=_0x10beb0,_0x985088['node']['index']=_0x14ad91;};}[_0x40c820(0x150)](_0x3e6c99,_0x96cdeb,_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b){var _0x102764=_0x40c820,_0xbcca65=this;return _0x96cdeb[_0x102764(0xc7)+_0x5e1ed6[_0x102764(0xa5)]()]=!0x0,function(_0x4f07e2){var _0x11a9a9=_0x102764,_0x41481f=_0x6bb8c1['node'][_0x11a9a9(0x16b)],_0x45aeeb=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)],_0x44cce6=_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0x9b)];_0x6bb8c1['node']['parent']=_0x41481f,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x4f07e2,_0x3e6c99['push'](_0xbcca65[_0x11a9a9(0x17d)](_0x4c6077,_0x42a66f,_0x5e1ed6,_0x6bb8c1,_0x24f98b)),_0x6bb8c1['node']['parent']=_0x44cce6,_0x6bb8c1[_0x11a9a9(0xc8)][_0x11a9a9(0xa6)]=_0x45aeeb;};}['_property'](_0x5a954c,_0x11a196,_0x34292c,_0x53d319,_0x300135){var _0x3f13ca=_0x40c820,_0x350c39=this;_0x300135||(_0x300135=function(_0x26467a,_0x467a10){return _0x26467a[_0x467a10];});var _0x112124=_0x34292c[_0x3f13ca(0xa5)](),_0x42837e=_0x53d319[_0x3f13ca(0xc6)]||{},_0x265c6d=_0x53d319['depth'],_0x31debf=_0x53d319[_0x3f13ca(0xb5)];try{var _0x3c8586=this[_0x3f13ca(0x186)](_0x5a954c),_0x5579d4=_0x112124;_0x3c8586&&_0x5579d4[0x0]==='\\\\x27'&&(_0x5579d4=_0x5579d4[_0x3f13ca(0x147)](0x1,_0x5579d4[_0x3f13ca(0x14f)]-0x2));var _0x46f777=_0x53d319[_0x3f13ca(0xc6)]=_0x42837e[_0x3f13ca(0xc7)+_0x5579d4];_0x46f777&&(_0x53d319[_0x3f13ca(0xf0)]=_0x53d319[_0x3f13ca(0xf0)]+0x1),_0x53d319[_0x3f13ca(0xb5)]=!!_0x46f777;var _0x14534f=typeof _0x34292c=='symbol',_0x124dd3={'name':_0x14534f||_0x3c8586?_0x112124:this['_propertyName'](_0x112124)};if(_0x14534f&&(_0x124dd3[_0x3f13ca(0xd4)]=!0x0),!(_0x11a196===_0x3f13ca(0x130)||_0x11a196===_0x3f13ca(0x13b))){var _0x4ea27f=this[_0x3f13ca(0x127)](_0x5a954c,_0x34292c);if(_0x4ea27f&&(_0x4ea27f[_0x3f13ca(0x18c)]&&(_0x124dd3['setter']=!0x0),_0x4ea27f[_0x3f13ca(0x108)]&&!_0x46f777&&!_0x53d319['resolveGetters']))return _0x124dd3[_0x3f13ca(0x172)]=!0x0,this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x3214f9;try{_0x3214f9=_0x300135(_0x5a954c,_0x34292c);}catch(_0x3ef7eb){return _0x124dd3={'name':_0x112124,'type':_0x3f13ca(0xca),'error':_0x3ef7eb['message']},this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319),_0x124dd3;}var _0x4c3356=this[_0x3f13ca(0x182)](_0x3214f9),_0x18e3f8=this[_0x3f13ca(0xae)](_0x4c3356);if(_0x124dd3[_0x3f13ca(0x10b)]=_0x4c3356,_0x18e3f8)this[_0x3f13ca(0x133)](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x7d7701=_0x3f13ca;_0x124dd3['value']=_0x3214f9[_0x7d7701(0xbf)](),!_0x46f777&&_0x350c39[_0x7d7701(0xe9)](_0x4c3356,_0x124dd3,_0x53d319,{});});else{var _0x275cea=_0x53d319[_0x3f13ca(0xc0)]&&_0x53d319['level']<_0x53d319['autoExpandMaxDepth']&&_0x53d319[_0x3f13ca(0x118)][_0x3f13ca(0x129)](_0x3214f9)<0x0&&_0x4c3356!==_0x3f13ca(0x13e)&&_0x53d319['autoExpandPropertyCount']<_0x53d319['autoExpandLimit'];_0x275cea||_0x53d319[_0x3f13ca(0xe3)]<_0x265c6d||_0x46f777?(this['serialize'](_0x124dd3,_0x3214f9,_0x53d319,_0x46f777||{}),this[_0x3f13ca(0x10f)](_0x3214f9,_0x124dd3)):this['_processTreeNodeResult'](_0x124dd3,_0x53d319,_0x3214f9,function(){var _0x2b8765=_0x3f13ca;_0x4c3356===_0x2b8765(0x187)||_0x4c3356===_0x2b8765(0x142)||(delete _0x124dd3[_0x2b8765(0xce)],_0x124dd3[_0x2b8765(0xf3)]=!0x0);});}return _0x124dd3;}finally{_0x53d319['expressionsToEvaluate']=_0x42837e,_0x53d319[_0x3f13ca(0xf0)]=_0x265c6d,_0x53d319['isExpressionToEvaluate']=_0x31debf;}}[_0x40c820(0xe9)](_0x3711dd,_0x3273d6,_0x30712a,_0x2bfd2c){var _0x22c791=_0x40c820,_0x3ed3d6=_0x2bfd2c['strLength']||_0x30712a[_0x22c791(0x126)];if((_0x3711dd==='string'||_0x3711dd==='String')&&_0x3273d6[_0x22c791(0xce)]){let _0xffdbb0=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x14f)];_0x30712a['allStrLength']+=_0xffdbb0,_0x30712a[_0x22c791(0xa2)]>_0x30712a['totalStrLength']?(_0x3273d6[_0x22c791(0xf3)]='',delete _0x3273d6[_0x22c791(0xce)]):_0xffdbb0>_0x3ed3d6&&(_0x3273d6[_0x22c791(0xf3)]=_0x3273d6[_0x22c791(0xce)][_0x22c791(0x147)](0x0,_0x3ed3d6),delete _0x3273d6[_0x22c791(0xce)]);}}['_isMap'](_0x261c40){var _0xd2ec63=_0x40c820;return!!(_0x261c40&&_0x3830e6['Map']&&this[_0xd2ec63(0x156)](_0x261c40)===_0xd2ec63(0x114)&&_0x261c40[_0xd2ec63(0x178)]);}[_0x40c820(0xd5)](_0x24e250){var _0x49be73=_0x40c820;if(_0x24e250[_0x49be73(0x132)](/^\\\\d+$/))return _0x24e250;var _0x1d38d;try{_0x1d38d=JSON['stringify'](''+_0x24e250);}catch{_0x1d38d='\\\\x22'+this['_objectToString'](_0x24e250)+'\\\\x22';}return _0x1d38d['match'](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x1d38d=_0x1d38d[_0x49be73(0x147)](0x1,_0x1d38d[_0x49be73(0x14f)]-0x2):_0x1d38d=_0x1d38d[_0x49be73(0x16d)](/'/g,'\\\\x5c\\\\x27')[_0x49be73(0x16d)](/\\\\\\\\\\\"/g,'\\\\x22')[_0x49be73(0x16d)](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x1d38d;}[_0x40c820(0x133)](_0x1b7571,_0x8f439b,_0x2c2981,_0x188fd5){var _0xf97f10=_0x40c820;this[_0xf97f10(0xa8)](_0x1b7571,_0x8f439b),_0x188fd5&&_0x188fd5(),this[_0xf97f10(0x10f)](_0x2c2981,_0x1b7571),this['_treeNodePropertiesAfterFullValue'](_0x1b7571,_0x8f439b);}[_0x40c820(0xa8)](_0x231eb3,_0x5ea482){var _0x31f690=_0x40c820;this['_setNodeId'](_0x231eb3,_0x5ea482),this[_0x31f690(0xd1)](_0x231eb3,_0x5ea482),this[_0x31f690(0x111)](_0x231eb3,_0x5ea482),this[_0x31f690(0x125)](_0x231eb3,_0x5ea482);}[_0x40c820(0xcb)](_0x10d2e1,_0x3c8083){}[_0x40c820(0xd1)](_0x53d949,_0x188c67){}[_0x40c820(0x163)](_0x3f8259,_0x16e80a){}[_0x40c820(0x122)](_0x25a3a3){return _0x25a3a3===this['_undefined'];}[_0x40c820(0xe4)](_0x44987b,_0x4ed592){var _0x37fb3e=_0x40c820;this[_0x37fb3e(0x163)](_0x44987b,_0x4ed592),this[_0x37fb3e(0xeb)](_0x44987b),_0x4ed592[_0x37fb3e(0xfb)]&&this[_0x37fb3e(0x12d)](_0x44987b),this['_addFunctionsNode'](_0x44987b,_0x4ed592),this[_0x37fb3e(0x17e)](_0x44987b,_0x4ed592),this[_0x37fb3e(0x16c)](_0x44987b);}['_additionalMetadata'](_0x1c2784,_0x4c1dde){var _0x4f21c1=_0x40c820;try{_0x1c2784&&typeof _0x1c2784[_0x4f21c1(0x14f)]==_0x4f21c1(0x12b)&&(_0x4c1dde[_0x4f21c1(0x14f)]=_0x1c2784[_0x4f21c1(0x14f)]);}catch{}if(_0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x12b)||_0x4c1dde[_0x4f21c1(0x10b)]==='Number'){if(isNaN(_0x4c1dde[_0x4f21c1(0xce)]))_0x4c1dde[_0x4f21c1(0x18d)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];else switch(_0x4c1dde[_0x4f21c1(0xce)]){case Number[_0x4f21c1(0xfa)]:_0x4c1dde[_0x4f21c1(0x102)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case Number['NEGATIVE_INFINITY']:_0x4c1dde[_0x4f21c1(0x189)]=!0x0,delete _0x4c1dde[_0x4f21c1(0xce)];break;case 0x0:this['_isNegativeZero'](_0x4c1dde[_0x4f21c1(0xce)])&&(_0x4c1dde[_0x4f21c1(0xd0)]=!0x0);break;}}else _0x4c1dde[_0x4f21c1(0x10b)]===_0x4f21c1(0x13e)&&typeof _0x1c2784['name']==_0x4f21c1(0x101)&&_0x1c2784[_0x4f21c1(0xb7)]&&_0x4c1dde['name']&&_0x1c2784[_0x4f21c1(0xb7)]!==_0x4c1dde[_0x4f21c1(0xb7)]&&(_0x4c1dde[_0x4f21c1(0xad)]=_0x1c2784[_0x4f21c1(0xb7)]);}['_isNegativeZero'](_0x289882){var _0x1b66c9=_0x40c820;return 0x1/_0x289882===Number[_0x1b66c9(0x181)];}['_sortProps'](_0x3992ee){var _0x3db550=_0x40c820;!_0x3992ee[_0x3db550(0x180)]||!_0x3992ee[_0x3db550(0x180)][_0x3db550(0x14f)]||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0x130)||_0x3992ee[_0x3db550(0x10b)]==='Map'||_0x3992ee[_0x3db550(0x10b)]===_0x3db550(0xf6)||_0x3992ee[_0x3db550(0x180)][_0x3db550(0x175)](function(_0x57a739,_0x31b40b){var _0x5dcaae=_0x3db550,_0x3d0d50=_0x57a739[_0x5dcaae(0xb7)][_0x5dcaae(0x10e)](),_0xd6d4fc=_0x31b40b[_0x5dcaae(0xb7)]['toLowerCase']();return _0x3d0d50<_0xd6d4fc?-0x1:_0x3d0d50>_0xd6d4fc?0x1:0x0;});}[_0x40c820(0xe8)](_0x12537a,_0x57f3dc){var _0x2884a4=_0x40c820;if(!(_0x57f3dc[_0x2884a4(0xa4)]||!_0x12537a['props']||!_0x12537a[_0x2884a4(0x180)][_0x2884a4(0x14f)])){for(var _0x53c006=[],_0x347d6e=[],_0x52e85a=0x0,_0x273297=_0x12537a['props']['length'];_0x52e85a<_0x273297;_0x52e85a++){var _0x1ee5b3=_0x12537a[_0x2884a4(0x180)][_0x52e85a];_0x1ee5b3[_0x2884a4(0x10b)]==='function'?_0x53c006[_0x2884a4(0x157)](_0x1ee5b3):_0x347d6e['push'](_0x1ee5b3);}if(!(!_0x347d6e[_0x2884a4(0x14f)]||_0x53c006[_0x2884a4(0x14f)]<=0x1)){_0x12537a[_0x2884a4(0x180)]=_0x347d6e;var _0x15f515={'functionsNode':!0x0,'props':_0x53c006};this[_0x2884a4(0xcb)](_0x15f515,_0x57f3dc),this[_0x2884a4(0x163)](_0x15f515,_0x57f3dc),this[_0x2884a4(0xeb)](_0x15f515),this[_0x2884a4(0x125)](_0x15f515,_0x57f3dc),_0x15f515['id']+='\\\\x20f',_0x12537a['props']['unshift'](_0x15f515);}}}['_addLoadNode'](_0x5bea6e,_0x14049e){}[_0x40c820(0xeb)](_0x199084){}[_0x40c820(0x98)](_0xf50c17){var _0x35cb98=_0x40c820;return Array['isArray'](_0xf50c17)||typeof _0xf50c17==_0x35cb98(0x117)&&this['_objectToString'](_0xf50c17)===_0x35cb98(0xdc);}[_0x40c820(0x125)](_0x3ea390,_0x54c209){}[_0x40c820(0x16c)](_0x25cdb9){var _0x1aa0a5=_0x40c820;delete _0x25cdb9[_0x1aa0a5(0x136)],delete _0x25cdb9[_0x1aa0a5(0x143)],delete _0x25cdb9[_0x1aa0a5(0x138)];}['_setNodeExpressionPath'](_0x17f351,_0x40c77e){}}let _0x459cb0=new _0x1e3ba1(),_0x218fe5={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x11fc4c={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x482c8e(_0x50675f,_0x2f7559,_0x19c481,_0x2c8a95,_0x245f16,_0x24484e){var _0x4c67af=_0x40c820;let _0x3ce9b8,_0x1af844;try{_0x1af844=_0x1169a5(),_0x3ce9b8=_0x2f8209[_0x2f7559],!_0x3ce9b8||_0x1af844-_0x3ce9b8['ts']>0x1f4&&_0x3ce9b8[_0x4c67af(0xaa)]&&_0x3ce9b8['time']/_0x3ce9b8[_0x4c67af(0xaa)]<0x64?(_0x2f8209[_0x2f7559]=_0x3ce9b8={'count':0x0,'time':0x0,'ts':_0x1af844},_0x2f8209[_0x4c67af(0xc5)]={}):_0x1af844-_0x2f8209[_0x4c67af(0xc5)]['ts']>0x32&&_0x2f8209['hits']['count']&&_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x161)]/_0x2f8209[_0x4c67af(0xc5)]['count']<0x64&&(_0x2f8209[_0x4c67af(0xc5)]={});let _0x157126=[],_0x1464d6=_0x3ce9b8[_0x4c67af(0x112)]||_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]?_0x11fc4c:_0x218fe5,_0x553948=_0x2ff7b4=>{var _0x3a1316=_0x4c67af;let _0x5d676c={};return _0x5d676c[_0x3a1316(0x180)]=_0x2ff7b4[_0x3a1316(0x180)],_0x5d676c['elements']=_0x2ff7b4['elements'],_0x5d676c[_0x3a1316(0x126)]=_0x2ff7b4[_0x3a1316(0x126)],_0x5d676c['totalStrLength']=_0x2ff7b4['totalStrLength'],_0x5d676c['autoExpandLimit']=_0x2ff7b4[_0x3a1316(0x177)],_0x5d676c[_0x3a1316(0x115)]=_0x2ff7b4['autoExpandMaxDepth'],_0x5d676c['sortProps']=!0x1,_0x5d676c['noFunctions']=!_0x45a6b5,_0x5d676c['depth']=0x1,_0x5d676c[_0x3a1316(0xe3)]=0x0,_0x5d676c['expId']='root_exp_id',_0x5d676c[_0x3a1316(0x140)]=_0x3a1316(0x151),_0x5d676c['autoExpand']=!0x0,_0x5d676c[_0x3a1316(0x118)]=[],_0x5d676c[_0x3a1316(0x9d)]=0x0,_0x5d676c[_0x3a1316(0x168)]=!0x0,_0x5d676c['allStrLength']=0x0,_0x5d676c[_0x3a1316(0xc8)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0x5d676c;};for(var _0x33de8f=0x0;_0x33de8f<_0x245f16[_0x4c67af(0x14f)];_0x33de8f++)_0x157126[_0x4c67af(0x157)](_0x459cb0[_0x4c67af(0x14a)]({'timeNode':_0x50675f===_0x4c67af(0x161)||void 0x0},_0x245f16[_0x33de8f],_0x553948(_0x1464d6),{}));if(_0x50675f==='trace'||_0x50675f===_0x4c67af(0x184)){let _0x1d9735=Error[_0x4c67af(0xd7)];try{Error[_0x4c67af(0xd7)]=0x1/0x0,_0x157126[_0x4c67af(0x157)](_0x459cb0['serialize']({'stackNode':!0x0},new Error()['stack'],_0x553948(_0x1464d6),{'strLength':0x1/0x0}));}finally{Error[_0x4c67af(0xd7)]=_0x1d9735;}}return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':_0x157126,'id':_0x2f7559,'context':_0x24484e}]};}catch(_0x2d5a77){return{'method':_0x4c67af(0xff),'version':_0x3cee70,'args':[{'ts':_0x19c481,'session':_0x2c8a95,'args':[{'type':_0x4c67af(0xca),'error':_0x2d5a77&&_0x2d5a77[_0x4c67af(0xb4)]}],'id':_0x2f7559,'context':_0x24484e}]};}finally{try{if(_0x3ce9b8&&_0x1af844){let _0xff386f=_0x1169a5();_0x3ce9b8[_0x4c67af(0xaa)]++,_0x3ce9b8['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x3ce9b8['ts']=_0xff386f,_0x2f8209[_0x4c67af(0xc5)]['count']++,_0x2f8209[_0x4c67af(0xc5)]['time']+=_0x5b41b9(_0x1af844,_0xff386f),_0x2f8209['hits']['ts']=_0xff386f,(_0x3ce9b8[_0x4c67af(0xaa)]>0x32||_0x3ce9b8[_0x4c67af(0x161)]>0x64)&&(_0x3ce9b8['reduceLimits']=!0x0),(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0xaa)]>0x3e8||_0x2f8209[_0x4c67af(0xc5)]['time']>0x12c)&&(_0x2f8209[_0x4c67af(0xc5)][_0x4c67af(0x112)]=!0x0);}}catch{}}}return _0x482c8e;}((_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x296e29,_0x567fe9,_0x14adfa,_0x6b3989,_0x593945,_0x42f609)=>{var _0x543ef9=_0x418f23;if(_0x12a02f[_0x543ef9(0xdb)])return _0x12a02f[_0x543ef9(0xdb)];if(!X(_0x12a02f,_0x14adfa,_0x1164b7))return _0x12a02f[_0x543ef9(0xdb)]={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x12a02f[_0x543ef9(0xdb)];let _0x5a7d78=B(_0x12a02f),_0x236b4f=_0x5a7d78[_0x543ef9(0xd3)],_0x57b9d9=_0x5a7d78['timeStamp'],_0x163b61=_0x5a7d78[_0x543ef9(0x11b)],_0x384cd9={'hits':{},'ts':{}},_0x9c7997=J(_0x12a02f,_0x6b3989,_0x384cd9,_0x296e29),_0x3ffb36=_0xa1ec34=>{_0x384cd9['ts'][_0xa1ec34]=_0x57b9d9();},_0x4ce4d2=(_0x173258,_0x2e0c6d)=>{var _0x3b6e53=_0x543ef9;let _0x2b64c1=_0x384cd9['ts'][_0x2e0c6d];if(delete _0x384cd9['ts'][_0x2e0c6d],_0x2b64c1){let _0x557981=_0x236b4f(_0x2b64c1,_0x57b9d9());_0x593a90(_0x9c7997(_0x3b6e53(0x161),_0x173258,_0x163b61(),_0x50e896,[_0x557981],_0x2e0c6d));}},_0x46c5f3=_0x1f105e=>{var _0x1152c8=_0x543ef9,_0x4a2783;return _0x1164b7===_0x1152c8(0x110)&&_0x12a02f[_0x1152c8(0x152)]&&((_0x4a2783=_0x1f105e==null?void 0x0:_0x1f105e[_0x1152c8(0xab)])==null?void 0x0:_0x4a2783[_0x1152c8(0x14f)])&&(_0x1f105e[_0x1152c8(0xab)][0x0][_0x1152c8(0x152)]=_0x12a02f[_0x1152c8(0x152)]),_0x1f105e;};_0x12a02f[_0x543ef9(0xdb)]={'consoleLog':(_0x204f4b,_0x3e1804)=>{var _0x309615=_0x543ef9;_0x12a02f['console'][_0x309615(0xff)][_0x309615(0xb7)]!==_0x309615(0x134)&&_0x593a90(_0x9c7997(_0x309615(0xff),_0x204f4b,_0x163b61(),_0x50e896,_0x3e1804));},'consoleTrace':(_0x267a3f,_0x51c339)=>{var _0x4c4943=_0x543ef9,_0x33fd8c,_0x24e61f;_0x12a02f[_0x4c4943(0xbc)][_0x4c4943(0xff)]['name']!==_0x4c4943(0xbd)&&((_0x24e61f=(_0x33fd8c=_0x12a02f[_0x4c4943(0x104)])==null?void 0x0:_0x33fd8c['versions'])!=null&&_0x24e61f[_0x4c4943(0xc8)]&&(_0x12a02f[_0x4c4943(0x165)]=!0x0),_0x593a90(_0x46c5f3(_0x9c7997(_0x4c4943(0xc4),_0x267a3f,_0x163b61(),_0x50e896,_0x51c339))));},'consoleError':(_0xf7f1fc,_0x1384d7)=>{var _0x28d83c=_0x543ef9;_0x12a02f['_ninjaIgnoreNextError']=!0x0,_0x593a90(_0x46c5f3(_0x9c7997(_0x28d83c(0x184),_0xf7f1fc,_0x163b61(),_0x50e896,_0x1384d7)));},'consoleTime':_0x2ad865=>{_0x3ffb36(_0x2ad865);},'consoleTimeEnd':(_0x3c91cf,_0x308c8b)=>{_0x4ce4d2(_0x308c8b,_0x3c91cf);},'autoLog':(_0x4bbc9f,_0x3599a3)=>{var _0x598cfa=_0x543ef9;_0x593a90(_0x9c7997(_0x598cfa(0xff),_0x3599a3,_0x163b61(),_0x50e896,[_0x4bbc9f]));},'autoLogMany':(_0x158592,_0x29b77d)=>{var _0x425f64=_0x543ef9;_0x593a90(_0x9c7997(_0x425f64(0xff),_0x158592,_0x163b61(),_0x50e896,_0x29b77d));},'autoTrace':(_0x3f5f9d,_0xc378ab)=>{var _0x377a7d=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x377a7d(0xc4),_0xc378ab,_0x163b61(),_0x50e896,[_0x3f5f9d])));},'autoTraceMany':(_0x2c6f73,_0x35405b)=>{var _0x4f4e7f=_0x543ef9;_0x593a90(_0x46c5f3(_0x9c7997(_0x4f4e7f(0xc4),_0x2c6f73,_0x163b61(),_0x50e896,_0x35405b)));},'autoTime':(_0x4915d6,_0xaaf0db,_0x4c1f1e)=>{_0x3ffb36(_0x4c1f1e);},'autoTimeEnd':(_0x397624,_0x436d57,_0x47b9b8)=>{_0x4ce4d2(_0x436d57,_0x47b9b8);},'coverage':_0x45a646=>{var _0x1e9860=_0x543ef9;_0x593a90({'method':_0x1e9860(0x13a),'version':_0x296e29,'args':[{'id':_0x45a646}]});}};let _0x593a90=H(_0x12a02f,_0x4ac981,_0x2c6df3,_0x42bf03,_0x1164b7,_0x593945,_0x42f609),_0x50e896=_0x12a02f[_0x543ef9(0xd8)];return _0x12a02f[_0x543ef9(0xdb)];})(globalThis,'127.0.0.1',_0x418f23(0xec),_0x418f23(0x17a),_0x418f23(0x183),'1.0.0','1751470503250',_0x418f23(0x137),_0x418f23(0x18a),_0x418f23(0x10a),_0x418f23(0xb1));\");}catch(e){}};/* istanbul ignore next */function oo_oo(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleLog(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_tr(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleTrace(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_tx(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleError(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_ts(/**@type{any}**/v){try{oo_cm().consoleTime(v);}catch(e){} return v;};/* istanbul ignore next */function oo_te(/**@type{any}**/v, /**@type{any}**/i){try{oo_cm().consoleTimeEnd(v, i);}catch(e){} return v;};/*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/"]}, "metadata": {}, "sourceType": "module"}