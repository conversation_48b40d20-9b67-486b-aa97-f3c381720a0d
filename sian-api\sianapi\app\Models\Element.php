<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Element
 * 
 * @property bool $element_code
 * @property string $element_name
 * @property bool $element_class_code
 * 
 * @property ElementClass $element_class
 * @property Collection|Account[] $accounts
 *
 * @package App\Models
 */
class Element extends Model
{
	protected $table = 'element';
	protected $primaryKey = 'element_code';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'element_code' => 'bool',
		'element_class_code' => 'bool'
	];

	protected $fillable = [
		'element_name',
		'element_class_code'
	];

	public function element_class()
	{
		return $this->belongsTo(ElementClass::class, 'element_class_code');
	}

	public function accounts()
	{
		return $this->hasMany(Account::class, 'element_code');
	}
}
