<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class OwnerName
 * 
 * @property string $owner
 * @property int $owner_id
 * @property string $owner_aux
 * @property string $owner_name
 * 
 *
 * @package App\Models
 */
class OwnerName extends Model
{
	protected $table = 'owner_name';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'owner_id' => 'int'
	];

	protected $fillable = [
		'owner_aux',
		'owner_name'
	];

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}
}
