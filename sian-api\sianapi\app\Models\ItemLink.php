<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ItemLink
 * 
 * @property int $item_id
 * @property int $parent_item_id
 * @property bool $status
 * @property float $unit_quantity
 * @property float $total_pen
 * @property float $total_usd
 * @property bool $kardex_unlock
 * 
 * @property Item $item
 *
 * @package App\Models
 */
class ItemLink extends Model
{
	protected $table = 'item_link';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'item_id' => 'int',
		'parent_item_id' => 'int',
		'status' => 'bool',
		'unit_quantity' => 'float',
		'total_pen' => 'float',
		'total_usd' => 'float',
		'kardex_unlock' => 'bool'
	];

	protected $fillable = [
		'item_id',
		'parent_item_id',
		'status',
		'unit_quantity',
		'total_pen',
		'total_usd',
		'kardex_unlock'
	];

	public function item()
	{
		return $this->belongsTo(Item::class, 'parent_item_id');
	}
}
