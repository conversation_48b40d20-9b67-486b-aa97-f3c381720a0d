<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ScheduledEntry
 * 
 * @property int $scheduled_entry_id
 * @property int|null $entry_parent_id
 * @property int $movement_parent_id
 * @property int $movement_id
 * @property bool $status
 * @property Carbon $pay_date
 * @property string $currency
 * @property float $amount_pen
 * @property float $amount_usd
 * @property bool $finished
 * 
 * @property Movement $movement
 * @property Entry|null $entry
 * @property Collection|Entry[] $entries
 *
 * @package App\Models
 */
class ScheduledEntry extends Model
{
	protected $table = 'scheduled_entry';
	protected $primaryKey = 'scheduled_entry_id';
	public $timestamps = false;

	protected $casts = [
		'entry_parent_id' => 'int',
		'movement_parent_id' => 'int',
		'movement_id' => 'int',
		'status' => 'bool',
		'amount_pen' => 'float',
		'amount_usd' => 'float',
		'finished' => 'bool'
	];

	protected $dates = [
		'pay_date'
	];

	protected $fillable = [
		'entry_parent_id',
		'movement_parent_id',
		'movement_id',
		'status',
		'pay_date',
		'currency',
		'amount_pen',
		'amount_usd',
		'finished'
	];

	public function movement()
	{
		return $this->belongsTo(Movement::class, 'movement_parent_id');
	}

	public function entry()
	{
		return $this->belongsTo(Entry::class, 'entry_parent_id');
	}

	public function entries()
	{
		return $this->hasMany(Entry::class);
	}
}
