<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Seller
 * 
 * @property int $seller_id
 * @property string $seller_code
 * @property int $person_id
 * @property int|null $seller_type_id
 * @property bool $status
 * 
 * @property Multitable|null $multitable
 * @property Collection|ApiUser[] $api_users
 * @property Collection|CommercialCase[] $commercial_cases
 * @property Collection|CommercialMovement[] $commercial_movements
 * @property Collection|Person[] $people
 * @property Collection|WebUser[] $web_users
 *
 * @package App\Models
 */
class Seller extends Model
{
	protected $table = 'seller';
	protected $primaryKey = 'seller_id';
	public $timestamps = false;

	protected $casts = [
		'person_id' => 'int',
		'seller_type_id' => 'int',
		'status' => 'bool'
	];

	protected $fillable = [
		'seller_code',
		'person_id',
		'seller_type_id',
		'status'
	];

	public function multitable()
	{
		return $this->belongsTo(Multitable::class, 'seller_type_id');
	}

	public function api_users()
	{
		return $this->hasMany(ApiUser::class);
	}

	public function commercial_cases()
	{
		return $this->hasMany(CommercialCase::class);
	}

	public function commercial_movements()
	{
		return $this->hasMany(CommercialMovement::class);
	}

	public function people()
	{
		return $this->hasMany(Person::class, 'user_catchment_id');
	}

	public function web_users()
	{
		return $this->hasMany(WebUser::class);
	}
}
