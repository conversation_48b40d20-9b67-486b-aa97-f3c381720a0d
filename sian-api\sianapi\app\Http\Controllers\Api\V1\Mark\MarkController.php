<?php

namespace App\Http\Controllers\Api\V1\Mark;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Models\Mark;


class MarkController extends Controller {
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getItems(Request $request) {
        try {

            $query = Mark::where("status", 1);

            if ($request->has('keyword')) {
                $keyword = $request->input('keyword');
                $query->where('mark_name', 'like', '%' . $keyword . '%');
            }


            if ($request->has('mark')) {
                $markSelected = $request->input('mark');
                $arrayMark = explode(",", $markSelected);
                $query->whereNotIn("mark_id", $arrayMark);
            }

            if ($request->has('limit')) {
                $limit = $request->input('limit');
                $query->limit($limit);
            } else {
                $query->limit(10);
            }


            $data = $query->get();

            return response()->json([
                'success' => true,
                'data' => $data,

            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }
}
