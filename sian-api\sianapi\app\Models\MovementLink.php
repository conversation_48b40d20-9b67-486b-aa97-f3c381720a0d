<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class MovementLink
 * 
 * @property int $movement_id
 * @property int $movement_parent_id
 * @property string $type
 * @property string $route
 * @property string $parent_route
 * @property Carbon $emission_date
 * @property float $munit_quantity
 * @property float $sunit_quantity
 * @property bool $status
 * @property string $class
 * @property bool $link
 * @property bool $manual
 * @property string|null $default_ability
 * 
 * @property Movement $movement
 * @property Scenario $scenario
 *
 * @package App\Models
 */
class MovementLink extends Model
{
	protected $table = 'movement_link';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'movement_parent_id' => 'int',
		'munit_quantity' => 'float',
		'sunit_quantity' => 'float',
		'status' => 'bool',
		'link' => 'bool',
		'manual' => 'bool'
	];

	protected $dates = [
		'emission_date'
	];

	protected $fillable = [
		'type',
		'route',
		'parent_route',
		'emission_date',
		'munit_quantity',
		'sunit_quantity',
		'status',
		'class',
		'link',
		'manual',
		'default_ability'
	];

	public function movement()
	{
		return $this->belongsTo(Movement::class, 'movement_parent_id');
	}

	public function scenario()
	{
		return $this->belongsTo(Scenario::class, 'route');
	}
}
