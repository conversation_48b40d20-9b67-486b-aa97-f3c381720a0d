<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ExchangeRate
 *
 * @property int $exchange_rate_id
 * @property Carbon $date
 * @property float $purchase
 * @property float $sale
 * @property bool $is_temp
 *
 * @package App\Models
 */
class ExchangeRate extends Model
{
	protected $table = 'exchange_rate';
	protected $primaryKey = 'date';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'exchange_rate_id' => 'int',
		'purchase' => 'float',
		'sale' => 'float',
		'is_temp' => 'bool'
	];

	protected $dates = [
		'date'
	];

	protected $fillable = [
		'exchange_rate_id',
		'purchase',
		'sale',
		'is_temp'
	];

     /**
     * Obtiene el último tipo de cambio registrado.
     *
     * @return ExchangeRate|null
     */
    public static function getLatestExchangeRate()
    {
        return self::orderBy('date', 'desc')->first();
    }

    /**
     * Convierte un monto de soles a dólares usando el último tipo de cambio de venta.
     *
     * @param float $amountInSoles
     * @return float|null
     */
    public static function convertToDollars(float $amountInSoles): ?float
    {
        $latestExchangeRate = self::getLatestExchangeRate();

        if (!$latestExchangeRate || $latestExchangeRate->sale == 0) {
            return null; // Evitar división por cero o datos inexistentes
        }

        return $amountInSoles / $latestExchangeRate->sale;
    }
}
