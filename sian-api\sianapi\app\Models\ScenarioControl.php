<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ScenarioControl
 * 
 * @property string $route
 * @property string $parent_route
 * @property string $default_ability
 * @property bool $same_exchange
 * @property string $class
 * @property bool $later_than
 * @property bool $link
 * @property bool $link_item_quantity
 * @property bool $link_item_total
 * @property bool $kardex_unlock
 * 
 * @property Scenario $scenario
 *
 * @package App\Models
 */
class ScenarioControl extends Model
{
	protected $table = 'scenario_control';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'same_exchange' => 'bool',
		'later_than' => 'bool',
		'link' => 'bool',
		'link_item_quantity' => 'bool',
		'link_item_total' => 'bool',
		'kardex_unlock' => 'bool'
	];

	protected $fillable = [
		'default_ability',
		'same_exchange',
		'class',
		'later_than',
		'link',
		'link_item_quantity',
		'link_item_total',
		'kardex_unlock'
	];

	public function scenario()
	{
		return $this->belongsTo(Scenario::class, 'route');
	}
}
