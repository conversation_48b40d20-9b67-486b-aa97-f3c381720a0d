<?php

namespace App\Http\Resources\Procedures;

use Illuminate\Http\Resources\Json\ResourceCollection;

class DocumentaryProcedureCollection extends ResourceCollection
{

    public $collects = DocumentaryProcedureResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            'data' => $this->collection,
            // 'meta' => [
            //     'organization' => 'Hardtech',
            //     'authors' => [
            //         'Luis <PERSON>',
            //         'Hardtech'
            //     ]
            // ],
            // 'type' => 'Procedure'
        ];
    }
}
