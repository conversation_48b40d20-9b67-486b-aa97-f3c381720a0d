<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ECommerceDestinyType
 * 
 * @property int $person_id
 * @property string $destiny_type
 * 
 * @property ECommerceProvider $e_commerce_provider
 * @property Collection|ECommerceDestiny[] $e_commerce_destinies
 * @property Collection|ECommerceTariff[] $e_commerce_tariffs
 *
 * @package App\Models
 */
class ECommerceDestinyType extends Model
{
	protected $table = 'e_commerce_destiny_type';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'person_id' => 'int'
	];

	public function e_commerce_provider()
	{
		return $this->belongsTo(ECommerceProvider::class, 'person_id');
	}

	public function e_commerce_destinies()
	{
		return $this->hasMany(ECommerceDestiny::class, 'person_id');
	}

	public function e_commerce_tariffs()
	{
		return $this->hasMany(ECommerceTariff::class, 'person_id');
	}
}
