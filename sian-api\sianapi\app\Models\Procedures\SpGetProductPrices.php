<?php

namespace App\Models\Procedures;

use App\Models\Fake\Currency;
use Illuminate\Support\Facades\DB;

class SpGetProductPrices {
    const MODE_ALL = 0;
    const MODE_UNIT = 1;
    const MODE_DEFAULT = 2;

    public static function execute($product_ids = [], $store_ids = [], $price_ids = [], $mode = self::MODE_ALL, $currency = Currency::PEN) {
        $stringProductIds = implode(',', $product_ids);
        $stringStoreIds = implode(',', $store_ids);
        $stringPriceIds = implode(',', $price_ids);

        $sql = "CALL sp_get_prices_store(?, ?, ?, ?, ?)";
        $parameters = [
            $stringProductIds,
            $stringStoreIds,
            $stringPriceIds,
            $mode,
            $currency,
        ];

        return DB::select($sql, $parameters);
    }
}
