<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Kardex
 * 
 * @property int $kardex_id
 * @property int $item_id
 * @property int $product_id
 * @property int $warehouse_id
 * @property int $movement_id
 * @property string $direction
 * @property bool $priority
 * @property float $in_unit_quantity
 * @property float $in_unit_cost
 * @property float $in_unit_net
 * @property float $out_unit_quantity
 * @property float $out_unit_cost
 * @property float $out_unit_net
 * @property float $unit_incoming
 * @property float $unit_outgoing
 * @property float $unit_rquantity
 * @property float $unit_cquantity
 * @property float $unit_stock
 * @property float $unit_rstock
 * @property float $unit_cstock
 * @property float $unit_pstock
 * @property float $unit_sstock
 * @property float $unit_cost
 * @property float $unit_net
 * @property bool $last_of_day
 * @property int $order
 * @property bool $is_in_before_insertable
 * @property bool $is_out_before_insertable
 * @property bool $is_nullable
 * @property float $default_stock
 * @property float $default_rstock
 * @property float $default_cstock
 * @property float $default_pstock
 * @property float $default_sstock
 * @property string $mixed_stock
 * @property string $mixed_rstock
 * @property string $mixed_cstock
 * @property string $mixed_pstock
 * @property string $mixed_sstock
 * 
 * @property Item $item
 * @property Movement $movement
 * @property Merchandise $merchandise
 * @property Warehouse $warehouse
 * @property Collection|KardexRange[] $kardex_ranges
 * @property Collection|MerchandiseMaster[] $merchandise_masters
 *
 * @package App\Models
 */
class Kardex extends Model
{
	protected $table = 'kardex';
	protected $primaryKey = 'kardex_id';
	public $timestamps = false;

	protected $casts = [
		'item_id' => 'int',
		'product_id' => 'int',
		'warehouse_id' => 'int',
		'movement_id' => 'int',
		'priority' => 'bool',
		'in_unit_quantity' => 'float',
		'in_unit_cost' => 'float',
		'in_unit_net' => 'float',
		'out_unit_quantity' => 'float',
		'out_unit_cost' => 'float',
		'out_unit_net' => 'float',
		'unit_incoming' => 'float',
		'unit_outgoing' => 'float',
		'unit_rquantity' => 'float',
		'unit_cquantity' => 'float',
		'unit_stock' => 'float',
		'unit_rstock' => 'float',
		'unit_cstock' => 'float',
		'unit_pstock' => 'float',
		'unit_sstock' => 'float',
		'unit_cost' => 'float',
		'unit_net' => 'float',
		'last_of_day' => 'bool',
		'order' => 'int',
		'is_in_before_insertable' => 'bool',
		'is_out_before_insertable' => 'bool',
		'is_nullable' => 'bool',
		'default_stock' => 'float',
		'default_rstock' => 'float',
		'default_cstock' => 'float',
		'default_pstock' => 'float',
		'default_sstock' => 'float'
	];

	protected $fillable = [
		'item_id',
		'product_id',
		'warehouse_id',
		'movement_id',
		'direction',
		'priority',
		'in_unit_quantity',
		'in_unit_cost',
		'in_unit_net',
		'out_unit_quantity',
		'out_unit_cost',
		'out_unit_net',
		'unit_incoming',
		'unit_outgoing',
		'unit_rquantity',
		'unit_cquantity',
		'unit_stock',
		'unit_rstock',
		'unit_cstock',
		'unit_pstock',
		'unit_sstock',
		'unit_cost',
		'unit_net',
		'last_of_day',
		'order',
		'is_in_before_insertable',
		'is_out_before_insertable',
		'is_nullable',
		'default_stock',
		'default_rstock',
		'default_cstock',
		'default_pstock',
		'default_sstock',
		'mixed_stock',
		'mixed_rstock',
		'mixed_cstock',
		'mixed_pstock',
		'mixed_sstock'
	];

	public function item()
	{
		return $this->belongsTo(Item::class);
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function merchandise()
	{
		return $this->belongsTo(Merchandise::class, 'product_id');
	}

	public function warehouse()
	{
		return $this->belongsTo(Warehouse::class);
	}

	public function kardex_ranges()
	{
		return $this->hasMany(KardexRange::class);
	}

	public function merchandise_masters()
	{
		return $this->hasMany(MerchandiseMaster::class);
	}
}
