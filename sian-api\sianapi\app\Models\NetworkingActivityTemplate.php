<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class NetworkingActivityTemplate
 * 
 * @property int $networking_activity_template_id
 * @property string $networking_activity_name
 * @property int $networking_activity_type_id
 * @property int $networking_project_type_id
 * @property string $description
 * @property string|null $estimated_time
 * @property bool $status
 * 
 * @property NetworkingActivityType $networking_activity_type
 * @property NetworkingProjectType $networking_project_type
 *
 * @package App\Models
 */
class NetworkingActivityTemplate extends Model
{
	protected $table = 'networking_activity_template';
	protected $primaryKey = 'networking_activity_template_id';
	public $timestamps = false;

	protected $casts = [
		'networking_activity_type_id' => 'int',
		'networking_project_type_id' => 'int',
		'status' => 'bool'
	];

	protected $fillable = [
		'networking_activity_name',
		'networking_activity_type_id',
		'networking_project_type_id',
		'description',
		'estimated_time',
		'status'
	];

	public function networking_activity_type()
	{
		return $this->belongsTo(NetworkingActivityType::class);
	}

	public function networking_project_type()
	{
		return $this->belongsTo(NetworkingProjectType::class);
	}
}
