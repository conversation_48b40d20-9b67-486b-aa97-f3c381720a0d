<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Area
 * 
 * @property int $area_id
 * @property string $area_name
 * @property string $alias
 * @property bool $status
 * @property int $combination_id
 * 
 * @property Combination $combination
 * @property Collection|Station[] $stations
 *
 * @package App\Models
 */
class Area extends Model
{
	protected $table = 'area';
	protected $primaryKey = 'area_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'combination_id' => 'int'
	];

	protected $fillable = [
		'area_name',
		'alias',
		'status',
		'combination_id'
	];

	public function combination()
	{
		return $this->belongsTo(Combination::class);
	}

	public function stations()
	{
		return $this->hasMany(Station::class);
	}
}
