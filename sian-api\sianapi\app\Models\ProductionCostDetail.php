<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ProductionCostDetail
 *
 * @property int $production_cost_id
 * @property int $production_cost_concept_id
 * @property null|float $amount
 *
 * @property-read ProductionCost $productionCost
 * @property-read ProductionCostConcept $productionCostConcept
 *
 * @package App\Models
 */
class ProductionCostDetail extends Model {
    protected $table = 'production_cost_detail';
    public $timestamps = false;
    public $incrementing = false;
    protected $primaryKey = null;


    protected $casts = [
        'production_cost_id' => 'int',
        'production_cost_concept_id' => 'int',
        'amount' => 'float',
    ];

    protected $fillable = [
        'production_cost_id',
        'production_cost_concept_id',
        'amount',
    ];

    public function productionCost() {
        return $this->belongsTo(ProductionCost::class, 'production_cost_id');
    }

    public function productionCostConcept() {
        return $this->belongsTo(ProductionCostConcept::class, 'production_cost_concept_id');
    }

    protected function setKeysForSaveQuery($query) // PARA TABLAS CON LLAVE COMPUESTA
    {
        return $query->where('production_cost_id', $this->getAttribute('production_cost_id'))
                     ->where('production_cost_concept_id', $this->getAttribute('production_cost_concept_id'));
    }

}
