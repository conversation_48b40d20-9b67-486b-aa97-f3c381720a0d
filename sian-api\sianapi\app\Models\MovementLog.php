<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class MovementLog
 * 
 * @property int $movement_log_id
 * @property int $movement_id
 * @property int $movement_log_type_id
 * @property string $description
 * @property int $person_id
 * @property Carbon $register_date
 * @property bool $last
 * @property bool $last_by_type
 * 
 * @property Movement $movement
 * @property MovementLogType $movement_log_type
 * @property Person $person
 *
 * @package App\Models
 */
class MovementLog extends Model
{
	protected $table = 'movement_log';
	protected $primaryKey = 'movement_log_id';
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'movement_log_type_id' => 'int',
		'person_id' => 'int',
		'last' => 'bool',
		'last_by_type' => 'bool'
	];

	protected $dates = [
		'register_date'
	];

	protected $fillable = [
		'movement_id',
		'movement_log_type_id',
		'description',
		'person_id',
		'register_date',
		'last',
		'last_by_type'
	];

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function movement_log_type()
	{
		return $this->belongsTo(MovementLogType::class);
	}

	public function person()
	{
		return $this->belongsTo(Person::class);
	}
}
