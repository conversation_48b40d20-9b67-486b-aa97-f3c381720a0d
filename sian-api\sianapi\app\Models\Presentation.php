<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Presentation
 * 
 * @property int $presentation_id
 * @property int $product_id
 * @property float $equivalence
 * @property int $measure_id
 * @property string $abbreviation
 * @property int|null $custom_measure_id
 * @property string $measure_name
 * @property string $measure_alias
 * @property string|null $barcode
 * @property float $mprice_pen
 * @property float $aprice_pen
 * @property float $wprice_pen
 * @property float $mprice_usd
 * @property float $aprice_usd
 * @property float $wprice_usd
 * @property float $min_stock
 * @property float $weight
 * @property float $length
 * @property float $width
 * @property float $height
 * @property bool $default
 * 
 * @property Multitable $multitable
 * @property Product $product
 * @property Collection|ActivePromotion[] $active_promotions
 * @property Collection|ComboItem[] $combo_items
 * @property Collection|InventoryDetail[] $inventory_details
 * @property Collection|Item[] $items
 * @property Collection|PromotionGiftOption[] $promotion_gift_options
 * @property Collection|PromotionItem[] $promotion_items
 * @property Collection|ShoppingCartItem[] $shopping_cart_items
 *
 * @package App\Models
 */
class Presentation extends Model
{

	public const UNIT_EQUIVALENCE = '1.00';
	protected $table = 'presentation';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'presentation_id' => 'int',
		'product_id' => 'int',
		'equivalence' => 'float',
		'measure_id' => 'int',
		'custom_measure_id' => 'int',
		'mprice_pen' => 'float',
		'aprice_pen' => 'float',
		'wprice_pen' => 'float',
		'mprice_usd' => 'float',
		'aprice_usd' => 'float',
		'wprice_usd' => 'float',
		'min_stock' => 'float',
		'weight' => 'float',
		'length' => 'float',
		'width' => 'float',
		'height' => 'float',
		'default' => 'bool'
	];

	protected $fillable = [
		'presentation_id',
		'measure_id',
		'abbreviation',
		'custom_measure_id',
		'measure_name',
		'measure_alias',
		'barcode',
		'mprice_pen',
		'aprice_pen',
		'wprice_pen',
		'mprice_usd',
		'aprice_usd',
		'wprice_usd',
		'min_stock',
		'weight',
		'length',
		'width',
		'height',
		'default'
	];

	public function multitable()
	{
		return $this->belongsTo(Multitable::class, 'measure_id');
	}

	public function product()
	{
		return $this->belongsTo(Product::class);
	}

	public function active_promotions()
	{
		return $this->hasMany(ActivePromotion::class, 'product_id');
	}

	public function combo_items()
	{
		return $this->hasMany(ComboItem::class, 'sub_product_id');
	}

	public function inventory_details()
	{
		return $this->hasMany(InventoryDetail::class, 'product_id');
	}

	public function items()
	{
		return $this->hasMany(Item::class, 'product_id');
	}

	public function promotion_gift_options()
	{
		return $this->hasMany(PromotionGiftOption::class, 'product_id');
	}

	public function promotion_items()
	{
		return $this->hasMany(PromotionItem::class, 'product_id');
	}

	public function shopping_cart_items()
	{
		return $this->hasMany(ShoppingCartItem::class, 'product_id');
	}
}
