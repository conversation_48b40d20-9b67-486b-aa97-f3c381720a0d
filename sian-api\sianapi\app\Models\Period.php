<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Period
 * 
 * @property int $period_id
 * @property int $year
 * @property int $period
 * 
 * @property Collection|Calendar[] $calendars
 *
 * @package App\Models
 */
class Period extends Model
{
	protected $table = 'period';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'period_id' => 'int',
		'year' => 'int',
		'period' => 'int'
	];

	protected $fillable = [
		'period_id'
	];

	public function calendars()
	{
		return $this->hasMany(Calendar::class, 'year');
	}
}
