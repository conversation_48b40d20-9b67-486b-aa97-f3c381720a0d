<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Depreciation
 * 
 * @property int $depreciation_id
 * @property int $fixed_asset_id
 * @property int $business_unit_id
 * @property int $year
 * @property int $period
 * @property float $initial_value
 * @property float $historical_depreciation
 * @property float $annual_depreciation
 * @property float $period_depreciation
 * @property float $total_depreciation
 * @property float $final_value
 * @property bool $locked
 * @property int|null $movement_id
 * 
 * @property BusinessUnit $business_unit
 * @property FixedAsset $fixed_asset
 * @property Movement|null $movement
 *
 * @package App\Models
 */
class Depreciation extends Model
{
    protected $table = 'depreciation';
    protected $primaryKey = 'depreciation_id';
    public $timestamps = false;

    protected $casts = [
        'fixed_asset_id' => 'int',
        'business_unit_id' => 'int',
        'year' => 'int',
        'period' => 'int',
        'initial_value' => 'float',
        'historical_depreciation' => 'float',
        'annual_depreciation' => 'float',
        'period_depreciation' => 'float',
        'total_depreciation' => 'float',
        'final_value' => 'float',
        'locked' => 'bool',
        'movement_id' => 'int'
    ];

    protected $fillable = [
        'fixed_asset_id',
        'business_unit_id',
        'year',
        'period',
        'initial_value',
        'historical_depreciation',
        'annual_depreciation',
        'period_depreciation',
        'total_depreciation',
        'final_value',
        'locked',
        'movement_id'
    ];

    public function businessUnit()
    {
        return $this->belongsTo(BusinessUnit::class, 'business_unit_id', 'business_unit_id');
    }

    public function fixedAsset()
    {
        return $this->belongsTo(FixedAsset::class, 'fixed_asset_id', 'fixed_asset_id');
    }

    public function movement()
    {
        return $this->belongsTo(Movement::class, 'movement_id', 'movement_id');
    }
}
