<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class WarehouseArea
 * 
 * @property int $warehouse_area_id
 * @property string $warehouse_area_name
 * @property string $warehouse_area_code
 * @property bool $status
 * @property int $warehouse_id
 * @property bool $level
 * @property int|null $parent_id
 * @property int|null $system
 * @property string|null $search_code
 * 
 * @property Warehouse $warehouse
 * @property WarehouseArea|null $warehouse_area
 * @property Collection|InventoryDetail[] $inventory_details
 * @property Collection|InventoryDetailCounting[] $inventory_detail_countings
 * @property Collection|MerchandiseMaster[] $merchandise_masters
 * @property Collection|MerchandiseMasterArea[] $merchandise_master_areas
 * @property Collection|WarehouseArea[] $warehouse_areas
 *
 * @package App\Models
 */
class WarehouseArea extends Model
{
	protected $table = 'warehouse_area';
	protected $primaryKey = 'warehouse_area_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'warehouse_id' => 'int',
		'level' => 'bool',
		'parent_id' => 'int',
		'system' => 'int'
	];

	protected $fillable = [
		'warehouse_area_name',
		'warehouse_area_code',
		'status',
		'warehouse_id',
		'level',
		'parent_id',
		'system',
		'search_code'
	];

	public function warehouse()
	{
		return $this->belongsTo(Warehouse::class);
	}

	public function warehouse_area()
	{
		return $this->belongsTo(WarehouseArea::class, 'parent_id');
	}

	public function inventory_details()
	{
		return $this->hasMany(InventoryDetail::class, 'real_warehouse_area_id');
	}

	public function inventory_detail_countings()
	{
		return $this->hasMany(InventoryDetailCounting::class);
	}

	public function merchandise_masters()
	{
		return $this->hasMany(MerchandiseMaster::class);
	}

	public function merchandise_master_areas()
	{
		return $this->hasMany(MerchandiseMasterArea::class);
	}

	public function warehouse_areas()
	{
		return $this->hasMany(WarehouseArea::class, 'parent_id');
	}
}
