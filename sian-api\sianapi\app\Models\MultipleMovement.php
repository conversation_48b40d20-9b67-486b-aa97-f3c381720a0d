<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class MultipleMovement
 * 
 * @property int $movement_id
 * @property float $amount_pen
 * @property float $amount_usd
 * @property bool $manual
 * @property int|null $aval_id
 * 
 * @property Person|null $person
 * @property Movement $movement
 *
 * @package App\Models
 */
class MultipleMovement extends Model
{
	protected $table = 'multiple_movement';
	protected $primaryKey = 'movement_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'amount_pen' => 'float',
		'amount_usd' => 'float',
		'manual' => 'bool',
		'aval_id' => 'int'
	];

	protected $fillable = [
		'amount_pen',
		'amount_usd',
		'manual',
		'aval_id'
	];

	public function person()
	{
		return $this->belongsTo(Person::class, 'aval_id');
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}
}
