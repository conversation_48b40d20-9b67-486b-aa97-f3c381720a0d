<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class JobOffer
 * 
 * @property int $job_offer_id
 * @property string $title
 * @property string $description
 * @property string $requirements
 * @property bool $status
 * @property int $station_id
 * @property int $postulant_count
 * 
 * @property Station $station
 *
 * @package App\Models
 */
class JobOffer extends Model
{
	protected $table = 'job_offer';
	protected $primaryKey = 'job_offer_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'station_id' => 'int',
		'postulant_count' => 'int'
	];

	protected $fillable = [
		'title',
		'description',
		'requirements',
		'status',
		'station_id',
		'postulant_count'
	];

	public function station()
	{
		return $this->belongsTo(Station::class);
	}
}
