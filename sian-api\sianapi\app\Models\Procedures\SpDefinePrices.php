<?php

namespace App\Models\Procedures;

use Illuminate\Support\Facades\DB;

class SpDefinePrices {
    public static function execute($product_id, $equivalence = 1, $imprice, $iaprice, $iwprice) {
        $sql = "CALL sp_define_prices(?, ?, ?, ?, ?)";
        $parameters = [
            $product_id,
            $equivalence,
            $imprice,
            $iaprice,
            $iwprice,
        ];

        DB::statement($sql, $parameters);
    }
}
