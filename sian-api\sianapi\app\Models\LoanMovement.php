<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class LoanMovement
 * 
 * @property int $movement_id
 * @property string $fee_type
 * @property int $fee_count
 * @property int $fee_frequency
 * @property float $tea_percent
 * @property float $te_percent
 * @property float $tcea_percent
 * @property float $desgravamen_percent
 * @property float $pporte_pen
 * @property float $pporte_usd
 * @property float $itf_percent
 * @property float $amortization_pen
 * @property float $interest_pen
 * @property float $desgravamen_pen
 * @property float $oae_adm_pen
 * @property float $oae_financial_pen
 * @property float $oae_sales_pen
 * @property float $porte_pen
 * @property float $financing_pen
 * @property float $itf_pen
 * @property float $real_pen
 * @property float $amortization_usd
 * @property float $interest_usd
 * @property float $desgravamen_usd
 * @property float $oae_adm_usd
 * @property float $oae_financial_usd
 * @property float $oae_sales_usd
 * @property float $porte_usd
 * @property float $financing_usd
 * @property float $itf_usd
 * @property float $real_usd
 * @property int|null $cashbox_id
 * 
 * @property Cashbox|null $cashbox
 * @property Movement $movement
 * @property Collection|LoanMovementFee[] $loan_movement_fees
 *
 * @package App\Models
 */
class LoanMovement extends Model
{
	protected $table = 'loan_movement';
	protected $primaryKey = 'movement_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'fee_count' => 'int',
		'fee_frequency' => 'int',
		'tea_percent' => 'float',
		'te_percent' => 'float',
		'tcea_percent' => 'float',
		'desgravamen_percent' => 'float',
		'pporte_pen' => 'float',
		'pporte_usd' => 'float',
		'itf_percent' => 'float',
		'amortization_pen' => 'float',
		'interest_pen' => 'float',
		'desgravamen_pen' => 'float',
		'oae_adm_pen' => 'float',
		'oae_financial_pen' => 'float',
		'oae_sales_pen' => 'float',
		'porte_pen' => 'float',
		'financing_pen' => 'float',
		'itf_pen' => 'float',
		'real_pen' => 'float',
		'amortization_usd' => 'float',
		'interest_usd' => 'float',
		'desgravamen_usd' => 'float',
		'oae_adm_usd' => 'float',
		'oae_financial_usd' => 'float',
		'oae_sales_usd' => 'float',
		'porte_usd' => 'float',
		'financing_usd' => 'float',
		'itf_usd' => 'float',
		'real_usd' => 'float',
		'cashbox_id' => 'int'
	];

	protected $fillable = [
		'fee_type',
		'fee_count',
		'fee_frequency',
		'tea_percent',
		'te_percent',
		'tcea_percent',
		'desgravamen_percent',
		'pporte_pen',
		'pporte_usd',
		'itf_percent',
		'amortization_pen',
		'interest_pen',
		'desgravamen_pen',
		'oae_adm_pen',
		'oae_financial_pen',
		'oae_sales_pen',
		'porte_pen',
		'financing_pen',
		'itf_pen',
		'real_pen',
		'amortization_usd',
		'interest_usd',
		'desgravamen_usd',
		'oae_adm_usd',
		'oae_financial_usd',
		'oae_sales_usd',
		'porte_usd',
		'financing_usd',
		'itf_usd',
		'real_usd',
		'cashbox_id'
	];

	public function cashbox()
	{
		return $this->belongsTo(Cashbox::class);
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function loan_movement_fees()
	{
		return $this->hasMany(LoanMovementFee::class, 'movement_id');
	}
}
