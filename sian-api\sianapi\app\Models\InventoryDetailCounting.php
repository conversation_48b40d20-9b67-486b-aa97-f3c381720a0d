<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class InventoryDetailCounting
 * 
 * @property int $inventory_detail_counting_id
 * @property int $inventory_detail_id
 * @property int|null $warehouse_area_id
 * @property float $quantity
 * @property string|null $serie
 * @property string|null $observation
 * @property Carbon $register_date
 * @property int $person_id
 * 
 * @property InventoryDetail $inventory_detail
 * @property Person $person
 * @property WarehouseArea|null $warehouse_area
 *
 * @package App\Models
 */
class InventoryDetailCounting extends Model
{
	protected $table = 'inventory_detail_counting';
	protected $primaryKey = 'inventory_detail_counting_id';
	public $timestamps = false;

	protected $casts = [
		'inventory_detail_id' => 'int',
		'warehouse_area_id' => 'int',
		'quantity' => 'float',
		'person_id' => 'int'
	];

	protected $dates = [
		'register_date'
	];

	protected $fillable = [
		'inventory_detail_id',
		'warehouse_area_id',
		'quantity',
		'serie',
		'observation',
		'register_date',
		'person_id'
	];

	public function inventory_detail()
	{
		return $this->belongsTo(InventoryDetail::class);
	}

	public function person()
	{
		return $this->belongsTo(Person::class);
	}

	public function warehouse_area()
	{
		return $this->belongsTo(WarehouseArea::class);
	}
}
