<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class LockerBoxRequest
 * 
 * @property int $locker_box_request_id
 * @property string $code
 * @property bool $status
 * @property string $state
 * @property int $article_count
 * @property int $article_quantity
 * @property int $locker_request_id
 * @property int $locker_box_id
 * @property bool $backed_to_fill
 * @property bool $backed_to_pick_up
 * @property bool $backed_to_return
 * @property bool $is_editable
 * @property bool $is_back_fillable
 * @property bool $is_not_fillable
 * @property bool $is_back_pickable
 * @property bool $is_returnable
 * @property bool $is_back_returnable
 * @property bool $is_nullable
 * @property int $order
 * 
 * @property LockerBox $locker_box
 * @property LockerRequest $locker_request
 * @property Collection|LockerArticle[] $locker_articles
 * @property Collection|LockerBox[] $locker_boxes
 *
 * @package App\Models
 */
class LockerBoxRequest extends Model
{
	protected $table = 'locker_box_request';
	protected $primaryKey = 'locker_box_request_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'article_count' => 'int',
		'article_quantity' => 'int',
		'locker_request_id' => 'int',
		'locker_box_id' => 'int',
		'backed_to_fill' => 'bool',
		'backed_to_pick_up' => 'bool',
		'backed_to_return' => 'bool',
		'is_editable' => 'bool',
		'is_back_fillable' => 'bool',
		'is_not_fillable' => 'bool',
		'is_back_pickable' => 'bool',
		'is_returnable' => 'bool',
		'is_back_returnable' => 'bool',
		'is_nullable' => 'bool',
		'order' => 'int'
	];

	protected $fillable = [
		'code',
		'status',
		'state',
		'article_count',
		'article_quantity',
		'locker_request_id',
		'locker_box_id',
		'backed_to_fill',
		'backed_to_pick_up',
		'backed_to_return',
		'is_editable',
		'is_back_fillable',
		'is_not_fillable',
		'is_back_pickable',
		'is_returnable',
		'is_back_returnable',
		'is_nullable',
		'order'
	];

	public function locker_box()
	{
		return $this->belongsTo(LockerBox::class);
	}

	public function locker_request()
	{
		return $this->belongsTo(LockerRequest::class);
	}

	public function locker_articles()
	{
		return $this->hasMany(LockerArticle::class);
	}

	public function locker_boxes()
	{
		return $this->hasMany(LockerBox::class, 'last_locker_box_request_id');
	}
}
