<?php
namespace App\Models\Procedures;

use Illuminate\Support\Facades\DB;

class SpGetProductPresentationsStocks
{
    public static function getAssociative($productIds, $date = null, $excludeId = null, $storeId = null)
    {
        $productIdsString = implode(',', $productIds);

        $items = DB::select('CALL sp_get_product_presentations_stocks(?, ?, ?, ?)', [
            $productIdsString,
            $date,
            $excludeId,
            $storeId,
        ]);

        $data = [];
        foreach ($items as $item) {
            $data[$item->pk] = $item;
        }

        return $data;
    }
}
