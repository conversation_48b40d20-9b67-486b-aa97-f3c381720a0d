<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ApiUserAdminVariant
 * 
 * @property int $api_user_admin_variant_id
 * @property int $person_id
 * @property string $email_address
 * @property string $phone_number
 * @property int $api_user_variant_id
 * @property int $order
 * 
 * @property ApiUserVariant $api_user_variant
 * @property Person $person
 *
 * @package App\Models
 */
class ApiUserAdminVariant extends Model
{
	protected $table = 'api_user_admin_variant';
	protected $primaryKey = 'api_user_admin_variant_id';
	public $timestamps = false;

	protected $casts = [
		'person_id' => 'int',
		'api_user_variant_id' => 'int',
		'order' => 'int'
	];

	protected $fillable = [
		'person_id',
		'email_address',
		'phone_number',
		'api_user_variant_id',
		'order'
	];

	public function api_user_variant()
	{
		return $this->belongsTo(ApiUserVariant::class);
	}

	public function person()
	{
		return $this->belongsTo(Person::class);
	}
}
