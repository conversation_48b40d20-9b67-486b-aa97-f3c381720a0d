<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class HardtechPeProduct
 * 
 * @property int $product_id
 * @property string $product_name
 * @property string|null $model
 * @property string|null $part_number
 * @property bool $frontend
 *
 * @package App\Models
 */
class HardtechPeProduct extends Model
{
	protected $table = 'hardtech_pe_products';
	protected $primaryKey = 'product_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'product_id' => 'int',
		'frontend' => 'bool'
	];

	protected $fillable = [
		'product_name',
		'model',
		'part_number',
		'frontend'
	];
}
