<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Hit
 * 
 * @property string $owner
 * @property int $owner_id
 * @property Carbon $date
 * @property string $ip
 * @property string $agent
 * @property string $hostname
 * 
 *
 * @package App\Models
 */
class Hit extends Model
{
	protected $table = 'hit';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'owner_id' => 'int'
	];

	protected $dates = [
		'date'
	];

	protected $fillable = [
		'ip',
		'agent',
		'hostname'
	];

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}
}
