<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class CommercialGoal
 * 
 * @property int $commercial_goal_id
 * @property int $month
 * @property int $year
 * @property int $business_unit_id
 * @property int $store_id
 * @property int $person_id
 * @property float $monthly_amount
 * @property string $currency
 * 
 * @property BusinessUnit $business_unit
 * @property Person $person
 * @property Store $store
 *
 * @package App\Models
 */
class CommercialGoal extends Model
{
	protected $table = 'commercial_goal';
	protected $primaryKey = 'commercial_goal_id';
	public $timestamps = false;

	protected $casts = [
		'month' => 'int',
		'year' => 'int',
		'business_unit_id' => 'int',
		'store_id' => 'int',
		'person_id' => 'int',
		'monthly_amount' => 'float'
	];

	protected $fillable = [
		'month',
		'year',
		'business_unit_id',
		'store_id',
		'person_id',
		'monthly_amount',
		'currency'
	];

	public function business_unit()
	{
		return $this->belongsTo(BusinessUnit::class);
	}

	public function person()
	{
		return $this->belongsTo(Person::class);
	}

	public function store()
	{
		return $this->belongsTo(Store::class);
	}
}
