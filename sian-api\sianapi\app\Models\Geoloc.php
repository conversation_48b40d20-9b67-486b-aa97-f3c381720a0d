<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Geoloc
 * 
 * @property string $dept_code
 * @property string $prov_code
 * @property string $dist_code
 * @property string $dept_name
 * @property string $prov_name
 * @property string $dist_name
 * 
 * @property Collection|Address[] $addresses
 * @property Collection|ECommerceDestiny[] $e_commerce_destinies
 *
 * @package App\Models
 */
class Geoloc extends Model
{
	protected $table = 'geoloc';
	public $incrementing = false;
	public $timestamps = false;

	protected $fillable = [
		'dept_name',
		'prov_name',
		'dist_name'
	];

	public function addresses()
	{
		return $this->hasMany(Address::class, 'dept_code');
	}

	public function e_commerce_destinies()
	{
		return $this->hasMany(ECommerceDestiny::class, 'dept_code');
	}
}
