{"ast": null, "code": "var _jsxFileName = \"D:\\\\www\\\\SWF\\\\sian-web-frontend\\\\src\\\\ui-component\\\\grid\\\\NestedGrid.jsx\",\n    _s = $RefreshSig$();\n\nimport React, { useState } from 'react';\nimport MUIDataTable from 'mui-datatables';\nimport { makeStyles } from '@mui/styles';\nimport CustomOptionsMUIDtb from 'assets/customization/mui-datatable/CustomOptionsMUIDtb';\nimport useGridColumn from 'hooks/useGridColumn';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst defaultOptions = {\n  search: true,\n  download: false,\n  print: false,\n  sort: true,\n  viewColumns: true,\n  filter: true,\n  filterType: 'multiselect',\n  responsive: 'vertical',\n  fixedHeader: true,\n  fixedSelectColumn: true,\n  textLabels: CustomOptionsMUIDtb.textLabels,\n  jumpToPage: true,\n  resizableColumns: false,\n  draggableColumns: {\n    enabled: true\n  },\n  selectableRows: 'none',\n  selectableRowsOnClick: false,\n  pagination: false,\n  confirmFilters: false,\n  rowHover: true,\n  toolbar: false,\n  setTableProps: () => ({\n    size: 'small'\n  })\n};\nconst useTableStyles = makeStyles({\n  tableRoot: {\n    border: 'none',\n    boxShadow: 'none',\n    backgroundColor: 'transparent'\n  },\n  paper: {\n    backgroundColor: 'transparent !important'\n  },\n  table: {\n    backgroundColor: 'transparent'\n  }\n});\nexport default function NestedGrid(_ref) {\n  _s();\n\n  let {\n    columns = [],\n    data = [],\n    options = defaultOptions,\n    onSortChange = null,\n    RenderNestedContent,\n    title = ''\n  } = _ref;\n  const tableClasses = useTableStyles();\n  const {\n    gridColumns,\n    handleColumnVisibilityChange\n  } = useGridColumn(columns);\n  const [expandedRows, setExpandedRows] = useState([]);\n  const nestedOptions = { ...options,\n    expandableRows: !!RenderNestedContent,\n    rowsExpanded: expandedRows,\n    ...(RenderNestedContent && {\n      renderExpandableRow: rowData => /*#__PURE__*/_jsxDEV(\"tr\", {\n        children: /*#__PURE__*/_jsxDEV(\"td\", {\n          colSpan: gridColumns.length + 1,\n          children: /*#__PURE__*/_jsxDEV(RenderNestedContent, {\n            row: rowData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this)\n    }),\n    onColumnViewChange: (changedColumn, action) => {\n      handleColumnVisibilityChange(changedColumn, action === 'add');\n    },\n    onTableChange: (action, tableState) => {\n      if (action === 'sort' && onSortChange) {\n        const {\n          name,\n          direction\n        } = tableState.sortOrder;\n        onSortChange(name, direction);\n      }\n    },\n    onRowExpansionChange: (curExpanded, allExpanded) => setExpandedRows(allExpanded.map(row => row.dataIndex))\n  };\n  return /*#__PURE__*/_jsxDEV(MUIDataTable, {\n    classes: {\n      root: tableClasses.tableRoot\n    },\n    columns: gridColumns,\n    data: data,\n    options: nestedOptions,\n    title: title\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 9\n  }, this);\n}\n\n_s(NestedGrid, \"LPcuYJdgAbwxsUNkzMu3Q+NIFCI=\", false, function () {\n  return [useTableStyles, useGridColumn];\n});\n\n_c = NestedGrid;\n\nvar _c;\n\n$RefreshReg$(_c, \"NestedGrid\");", "map": {"version": 3, "sources": ["D:/www/SWF/sian-web-frontend/src/ui-component/grid/NestedGrid.jsx"], "names": ["React", "useState", "MUIDataTable", "makeStyles", "CustomOptionsMUIDtb", "useGridColumn", "defaultOptions", "search", "download", "print", "sort", "viewColumns", "filter", "filterType", "responsive", "fixedHeader", "fixedSelectColumn", "textLabels", "jumpToPage", "resizableColumns", "draggableColumns", "enabled", "selectableRows", "selectableRowsOnClick", "pagination", "confirmFilters", "rowHover", "toolbar", "setTableProps", "size", "useTableStyles", "tableRoot", "border", "boxShadow", "backgroundColor", "paper", "table", "NestedGrid", "columns", "data", "options", "onSortChange", "RenderNestedContent", "title", "tableClasses", "gridColumns", "handleColumnVisibilityChange", "expandedRows", "setExpandedRows", "nestedOptions", "expandableRows", "rowsExpanded", "renderExpandableRow", "rowData", "length", "onColumnViewChange", "changedColumn", "action", "onTableChange", "tableState", "name", "direction", "sortOrder", "onRowExpansionChange", "curExpanded", "allExpanded", "map", "row", "dataIndex", "root"], "mappings": ";;;AAAA,OAAOA,KAAP,IAAgBC,QAAhB,QAAgC,OAAhC;AACA,OAAOC,YAAP,MAAyB,gBAAzB;AACA,SAASC,UAAT,QAA2B,aAA3B;AACA,OAAOC,mBAAP,MAAgC,wDAAhC;AACA,OAAOC,aAAP,MAA0B,qBAA1B;;AAEA,MAAMC,cAAc,GAAG;AACnBC,EAAAA,MAAM,EAAE,IADW;AAEnBC,EAAAA,QAAQ,EAAE,KAFS;AAGnBC,EAAAA,KAAK,EAAE,KAHY;AAInBC,EAAAA,IAAI,EAAE,IAJa;AAKnBC,EAAAA,WAAW,EAAE,IALM;AAMnBC,EAAAA,MAAM,EAAE,IANW;AAOnBC,EAAAA,UAAU,EAAE,aAPO;AAQnBC,EAAAA,UAAU,EAAE,UARO;AASnBC,EAAAA,WAAW,EAAE,IATM;AAUnBC,EAAAA,iBAAiB,EAAE,IAVA;AAWnBC,EAAAA,UAAU,EAAEb,mBAAmB,CAACa,UAXb;AAYnBC,EAAAA,UAAU,EAAE,IAZO;AAanBC,EAAAA,gBAAgB,EAAE,KAbC;AAcnBC,EAAAA,gBAAgB,EAAE;AACdC,IAAAA,OAAO,EAAE;AADK,GAdC;AAiBnBC,EAAAA,cAAc,EAAE,MAjBG;AAkBnBC,EAAAA,qBAAqB,EAAE,KAlBJ;AAmBnBC,EAAAA,UAAU,EAAE,KAnBO;AAoBnBC,EAAAA,cAAc,EAAE,KApBG;AAqBnBC,EAAAA,QAAQ,EAAE,IArBS;AAsBnBC,EAAAA,OAAO,EAAE,KAtBU;AAuBnBC,EAAAA,aAAa,EAAE,OAAO;AAClBC,IAAAA,IAAI,EAAE;AADY,GAAP;AAvBI,CAAvB;AA4BA,MAAMC,cAAc,GAAG3B,UAAU,CAAC;AAC9B4B,EAAAA,SAAS,EAAE;AACPC,IAAAA,MAAM,EAAE,MADD;AAEPC,IAAAA,SAAS,EAAE,MAFJ;AAGPC,IAAAA,eAAe,EAAE;AAHV,GADmB;AAM9BC,EAAAA,KAAK,EAAE;AACHD,IAAAA,eAAe,EAAE;AADd,GANuB;AAS9BE,EAAAA,KAAK,EAAE;AACHF,IAAAA,eAAe,EAAE;AADd;AATuB,CAAD,CAAjC;AAcA,eAAe,SAASG,UAAT,OAOZ;AAAA;;AAAA,MAPgC;AAC/BC,IAAAA,OAAO,GAAG,EADqB;AAE/BC,IAAAA,IAAI,GAAG,EAFwB;AAG/BC,IAAAA,OAAO,GAAGlC,cAHqB;AAI/BmC,IAAAA,YAAY,GAAG,IAJgB;AAK/BC,IAAAA,mBAL+B;AAM/BC,IAAAA,KAAK,GAAG;AANuB,GAOhC;AACC,QAAMC,YAAY,GAAGd,cAAc,EAAnC;AACA,QAAM;AAAEe,IAAAA,WAAF;AAAeC,IAAAA;AAAf,MAAgDzC,aAAa,CAACiC,OAAD,CAAnE;AACA,QAAM,CAACS,YAAD,EAAeC,eAAf,IAAkC/C,QAAQ,CAAC,EAAD,CAAhD;AAEA,QAAMgD,aAAa,GAAG,EAClB,GAAGT,OADe;AAElBU,IAAAA,cAAc,EAAE,CAAC,CAACR,mBAFA;AAGlBS,IAAAA,YAAY,EAAEJ,YAHI;AAIlB,QAAIL,mBAAmB,IAAI;AACvBU,MAAAA,mBAAmB,EAAGC,OAAD,iBACjB;AAAA,+BACI;AAAI,UAAA,OAAO,EAAER,WAAW,CAACS,MAAZ,GAAqB,CAAlC;AAAA,iCACI,QAAC,mBAAD;AAAqB,YAAA,GAAG,EAAED;AAA1B;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA;AAFmB,KAA3B,CAJkB;AAalBE,IAAAA,kBAAkB,EAAE,CAACC,aAAD,EAAgBC,MAAhB,KAA2B;AAC3CX,MAAAA,4BAA4B,CAACU,aAAD,EAAgBC,MAAM,KAAK,KAA3B,CAA5B;AACH,KAfiB;AAgBlBC,IAAAA,aAAa,EAAE,CAACD,MAAD,EAASE,UAAT,KAAwB;AACnC,UAAIF,MAAM,KAAK,MAAX,IAAqBhB,YAAzB,EAAuC;AACnC,cAAM;AAAEmB,UAAAA,IAAF;AAAQC,UAAAA;AAAR,YAAsBF,UAAU,CAACG,SAAvC;AACArB,QAAAA,YAAY,CAACmB,IAAD,EAAOC,SAAP,CAAZ;AACH;AACJ,KArBiB;AAsBlBE,IAAAA,oBAAoB,EAAE,CAACC,WAAD,EAAcC,WAAd,KAA8BjB,eAAe,CAACiB,WAAW,CAACC,GAAZ,CAAiBC,GAAD,IAASA,GAAG,CAACC,SAA7B,CAAD;AAtBjD,GAAtB;AAyBA,sBACI,QAAC,YAAD;AAAc,IAAA,OAAO,EAAE;AAAEC,MAAAA,IAAI,EAAEzB,YAAY,CAACb;AAArB,KAAvB;AAAyD,IAAA,OAAO,EAAEc,WAAlE;AAA+E,IAAA,IAAI,EAAEN,IAArF;AAA2F,IAAA,OAAO,EAAEU,aAApG;AAAmH,IAAA,KAAK,EAAEN;AAA1H;AAAA;AAAA;AAAA;AAAA,UADJ;AAGH;;GAxCuBN,U;UAQCP,c,EACiCzB,a;;;KATlCgC,U", "sourcesContent": ["import React, { useState } from 'react';\r\nimport MUIDataTable from 'mui-datatables';\r\nimport { makeStyles } from '@mui/styles';\r\nimport CustomOptionsMUIDtb from 'assets/customization/mui-datatable/CustomOptionsMUIDtb';\r\nimport useGridColumn from 'hooks/useGridColumn';\r\n\r\nconst defaultOptions = {\r\n    search: true,\r\n    download: false,\r\n    print: false,\r\n    sort: true,\r\n    viewColumns: true,\r\n    filter: true,\r\n    filterType: 'multiselect',\r\n    responsive: 'vertical',\r\n    fixedHeader: true,\r\n    fixedSelectColumn: true,\r\n    textLabels: CustomOptionsMUIDtb.textLabels,\r\n    jumpToPage: true,\r\n    resizableColumns: false,\r\n    draggableColumns: {\r\n        enabled: true\r\n    },\r\n    selectableRows: 'none',\r\n    selectableRowsOnClick: false,\r\n    pagination: false,\r\n    confirmFilters: false,\r\n    rowHover: true,\r\n    toolbar: false,\r\n    setTableProps: () => ({\r\n        size: 'small'\r\n    })\r\n};\r\n\r\nconst useTableStyles = makeStyles({\r\n    tableRoot: {\r\n        border: 'none',\r\n        boxShadow: 'none',\r\n        backgroundColor: 'transparent'\r\n    },\r\n    paper: {\r\n        backgroundColor: 'transparent !important'\r\n    },\r\n    table: {\r\n        backgroundColor: 'transparent'\r\n    }\r\n});\r\n\r\nexport default function NestedGrid({\r\n    columns = [],\r\n    data = [],\r\n    options = defaultOptions,\r\n    onSortChange = null,\r\n    RenderNestedContent,\r\n    title = ''\r\n}) {\r\n    const tableClasses = useTableStyles();\r\n    const { gridColumns, handleColumnVisibilityChange } = useGridColumn(columns);\r\n    const [expandedRows, setExpandedRows] = useState([]);\r\n\r\n    const nestedOptions = {\r\n        ...options,\r\n        expandableRows: !!RenderNestedContent,\r\n        rowsExpanded: expandedRows,\r\n        ...(RenderNestedContent && {\r\n            renderExpandableRow: (rowData) => (\r\n                <tr>\r\n                    <td colSpan={gridColumns.length + 1}>\r\n                        <RenderNestedContent row={rowData} />\r\n                    </td>\r\n                </tr>\r\n            )\r\n        }),\r\n        onColumnViewChange: (changedColumn, action) => {\r\n            handleColumnVisibilityChange(changedColumn, action === 'add');\r\n        },\r\n        onTableChange: (action, tableState) => {\r\n            if (action === 'sort' && onSortChange) {\r\n                const { name, direction } = tableState.sortOrder;\r\n                onSortChange(name, direction);\r\n            }\r\n        },\r\n        onRowExpansionChange: (curExpanded, allExpanded) => setExpandedRows(allExpanded.map((row) => row.dataIndex))\r\n    };\r\n\r\n    return (\r\n        <MUIDataTable classes={{ root: tableClasses.tableRoot }} columns={gridColumns} data={data} options={nestedOptions} title={title} />\r\n    );\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}