<?php

namespace App\Http\Controllers\Api\V1\Line;

use App\Models\Division;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


use App\Models\Line;


class LineController extends Controller {
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getItems(Request $request) {
        try {
            $divisionFoodId = Division::where('status', 1)
                ->where('division_name', '=', Division::DIVISION_FOOD_NAME)
                ->value('division_id');

            $lineServicesId = Line::where('status', 1)
                ->where('line_name', '=', Line::LINE_NAME_SERVICES)
                ->value('line_id');


            $query = Line::where("status", 1)
                    ->where('division_id', '!=', $divisionFoodId)
                    ->where('line_id', '!=', $lineServicesId);

            if ($request->has('keyword')) {
                $keyword = $request->input('keyword');
                $query->where('line_name', 'like', '%' . $keyword . '%');
            }

            if ($request->has('division')) {
                $division = $request->input('division');
                $arrayDivision = explode(",", $division);
                $query->whereIn("division_id", $arrayDivision);
            }

            if ($request->has('line')) {
                $lineSelected = $request->input('line');
                $arrayLine = explode(",", $lineSelected);
                $query->whereNotIn("line_id", $arrayLine);
            }

            if ($request->has('limit')) {
                $limit = $request->input('limit');
                $query->limit($limit);
            } else {
                $query->limit(10);
            }


            $data = $query->get();

            return response()->json([
                'success' => true,
                'data' => $data,

            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }

    public function getFoodItems(Request $request) {
        try {
            $divisionFoodId = Division::where('status', 1)
                ->where('division_name', '=', Division::DIVISION_FOOD_NAME)
                ->value('division_id');

            $lineServicesId = Line::where('status', 1)
                ->where('line_name', '=', Line::LINE_NAME_SERVICES)
                ->value('line_id');

            $lines_query = DB::table('line')
                ->select(DB::raw("CONCAT('M-', line_id) AS line_id"), 'line_name')
                ->where('division_id', '=', $divisionFoodId)
                ->where('line_id','!=', $lineServicesId)
                ->where('status', 1);

            $categories_query = DB::table('service_category')
                ->select(DB::raw("CONCAT('S-', service_category_id) AS line_id"), DB::raw('service_category_name AS line_name'))
                ->where('status', 1);

            if ($request->has('keyword')) {
                $keyword = $request->input('keyword');
                $lines_query->where('line_name', 'like', '%' . $keyword . '%');
                $categories_query->where('service_category_name', 'like', '%' . $keyword . '%');
            }

            if ($request->has('division')) {
                $division = $request->input('division');
                $arrayDivision = explode(",", $division);
                $lines_query->whereIn("division_id", $arrayDivision);
            }

            if ($request->has('line')) {
                $lineSelected = $request->input('line');
                $arrayLine = explode(",", $lineSelected);
                $lines_query->whereNotIn("line_id", $arrayLine);
            }

            $union = $lines_query->unionAll($categories_query);

            $query = DB::table(DB::raw("({$union->toSql()}) as combined"))
                ->mergeBindings($union);

            $limit = $request->input('limit', 10);
            $data = $query->limit($limit)->get();

            return response()->json([
                'success' => true,
                'data' => $data,
            ]);

        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }

}
