<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Controller
 * 
 * @property string $module
 * @property string $controller
 * @property string $module_parent
 * @property string $controller_parent
 * @property string $title
 * @property bool $status
 * 
 * @property Collection|Action[] $actions
 *
 * @package App\Models
 */
class Controller extends Model
{
	protected $table = 'controller';
	public $incrementing = false;
	public $timestamps = false;

	protected $fillable = [
		'title',
		'status'
	];

	protected $casts = [
		'status' => 'bool'
	];

	public function module()
	{
		return $this->belongsTo(Module::class, 'module');
	}

	public function actions()
	{
		return $this->hasMany(Action::class, 'module');
	}

	public function controller()
	{
		return $this->belongsTo(Controller::class, 'module_parent')
					->where('controller.module', '=', 'controller.module_parent')
					->where('controller.controller', '=', 'controller.controller_parent');
	}
}
