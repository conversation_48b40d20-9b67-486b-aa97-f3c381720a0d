import { useState } from 'react';
import Grid from '@mui/material/Grid';
import useLoading from 'hooks/useLoading';
import { getProvidersPromise } from 'services/personService';
import Autocomplete from 'ui-component/inputs/Autocomplete';
import Modal from 'ui-component/modal/Modal';
import { Box } from '@mui/material';

export function SelectProvider({ setValue, modal = null }) {
    const [options, setOptions] = useState([]);
    const [loading, startLoading, endLoading] = useLoading(false);

    const loadOptions = (keyword) => {
        startLoading();
        getProvidersPromise({ keyword })
            .then((response) => {
                if (response.success) {
                    setOptions(response.data);
                }
            })
            .finally(() => endLoading());
    };

    const content = (
        <Box sx={{ p: 2 }}>
            <Autocomplete
                label=""
                onChange={(value) => setValue(value)}
                options={options}
                name="person"
                loading={loading}
                onLoad={loadOptions}
                fullWidth
                unique
            />
        </Box>
    );

    return modal ? (
        <Modal isOpen={modal.isOpen} handleClose={modal.handleClose} maxWidth="sm" title="Seleccionar Proveedor">
            {content}
        </Modal>
    ) : (
        <Grid container spacing={2}>
            <Grid item xs={12}>
                {content}
            </Grid>
        </Grid>
    );
}
