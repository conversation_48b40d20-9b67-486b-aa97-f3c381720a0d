<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AssistanceSummaryDate
 * 
 * @property int $assistance_summary_employee_id
 * @property Carbon $date
 * @property float $work_hours
 * @property float $extrahours
 * @property float $night_hours
 * @property float $holiday_hours
 * @property int $lateness
 * @property bool $absence
 * @property int|null $type_absence
 * 
 * @property AssistanceSummaryEmployee $assistance_summary_employee
 *
 * @package App\Models
 */
class AssistanceSummaryDate extends Model
{
	protected $table = 'assistance_summary_date';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'assistance_summary_employee_id' => 'int',
		'work_hours' => 'float',
		'extrahours' => 'float',
		'night_hours' => 'float',
		'holiday_hours' => 'float',
		'lateness' => 'int',
		'absence' => 'bool',
		'type_absence' => 'int'
	];

	protected $dates = [
		'date'
	];

	protected $fillable = [
		'work_hours',
		'extrahours',
		'night_hours',
		'holiday_hours',
		'lateness',
		'absence',
		'type_absence'
	];

	public function assistance_summary_employee()
	{
		return $this->belongsTo(AssistanceSummaryEmployee::class);
	}
}
