<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Changelog
 * 
 * @property int $changelog_id
 * @property string|null $log_code
 * @property string $owner
 * @property int $owner_id
 * @property string $owner_pk
 * @property string|null $to_string
 * @property string $action
 * @property string $type
 * @property Carbon $date
 * @property string $ws
 * @property string $route
 * @property int $person_id
 * @property string $reason_code
 * @property string $reason
 * @property bool $notified
 * @property bool $last_by_object_action
 * @property bool $last_by_object
 * 
 * @property Person $person
 *
 * @package App\Models
 */
class Changelog extends Model
{
	protected $table = 'changelog';
	protected $primaryKey = 'changelog_id';
	public $timestamps = false;

	protected $casts = [
		'owner_id' => 'int',
		'person_id' => 'int',
		'notified' => 'bool',
		'last_by_object_action' => 'bool',
		'last_by_object' => 'bool'
	];

	protected $dates = [
		'date'
	];

	protected $fillable = [
		'log_code',
		'owner',
		'owner_id',
		'owner_pk',
		'to_string',
		'action',
		'type',
		'date',
		'ws',
		'route',
		'person_id',
		'reason_code',
		'reason',
		'notified',
		'last_by_object_action',
		'last_by_object'
	];

	public function person()
	{
		return $this->belongsTo(Person::class);
	}

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}
}
