<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class NetworkingActivityType
 * 
 * @property int $networking_activity_type_id
 * @property string $networking_activity_type_name
 * @property bool $status
 * 
 * @property Collection|NetworkingActivity[] $networking_activities
 * @property Collection|NetworkingActivityTemplate[] $networking_activity_templates
 *
 * @package App\Models
 */
class NetworkingActivityType extends Model
{
	protected $table = 'networking_activity_type';
	protected $primaryKey = 'networking_activity_type_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool'
	];

	protected $fillable = [
		'networking_activity_type_name',
		'status'
	];

	public function networking_activities()
	{
		return $this->hasMany(NetworkingActivity::class);
	}

	public function networking_activity_templates()
	{
		return $this->hasMany(NetworkingActivityTemplate::class);
	}
}
