<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class MobilityPerson
 * 
 * @property int $movement_id
 * @property int $person_id
 * @property float $amount_pen
 * @property float $amount_usd
 * @property int|null $combination_id
 * 
 * @property MobilityMovement $mobility_movement
 * @property Person $person
 * @property Combination|null $combination
 * @property Collection|MobilityPersonItem[] $mobility_person_items
 *
 * @package App\Models
 */
class MobilityPerson extends Model
{
	protected $table = 'mobility_person';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'person_id' => 'int',
		'amount_pen' => 'float',
		'amount_usd' => 'float',
		'combination_id' => 'int'
	];

	protected $fillable = [
		'amount_pen',
		'amount_usd',
		'combination_id'
	];

	public function mobility_movement()
	{
		return $this->belongsTo(MobilityMovement::class, 'movement_id');
	}

	public function person()
	{
		return $this->belongsTo(Person::class);
	}

	public function combination()
	{
		return $this->belongsTo(Combination::class);
	}

	public function mobility_person_items()
	{
		return $this->hasMany(MobilityPersonItem::class, 'movement_id');
	}
}
