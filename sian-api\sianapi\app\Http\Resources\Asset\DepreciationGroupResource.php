<?php

namespace App\Http\Resources\Asset;

use App\Http\Resources\Accounting\SimpleAccountResource;
use App\Http\Resources\Common\SimpleMultitableResource;
use Illuminate\Http\Resources\Json\JsonResource;

class DepreciationGroupResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->depreciation_group_id,
            'name' => $this->depreciation_group_name,
            'status' => $this->status,
            'observation' => $this->observation,
            'depreciationMethod' => new SimpleMultitableResource($this->depreciationMethod),
            'accountDepreciation' => new SimpleAccountResource($this->accountDepreciation),
            'accountCost' => new SimpleAccountResource($this->accountCost),
            'details' => DepreciationGroupDetailResource::collection($this->depreciationGroupDetails)
        ];
    }
}
