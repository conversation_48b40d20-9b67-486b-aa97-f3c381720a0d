<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ShoppingCart
 * 
 * @property int $shopping_cart_id
 * @property string $currency
 * @property Carbon $register_date
 * @property float $total
 * @property int $item_count
 * 
 * @property Collection|ShoppingCartItem[] $shopping_cart_items
 * @property Collection|WebUser[] $web_users
 *
 * @package App\Models
 */
class ShoppingCart extends Model
{
	protected $table = 'shopping_cart';
	protected $primaryKey = 'shopping_cart_id';
	public $timestamps = false;

	protected $casts = [
		'total' => 'float',
		'item_count' => 'int'
	];

	protected $dates = [
		'register_date'
	];

	protected $fillable = [
		'currency',
		'register_date',
		'total',
		'item_count'
	];

	public function shopping_cart_items()
	{
		return $this->hasMany(ShoppingCartItem::class);
	}

	public function web_users()
	{
		return $this->hasMany(WebUser::class);
	}
}
