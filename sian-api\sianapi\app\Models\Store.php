<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Store
 * 
 * @property int $store_id
 * @property string $store_name
 * @property string $alias
 * @property bool $status
 * @property bool $physical
 * @property bool $frontend
 * @property int|null $business_unit_id
 * @property bool $print
 * @property bool $show_without_warehouses
 * @property bool $locked
 * @property bool $web_enabled
 * @property string|null $transaction_code
 * 
 * @property BusinessUnit|null $business_unit
 * @property Collection|ActivePromotion[] $active_promotions
 * @property Collection|ApiUserVariant[] $api_user_variants
 * @property Collection|Cashbox[] $cashboxes
 * @property Collection|CommercialGoal[] $commercial_goals
 * @property Collection|DocumentSerie[] $document_series
 * @property Collection|EntryGroup[] $entry_groups
 * @property Collection|Movement[] $movements
 * @property Collection|Station[] $stations
 * @property Collection|Warehouse[] $warehouses
 *
 * @package App\Models
 */
class Store extends Model
{
	protected $table = 'store';
	protected $primaryKey = 'store_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'physical' => 'bool',
		'frontend' => 'bool',
		'business_unit_id' => 'int',
		'print' => 'bool',
		'show_without_warehouses' => 'bool',
		'locked' => 'bool',
		'web_enabled' => 'bool'
	];

	protected $fillable = [
		'store_name',
		'alias',
		'status',
		'physical',
		'frontend',
		'business_unit_id',
		'print',
		'show_without_warehouses',
		'locked',
		'web_enabled',
		'transaction_code'
	];

	public function business_unit()
	{
		return $this->belongsTo(BusinessUnit::class);
	}

	public function active_promotions()
	{
		return $this->hasMany(ActivePromotion::class);
	}

	public function api_user_variants()
	{
		return $this->hasMany(ApiUserVariant::class);
	}

	public function cashboxes()
	{
		return $this->hasMany(Cashbox::class);
	}

	public function commercial_goals()
	{
		return $this->hasMany(CommercialGoal::class);
	}

	public function document_series()
	{
		return $this->hasMany(DocumentSerie::class);
	}

	public function entry_groups()
	{
		return $this->hasMany(EntryGroup::class);
	}

	public function movements()
	{
		return $this->hasMany(Movement::class);
	}

	public function stations()
	{
		return $this->hasMany(Station::class);
	}

	public function warehouses()
	{
		return $this->hasMany(Warehouse::class);
	}
}
