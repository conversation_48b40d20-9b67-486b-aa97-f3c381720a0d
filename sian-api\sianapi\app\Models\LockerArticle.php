<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class LockerArticle
 * 
 * @property int $locker_article_id
 * @property string $article_code
 * @property string $article_description
 * @property float $quantity
 * @property bool $allow_decimals
 * @property string|null $article_img
 * @property int $locker_box_request_id
 * @property int $order
 * 
 * @property LockerBoxRequest $locker_box_request
 *
 * @package App\Models
 */
class LockerArticle extends Model
{
	protected $table = 'locker_article';
	protected $primaryKey = 'locker_article_id';
	public $timestamps = false;

	protected $casts = [
		'quantity' => 'float',
		'allow_decimals' => 'bool',
		'locker_box_request_id' => 'int',
		'order' => 'int'
	];

	protected $fillable = [
		'article_code',
		'article_description',
		'quantity',
		'allow_decimals',
		'article_img',
		'locker_box_request_id',
		'order'
	];

	public function locker_box_request()
	{
		return $this->belongsTo(LockerBoxRequest::class);
	}
}
