<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class OwnerPair
 *
 * @property int $pair_id
 * @property string $pair_code
 * @property string $type
 * @property string $owner
 * @property int $owner_id
 * @property string|null $value
 * @property bool $default
 * @property bool $final
 * @property int|null $parent_id
 * @property int $order
 *
 * @property OwnerPair|null $owner_pair
 * @property Collection|OwnerPair[] $owner_pairs
 *
 * @package App\Models
 */
class OwnerPair extends Model
{
    const OWNER = 'OwnerPair';
    //Types
    const TYPE_API_USER_ENABLED_SCENARIO = 'ApiUserEnabledScenario';
    const TYPE_WEB_USER_ENABLED_SCENARIO = 'WebUserEnabledScenario';
    const TYPE_SCENARIO_DOCUMENT = 'ScenarioDocument';
    const TYPE_MERCHANDISE_FEATURE = 'MerchandiseFeature';
    const TYPE_SCENARIO_OPERATION = 'ScenarioOperation';
    const TYPE_SUBLINE_FEATURE = 'SublineFeature';
    const TYPE_RELATED_SUBLINE = 'RelatedSubline';
    const TYPE_USER_WAREHOUSE = 'UserWarehouse';
    const TYPE_USER_CASHBOX = 'UserCashbox';
    const TYPE_USER_STORE = 'UserStore';
    const TYPE_USER_BUSINESS_UNIT = 'UserBusinessUnit';
    const TYPE_USER_CONFIRMATION = 'UserConfirmation';
    const TYPE_BUSINESS_UNIT_DIVISION = 'BusinessUnitDivision';
    const TYPE_WEB_USER_BILLING_ADDRESS = 'WebUserBillingAddress';
    const TYPE_PRODUCT_RATING = 'ProductRating';
    const TYPE_PROMOTION_STORE = 'PromotionStore';
    const TYPE_JOB_OFFER_WEB_USER = 'JobOfferWebUser';
    const TYPE_ACCOUNT_DESTINY = 'AccountDestiny';
    const TYPE_CROSS_REPORT_OWNER = 'CrossReportOwner';
    const TYPE_OWNER_GROUP_OWNER = 'OwnerGroupOwner';
    const TYPE_INVENTORY_DIVISION = 'InventoryDivision';
    const TYPE_INVENTORY_LINE = 'InventoryLine';
    const TYPE_INVENTORY_SUBLINE = 'InventorySubline';
    const TYPE_INVENTORY_MARK = 'InventoryMark';
    const TYPE_INVENTORY_MERCHANDISE = 'InventoryMerchandise';
    const TYPE_MOVEMENT_SHIPPER = 'MovementShipper';
    const TYPE_MOVEMENT_TRANSPORT_UNIT = 'MovementTransportUnit';
    const TYPE_PRICE_STORE = 'PriceStore';
    const TYPE_USER_EXCLUDE_OPERATION = 'UserExcludeOperation';
    const TYPE_USER_GROUP_USER = 'UserGroupUser';
    const TYPE_STOCK_STORE_WAREHOUSE = 'StockStoreWarehouse';
    const TYPE_USER_PAY_ACCOUNT = 'UserPayAccount';
    const TYPE_USER_PAY_SCENARIO = 'UserPayScenario';

	protected $table = 'owner_pair';
	protected $primaryKey = 'pair_id';
	public $timestamps = false;

	protected $casts = [
		'owner_id' => 'int',
		'default' => 'bool',
		'final' => 'bool',
		'parent_id' => 'int',
		'order' => 'int'
	];

	protected $fillable = [
		'pair_code',
		'type',
		'owner',
		'owner_id',
		'value',
		'default',
		'final',
		'parent_id',
		'order'
	];

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}

	public function owner_pair()
	{
		return $this->belongsTo(OwnerPair::class, 'parent_id');
	}

	public function owner_pairs()
	{
		return $this->hasMany(OwnerPair::class, 'parent_id');
	}
}
