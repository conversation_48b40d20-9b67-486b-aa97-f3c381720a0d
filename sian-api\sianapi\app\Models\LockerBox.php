<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class LockerBox
 * 
 * @property int $locker_box_id
 * @property int $box_number
 * @property float $height
 * @property float $width
 * @property float $depth
 * @property bool $status
 * @property bool $available
 * @property string|null $error_code
 * @property Carbon|null $last_sync
 * @property int $locker_id
 * @property int|null $last_locker_box_request_id
 * 
 * @property Locker $locker
 * @property LockerBoxRequest|null $locker_box_request
 * @property Collection|LockerBoxRequest[] $locker_box_requests
 *
 * @package App\Models
 */
class LockerBox extends Model
{
	protected $table = 'locker_box';
	protected $primaryKey = 'locker_box_id';
	public $timestamps = false;

	protected $casts = [
		'box_number' => 'int',
		'height' => 'float',
		'width' => 'float',
		'depth' => 'float',
		'status' => 'bool',
		'available' => 'bool',
		'locker_id' => 'int',
		'last_locker_box_request_id' => 'int'
	];

	protected $dates = [
		'last_sync'
	];

	protected $fillable = [
		'box_number',
		'height',
		'width',
		'depth',
		'status',
		'available',
		'error_code',
		'last_sync',
		'locker_id',
		'last_locker_box_request_id'
	];

	public function locker()
	{
		return $this->belongsTo(Locker::class);
	}

	public function locker_box_request()
	{
		return $this->belongsTo(LockerBoxRequest::class, 'last_locker_box_request_id');
	}

	public function locker_box_requests()
	{
		return $this->hasMany(LockerBoxRequest::class);
	}
}
