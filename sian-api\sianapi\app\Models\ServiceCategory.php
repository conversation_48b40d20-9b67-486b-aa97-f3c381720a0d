<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Service
 *
 * @property int $service_category_id
 * @property string $a
 * @property string $alias
 * @property bool $status
 * @property int $order
 *
 * @property Product $product
 * @property mixed $name
 *
 * @package App\Models
 */
class ServiceCategory extends Model {
    protected $table = 'service_category';
    protected $primaryKey = 'service_category_id';
    public $timestamps = false;

    protected $casts = [
        'service_category_id' => 'int',
        'status' => 'bool',
        'order' => 'int'
    ];

}
