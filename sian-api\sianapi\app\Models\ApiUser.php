<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ApiUser
 * 
 * @property int $api_user_id
 * @property string $api_user_code
 * @property string $username
 * @property string $password
 * @property bool $status
 * @property bool $manual_check
 * @property bool $realtime
 * @property string $type
 * @property int $aux_person_id
 * @property int|null $person_id
 * @property int|null $seller_id
 * @property string|null $product_url_pattern
 * @property int|null $pin_duration
 * @property string|null $pay_url_pattern
 * @property int $variant_count
 * @property bool $require_validation
 * 
 * @property Person|null $person
 * @property Seller|null $seller
 * @property Collection|ApiUserVariant[] $api_user_variants
 * @property Collection|LockerRequest[] $locker_requests
 * @property Collection|PaymentLink[] $payment_links
 *
 * @package App\Models
 */
class ApiUser extends Model
{
	protected $table = 'api_user';
	protected $primaryKey = 'api_user_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'manual_check' => 'bool',
		'realtime' => 'bool',
		'aux_person_id' => 'int',
		'person_id' => 'int',
		'seller_id' => 'int',
		'pin_duration' => 'int',
		'variant_count' => 'int',
		'require_validation' => 'bool'
	];

	protected $hidden = [
		'password'
	];

	protected $fillable = [
		'api_user_code',
		'username',
		'password',
		'status',
		'manual_check',
		'realtime',
		'type',
		'aux_person_id',
		'person_id',
		'seller_id',
		'product_url_pattern',
		'pin_duration',
		'pay_url_pattern',
		'variant_count',
		'require_validation'
	];

	public function person()
	{
		return $this->belongsTo(Person::class);
	}

	public function seller()
	{
		return $this->belongsTo(Seller::class);
	}

	public function api_user_variants()
	{
		return $this->hasMany(ApiUserVariant::class);
	}

	public function locker_requests()
	{
		return $this->hasMany(LockerRequest::class);
	}

	public function payment_links()
	{
		return $this->hasMany(PaymentLink::class);
	}
}
