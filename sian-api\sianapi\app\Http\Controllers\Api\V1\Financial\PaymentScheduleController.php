<?php

namespace App\Http\Controllers\Api\V1\Financial;


use App\Http\Controllers\SianController;
use App\Models\ExchangeRate;
use App\Models\Fake\Currency;
use App\Models\GlobalVar;
use App\Models\Owner;
use App\Models\PaymentScheduleDate;
use App\Models\PaymentScheduleDetail;
use App\Models\Person;
use App\Models\ProductList;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use App\Models\PaymentSchedule;
use Illuminate\Support\Facades\Log;


class PaymentScheduleController extends Controller {

    /**
     *
     *
     * @return \Illuminate\Http\Response
     */

    public function index(Request $request) {
        $validator = Validator::make($request->all(), [
            "page" => "required|integer",
            "pageSize" => "required|integer",
            "status" => 'required|string',
            "startDateEmission" => 'date',
            "endDateEmission" => " date | after_or_equal:startDateEmission | required_with:startDateEmission",
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()
            ], 400);
        }
        try {
            $page = intval($request->query('page'));
            $pageSize = intval($request->query('pageSize'));

            $startIndex = ($page - 1) * $pageSize;

            $query = DB::table('payment_schedule as PS')
                ->select(
                    'PS.payment_schedule_id as paymentScheduleId',
                    'PS.description as description',
                    DB::raw("REPLACE(P.person_name, ',', ' ') as personName"),
                    'PS.emission_date as emissionDate',
                    'PS.updated_at as updatedDate',
                    DB::raw('COUNT(DISTINCT CASE WHEN PSDT.state IN ("OPEN", "CLOSED") THEN PSD.payment_schedule_detail_id END) AS totalDocuments'),
                    DB::raw('SUM(CASE WHEN M.currency = "usd" AND PSDT.state IN ("OPEN", "CLOSED") THEN PSDT.amount ELSE 0 END) AS amountUSD'),
                    DB::raw('SUM(CASE WHEN M.currency = "pen" AND PSDT.state IN ("OPEN", "CLOSED") THEN PSDT.amount ELSE 0 END) AS amountPEN'),
                    DB::raw('CASE
                        WHEN COUNT(DISTINCT PSDT.state) = 1 THEN MAX(PSDT.state)
                        WHEN SUM(CASE WHEN PSDT.state = "OPEN" THEN 1 ELSE 0 END) > 0 AND SUM(CASE WHEN PSDT.state = "' . PaymentScheduleDate::STATE_CLOSED . '" THEN 1 ELSE 0 END) > 0 THEN "PARTIAL_OPEN"
                        WHEN SUM(CASE WHEN PSDT.state = "OPEN" THEN 1 ELSE 0 END) > 0 AND SUM(CASE WHEN PSDT.state = "' . PaymentScheduleDate::STATE_CANCELED . '" THEN 1 ELSE 0 END) > 0 THEN "OPEN"
                        WHEN SUM(CASE WHEN PSDT.state = "CLOSED" THEN 1 ELSE 0 END) > 0 AND SUM(CASE WHEN PSDT.state = "' . PaymentScheduleDate::STATE_CANCELED . '" THEN 1 ELSE 0 END) > 0 THEN "CLOSED"
                        WHEN SUM(CASE WHEN PSDT.state IN ("'. PaymentScheduleDate::STATE_OPEN . '", "' . PaymentScheduleDate::STATE_CLOSED . '", "' . PaymentScheduleDate::STATE_CANCELED . '") THEN 1 ELSE 0 END) > 0 THEN "PARTIAL_OPEN"
                        ELSE "PARTIAL_OPEN"
                    END AS status')
                )
                ->join('person as P', 'P.person_id', '=', 'PS.person_id')
                ->join('payment_schedule_detail as PSD', 'PSD.payment_schedule_id', '=', 'PS.payment_schedule_id')
                ->join('payment_schedule_date as PSDT', 'PSDT.payment_schedule_detail_id', '=', 'PSD.payment_schedule_detail_id')
                ->join('movement as M', 'M.movement_id', '=', 'PSD.movement_id')
                ->groupBy('PS.payment_schedule_id', 'PS.description', 'P.person_name', 'PS.emission_date', 'PS.updated_at');

            if ($request->has('status')) {
                $statusString = $request->input('status');
                $statusArray = explode(',', $statusString);

                $query->havingRaw('CASE
                        WHEN COUNT(DISTINCT PSDT.state) = 1 THEN MAX(PSDT.state)
                        WHEN SUM(CASE WHEN PSDT.state = "OPEN" THEN 1 ELSE 0 END) > 0 AND SUM(CASE WHEN PSDT.state = "'. PaymentScheduleDate::STATE_CLOSED .'" THEN 1 ELSE 0 END) > 0 THEN "PARTIAL_OPEN"
                        WHEN SUM(CASE WHEN PSDT.state = "OPEN" THEN 1 ELSE 0 END) > 0 AND SUM(CASE WHEN PSDT.state = "'. PaymentScheduleDate::STATE_CANCELED .'" THEN 1 ELSE 0 END) > 0 THEN "OPEN"
                        WHEN SUM(CASE WHEN PSDT.state = "CLOSED" THEN 1 ELSE 0 END) > 0 AND SUM(CASE WHEN PSDT.state = "' . PaymentScheduleDate::STATE_CANCELED . '" THEN 1 ELSE 0 END) > 0 THEN "CLOSED"
                        WHEN SUM(CASE WHEN PSDT.state IN ("'. PaymentScheduleDate::STATE_OPEN .'", "'. PaymentScheduleDate::STATE_CLOSED .'", "' . PaymentScheduleDate::STATE_CANCELED . '") THEN 1 ELSE 0 END) > 0 THEN "PARTIAL_OPEN"
                        ELSE "PARTIAL_OPEN"
                    END IN ("' . implode('","', $statusArray) . '")');
            }


            if ($request->has('startDateEmission') && $request->has('endDateEmission')) {
                $startDate = $request->input('startDateEmission');
                $endDate = $request->input('endDateEmission');
                $query->whereDate('PS.emission_date', '>=', $startDate);
                $query->whereDate('PS.emission_date', '<=', $endDate);
            }

            $totalItems = $query->count();

            $results = $query
                ->orderBy('PS.emission_date', 'desc')
                ->offset($startIndex)
                ->limit($pageSize)
                ->get();

            $response = [
                'success' => true,
                'pagination' => [
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'totalRecords' => $totalItems,
                    'totalPages' => ceil($totalItems / $pageSize)
                ],
                'data' => $results
            ];

            return response()->json($response);

        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function show($paymentScheduleId) {
        $paymentSchedule = PaymentSchedule::find($paymentScheduleId);

        if (!$paymentSchedule) {
            return response()->json([
                'success' => false,
                'error' => 'Payment Schedule not found with this id'
            ]);
        }

        $person = Person::find($paymentSchedule->person_id);

        $result = [
            'success' => true,
            'data' => $paymentSchedule->toArray() + ['person_name' => optional($person)->person_name, 'details' => []]
        ];

        $details = DB::table('payment_schedule_detail as PSD')
            ->join('payment_schedule_date as PSDT', 'PSD.payment_schedule_detail_id', '=', 'PSDT.payment_schedule_detail_id')
            ->whereIn('PSDT.state', [ PaymentScheduleDate::STATE_OPEN, PaymentScheduleDate::STATE_CLOSED ])
            ->where('PSD.payment_schedule_id', $paymentScheduleId)
            ->select([
                'PSD.*',
                'PSDT.payment_schedule_date_id',
                'PSDT.date',
                'PSDT.amount',
                'PSDT.state',
                'PSDT.cashbox_id',
                'PSDT.payment_movement_id',
                'PSDT.updated_at',
                'PSDT.created_at',
            ])
            ->get();

        $uniqueDetails = [];

        foreach ($details as $detail) {
            $detailId = $detail->payment_schedule_detail_id;
            $document_origin = $detail->document_origin;

            if (!isset($uniqueDetails[$detailId])) {
                $detail_data = [];

                switch ($document_origin) {
                    case 'EG':
                        $detail_data = $this->getEntryGroupDetails($detail->entry_group_id);
                        break;
                    case 'M':
                        $detail_data = $this->getMovementDetails($detail->movement_id);
                        break;
                }

                $dates = DB::table('payment_schedule_date as PSDT')
                    ->join('payment_schedule_detail as PSD', 'PSD.payment_schedule_detail_id', '=', 'PSDT.payment_schedule_detail_id')
                    ->leftJoin('movement as M', 'M.movement_id', '=', 'PSDT.payment_movement_id')
                    ->where('PSDT.payment_schedule_detail_id', $detailId)
                    ->select(
                        'PSDT.payment_schedule_date_id',
                        'PSDT.date',
                        'PSDT.amount',
                        'PSDT.state',
                        'PSDT.cashbox_id',
                        'PSDT.payment_movement_id',
                        'PSDT.updated_at',
                        'PSDT.created_at',
                        'M.route', 
                        'PSD.movement_id', 
                        'PSD.entry_group_id')
                    ->whereIn('state', [ PaymentScheduleDate::STATE_OPEN, PaymentScheduleDate::STATE_CLOSED ])
                    ->get();

                $result['data']['details'][] = (array) $detail + (array) $detail_data + [
                    'dates' => $dates
                ];

                $uniqueDetails[$detailId] = true;
            }
        }

        return response()->json($result);
    }

    public function store(Request $request) {

        $saveData = $request->json()->all();

        $validator = Validator::make(
            $saveData,
            [
                "descripcion" => 'string',
                "personID" => 'required | int',
                "details" => 'required | array ',
                'startRegisterDate' => 'required | date',
            ]
        );


        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()
            ], 400);
        }

        try {

            DB::beginTransaction();
            $details = $saveData['details'];
            $paymentSchedule = [
                'description' => $saveData['description'],
                'person_id' => $saveData['personID'],
                'start_register_date' => $saveData['startRegisterDate'],
            ];

            $newPaymentSchedule = PaymentSchedule::create($paymentSchedule);
            $paymentScheduleId = $newPaymentSchedule['payment_schedule_id'];

            foreach ($details as $detail) {
                $paymentScheduleDetailData = [
                    'payment_schedule_id' => $paymentScheduleId,
                    'entry_group_id' => $detail['entryGroupId'],
                    'movement_id' => $detail['movementId'],
                    'document_origin' => $detail['documentOrigin'],
                    'total_amount' => $detail['totalAmount'],
                    'balance_amount' => $detail['balanceAmount'],
                    'order' => $detail['order'],
                ];

                $newPaymentScheduleDetail = PaymentScheduleDetail::create($paymentScheduleDetailData);
                $paymentScheduleDetailId = $newPaymentScheduleDetail['payment_schedule_detail_id'];

                $dates = $detail['dates'];

                foreach ($dates as $date) {
                    $paymentScheduleDateData = [
                        'payment_schedule_detail_id' => $paymentScheduleDetailId,
                        'date' => $date['date'],
                        'amount' => $date['amount'],
                        'state' => $date['state'],
                        'cashbox_id' => isset($date['cashboxId']) ? $date['cashboxId'] : null,
                    ];

                    PaymentScheduleDate::create($paymentScheduleDateData);
                }
            }

            DB::commit();

            $response = [
                'success' => true,
                'message' => 'Payment Schedule Save!',
            ];

            return response()->json($response);

        } catch (\Exception $ex) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }

    }

    public function update(Request $request) {

        $saveData = $request->json()->all();

        $validator = Validator::make($saveData, [
            "paymentSchedule_id" => "required",
            "personID" => 'required',
            "descripcion" => 'string',
            "updatedDates" => 'array',
            "newDetails" => 'array',
            "deletedDetails" => 'array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()
            ], 500);
        }

        try {
            DB::beginTransaction();

            $paymentScheduleId = $saveData['paymentSchedule_id'];
            $paymentSchedule = PaymentSchedule::findOrFail($paymentScheduleId);

            $updateData = [
                'person_id' => $saveData['personID'],
            ];

            if (array_key_exists('description', $saveData)) {
                $updateData['description'] = $saveData['description'];
            }

            $paymentSchedule->update($updateData);


            if (array_key_exists('updatedDates', $saveData)) {
                $updatedDates = $saveData['updatedDates'];

                foreach ($updatedDates as $updateDate) {
                    $detailID = $updateDate['payment_schedule_detail_id'];
                    $date = $updateDate['date'];
                    $updated_date = PaymentScheduleDate::where('payment_schedule_detail_id', $detailID)->where('date', $date)->first();

                    if ($updated_date) {
                        $updated_date->update([
                            'amount' => $updateDate['amount'],
                            'state' => $updateDate['state'],
                            'cashbox_id' => isset($updateDate['cashboxId']) ? $updateDate['cashboxId'] : null,
                        ]);
                    } else {
                        $paymentScheduleDateData = [
                            'payment_schedule_detail_id' => $detailID,
                            'date' => $updateDate['date'],
                            'amount' => $updateDate['amount'],
                            'state' => $updateDate['state'],
                            'cashbox_id' => isset($updateDate['cashboxId']) ? $updateDate['cashboxId'] : null,
                        ];

                        PaymentScheduleDate::create($paymentScheduleDateData);
                    }
                }
            }


            if (array_key_exists('newDetails', $saveData)) {
                $details = $saveData['newDetails'];

                foreach ($details as $detail) {
                    $currentDetail = null;
                    switch ($detail['documentOrigin']) {
                        case 'EG':
                            $currentDetail = PaymentScheduleDetail::where('payment_schedule_id', $paymentScheduleId)->where('entry_group_id', $detail['entryGroupId'])->first();
                            break;
                        case 'M':
                            $currentDetail = PaymentScheduleDetail::where('payment_schedule_id', $paymentScheduleId)->where('movement_id', $detail['movementId'])->first();
                            break;
                    }

                    if ($currentDetail) {
                        $currentPaymentScheduleDetailId = $currentDetail['payment_schedule_detail_id'];
                        $currentDates = $detail['dates'];

                        foreach ($currentDates as $currentDate) {
                            $date = $currentDate['date'];
                            $current_date = PaymentScheduleDate::where('payment_schedule_detail_id', $currentPaymentScheduleDetailId)
                                ->where('date', $date)->first();

                            if ($current_date) {
                                $current_date->update([
                                    'amount' => $currentDate['amount'],
                                    'state' => $currentDate['state']
                                ]);
                            } else {
                                $paymentScheduleDateData = [
                                    'payment_schedule_detail_id' => $currentPaymentScheduleDetailId,
                                    'date' => $currentDate['date'],
                                    'amount' => $currentDate['amount'],
                                    'state' => $currentDate['state'],
                                    'cashbox_id' => isset($updateDate['cashboxId']) ? $updateDate['cashboxId'] : null,
                                ];

                                PaymentScheduleDate::create($paymentScheduleDateData);
                            }
                        }
                    } else {
                        $paymentScheduleDetailData = [
                            'payment_schedule_id' => $paymentScheduleId,
                            'entry_group_id' => $detail['entryGroupId'],
                            'movement_id' => $detail['movementId'],
                            'total_amount' => $detail['totalAmount'],
                            'balance_amount' => $detail['balanceAmount'],
                            'order' => $detail['order'],
                            'document_origin' => $detail['documentOrigin'],
                        ];

                        $newPaymentScheduleDetail = PaymentScheduleDetail::create($paymentScheduleDetailData);
                        $paymentScheduleDetailId = $newPaymentScheduleDetail['payment_schedule_detail_id'];

                        $dates = $detail['dates'];

                        foreach ($dates as $date) {
                            $paymentScheduleDateData = [
                                'payment_schedule_detail_id' => $paymentScheduleDetailId,
                                'date' => $date['date'],
                                'amount' => $date['amount'],
                                'state' => $date['state'],
                                'cashbox_id' => isset($updateDate['cashboxId']) ? $updateDate['cashboxId'] : null,
                            ];

                            PaymentScheduleDate::create($paymentScheduleDateData);
                        }
                    }
                }
            }


            if (array_key_exists('deletedDetails', $saveData)) {
                $deletedDetails = $saveData['deletedDetails'];

                foreach ($deletedDetails as $detail) {
                    $paymentScheduleDetailId = $detail['payment_schedule_detail_id'];
                    PaymentScheduleDate::where('payment_schedule_detail_id', $paymentScheduleDetailId)
                        ->update(['state' => PaymentScheduleDate::STATE_CANCELED]);
                }
            }


            DB::commit();

            PaymentScheduleDate::updateEntryGroup();

            $response = [
                'success' => true,
                'message' => 'Payment Schedule Updated!',
            ];

            return response()->json($response);

        } catch (\Exception $ex) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }

    }

    public function getBalances() {
        try {
            $today = Carbon::today()->toDateString();

            $results = DB::select('CALL sp_get_banks_balance(?)', array($today));

            return response()->json($results);


        } catch (\Exception $ex) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }

    private function getEntryGroupDetails($entryGroupId) {
        $limit_retention_amount_pen = GlobalVar::getValue('no_retention_max_amount_pen');
        $limit_retention_amount_usd = ExchangeRate::convertToDollars($limit_retention_amount_pen);

        return DB::table('entry_group as EG')
            ->join('movement as M', 'M.movement_id', '=', 'EG.movement_id')
            ->join('payment_schedule_detail as PSD', 'PSD.entry_group_id', '=', 'EG.entry_group_id')
            ->join('ability as AB', 'AB.movement_id', '=', 'M.movement_id')
            ->join('document as D', 'D.document_code', '=', 'M.document_code')
            ->join('scenario as S', 'S.route', '=', 'EG.route')
            ->join('person as XP', 'XP.person_id', '=', 'EG.owner_id')
            ->join('person as PER', 'PER.person_id', '=', 'M.person_id')
            ->leftJoin('commercial_movement as CM', 'CM.movement_id', '=', 'M.movement_id')
            ->leftJoin('entry as E', 'E.entry_id', '=', 'EG.pk')
            ->leftJoin('movement_link as ML', function ($join) {
                $join->on('ML.movement_id', '=', 'M.movement_id')
                    ->where('ML.status', '=', 1)
                    ->where('ML.parent_route', '=', 'logistic/purchaseOrder');
            })
            ->leftJoin('commercial_movement as PCM', 'PCM.movement_id', '=', 'ML.movement_parent_id')
            ->where("EG.entry_group_id", "=", $entryGroupId)
            ->select(
                DB::raw("CONCAT(EG.entry_group_id, '-EG') AS pk"),
                'EG.entry_group_id as entryGroupId',
                'EG.movement_id as movementId',
                'EG.detail as detail',
                DB::raw("CONCAT(M.document_code, '-', M.document_serie, '-', CAST(M.document_correlative AS INTEGER)) AS document"),
                'D.document_name as documentType',
                'M.document_code as documentCode',
                'M.document_serie as documentSerie',
                DB::raw("CAST(M.document_correlative AS INTEGER) as documentCorrelative"),
                'S.title',
                'XP.identification_number as identificationNumber',
                DB::raw("REPLACE(XP.person_name, ',', ' ') AS auxPersonName"),
                'EG.emission_date as emissionDate',
                'EG.expiration_date as expirationDate',
                'EG.CONDITION AS paymentCondition',
                'EG.days_overdue as daysOverdue',
                DB::raw("(E.field = 'det') AS isDetraction"),
                DB::raw('DATEDIFF(EG.expiration_date, CURDATE()) AS daysRemaining'),
                'M.currency AS currency',
                'EG.total',
                'PSD.balance_amount as balance',
                'PSD.document_origin as documentOrigin',
                'EG.balance_percent as balancePercent',
                'EG.is_paid as isPaid',
                'EG.is_payable as isPayable',
                'D.sunat_code as sunatCode',
                'M.route',
                'EG.owner_id',
                "EG.route",
                "EG.owner",
                "EG.account_code as accountCode",
                'EG.checked as isProvisioned',
                DB::raw("(E.field = 'det') AS isDetraction"),
                DB::raw("REPLACE(PER.person_name, ',', ' ') AS personName"),
                DB::raw(value: "(CM.det_pen > 0) as hasDetraction"),
                DB::raw("IF(M.currency = '" . Currency::PEN . "', (EG.total > " . $limit_retention_amount_pen . "),(EG.total > " . $limit_retention_amount_usd . ")) as exceedsRetentionAmount"),
                "XP.retention as bpIsRetentionAgent",
                "XP.perception as bpIsPerceptionAgent",
                "XP.good_contributor as goodContributor",
                DB::raw('(CM.igv_pen = 0) as no_igv'),
                DB::raw("IF(PCM.movement_id IS NOT NULL,
                    IF(M.currency = '" . Currency::PEN . "',
                        PCM.total_pen > " . $limit_retention_amount_pen . ",
                        PCM.total_usd > " . $limit_retention_amount_usd . "
                    ),
                    0
                ) AS parentExceedsRetentionAmount"),
            )->first();
    }

    private function getMovementDetails($movementId) {
        $limit_retention_amount_pen = GlobalVar::getValue('no_retention_max_amount_pen');
        $limit_retention_amount_usd = ExchangeRate::convertToDollars($limit_retention_amount_pen);

        return DB::table('movement as M')
            ->join('payment_schedule_detail as PSD', 'PSD.movement_id', '=', 'M.movement_id')
            ->join('ability as AB', 'AB.movement_id', '=', 'M.movement_id')
            ->join('document as D', 'D.document_code', '=', 'M.document_code')
            ->join('scenario as S', 'S.route', '=', 'M.route')
            ->join('person as XP', 'XP.person_id', '=', 'M.aux_person_id')
            ->join('person as PER', 'PER.person_id', '=', 'M.person_id')
            ->leftJoin('commercial_movement as CM', 'CM.movement_id', '=', 'M.movement_id')
            ->leftJoin('product_list AS PL', function ($join) {
                $join->on('PL.owner_id', '=', 'M.movement_id')
                    ->where('PL.owner', '=', Owner::OWNER_MOVEMENT);
            })
            ->whereIn('M.route', [
                'logistic/purchaseOrder',
                'treasury/cashOutOrder'
            ])
            ->where("M.movement_id", "=", $movementId)
            ->select(
                DB::raw("CONCAT(M.movement_id, '-M') AS pk"),
                DB::raw("NULL as entryGroupId"),
                'M.movement_id as movementId',
                DB::raw("CASE
                    WHEN M.route = 'logistic/purchaseOrder' THEN 'Adelanto de Proveedor'
                    WHEN M.route = 'treasury/cashOutOrder' THEN
                        CASE
                            WHEN PL.requirement_type = 'prePay' THEN '" . ProductList::LABEL_TYPE_PRE_PAY . "'
                            WHEN PL.requirement_type = 'refund' THEN '" . ProductList::LABEL_TYPE_REFUND . "'
                            ELSE ''
                        END
                    ELSE ''
                END AS detail"),
                DB::raw("CONCAT(M.document_code, '-', M.document_serie, '-', CAST(M.document_correlative AS INTEGER)) AS document"),
                'D.document_name as documentType',
                'M.document_code as documentCode',
                'M.document_serie as documentSerie',
                DB::raw("CAST(M.document_correlative AS INTEGER) as documentCorrelative"),
                'S.title',
                'XP.identification_number as identificationNumber',
                DB::raw("REPLACE(XP.person_name, ',', ' ') AS auxPersonName"),
                'M.emission_date as emissionDate',
                'M.expiration_date as expirationDate',
                DB::raw("CASE
                    WHEN M.route = 'logistic/purchaseOrder' THEN CM.CONDITION
                    WHEN M.route = 'treasury/cashOutOrder' THEN ''
                END AS paymentCondition"),
                DB::raw("NULL as daysOverdue"),
                DB::raw('DATEDIFF(M.expiration_date, CURDATE()) AS daysRemaining'),
                'M.currency AS currency',
                DB::raw("CASE
                    WHEN M.route = 'logistic/purchaseOrder' THEN  CM.advance_amount
                    WHEN M.route = 'treasury/cashOutOrder' THEN
                        CASE
                            WHEN M.currency = 'pen' THEN PL.total_pen
                            WHEN M.currency = 'usd' THEN PL.total_usd
                            ELSE 0
                        END
                    ELSE ''
                END AS total"),
                'PSD.balance_amount as balance',
                DB::raw("CASE
                    WHEN M.route = 'logistic/purchaseOrder' THEN CM.advance_percentage
                    WHEN M.route = 'treasury/cashOutOrder' THEN 100
                END AS balancePercent"),
                'PSD.document_origin as documentOrigin',
                DB::raw("true as isPaid"),
                DB::raw("true as isPayable"),
                'D.sunat_code as sunatCode',
                'M.route',
                DB::raw("REPLACE(PER.person_name, ',', ' ') AS personName"),
                DB::raw("IF(M.currency = '" . Currency::PEN . "', (CM.total_pen > " . $limit_retention_amount_pen . "),(CM.total_usd > " . $limit_retention_amount_usd . ")) as exceedsRetentionAmount"),
                "XP.retention as bpIsRetentionAgent",
                "XP.perception as bpIsPerceptionAgent",
                "XP.good_contributor as goodContributor",
                DB::raw("((SELECT COUNT(*) FROM item WHERE movement_id = M.movement_id AND product_type != 'M') > 0) AS hasDetraction"),
                DB::raw("IF(CM.movement_id IS NOT NULL,CM.igv_pen = 0, 1) as no_igv")
            )->first();
    }

}
