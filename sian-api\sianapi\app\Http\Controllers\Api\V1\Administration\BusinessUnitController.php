<?php

namespace App\Http\Controllers\Api\V1\Administration;

use Illuminate\Http\Request;
use App\Models\BusinessUnit;
use App\Http\Controllers\Controller;
use App\Http\Resources\Administration\SimpleBusinessUnitResource;

class BusinessUnitController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getItems(Request $request)
    {        
        $a_response = [
            'success' => true,
            'data' => [
                'items' => SimpleBusinessUnitResource::collection(BusinessUnit::where('status', 1)->get())
            ]
        ];
        return response()->json($a_response);
    }
}
