<?php

use App\Http\Controllers\Controller\Api;
use App\Http\Controllers\Controller;

class ApiController extends Controller {

    public $request;


    private function _getAttributes($p_a_unset) {

        $s_request_type = $this->request->method();
        $p_attributes = [];

        switch ($s_request_type) {
            case 'POST':
            case 'PUT':
            case 'PATCH':
            case 'DELETE':
                //$s_raw = Yii::app()->request->getRawBody();
                //if (!USString::isBlank($s_raw)) {
                  //  $p_attributes = CJSON::decode($s_raw);
                //}
                break;
            case 'GET';
                $p_attributes = $_GET;
                break;
            default:
        }

        foreach ($p_a_unset as $s_unset_attribute) {
            if (isset($p_attributes[$s_unset_attribute])) {
                unset($p_attributes[$s_unset_attribute]);
            }
        }

        return $p_attributes;
    }
}

