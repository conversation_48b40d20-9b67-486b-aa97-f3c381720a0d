<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Email
 * 
 * @property string $owner
 * @property int $owner_id
 * @property string $email_address
 * @property string $email_type
 * @property int $order
 * 
 *
 * @package App\Models
 */
class Email extends Model
{
	protected $table = 'email';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'owner_id' => 'int',
		'order' => 'int'
	];

	protected $fillable = [
		'email_type',
		'order'
	];

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}
}
