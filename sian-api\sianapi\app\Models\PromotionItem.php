<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PromotionItem
 * 
 * @property int $promotion_item_id
 * @property string $promotion_item_code
 * @property int $promotion_id
 * @property int $product_id
 * @property float $equivalence
 * @property float $carry
 * @property float $pay
 * @property float $discount_factor
 * @property float $pprice_pen
 * @property float $rprice_pen
 * @property float $rtotal_pen
 * @property float $pprice_usd
 * @property float $rprice_usd
 * @property float $rtotal_usd
 * @property float $pres_stock
 * @property float $unit_stock
 * @property float $unit_balance
 * @property int $order
 * @property bool $last
 * @property int $gift_count
 * @property bool $is_removable
 * 
 * @property Presentation $presentation
 * @property Promotion $promotion
 * @property Collection|ActivePromotion[] $active_promotions
 * @property Collection|Item[] $items
 * @property Collection|PromotionGift[] $promotion_gifts
 * @property Collection|PromotionStock[] $promotion_stocks
 *
 * @package App\Models
 */
class PromotionItem extends Model
{
	protected $table = 'promotion_item';
	protected $primaryKey = 'promotion_item_id';
	public $timestamps = false;

	protected $casts = [
		'promotion_id' => 'int',
		'product_id' => 'int',
		'equivalence' => 'float',
		'carry' => 'float',
		'pay' => 'float',
		'discount_factor' => 'float',
		'pprice_pen' => 'float',
		'rprice_pen' => 'float',
		'rtotal_pen' => 'float',
		'pprice_usd' => 'float',
		'rprice_usd' => 'float',
		'rtotal_usd' => 'float',
		'pres_stock' => 'float',
		'unit_stock' => 'float',
		'unit_balance' => 'float',
		'order' => 'int',
		'last' => 'bool',
		'gift_count' => 'int',
		'is_removable' => 'bool'
	];

	protected $fillable = [
		'promotion_item_code',
		'promotion_id',
		'product_id',
		'equivalence',
		'carry',
		'pay',
		'discount_factor',
		'pprice_pen',
		'rprice_pen',
		'rtotal_pen',
		'pprice_usd',
		'rprice_usd',
		'rtotal_usd',
		'pres_stock',
		'unit_stock',
		'unit_balance',
		'order',
		'last',
		'gift_count',
		'is_removable'
	];

	public function presentation()
	{
		return $this->belongsTo(Presentation::class, 'product_id')
					->where('presentation.product_id', '=', 'promotion_item.product_id')
					->where('presentation.equivalence', '=', 'promotion_item.equivalence');
	}

	public function promotion()
	{
		return $this->belongsTo(Promotion::class);
	}

	public function active_promotions()
	{
		return $this->hasMany(ActivePromotion::class);
	}

	public function items()
	{
		return $this->hasMany(Item::class);
	}

	public function promotion_gifts()
	{
		return $this->hasMany(PromotionGift::class);
	}

	public function promotion_stocks()
	{
		return $this->hasMany(PromotionStock::class);
	}
}
