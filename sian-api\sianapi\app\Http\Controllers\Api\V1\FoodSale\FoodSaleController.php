<?php

namespace App\Http\Controllers\Api\V1\FoodSale;

use App\Models\Division;
use App\Models\Item;
use App\Models\Procedures\SpReportsFoodSales;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class FoodSaleController extends Controller {
    /**
     *
     *
     * @return \Illuminate\Http\Response
     */
    public function report(Request $request) {
        $validate = Validator::make($request->all(), [
            'startDate' => 'required|string',
            'endDate' => 'required|string',
            'limit' => 'required|integer',
            'productType' => 'required|string',
        ]);

        if ($validate->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validate->errors()
            ], 400);
        }


        try {
            $s_divisionIds = "";
            $s_lineIds = "";
            $s_sublineIds = "";
            $s_categoryIds = "";
            $s_productIds = "";
            $s_storeIds = "";
            $i_order_by = 1;

            if ($request->has('division')) {
                $s_divisionIds = $request->input('division');
            }

            if ($request->has('line')) {
                $s_lineIds = $request->input('line');
            }

            if ($request->has('subline')) {
                $s_sublineIds = $request->input('subline');
            }

            if ($request->has('category')) {
                $s_categoryIds = $request->input('category');
            }

            if ($request->has('productIds')) {
                $s_productIds = $request->input('productIds');
            }

            if ($request->has('storeIds')) {
                $s_storeIds = $request->input('storeIds');
            }

            if ($request->input('productType') == Division::DIVISION_FOOD_NAME) {

                $s_lineIds = [];
                $s_categoryIds = [];

                if ($request->input('line') != '') {
                    $a_lines = explode(',', $request->input('line'));

                    foreach ($a_lines as $line_selected_key) {
                        $a_key = explode('-', $line_selected_key);
                        if (count($a_key) >= 1) {
                            $a_line_type = $a_key[0];
                            $a_id = $a_key[1];
                            if ($a_line_type == Item::PRODUCT_TYPE_MERCHANDISE) {
                                $s_lineIds[] = $a_id;
                            } elseif ($a_line_type == Item::PRODUCT_TYPE_SERVICE) {
                                $s_categoryIds[] = $a_id;
                            }
                        }
                    }
                }

                $s_lineIds = implode(',', $s_lineIds);
                $s_categoryIds = implode(',', $s_categoryIds);

            }

            if ($request->has('orderBy')) {
                $i_order_by = $request->input('orderBy');
            }

            $data = SpReportsFoodSales::execute(
                $request->input('startDate'),
                $request->input('endDate'),
                $request->input('limit'),
                $request->input('productType'),
                $s_divisionIds,
                $s_lineIds,
                $s_sublineIds,
                $s_categoryIds,
                $s_productIds,
                $s_storeIds,
                $request->input('group'),
                $i_order_by
            );

            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }

}
