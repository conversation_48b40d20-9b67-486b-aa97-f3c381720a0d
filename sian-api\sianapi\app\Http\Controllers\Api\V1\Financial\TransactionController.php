<?php

namespace App\Http\Controllers\Api\V1\Financial;


use App\Http\Controllers\SianController;
use App\Models\Auth\LoginPivot;
use App\Models\Cashbox;
use App\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;


class TransactionController extends Controller {

    /**
     *
     *
     * @return \Illuminate\Http\Response
     */


    public function index(Request $request) {

        $validator = Validator::make($request->all(), [
            "page" => "required|integer",
            "pageSize" => "required|integer",
            "startDateEmission" => 'date',
            "endDateEmission" => " date",
            "currency" => 'string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()
            ], 400);
        }

        try {
            $page = intval($request->query('page'));
            $pageSize = intval($request->query('pageSize'));

            $startIndex = ($page - 1) * $pageSize;

            $query = DB::table('movement as M')
                ->join('cashbox_movement as CM', 'CM.movement_id', '=', 'M.movement_id')
                ->join('cashbox as C', 'C.cashbox_id', '=', 'CM.cashbox_id')
                ->join('person as XP', 'XP.person_id', '=', 'M.aux_person_id')
                ->join('person as PE', 'PE.person_id', '=', 'M.person_id')
                ->join('business_unit as BU', 'BU.business_unit_id', '=', 'M.business_unit_id')
                ->join('store as ST', 'ST.store_id', '=', 'M.store_id')
                ->join('scenario as SC', 'SC.route', '=', 'M.route')
                ->join('payment_schedule_date AS PSD', 'PSD.payment_movement_id', '=', 'CM.movement_id')
                ->where('M.status', 1)
                ->whereIn('M.route', ['financial/massiveOutgoing', 'financial/outgoing', 'financial/feePay', 'treasury/prePay', 'treasury/transferenceOut'])
                ->whereIn('M.store_id', [18, 13, 21, 17, 12, 22, 10, 1, 16, 7, 9, 5, 6, 20]);

            if ($request->has('startDateEmision') && $request->has('endDateEmision')) {
                $query->whereDate('M.emission_date', '>=', $request->input('startDateEmision'));
                $query->whereDate('M.emission_date', '<=', $request->input('endDateEmision'));
            }

            if ($request->has('currency')) {
                $curencyValues = $request->input('currency');
                $currencyArray = explode(',', $curencyValues);
                $query->whereIn('M.currency', $currencyArray);
            }

            $totalItems = $query->count();

            $results = $query->select(
                'M.movement_id as movementID',
                'BU.business_unit_name as businessUnitName',
                'ST.store_name as  storeName',
                DB::raw("CONCAT_WS('-', M.document_code, M.document_serie, CAST(M.document_correlative AS INTEGER)) AS document"),
                'M.emission_date as emissionDate',
                'C.cashbox_name as boxAccountName',
                'CM.type as typePay',
                'SC.title as typeExit',
                'M.route',
                'M.currency',
                DB::raw("IF(M.currency = 'pen', CM.total_pen, CM.total_usd) as total"),
                DB::raw("REPLACE(XP.person_name, ',' , ' ' ) as businessPartner"),
                DB::raw("REPLACE(PE.person_name, ',' , ' ' ) as user"),
            )->orderBy('M.register_date', 'desc')->offset($startIndex)->limit($pageSize)->get();

            $response = [
                'success' => true,
                'pagination' => [
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'totalRecords' => $totalItems,
                    'totalPages' => ceil($totalItems / $pageSize)
                ],
                'data' => $results
            ];

            return response()->json($response);

        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }


    }

    public function store(Request $request) {
        $requestData = $request->json()->all();

        $validator = Validator::make(
            $requestData,
            [
                "transaction" => 'required',
                "username" => 'required',
            ]
        );

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()
            ], 400);
        }


        $host = '';

        $originHeader = $request->headers->get('Origin');

        if ($originHeader) {
            $parsedUrl = parse_url($originHeader);
            $host = $parsedUrl['host'] ?? '';
        }

        $tenant = Tenant::whereDomain($host)->first();

        try {

            $username = $requestData['username'];
            $hash = LoginPivot::getHashFromAuth($request->header('Authorization'));
            $transaction = $requestData['transaction'];
            $savePaysSianResponse = SianController::savePayOnSian($transaction, $username, $hash, 'https://' . $tenant['sian_domain']);

            return response()->json($savePaysSianResponse);
        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function getBoxAccountByUser(Request $request) {
        $validator = Validator::make($request->all(), [
            "userID" => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()
            ], 400);
        }
        try {

            $userID = $request->query('userID');

            $response = DB::table('user as U')
                ->select(
                    'C.cashbox_id as cashboxID',
                    'C.cashbox_name as cashboxName',
                    'E.multi_id as multiID',
                    'E.description AS bankName',
                    'E.abbreviation AS bank',
                    'C.type',
                    'C.movement_type as movementType',
                    'C.currency',
                    'C.store_id as storeID',
                    'C.is_detraction as isDetraction'
                )
                ->join('owner_pair as OPP', function ($join) {
                    $join->on('OPP.type', '=', DB::raw("'UserCashbox'"))
                        ->on('OPP.owner_id', '=', 'U.user_id')
                        ->on('OPP.owner', '=', DB::raw("'User'"));
                })
                ->join('owner_pair as OPH', function ($join) {
                    $join->on('OPH.type', '=', 'OPP.type')
                        ->on('OPH.owner', '=', DB::raw("'Cashbox'"))
                        ->on('OPH.parent_id', '=', 'OPP.pair_id');
                })
                ->join('cashbox as C', 'C.cashbox_id', '=', 'OPH.owner_id')
                ->leftJoin('multitable as E', 'E.multi_id', '=', 'C.entity_type_id')
                ->where('C.status', true)
                ->where('C.outgoing', true)
                ->where(function ($query) {
                    $query->whereRaw("FIND_IN_SET('Efectivo', movement_type) > 0")
                        ->orWhereRaw("FIND_IN_SET('Deposito', movement_type) > 0")
                        ->orWhereRaw("FIND_IN_SET('Cheque', movement_type) > 0");
                })
                ->where('C.type', '=', Cashbox::TYPE_BANK)
                ->where('U.person_id', '=', $userID)
                ->get();


            foreach ($response as $resp) {
                if ($resp->storeID) {
                    $storeID = $resp->storeID;
                    $store = DB::table('store as S')
                        ->where('S.store_id', '=', $storeID)
                        ->select(
                            'S.store_ID as storeID',
                            'S.store_name as storeName',
                            'S.alias',
                            'S.status',
                            'S.business_unit_id as businessUnitID',
                            'S.transaction_code as transactionCode'

                        )->first();
                    if ($store) {
                        $resp->store = $store;
                        if ($store->businessUnitID) {
                            $business_unit_id = $store->businessUnitID;
                            $businessUnit = DB::table('business_unit as BU')
                                ->where('BU.business_unit_id', '=', $business_unit_id)
                                ->select(
                                    'BU.business_unit_id as businessUnitID',
                                    'BU.business_unit_name as businessUnitName',
                                    'BU.alias',
                                    'BU.combination_id as combinationID '
                                )->first();
                            if ($businessUnit) {
                                $resp->store->businessUnit = $businessUnit;
                            }

                        }
                    }
                }

            }

            return response()->json($response);

        } catch (\Exception $ex) {

            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function getStoresByUser(Request $request) {
        $validator = Validator::make($request->all(), [
            "userID" => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()
            ], 400);
        }
        try {
            $userID = $request->query('userID');

            $result = DB::table('user as U')
                ->select(
                    'S.store_id as storeID',
                    'S.store_name as storeName',
                    'B.business_unit_id as businessUnitID',
                    'B.business_unit_name as businessUnitName',
                    'OPH.default as store_default'
                )
                ->join('owner_pair as OPP', function ($join) {
                    $join->on('OPP.type', '=', DB::raw("'UserStore'"))
                        ->on('OPP.owner_id', '=', 'U.user_id')
                        ->on('OPP.owner', '=', DB::raw("'User'"));
                })
                ->join('owner_pair as OPH', function ($join) {
                    $join->on('OPH.type', '=', 'OPP.type')
                        ->on('OPH.owner', '=', DB::raw("'Store'"))
                        ->on('OPH.parent_id', '=', 'OPP.pair_id');
                })
                ->join('store as S', 'S.store_id', '=', 'OPH.owner_id')
                ->leftJoin('business_unit as B', 'B.business_unit_id', '=', 'S.business_unit_id')
                ->where('OPH.default', '=', '1')
                ->where('S.status', true)
                ->where('U.person_id', '=', $userID)
                ->first();

            return response()->json($result);

        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function getBusinessUnitByUser(Request $request) {
        $validator = Validator::make($request->all(), [
            "userID" => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()
            ], 400);
        }

        try {
            $userID = $request->query('userID');

            $result = DB::table('user as U')
                ->select('B.business_unit_id as businessUnitID', 'B.business_unit_name as businessUnitName', 'OPH.default as bussiness_unit_default')
                ->join('owner_pair as OPP', function ($join) {
                    $join->on('OPP.type', '=', DB::raw("'UserBusinessUnit'"))
                        ->on('OPP.owner_id', '=', 'U.user_id')
                        ->on('OPP.owner', '=', DB::raw("'User'"));
                })
                ->join('owner_pair as OPH', function ($join) {
                    $join->on('OPH.type', '=', 'OPP.type')
                        ->on('OPH.owner', '=', DB::raw("'BusinessUnit'"))
                        ->on('OPH.parent_id', '=', 'OPP.pair_id');
                })
                ->join('business_unit as B', 'B.business_unit_id', '=', 'OPH.owner_id')
                ->where('OPH.default', '=', '1')
                ->where('B.status', true)
                ->where('U.person_id', '=', $userID)
                ->first();

            return response()->json($result);

        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }


    }

    public function getProjects() {
        try {
            $response = DB::table('project as P')
                ->select(
                    'P.project_id as projectID',
                    'P.project_name as projectName',
                    'P.short_name as shortName',
                    'P.person_id as personID',
                    'P.combination_id as combinationID'
                )->get();

            return response()->json($response);

        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function getExchangeRate() {
        try {
            $response = DB::table('exchange_rate as ER')
                ->select(
                    'ER.exchange_rate_id as exchangeRateID',
                    'ER.date',
                    'ER.purchase',
                    'ER.sale'
                )->orderBy('ER.date', 'desc')->first();
            return response()->json($response);

        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function files(Request $request) {
        $files = $request->allFiles();
        $username = $request->input('username');
        $logs = [];

        if (count($files) == 0) {
            return response()->json(['success' => false, 'error' => 'No hay archivos que subir'], 400);
        }

        $originHeader = $request->headers->get('Origin');
        $parsedUrl = parse_url($originHeader);
        $host = $parsedUrl['host'] ?? '';

        $tenant = Tenant::whereDomain($host)->first();
        $sianDomain = 'https://' . $tenant['sian_domain'];

        $hash = LoginPivot::getHashFromAuth($request->header('Authorization'));

        $appResponse = SianController::authenticateAPI($sianDomain);
        $appToken = $appResponse['appToken'];
        $userResponse = SianController::authenticateUser($appToken, $username, $hash, $sianDomain);
        $userToken = $userResponse['token'];

        $url = $sianDomain . '/admin/apiSian/pay/files';

        $arrayFilesAndIds = [];

        foreach ($files as $key => $file) {
            $keyArray = explode("_", $key);

            $movementID = $keyArray[0];

            $arrayFilesAndIds[] = [
                'movement_id' => $movementID,
                'name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'type' => $file->getClientMimeType(),
                'base64' => base64_encode($file->get())
            ];
        }

        $body = ['files' => $arrayFilesAndIds];
        $response = Http::withoutVerifying()->withHeaders([
            'Content-Type' => 'application/json',
            'App-Authorization' => $appToken,
            'Authorization' => $userToken,
        ])->post($url, $body);

        $jsonResponse = json_decode($response->body());

        if ($jsonResponse !== null) {
            $logs[] = ['message' => 'Respuesta completa de la solicitud a SIAN', 'response' => $jsonResponse];
        } else {
            $logs[] = ['message' => 'Respuesta completa de la solicitud a SIAN (no es JSON)', 'response' => $response->body()];
        }

        Log::info('Respuesta completa de la solicitud a SIAN', ['response' => $response->body()]);

        if (!($response instanceof \Illuminate\Http\Client\Response)) {
            throw new \Exception('Error al realizar la solicitud a SIAN: Respuesta inválida' . json_encode($logs) . json_encode($body));
        }

        if (!$response->successful()) {

            if ($response->status() == 404) {
                throw new \Exception('Error al realizar la solicitud a SIAN: Recurso no encontrado' . json_encode($logs));
            }
            throw new \Exception('Error al realizar la solicitud a SIAN: ' . ['response' => $response->status(), 'logs' => $logs]);
        }

        $logs[] = ['message' => 'Respuesta JSON de la solicitud a SIAN', 'response' => $response->json()];

        Log::info('Respuesta JSON de la solicitud a SIAN', ['json' => $response->json(), 'logs' => $logs]);

        return response()->json(['success' => true, 'data' => $response->json()['data']], 200);
    }

}
