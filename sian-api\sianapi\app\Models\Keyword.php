<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Keyword
 * 
 * @property int $keyword_id
 * @property string $owner
 * @property int $owner_id
 * @property string $alias
 * @property string $keyword
 * @property int $order
 * 
 *
 * @package App\Models
 */
class Keyword extends Model
{
	protected $table = 'keyword';
	protected $primaryKey = 'keyword_id';
	public $timestamps = false;

	protected $casts = [
		'owner_id' => 'int',
		'order' => 'int'
	];

	protected $fillable = [
		'owner',
		'owner_id',
		'alias',
		'keyword',
		'order'
	];

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}
}
