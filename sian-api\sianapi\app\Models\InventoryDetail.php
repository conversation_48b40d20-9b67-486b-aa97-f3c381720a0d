<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class InventoryDetail
 * 
 * @property int $inventory_detail_id
 * @property int $inventory_id
 * @property int $product_id
 * @property string|null $serie
 * @property float $equivalence
 * @property float $initial_stock
 * @property float $unit_price
 * @property float $cost
 * @property float $total_cost
 * @property int|null $initial_warehouse_area_id
 * @property float $real_stock
 * @property int|null $real_warehouse_area_id
 * @property float $difference_stock
 * @property float $difference_amount
 * @property string|null $observation
 * @property bool $inventoried
 * 
 * @property WarehouseArea|null $warehouse_area
 * @property Inventory $inventory
 * @property Presentation $presentation
 * @property Collection|InventoryDetailCounting[] $inventory_detail_countings
 * @property Collection|InventoryDetailResult[] $inventory_detail_results
 *
 * @package App\Models
 */
class InventoryDetail extends Model
{
	protected $table = 'inventory_detail';
	protected $primaryKey = 'inventory_detail_id';
	public $timestamps = false;

	protected $casts = [
		'inventory_id' => 'int',
		'product_id' => 'int',
		'equivalence' => 'float',
		'initial_stock' => 'float',
		'unit_price' => 'float',
		'cost' => 'float',
		'total_cost' => 'float',
		'initial_warehouse_area_id' => 'int',
		'real_stock' => 'float',
		'real_warehouse_area_id' => 'int',
		'difference_stock' => 'float',
		'difference_amount' => 'float',
		'inventoried' => 'bool'
	];

	protected $fillable = [
		'inventory_id',
		'product_id',
		'serie',
		'equivalence',
		'initial_stock',
		'unit_price',
		'cost',
		'total_cost',
		'initial_warehouse_area_id',
		'real_stock',
		'real_warehouse_area_id',
		'difference_stock',
		'difference_amount',
		'observation',
		'inventoried'
	];

	public function warehouse_area()
	{
		return $this->belongsTo(WarehouseArea::class, 'real_warehouse_area_id');
	}

	public function inventory()
	{
		return $this->belongsTo(Inventory::class);
	}

	public function presentation()
	{
		return $this->belongsTo(Presentation::class, 'product_id')
					->where('presentation.product_id', '=', 'inventory_detail.product_id')
					->where('presentation.equivalence', '=', 'inventory_detail.equivalence');
	}

	public function inventory_detail_countings()
	{
		return $this->hasMany(InventoryDetailCounting::class);
	}

	public function inventory_detail_results()
	{
		return $this->hasMany(InventoryDetailResult::class);
	}
}
