<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;

/**
 * Class Mark
 * 
 * @property int $mark_id
 * @property string $mark_name
 * @property string $alias
 * @property string $description
 * @property bool $status
 * @property bool $frontend
 * @property bool $outstanding
 * @property bool $web_enabled
 * @property int $web_order
 * @property string|null $transaction_code
 * 
 * @property Collection|FixedAsset[] $fixed_assets
 * @property Collection|Merchandise[] $merchandises
 *
 * @package App\Models
 */
class Mark extends Model
{
	use HasFactory, Notifiable; 

	protected $table = 'mark';
	protected $primaryKey = 'mark_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'frontend' => 'bool',
		'outstanding' => 'bool',
		'web_enabled' => 'bool',
		'web_order' => 'int'
	];

	protected $fillable = [
		'mark_name',
		'alias',
		'description',
		'status',
		'frontend',
		'outstanding',
		'web_enabled',
		'web_order',
		'transaction_code'
	];

	public function fixed_assets()
	{
		return $this->hasMany(FixedAsset::class);
	}

	public function merchandises()
	{
		return $this->hasMany(Merchandise::class);
	}
}
