<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Regime
 * 
 * @property int $regime_id
 * @property string $description
 * @property string $regime_code
 * @property string $type_period
 * @property bool $status
 * @property bool|null $control_detail
 * 
 * @property Collection|AssistanceSummary[] $assistance_summaries
 * @property Collection|Dictionary[] $dictionaries
 * @property Collection|Employee[] $employees
 *
 * @package App\Models
 */
class Regime extends Model
{
	protected $table = 'regime';
	protected $primaryKey = 'regime_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'control_detail' => 'bool'
	];

	protected $fillable = [
		'description',
		'regime_code',
		'type_period',
		'status',
		'control_detail'
	];

	public function assistance_summaries()
	{
		return $this->hasMany(AssistanceSummary::class);
	}

	public function dictionaries()
	{
		return $this->hasMany(Dictionary::class);
	}

	public function employees()
	{
		return $this->hasMany(Employee::class);
	}
}
