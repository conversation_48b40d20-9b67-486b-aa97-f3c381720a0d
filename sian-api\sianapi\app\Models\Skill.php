<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Skill
 * 
 * @property int $skill_id
 * @property string $owner
 * @property int $owner_id
 * @property string $value
 * 
 *
 * @package App\Models
 */
class Skill extends Model
{
	protected $table = 'skill';
	protected $primaryKey = 'skill_id';
	public $timestamps = false;

	protected $casts = [
		'owner_id' => 'int'
	];

	protected $fillable = [
		'owner',
		'owner_id',
		'value'
	];

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}
}
