<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Feature
 * 
 * @property int $feature_id
 * @property string $feature_name
 * @property string|null $description
 * @property string $alias
 * @property bool $status
 * @property bool $frontend
 *
 * @package App\Models
 */
class Feature extends Model
{
	protected $table = 'feature';
	protected $primaryKey = 'feature_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'frontend' => 'bool'
	];

	protected $fillable = [
		'feature_name',
		'description',
		'alias',
		'status',
		'frontend'
	];
}
