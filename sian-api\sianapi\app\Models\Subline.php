<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Subline
 *
 * @property int $subline_id
 * @property string $subline_name
 * @property string $alias
 * @property string $description
 * @property bool $status
 * @property bool $frontend
 * @property bool $outstanding
 * @property int $line_id
 * @property float $min_margin
 * @property float $avg_margin
 * @property float $web_margin
 * @property bool $web_enabled
 * @property int $web_order
 * @property string|null $transaction_code
 *
 * @property Line $line
 * @property Collection|Merchandise[] $merchandises
 *
 * @package App\Models
 */
class Subline extends Model {
    protected $table = 'subline';
    protected $primaryKey = 'subline_id';
    public $timestamps = false;

    protected $casts = [
        'status' => 'bool',
        'frontend' => 'bool',
        'outstanding' => 'bool',
        'line_id' => 'int',
        'min_margin' => 'float',
        'avg_margin' => 'float',
        'web_margin' => 'float',
        'web_enabled' => 'bool',
        'web_order' => 'int'
    ];

    protected $fillable = [
        'subline_name',
        'alias',
        'description',
        'status',
        'frontend',
        'outstanding',
        'line_id',
        'min_margin',
        'avg_margin',
        'web_margin',
        'web_enabled',
        'web_order',
        'transaction_code'
    ];

    public function line() {
        return $this->belongsTo(Line::class, 'line_id', 'line_id');
    }

    public function merchandises() {
        return $this->hasMany(Merchandise::class, 'subline_id');
    }
}
