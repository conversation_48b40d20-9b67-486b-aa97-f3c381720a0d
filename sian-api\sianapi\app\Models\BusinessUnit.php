<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class BusinessUnit
 * 
 * @property int $business_unit_id
 * @property string $business_unit_name
 * @property string $alias
 * @property bool $status
 * @property int $combination_id
 * 
 * @property Combination $combination
 * @property Collection|ApiUserVariant[] $api_user_variants
 * @property Collection|CommercialGoal[] $commercial_goals
 * @property Collection|Inventory[] $inventories
 * @property Collection|Movement[] $movements
 * @property Collection|NetworkingProject[] $networking_projects
 * @property Collection|Store[] $stores
 *
 * @package App\Models
 */
class BusinessUnit extends Model
{
	protected $table = 'business_unit';
	protected $primaryKey = 'business_unit_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'combination_id' => 'int'
	];

	protected $fillable = [
		'business_unit_name',
		'alias',
		'status',
		'combination_id'
	];

	public function combination()
	{
		return $this->belongsTo(Combination::class);
	}

	public function api_user_variants()
	{
		return $this->hasMany(ApiUserVariant::class);
	}

	public function commercial_goals()
	{
		return $this->hasMany(CommercialGoal::class);
	}

	public function inventories()
	{
		return $this->hasMany(Inventory::class);
	}

	public function movements()
	{
		return $this->hasMany(Movement::class);
	}

	public function networking_projects()
	{
		return $this->hasMany(NetworkingProject::class);
	}

	public function stores()
	{
		return $this->hasMany(Store::class);
	}
}
