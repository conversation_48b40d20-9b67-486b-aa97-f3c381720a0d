<?php

namespace App\Http\Controllers\Api\V1\Asset;

use Illuminate\Http\Request;
use App\Models\DepreciationGroup;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;

use App\Http\Resources\Asset\DepreciationGroupResource;
use App\Http\Resources\Asset\DepreciationGroupCollection;
use App\Http\Resources\Asset\SimpleDepreciationGroupResource;
use App\Models\DepreciationGroupDetail;

class DepreciationGroupController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $request->validate([
            'page' => 'int',
            'pageSize' => 'int',
            'sortField' => 'String',
            'direction' => 'String',
            'searchTerm' => 'String',
            'id' => 'int',
            'name' => 'String'
        ]);

        $i_page = (isset($request->page) && $request->page > 0) ? $request->page : 1;
        $i_pageSize = (isset($request->pageSize) && $request->pageSize > 0) ? $request->pageSize : 10;
        $s_sortField = isset($request->sortField) ? $request->sortField : 'depreciation_group_id';
        $s_direction = isset($request->direction) ? $request->direction : 'DESC';
        $s_searchTerm = isset($request->searchTerm) ? $request->searchTerm : '';
        $i_skip = ($i_page - 1) * $i_pageSize;

        //Query
        $s_query = DepreciationGroup::skip($i_skip);
        if (isset($request->id))
            $s_query->where('depreciation_group_id', $request->id);
        if (isset($request->name))
            $s_query->where('depreciation_group_name', 'like', '%' . $request->name . '%');
        $s_query->orderBy($s_sortField, $s_direction);

        $depreciationGroups = $s_query->paginate($i_pageSize);

        $paginator =  new DepreciationGroupCollection($depreciationGroups);

        $a_response = [
            'success' => true,
            'data' => [
                'count' => $paginator->count(),
                'totalPages' => $paginator->lastPage(),
                'page' => $paginator->currentPage(),
                'pageSize' => $paginator->perPage(),
                'totalItems' => $paginator->total(),
                'items' => $paginator->items(),
            ]
        ];
        return $a_response;
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\DepreciationGroup  $depreciationGroup
     * @return \Illuminate\Http\Response
     */
    public function show(DepreciationGroup $depreciationGroup)
    {
        $result["success"]  = true;
        $result["data"]  = new DepreciationGroupResource($depreciationGroup);
        return $result;
    }

    /**
     * create a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request): JsonResponse
    {
        $validate = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'depreciationMethod' => 'required',
            'accountDepreciation' => 'required|string|max:9',
            'accountCost' => 'string|max:9',
            'details' => 'required'
        ], [
            'name.required' => "El nombre del grupo de depreciación es obligatorio.",
            'depreciationMethod.required' => 'El método de depreciación es obligatorio.',
            'accountDepreciation.required' => 'La cuenta de depreciación es obligatorio.',
            'accountCost.required' => 'La cuenta de gasto es obligaria.',
            'details.required' => 'Los detalles son obligatorios.',
        ]);

        $a_response = [];

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {

            $o_depreciationGroup = new DepreciationGroup();
            $o_depreciationGroup->depreciation_group_name = $request->name;
            $o_depreciationGroup->depreciation_method = $request->depreciationMethod;
            $o_depreciationGroup->account_code_depreciation = $request->accountDepreciation;
            $o_depreciationGroup->account_code_cost = $request->accountCost;
            $o_depreciationGroup->status = 1;
            $o_depreciationGroup->observation = $request->observation;
            $a_depreciationGroupDetail = $request->details;

            foreach ($a_depreciationGroupDetail as $i => $item) {
                $a_depreciationGroupDetail[$i]["annual_rate"] = $item["annualRate"];
            }

            try {
                DB::beginTransaction();

                $o_depreciationGroup->save();
                $o_depreciationGroup->depreciationGroupDetails()->createMany($a_depreciationGroupDetail);

                DB::commit();

                $a_response = [
                    'success' => true,
                    'message' => 'Se registró correctamente.',
                ];
            } catch (QueryException $ex) {
                DB::rollBack();
                $a_response = [
                    'success' => false,
                    'message' => $ex->getMessage(),
                ];
            }
        }
        return response()->json($a_response);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validate = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'depreciationMethod' => 'required',
            'accountDepreciation' => 'required|string|max:9',
            'accountCost' => 'string|max:9',
            'details' => 'required'
        ], [
            'name.required' => "El nombre del grupo de depreciación es obligatorio.",
            'depreciationMethod.required' => 'El método de depreciación es obligatorio.',
            'accountDepreciation.required' => 'La cuenta de depreciación es obligatorio.',
            'accountCost.required' => 'La cuenta de gasto es obligaria.',
            'details.required' => 'Los detalles son obligatorios.',
        ]);

        $a_response = [];

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {
            $o_depreciationGroup = DepreciationGroup::find($id);

            if (isset($o_depreciationGroup)) {

                $o_depreciationGroup->depreciation_group_name = isset($request->name) ? $request->name : $o_depreciationGroup->depreciation_group_name;
                $o_depreciationGroup->depreciation_method = isset($request->depreciationMethod) ? $request->depreciationMethod : $o_depreciationGroup->depreciation_method;
                $o_depreciationGroup->account_code_depreciation =  isset($request->accountDepreciation) ? $request->accountDepreciation : $o_depreciationGroup->account_code_depreciation;
                $o_depreciationGroup->account_code_cost = isset($request->accountCost) ? $request->accountCost : $o_depreciationGroup->account_code_cost;
                $o_depreciationGroup->status = isset($request->status) ? $request->status : $o_depreciationGroup->status;
                $o_depreciationGroup->observation = isset($request->observation) ? $request->observation : $o_depreciationGroup->observation;
                $a_depreciationGroupDetail = $request->details;

                foreach ($a_depreciationGroupDetail as $i => $item) {
                    $a_depreciationGroupDetail[$i]["annual_rate"] = $item["annualRate"];
                }

                DB::beginTransaction();
                try {
                    $o_depreciationGroup->save();
                    DepreciationGroupDetail::where('depreciation_group_id', $id)->delete();
                    $o_depreciationGroup->depreciationGroupDetails()->createMany($a_depreciationGroupDetail);

                    DB::commit();

                    $a_response = [
                        'success' => true,
                        'message' => 'Se actualizó correctamente.',
                    ];
                } catch (QueryException $ex) {
                    DB::rollBack();
                    $a_response = [
                        'success' => false,
                        'message' => $ex->getMessage(),
                    ];
                }
            } else {
                $a_response = [
                    'success' => false,
                    'message' => 'No se pudo recuperar el grupo de depreciación.',
                ];
            }
        }
        return response()->json($a_response);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $i_deleted = DepreciationGroup::destroy($id);

            if ($i_deleted > 0) {
                $a_response = [
                    'success' => true,
                    'message' => 'Se eliminó correctamente.',
                ];
            } else {
                $a_response = [
                    'success' => false,
                    'message' => 'El objeto no existe.',
                ];
            }
        } catch (QueryException $ex) {
            $a_response = [
                'success' => false,
                'message' => $ex->getMessage(),
            ];
        }
        return response()->json($a_response);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getItems(Request $request)
    {
        $a_response = [
            'success' => true,
            'data' => [
                'items' => SimpleDepreciationGroupResource::collection(DepreciationGroup::where('status', 1)->get())
            ]
        ];
        return response()->json($a_response);
    }
}
