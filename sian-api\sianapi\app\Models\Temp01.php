<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Temp01
 * 
 * @property string|null $owner
 * @property string|null $owner_id
 * @property string|null $cost_center_name
 * @property int|null $level1_id
 * @property int|null $level2_id
 * @property int|null $level3_id
 * @property int|null $level4_id
 * @property int|null $level5_id
 * @property string|null $combination_name
 * @property bool|null $status
 *
 * @package App\Models
 */
class Temp01 extends Model
{
	protected $table = 'temp01';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'level1_id' => 'int',
		'level2_id' => 'int',
		'level3_id' => 'int',
		'level4_id' => 'int',
		'level5_id' => 'int',
		'status' => 'bool'
	];

	protected $fillable = [
		'owner',
		'owner_id',
		'cost_center_name',
		'level1_id',
		'level2_id',
		'level3_id',
		'level4_id',
		'level5_id',
		'combination_name',
		'status'
	];
}
