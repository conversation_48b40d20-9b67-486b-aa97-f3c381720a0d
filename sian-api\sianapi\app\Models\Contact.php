<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Contact
 * 
 * @property int $contact_id
 * @property string $firstname
 * @property string $lastname
 * @property string|null $dni
 * @property string $type
 * @property string|null $phone_number
 * @property string $email_address
 * @property int $person_id
 * 
 * @property Person $person
 * @property Collection|CommercialCase[] $commercial_cases
 *
 * @package App\Models
 */
class Contact extends Model
{
	protected $table = 'contact';
	protected $primaryKey = 'contact_id';
	public $timestamps = false;

	protected $casts = [
		'person_id' => 'int'
	];

	protected $fillable = [
		'firstname',
		'lastname',
		'dni',
		'type',
		'phone_number',
		'email_address',
		'person_id'
	];

	public function person()
	{
		return $this->belongsTo(Person::class);
	}

	public function commercial_cases()
	{
		return $this->hasMany(CommercialCase::class);
	}
}
