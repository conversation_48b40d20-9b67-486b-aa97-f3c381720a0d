<?php

namespace App\Http\Controllers\Api\V1\ProductionCost;

use App\Models\ProductionCost;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class ProductionCostController extends Controller {
    /**
     *
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        $validate = Validator::make($request->all(), [
            'period' => 'required|string',
            'store_id' => 'sometimes|string',
        ]);

        if ($validate->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validate->errors()
            ], 400);
        }

        list($startDate, $endDate, $daysInMonth, $month, $year) = $this->generateDateRange($request->query('period'));

        $productionCost = ProductionCost::where('year', '=', $year)
            ->where('month', '=', $month)
            ->where('store_id', '=', $request->query('store'))
            ->first();

        if ($productionCost) {

            $responseData = DB::table('production_cost_concept AS PCC')
                ->join('production_cost_detail AS PCD', 'PCC.production_cost_concept_id', '=', 'PCD.production_cost_concept_id')
                ->join('production_cost AS PC', 'PC.production_cost_id', '=', 'PCD.production_cost_id')
                ->select('PCC.production_cost_concept_id', 'PCC.concept_name', 'PCC.percentage')
                ->whereNull('PCC.parent_concept_id')
                ->whereNotNull('PCC.percentage')
                ->where('PC.month', $month)
                ->where('PC.year', $year)
                ->where('PC.store_id', $request->query('store'))
                ->get();

            $data = new \stdClass();
            $data->productionCost = $productionCost;
            $data->mainConcepts = $responseData;


            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        }

        return response()->json([
            'success' => false,
            'data' => null
        ]);
    }

    /**
     *
     public function show($recipeID) {
        try {
            $recipe = Recipe::with(['product', 'createUser', 'updateUser', 'productLinks'])->where('status', '=', 1)
            ->findOrFail($recipeID);

            $presentations = [];
            $pricesProduct = SpGetProductPresentations::getAssociative(
                SpGetProductPresentations::MODE_COMBOBOX_WITH_PRICES,
                [$recipe->product_id],
                Currency::PEN,
                1
            );

            // SpGetProductPresentations::execute([$recipe->product_id]);

            $presentation = null;

            if ($pricesProduct && count($pricesProduct) > 0) {
                $presentationsResult = $pricesProduct[$recipe->product_id];
                foreach ($presentationsResult as $pres) {
                    $presentationItem = new \stdClass();
                    $presentationItem = $pres;
                    $presentationItem->mprice = floatVal($pres->mprice);
                    $presentationItem->aprice = floatVal($pres->aprice);
                    $presentationItem->wprice = floatVal($pres->wprice);
                    $presentationItem->imprice = floatVal($pres->imprice);
                    $presentationItem->iaprice = floatVal($pres->iaprice);
                    $presentationItem->iwprice = floatVal($pres->iwprice);

                    if ($recipe->equivalence === $presentationItem->equivalence) {
                        $presentation = $presentationItem;
                    }

                    $presentations[$presentationItem->equivalence] = $presentationItem;
                }
            }


            if (!$presentation) {
                $presentation = reset($presentations) ?? ["iaprice" => 0];
            }



            $product_links = ProductLink::with(['merchandise' => function ($query) {
                $query->select('product_id', 'subline_id', 'mark_id')->with([
                    'product' => function ($productQuery) {
                        $productQuery->select('product_id', 'product_name');
                    },
                    'subline' => function ($sublineQuery) {
                        $sublineQuery->select('subline_id', 'subline_name', 'line_id')->with(['line' => function ($queryLine) {
                            $queryLine->select('line_id', 'line_name');
                        }]);
                    },
                    'mark' => function ($markQuery) {
                        $markQuery->select('mark_id', 'mark_name');
                    }
                ]);
            }])
                ->where('product_parent_id', '=', $recipe->product_id)
                ->get(['product_link_id', 'equivalence', 'product_id', 'quantity', 'unit_cost']);

                $ingredients = [];

            $ids = [];

            $queryResults = [];

            foreach ($product_links as $result) {
                $ids[] = $result->product_id;
                $product_link = new \stdClass();
                $product_link->product_link_id = $result->product_link_id;
                $product_link->productID = $result->product_id;
                $product_link->productName = $result->merchandise->product->product_name;
                $product_link->sublineName = $result->merchandise->subline->subline_name;
                $product_link->lineName = $result->merchandise->subline->line->line_name;
                $product_link->markName = $result->merchandise->mark->mark_name;
                $product_link->unitCost = $result->unit_cost;
                $product_link->presQuantity = $result->quantity;
                $product_link->tCost = $result->unit_cost * $result->quantity;
                $product_link->equivalence = $result->equivalence;
                $product_link->presentation = new \stdClass();
                $product_link->presentation->cost = 0;
                $product_link->presentation->icost = 0;
                $queryResults[] = $product_link;
            }

            $productPresentations = SpGetProductPresentations::getAssociative(
                SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES,
                $ids,
                Currency::PEN,
                1
            );

            MerchandiseController::processProductPresentations($queryResults, $productPresentations, true, true);


            $data = [
                "initialFormData" => [
                    "selectedProduct" => [
                        "productName" => $recipe->product->product_name,
                        "productID" => $recipe->product_id,
                        "presentation" => $presentation,
                        "presentations" => $presentations,
                    ],
                    'presQuantity' => $recipe->pres_quantity,
                    'equivalence' => $recipe->equivalence,
                    "description" => $recipe->description,
                    "totalCost" => $recipe->total_material_cost,
                ],
                "initialTableData" => $queryResults,
                "initialCost" => [
                    "totalMaterialCost" => $recipe->total_material_cost,
                    "marginErrorPercentage" => $recipe->margin_error_percentage,
                    "estimatedMaterialPercentage" => $recipe->estimated_material_percentage,
                    "taxPercentage" => $recipe->tax_percentage,
                    "estimatedPrice" => $recipe->estimated_price,
                    "price" => $recipe->price,
                    "realMaterialPercentage" => $recipe->real_material_percentage,
                    ]

                ];

            $recipe['ingredients'] = $ingredients;

            return response()->json([
                'success' => true,
                'data' => $data,
            ]);

        } catch (\Exception $ex) {
            Log::error('Error retrieving recipe: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error retrieving recipe: ' . $ex->getMessage(),
            ], 500);
        }
    }
    */

    public function store(Request $request) {
        $validate = Validator::make($request->all(), [
            'month' => 'required|integer',
            'year' => 'required|integer',
            'store_id' => 'required|integer',
            'work_days' => 'required|integer'
        ]);

        if ($validate->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validate->errors(),
            ], 400);
        }

        try {

            DB::beginTransaction();

            $productionCost = ProductionCost::create($request->all());

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $productionCost,
            ], 201);

        } catch (\Exception $ex) {
            Log::error('Error creating production cost: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error creating production cost: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function update(Request $request, $id) {
        $validate = Validator::make($request->all(), [
            'month' => 'required|integer',
            'year' => 'required|integer',
            'store_id' => 'required|integer',
            'work_days' => 'required|integer'
        ]);

        if ($validate->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validate->errors(),
            ], 400);
        }

        try {
            DB::beginTransaction();

            $productionCost = ProductionCost::find($id);
            if (!$productionCost) {
                return response()->json([
                    'success' => false,
                    'error' => 'Production cost not found',
                ], 404);
            }

            $productionCost->update($request->all());

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $productionCost,
            ], 200);

        } catch (\Exception $ex) {
            DB::rollBack();
            Log::error('Error updating production cost: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error updating production cost: ' . $ex->getMessage(),
            ], 500);
        }
    }

    private static function generateDateRange($period) {
        list($month, $year) = explode('-', $period);
        $month = (int) $month;
        $year = (int) $year;
        $startDate = (new \DateTime())->setDate($year, $month, 1)->format('Y-m-d');

        $currentDate = new \DateTime();

        $endDate = '';
        $daysInMonth = '';

        if ($month == $currentDate->format('n') && $year == $currentDate->format('Y')) {
            $endDate = $currentDate->format('Y-m-d');
            $daysInMonth = $currentDate->format('j');
        } else {
            $endDate = (new \DateTime())->setDate($year, $month, 1)->modify('last day of this month')->format('Y-m-d');
            $daysInMonth = (new \DateTime())->setDate($year, $month, 1)->format('t');
        }

        return [$startDate, $endDate, $daysInMonth, $month, $year];
    }

}
