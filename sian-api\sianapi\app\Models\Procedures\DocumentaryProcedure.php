<?php

namespace App\Models\Procedures;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

// use Illuminate\Database\Eloquent\Casts\Attribute;

class DocumentaryProcedure extends Model
{
    use HasFactory;

    public function type_documentary_procedure()
    {
        return $this->belongsTo(TypeDocumentaryProcedure::class);
    }

    public function user()
    {
        // return $this->belongsTo(User::class);
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }

    // protected function created_at(): Attribute
    // {
    //     return new Attribute()
    // }
    
    public function getPublishedAtAttribute()
    {
        return $this->created_at->format('d/m/Y');
    }

    // public function created_at()
    // {
    //     return $this->created_at->format('d/m/Y');
    // }

}
