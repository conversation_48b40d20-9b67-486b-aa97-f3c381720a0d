<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Claim
 * 
 * @property int $claim_id
 * @property string $claim_code
 * @property string $identification_type
 * @property string $identification_number
 * @property string $person_name
 * @property string $product_type
 * @property string $product_description
 * @property Carbon $claim_date
 * @property Carbon $register_date
 * @property string $type
 * @property string $detail
 * @property string $state
 * @property int|null $parent_id
 * @property string|null $response
 * @property int|null $user_response
 * @property Carbon|null $response_date
 * @property bool $show_username
 * 
 * @property Claim|null $claim
 * @property Person|null $person
 * @property Collection|Claim[] $claims
 *
 * @package App\Models
 */
class Claim extends Model
{
	protected $table = 'claim';
	protected $primaryKey = 'claim_id';
	public $timestamps = false;

	protected $casts = [
		'parent_id' => 'int',
		'user_response' => 'int',
		'show_username' => 'bool'
	];

	protected $dates = [
		'claim_date',
		'register_date',
		'response_date'
	];

	protected $fillable = [
		'claim_code',
		'identification_type',
		'identification_number',
		'person_name',
		'product_type',
		'product_description',
		'claim_date',
		'register_date',
		'type',
		'detail',
		'state',
		'parent_id',
		'response',
		'user_response',
		'response_date',
		'show_username'
	];

	public function claim()
	{
		return $this->belongsTo(Claim::class, 'parent_id');
	}

	public function person()
	{
		return $this->belongsTo(Person::class, 'user_response');
	}

	public function claims()
	{
		return $this->hasMany(Claim::class, 'parent_id');
	}
}
