<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Combination
 * 
 * @property int $combination_id
 * @property int $level1_id
 * @property int $level2_id
 * @property int $level3_id
 * @property int $level4_id
 * @property int $level5_id
 * @property string $combination_name
 * @property bool $status
 * 
 * @property CostLevelItem $cost_level_item
 * @property Collection|Area[] $areas
 * @property Collection|BusinessUnit[] $business_units
 * @property Collection|Entry[] $entries
 * @property Collection|ExtraInfo[] $extra_infos
 * @property Collection|Item[] $items
 * @property Collection|MobilityPerson[] $mobility_people
 * @property Collection|PayrollMovementHeader[] $payroll_movement_headers
 * @property Collection|Project[] $projects
 *
 * @package App\Models
 */
class Combination extends Model
{
	protected $table = 'combination';
	protected $primaryKey = 'combination_id';
	public $timestamps = false;

	protected $casts = [
		'level1_id' => 'int',
		'level2_id' => 'int',
		'level3_id' => 'int',
		'level4_id' => 'int',
		'level5_id' => 'int',
		'status' => 'bool'
	];

	protected $fillable = [
		'level1_id',
		'level2_id',
		'level3_id',
		'level4_id',
		'level5_id',
		'combination_name',
		'status'
	];

	public function cost_level_item()
	{
		return $this->belongsTo(CostLevelItem::class, 'level5_id');
	}

	public function areas()
	{
		return $this->hasMany(Area::class);
	}

	public function business_units()
	{
		return $this->hasMany(BusinessUnit::class);
	}

	public function entries()
	{
		return $this->hasMany(Entry::class);
	}

	public function extra_infos()
	{
		return $this->hasMany(ExtraInfo::class);
	}

	public function items()
	{
		return $this->hasMany(Item::class);
	}

	public function mobility_people()
	{
		return $this->hasMany(MobilityPerson::class);
	}

	public function payroll_movement_headers()
	{
		return $this->hasMany(PayrollMovementHeader::class);
	}

	public function projects()
	{
		return $this->hasMany(Project::class);
	}
}
