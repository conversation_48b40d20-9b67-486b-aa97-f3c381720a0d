<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ProductionCost
 *
 * @property int $production_cost_id
 * @property int $month
 * @property int $year
 * @property int $store_id
 * @property int|null $business_unit_id
 * @property int $work_days
 *
 * @property-read Store $store
 * @property-read BusinessUnit $businessUnit
 *
 * @package App\Models
 */
class ProductionCost extends Model {
    protected $table = 'production_cost';
    protected $primaryKey = 'production_cost_id';
    public $timestamps = false;

    protected $casts = [
        'production_cost_id' => 'int',
        'month' => 'int',
        'year' => 'int',
        'store_id' => 'int',
        'business_unit_id' => 'int',
        'work_days' => 'int',
    ];

    protected $fillable = [
        'month',
        'year',
        'store_id',
        'business_unit_id',
        'work_days',
    ];

    public function store() {
        return $this->belongsTo(Store::class, 'store_id');
    }

    public function businessUnit() {
        return $this->belongsTo(BusinessUnit::class, 'business_unit_id');
    }

}
