<?php

namespace App\Http\Resources\Asset;

use Illuminate\Http\Resources\Json\JsonResource;

class SimpleFixedAssetResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->fixed_asset_id,
            'name' => $this->description
        ];
    }
}
