<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ECommerceDestiny
 * 
 * @property int $person_id
 * @property string $destiny_type
 * @property string $dept_code
 * @property string $prov_code
 * @property string $dist_code
 * @property int $days
 * @property bool $collectable
 * 
 * @property ECommerceDestinyType $e_commerce_destiny_type
 * @property Geoloc $geoloc
 *
 * @package App\Models
 */
class ECommerceDestiny extends Model
{
	protected $table = 'e_commerce_destiny';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'person_id' => 'int',
		'days' => 'int',
		'collectable' => 'bool'
	];

	protected $fillable = [
		'days',
		'collectable'
	];

	public function e_commerce_destiny_type()
	{
		return $this->belongsTo(ECommerceDestinyType::class, 'person_id')
					->where('e_commerce_destiny_type.person_id', '=', 'e_commerce_destiny.person_id')
					->where('e_commerce_destiny_type.destiny_type', '=', 'e_commerce_destiny.destiny_type');
	}

	public function geoloc()
	{
		return $this->belongsTo(Geoloc::class, 'dept_code')
					->where('geoloc.dept_code', '=', 'e_commerce_destiny.dept_code')
					->where('geoloc.prov_code', '=', 'e_commerce_destiny.prov_code')
					->where('geoloc.dist_code', '=', 'e_commerce_destiny.dist_code');
	}
}
