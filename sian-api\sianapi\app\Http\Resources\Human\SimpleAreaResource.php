<?php

namespace App\Http\Resources\Human;

use Illuminate\Http\Resources\Json\JsonResource;

class SimpleAreaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->area_id,
            'name' => $this->area_name
        ];
    }
}
