<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Cashbox
 *
 * @property int $cashbox_id
 * @property string $cashbox_name
 * @property string $alias
 * @property string|null $description
 * @property string $currency
 * @property bool $incoming
 * @property bool $outgoing
 * @property string $type
 * @property string $movement_type
 * @property float $var_fee
 * @property float $fix_fee
 * @property bool $status
 * @property bool $default
 * @property int|null $store_id
 * @property bool $store_default
 * @property string $account_code
 * @property bool $show_in_print
 * @property bool $is_defaultable
 * @property bool $is_store_defaultable
 * @property bool $is_detraction
 * @property int|null $entity_type_id
 *
 * @property Account $account
 * @property Multitable|null $multitable
 * @property Store|null $store
 * @property Collection|ApiUserCashboxVariant[] $api_user_cashbox_variants
 * @property Collection|ApiUserVariant[] $api_user_variants
 * @property Collection|Movement[] $movements
 * @property Collection|ExtraInfo[] $extra_infos
 * @property Collection|LoanMovement[] $loan_movements
 *
 * @package App\Models
 */
class Cashbox extends Model {
    const TYPE_BANK = 'Banco';
    const TYPE_BOX = 'Caja';
    protected $table = 'cashbox';
    protected $primaryKey = 'cashbox_id';
    public $timestamps = false;

    protected $casts = [
        'incoming' => 'bool',
        'outgoing' => 'bool',
        'var_fee' => 'float',
        'fix_fee' => 'float',
        'status' => 'bool',
        'default' => 'bool',
        'store_id' => 'int',
        'store_default' => 'bool',
        'show_in_print' => 'bool',
        'is_defaultable' => 'bool',
        'is_store_defaultable' => 'bool',
        'is_detraction' => 'bool',
        'entity_type_id' => 'int'
    ];

    protected $fillable = [
        'cashbox_name',
        'alias',
        'description',
        'currency',
        'incoming',
        'outgoing',
        'type',
        'movement_type',
        'var_fee',
        'fix_fee',
        'status',
        'default',
        'store_id',
        'store_default',
        'account_code',
        'show_in_print',
        'is_defaultable',
        'is_store_defaultable',
        'is_detraction',
        'entity_type_id'
    ];

    public function account() {
        return $this->belongsTo(Account::class, 'account_code');
    }

    public function multitable() {
        return $this->belongsTo(Multitable::class, 'entity_type_id');
    }

    public function store() {
        return $this->belongsTo(Store::class);
    }

    public function api_user_cashbox_variants() {
        return $this->hasMany(ApiUserCashboxVariant::class);
    }

    public function api_user_variants() {
        return $this->hasMany(ApiUserVariant::class, 'destination_cashbox_id');
    }

    public function movements() {
        return $this->belongsToMany(Movement::class)
            ->withPivot('total_pen', 'itf_pen', 'port_pen', 'desgravamen_pen', 'interest_pen', 'cash_round_pen', 'cash_round_income_pen', 'real_pen', 'cash_pen', 'total_usd', 'itf_usd', 'port_usd', 'desgravamen_usd', 'interest_usd', 'cash_round_usd', 'cash_round_income_usd', 'real_usd', 'cash_usd', 'type', 'reference_number', 'credit_card', 'calculate_cash_round', 'calculate_change', 'confirmed');
    }

    public function extra_infos() {
        return $this->hasMany(ExtraInfo::class);
    }

    public function loan_movements() {
        return $this->hasMany(LoanMovement::class);
    }
}
