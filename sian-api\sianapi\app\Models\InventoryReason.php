<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class InventoryReason
 * 
 * @property int $inventory_reason_id
 * @property string $inventory_reason_name
 * @property string $alias
 * @property bool $status
 * @property int $inventory_type_id
 * @property string $type_result
 * @property string $action_type
 * @property int $operation_id
 * @property int $document_id
 * @property bool $default
 * @property bool $is_defaultable
 * 
 * @property Document $document
 * @property Multitable $multitable
 * @property Operation $operation
 * @property Collection|InventoryDetailResult[] $inventory_detail_results
 *
 * @package App\Models
 */
class InventoryReason extends Model
{
	protected $table = 'inventory_reason';
	protected $primaryKey = 'inventory_reason_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'inventory_type_id' => 'int',
		'operation_id' => 'int',
		'document_id' => 'int',
		'default' => 'bool',
		'is_defaultable' => 'bool'
	];

	protected $fillable = [
		'inventory_reason_name',
		'alias',
		'status',
		'inventory_type_id',
		'type_result',
		'action_type',
		'operation_id',
		'document_id',
		'default',
		'is_defaultable'
	];

	public function document()
	{
		return $this->belongsTo(Document::class, 'document_id', 'document_id');
	}

	public function multitable()
	{
		return $this->belongsTo(Multitable::class, 'inventory_type_id');
	}

	public function operation()
	{
		return $this->belongsTo(Operation::class, 'operation_id', 'operation_id');
	}

	public function inventory_detail_results()
	{
		return $this->hasMany(InventoryDetailResult::class);
	}
}
