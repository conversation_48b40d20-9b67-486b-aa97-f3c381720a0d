<?php

namespace App\Http\Controllers\Api\V1\Page;

use App\Models\ScheduleSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;


class PageController extends Controller {

    /**
     *
     *
     * @return \Illuminate\Http\Response
     */

    public function show(Request $request) {

        $requestData = $request->all();

        $validator = Validator::make($requestData, [
            'userID' => 'required|string',
            'module' => 'required|string',
            'controller' => 'required|string'
        ]);


        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()
            ], 400);
        }


        try {

            $userID = $request->query('userID');
            $module = $request->query('module');
            $controller = $request->query('controller');

            $results = DB::table('actionassignment AS AA')
                ->select(
                    'AA.module',
                    'AA.controller',
                    'AA.action',
                    DB::raw('MAX(AC.module_child) as module_child'),
                    DB::raw('MAX(AC.controller_child) as controller_child'),
                    DB::raw('MAX(AC.action_child) as action_child')
                )
                ->leftJoin('actionchild AS AC', function ($join) {
                    $join->on('AA.module', '=', 'AC.module_parent')
                        ->on('AA.controller', '=', 'AC.controller_parent')
                        ->on('AA.action', '=', 'AC.action_parent')
                        ->on('AA.owner', '=', 'AC.owner_parent');
                })
                ->where('AA.owner_id', '=', $userID)
                ->where('AA.module', '=', $module)
                ->where('AA.controller', '=', $controller)
                ->whereIn('AA.action', ['store', 'create', 'index', 'view', 'print', 'update', 'uploadAttachments', 'delete', 'costs'])
                ->groupBy('AA.module', 'AA.controller', 'AA.action')
                ->get();


            if ($results) {
                $response = [
                    'success' => true,
                    'data' => $results,
                ];
            } else {

                $response = [
                    'success' => true,
                    'data' => null,
                    'message' => 'No schedule configuration records.',
                ];
            }

            return response()->json($response);

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'error' => 'query error: ' . $e->getMessage()
            ];

            return response()->json($response, 500);
        }

    }


}
