<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\FilesController;
use App\Http\Controllers\SianController;
use App\Models\GlobalVar;
use App\Models\Organization;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Person;
use App\Models\UserAccessToken;
use App\Models\Auth\LoginPivot;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Resources\Administration\SimplePersonResource;
use App\Http\Resources\Human\SimpleUserResource;
use Illuminate\Support\Facades\Validator;
use PHPOpenSourceSaver\JWTAuth\Exceptions\JWTException;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth as FacadesJWTAuth;
use Illuminate\Support\Facades\Log;
use PHPOpenSourceSaver\JWTAuth\JWTAuth;

class AuthController extends Controller {

    public function __construct() {
        $this->middleware('auth:api', ['except' => ['login', 'loginFromSian', 'loginSian', 'register', 'getHash', 'getUser', 'getMenu']]);
    }

    public function login(LoginRequest $request) {
        $a_response = [];
        $code = null;

        $credentials = $request->only('username', 'hash');

        try {
            // $token = Auth::attempt($credentials);
            $token = FacadesJWTAuth::attempt($credentials);

            // Se guarda el Pivot
            LoginPivot::savePivot($token, $credentials['hash']);


            if (!$token) {
                $a_response = [
                    'success' => false,
                    'message' => 'Credenciales inválidas',
                    'error' => 'Credenciales inválidas'
                ];
                $code = 400;
            } else {

                $protocol = (env('IS_HTTPS') == true) ? 'https://' : 'http://';
                $org = Organization::findOrFail(1);
                $sian_url = $protocol . config('app.globals.sian_url');
                $org = [
                    'org_name' => $org->name,
                    'sian_url' => $sian_url,
                    'org_logo' => $sian_url . '/' . $org->name . '/images/logos/logo.png',
                    'background_color' => GlobalVar::value('color') ?? '#ff6b00',
                    'is_retention_agent' => GlobalVar::getValue('is_retention_agent')

                ];

                $user = Auth::user();
                $user->user_token = Hash::make($user->user_id);
                $person = Person::find($user->person_id);

                $payload = FacadesJWTAuth::setToken($token)->getPayload();
                $expiration = $payload['exp'];

                DB::table('user_access_token')->insertGetId([
                    'user_id' => $user->user_id,
                    'user_token' => $user->user_token,
                    'created_at' => now(),
                ]);

                $a_response = [
                    'success' => true,
                    'data' => [
                        'user' => new SimpleUserResource($user),
                        'person' => $person,
                        'org' => $org,
                    ],
                    'authorisation' => [
                        'token' => $token,
                        'type' => 'Bearer',
                        'expires_at' => Carbon::createFromTimestamp($expiration)->toISOString()
                    ],
                    'files' => FilesController::getFileRules()
                ];
            }
        } catch (JWTException $e) {
            $a_response = [
                'success' => false,
                'message' => 'Token no creado',
                'error' => 'Token no creado'
            ];
            $code = 500;
        }

        if (is_null($code)) {
            return response()->json($a_response);
        }
        return response()->json($a_response, $code);
    }

    public function loginFromSian(Request $request) {

        $validate = Validator::make($request->all(), [
            'username' => 'required|string',
            'token' => 'required|string',
        ], [
            'username.required' => 'El campo username es obligatorio.',
            'token.required' => 'El campo token es obligatorio.',
        ]);

        $a_response = [];

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {

            $loginPivot = LoginPivot::where('token', $request->token)->first();


            if ($loginPivot) {
                // El registro fue encontrado, haz lo que necesites con él aquí
                $hash = $loginPivot->password;

                $credentials = ['username' => $request->username, 'hash' => $hash];

                // $token = Auth::attempt($credentials);
                $token = FacadesJWTAuth::attempt($credentials);

                if (!$token) {
                    $a_response = [
                        'success' => false,
                        'message' => 'Credenciales inválidas',
                        'error' => 'Credenciales inválidas'
                    ];
                    // $code = 400;
                } else {
                    $protocol = (env('IS_HTTPS') == true) ? 'https://' : 'http://';
                    $org = Organization::findOrFail(1);
                    $sian_url = $protocol . config('app.globals.sian_url');

                    $org = [
                        'org_name' => $org->name,
                        'sian_url' => $sian_url,
                        'org_logo' => $sian_url . '/' . $org->name . '/images/logos/logo.png',
                        'background_color' => GlobalVar::value('color') ?? '#ff6b00',
                        'is_retention_agent' => GlobalVar::getValue('is_retention_agent')
                    ];

                    $user = Auth::user();
                    $user->user_token = Hash::make($user->user_id);
                    $person = Person::find($user->person_id);

                    DB::table('user_access_token')->insertGetId([
                        'user_id' => $user->user_id,
                        'user_token' => $user->user_token,
                        'created_at' => now(),
                    ]);
                    // Elimino el registro que contiene el password (hash) con el token de SIAN
                    $loginPivot->delete();

                    // Se agrega el Registro con el nuevo token de React
                    LoginPivot::savePivot($token, $hash);

                    $payload = FacadesJWTAuth::setToken($token)->getPayload();
                    $expiration = $payload['exp'];
                    $a_response = [
                        'success' => true,
                        'data' => [
                            'userToken' => $user->user_token,
                            'pathSian' => 'site/afterLogin',
                            'user' => new SimpleUserResource($user),
                            'person' => $person,
                            'org' => $org
                        ],
                        'authorisation' => [
                            'token' => $token,
                            'type' => 'Bearer',
                            'expires_at' => Carbon::createFromTimestamp($expiration)->toISOString()
                        ],
                        'files' => FilesController::getFileRules()
                    ];
                }
            } else {
                // El registro no fue encontrado
                $a_response = [
                    'success' => false,
                    'message' => 'El registro no fue encontrado'
                ];
            }
        }

        return response()->json($a_response);
    }

    public function logoutFromSian(Request $request) {
        $validate = Validator::make($request->all(), [
            'token' => 'required|string',
            'username' => 'required|string',
            'token_user_id' => 'required|string',
        ], [
            'username.required' => 'El campo username es obligatorio.',
            'token.required' => 'El campo token es obligatorio.',
            'token_user_id.required' => 'El campo token ID es obligatorio.',
        ]);

        $a_response = [];

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {

            if (LoginPivot::deletePivot($request->token) && UserAccessToken::deleteAccessToken($request->token_user_id)) {
                Auth::logout(); //Este método invalida el Authtoken de usuario
                $a_response = [
                    'success' => true,
                    'token' => $request->token,
                    'username' => $request->username,
                    'message' => 'La sesión se ha cerrado correctamente'
                ];
            } else {
                $a_response = [
                    'success' => false,
                    'message' => 'El token es incorrecto'
                ];
            }
        }

        return response()->json($a_response);
    }

    public function loginSian(Request $request) {
        $request->validate([
            'username' => 'required|string',
            'hash' => 'required|string',
        ]);

        $validate = Validator::make($request->all(), [
            'username' => 'required|string',
            'hash' => 'required|string',
        ], [
            'username.required' => 'El campo username es obligatorio.',
            'hash.required' => 'El campo hash es obligatorio.',
        ]);

        $a_response = [];

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {
            $user = User::find(1)
                ->where('username', $request->username)
                ->where('hash', $request->hash)->first();

            if (!isset($user)) {
                $a_response = [
                    'success' => false,
                    'message' => 'Usuario no encontrado'
                ];
            } else {
                if (!$user->status) {
                    $a_response = [
                        'success' => false,
                        'message' => 'Usuario inactivo'
                    ];
                } else {
                    $user->user_token = Hash::make($user->user_id);

                    DB::table('user_access_token')->insertGetId([
                        'user_id' => $user->user_id,
                        'user_token' => $user->user_token,
                        'created_at' => now(),
                    ]);

                    $a_response = [
                        'success' => true,
                        'data' => [
                            'user' => new SimpleUserResource($user)
                        ]
                    ];
                }
            }
        }
        return response()->json($a_response);
    }

    public function getUser(Request $request) {

        $validate = Validator::make($request->all(), [
            'id' => 'required|string',
            'username' => 'required|string'
        ], [
            'id.required' => 'El campo id es obligatorio.',
            'username.required' => 'El campo username es obligatorio.',
        ]);

        $a_response = [];

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {
            $userAccessToken = UserAccessToken::where('user_token', $request->id)->first();

            if (!isset($userAccessToken)) {
                $a_response = [
                    'success' => false,
                    'message' => 'Usuario no encontrado'
                ];
            }

            if (!$userAccessToken->user->status) {
                $a_response = [
                    'success' => false,
                    'message' => 'Usuario inactivo'
                ];
            } else {
                $a_response = [
                    'success' => true,
                    'data' => [
                        'user' => new SimpleUserResource($userAccessToken->user)
                    ]
                ];
            }
        }
        return response()->json($a_response);
    }

    /**
     * Este método autentica a un usuario con su correo electrónico y contraseña.
     * Cuando un usuario se autentica correctamente, el método de Auth fachada
     * devuelve el token JWT. El token generado se recupera y se devuelve como JSON
     * con el objeto de usuarioattempt()
     */
    public function getHash(Request $request) {
        $request->validate([
            'key' => 'required|string',
        ]);
        $hash = Hash::make($request->key);

        if ($hash == "") {
            return response()->json([
                'status' => 'error',
                'message' => 'No se puedo obtener el hash.',
            ], 401);
        }

        return response()->json([
            'status' => 'success',
            'hash' => $hash
        ]);
    }

    /**
     * este método crea el registro de usuario e inicia sesión
     * en el usuario con generaciones de token
     */
    public function register(Request $request) {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string|max:64',
            'person_id' => 'required|string',
            'email_address' => 'required|string',
            'phone_number' => 'required|string',
        ]);

        $user = User::create([
            'username' => $request->username,
            'password' => Hash::make($request->password),
            'person_id' => $request->person_id,
            'email_address' => $request->email_address,
            'phone_number' => $request->phone_number,
            'status' => 1,
            'level' => 1
        ]);

        $token = Auth::login($user);
        return response()->json([
            'status' => 'success',
            'message' => 'User created successfully',
            'user' => $user,
            'authorisation' => [
                'token' => $token,
                'type' => 'bearer',
            ]
        ]);
    }

    /**
     * Este método invalida el Authtoken de usuario
     */
    public function logout() {
        Auth::logout();
        return response()->json([
            'status' => 'success',
            'message' => 'Successfully logged out',
        ]);
    }

    /**
     * Este método invalida el Authtoken de
     * usuario y genera un nuevo token
     */
    public function refresh() {
        return response()->json([
            'status' => 'success',
            'user' => Auth::user(),
            'authorisation' => [
                'token' => Auth::refresh(),
                'type' => 'bearer',
            ]
        ]);
    }
}
