<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Object
 *
 * @property int $object_id
 * @property string $object_name
 * @property string $alias
 * @property string|null $description
 * @property bool $status
 * @property bool $outstanding
 * @property string $model
 * @property string $secret
 * @property int $subcategory_id
 * @property bool $web_enabled
 *
 * @property Subcategory $subcategory
 * @property Collection|Faq[] $faqs
 * @property Collection|Gallery[] $galleries
 * @property Collection|Html[] $htmls
 * @property Collection|Image[] $images
 * @property Collection|Link[] $links
 * @property Collection|News[] $news
 * @property Collection|Video[] $videos
 *
 * @package App\Models
 */
class ObjectModel extends Model
{
	protected $table = 'object';
	protected $primaryKey = 'object_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'outstanding' => 'bool',
		'subcategory_id' => 'int',
		'web_enabled' => 'bool'
	];

	protected $hidden = [
		'secret'
	];

	protected $fillable = [
		'object_name',
		'alias',
		'description',
		'status',
		'outstanding',
		'model',
		'secret',
		'subcategory_id',
		'web_enabled'
	];

	public function subcategory()
	{
		return $this->belongsTo(Subcategory::class);
	}

	public function faqs()
	{
		return $this->hasMany(Faq::class);
	}

	public function galleries()
	{
		return $this->hasMany(Gallery::class);
	}

	public function htmls()
	{
		return $this->hasMany(Html::class);
	}

	public function images()
	{
		return $this->hasMany(Image::class);
	}

	public function links()
	{
		return $this->hasMany(Link::class);
	}

	public function news()
	{
		return $this->hasMany(News::class);
	}

	public function videos()
	{
		return $this->hasMany(Video::class);
	}
}
