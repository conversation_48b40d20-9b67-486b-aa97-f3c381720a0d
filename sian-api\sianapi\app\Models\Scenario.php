<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Scenario
 * 
 * @property int $scenario_id
 * @property string $route
 * @property string $title
 * @property string $description
 * @property string $direction
 * @property string $type
 * @property string $scopes
 * @property bool $auto_person_id
 * @property bool $auto_expiration
 * @property bool $auto_send_email
 * @property bool $auto_deactivation
 * @property int $hours_to_deactivate
 * @property string|null $default_currency
 * @property string $module
 * @property int|null $accounting_file_id
 * @property int $operation_count
 * @property int $document_count
 * @property int $email_count
 * @property bool|null $edit_directly
 * @property bool|null $dispatch_directly
 * @property bool|null $upgrade_directly
 * @property bool|null $pay_directly
 * @property bool|null $apply_directly
 * @property bool|null $null_directly
 * @property bool|null $remove_directly
 * @property string|null $column
 * @property bool $to_do
 * @property string|null $affect_credit
 * @property bool $kardex_rlock
 * @property bool $kardex_clock
 * @property bool $kardex_unlock
 * @property bool|null $stock_mode
 * @property bool|null $validate_stock
 * @property bool $allow_logs
 * @property bool|null $not_scheduled_to_pay
 * @property bool|null $not_scheduled_to_redeem
 * @property string $extra_info_include
 * @property string $extra_info_require
 * 
 * @property AccountingFile|null $accounting_file
 * @property Collection|EntryGroup[] $entry_groups
 * @property Collection|Movement[] $movements
 * @property Collection|MovementLink[] $movement_links
 * @property Collection|MovementLogType[] $movement_log_types
 * @property Collection|ScenarioControl[] $scenario_controls
 * @property ScenarioSetting $scenario_setting
 *
 * @package App\Models
 */
class Scenario extends Model
{
	protected $table = 'scenario';
	protected $primaryKey = 'route';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'scenario_id' => 'int',
		'auto_person_id' => 'bool',
		'auto_expiration' => 'bool',
		'auto_send_email' => 'bool',
		'auto_deactivation' => 'bool',
		'hours_to_deactivate' => 'int',
		'accounting_file_id' => 'int',
		'operation_count' => 'int',
		'document_count' => 'int',
		'email_count' => 'int',
		'edit_directly' => 'bool',
		'dispatch_directly' => 'bool',
		'upgrade_directly' => 'bool',
		'pay_directly' => 'bool',
		'apply_directly' => 'bool',
		'null_directly' => 'bool',
		'remove_directly' => 'bool',
		'to_do' => 'bool',
		'kardex_rlock' => 'bool',
		'kardex_clock' => 'bool',
		'kardex_unlock' => 'bool',
		'stock_mode' => 'bool',
		'validate_stock' => 'bool',
		'allow_logs' => 'bool',
		'not_scheduled_to_pay' => 'bool',
		'not_scheduled_to_redeem' => 'bool'
	];

	protected $fillable = [
		'scenario_id',
		'title',
		'description',
		'direction',
		'type',
		'scopes',
		'auto_person_id',
		'auto_expiration',
		'auto_send_email',
		'auto_deactivation',
		'hours_to_deactivate',
		'default_currency',
		'module',
		'accounting_file_id',
		'operation_count',
		'document_count',
		'email_count',
		'edit_directly',
		'dispatch_directly',
		'upgrade_directly',
		'pay_directly',
		'apply_directly',
		'null_directly',
		'remove_directly',
		'column',
		'to_do',
		'affect_credit',
		'kardex_rlock',
		'kardex_clock',
		'kardex_unlock',
		'stock_mode',
		'validate_stock',
		'allow_logs',
		'not_scheduled_to_pay',
		'not_scheduled_to_redeem',
		'extra_info_include',
		'extra_info_require'
	];

	public function accounting_file()
	{
		return $this->belongsTo(AccountingFile::class);
	}

	public function module()
	{
		return $this->belongsTo(Module::class, 'module');
	}

	public function entry_groups()
	{
		return $this->hasMany(EntryGroup::class, 'route');
	}

	public function movements()
	{
		return $this->hasMany(Movement::class, 'route');
	}

	public function movement_links()
	{
		return $this->hasMany(MovementLink::class, 'route');
	}

	public function movement_log_types()
	{
		return $this->hasMany(MovementLogType::class, 'route');
	}

	public function scenario_controls()
	{
		return $this->hasMany(ScenarioControl::class, 'route');
	}

	public function scenario_setting()
	{
		return $this->hasOne(ScenarioSetting::class, 'scenario_id', 'scenario_id');
	}
}
