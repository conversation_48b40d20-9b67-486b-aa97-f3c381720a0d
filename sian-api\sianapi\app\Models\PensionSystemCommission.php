<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PensionSystemCommission
 * 
 * @property int $pension_system_commission_id
 * @property int|null $pension_system_id
 * @property Carbon|null $date_ini
 * @property Carbon|null $date_end
 * @property float|null $contribution
 * @property float|null $insurance_tax
 * @property float|null $var_comission
 * @property float|null $mix_comission
 * @property float|null $max_remuneration
 * 
 * @property PensionSystem|null $pension_system
 *
 * @package App\Models
 */
class PensionSystemCommission extends Model
{
	protected $table = 'pension_system_commission';
	protected $primaryKey = 'pension_system_commission_id';
	public $timestamps = false;

	protected $casts = [
		'pension_system_id' => 'int',
		'contribution' => 'float',
		'insurance_tax' => 'float',
		'var_comission' => 'float',
		'mix_comission' => 'float',
		'max_remuneration' => 'float'
	];

	protected $dates = [
		'date_ini',
		'date_end'
	];

	protected $fillable = [
		'pension_system_id',
		'date_ini',
		'date_end',
		'contribution',
		'insurance_tax',
		'var_comission',
		'mix_comission',
		'max_remuneration'
	];

	public function pension_system()
	{
		return $this->belongsTo(PensionSystem::class);
	}
}
