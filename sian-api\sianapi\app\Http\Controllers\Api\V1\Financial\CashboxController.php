<?php

namespace App\Http\Controllers\Api\V1\Financial;

use App\Models\Cashbox;
use App\Models\CommercialMovement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;

class CashboxController extends Controller {

    /**
     *
     *
     * @return \Illuminate\Http\Response
     */

    public function getBoxAccountByUser(Request $request) {
        $validator = Validator::make($request->all(), [
            "userID" => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()
            ], 400);
        }
        try {

            $userID = $request->query('userID');

            $response = DB::table('user as U')
                ->select(
                    'C.cashbox_id as cashboxID',
                    'C.cashbox_name as cashboxName',
                    'E.multi_id as multiID',
                    'E.description AS bankName',
                    'E.abbreviation AS bank',
                    'C.type',
                    'C.movement_type as movementType',
                    'C.currency',
                    'C.store_id as storeID',
                    'C.is_detraction as isDetraction'
                )
                ->join('owner_pair as OPP', function ($join) {
                    $join->on('OPP.type', '=', DB::raw("'UserCashbox'"))
                        ->on('OPP.owner_id', '=', 'U.user_id')
                        ->on('OPP.owner', '=', DB::raw("'User'"));
                })
                ->join('owner_pair as OPH', function ($join) {
                    $join->on('OPH.type', '=', 'OPP.type')
                        ->on('OPH.owner', '=', DB::raw("'Cashbox'"))
                        ->on('OPH.parent_id', '=', 'OPP.pair_id');
                })
                ->join('cashbox as C', 'C.cashbox_id', '=', 'OPH.owner_id')
                ->leftJoin('multitable as E', 'E.multi_id', '=', 'C.entity_type_id')
                ->where('C.status', true)
                ->where('C.outgoing', true)
                ->where(function ($query) {
                    $query->whereRaw("FIND_IN_SET('" . CommercialMovement::PAYMENT_METHOD_CASH  . "', movement_type) > 0")
                        ->orWhereRaw("FIND_IN_SET('" . CommercialMovement::PAYMENT_METHOD_DEPOSIT . "', movement_type) > 0")
                        ->orWhereRaw("FIND_IN_SET('" . CommercialMovement::PAYMENT_METHOD_CHECK . "', movement_type) > 0");
                })
                ->where('C.type', '=', Cashbox::TYPE_BANK)
                ->where('U.person_id', '=', $userID)
                ->get();


            foreach ($response as $resp) {
                if ($resp->storeID) {
                    $storeID = $resp->storeID;
                    $store = DB::table('store as S')
                        ->where('S.store_id', '=', $storeID)
                        ->select(
                            'S.store_ID as storeID',
                            'S.store_name as storeName',
                            'S.alias',
                            'S.status',
                            'S.business_unit_id as businessUnitID',
                            'S.transaction_code as transactionCode'

                        )->first();
                    if ($store) {
                        $resp->store = $store;
                        if ($store->businessUnitID) {
                            $business_unit_id = $store->businessUnitID;
                            $businessUnit = DB::table('business_unit as BU')
                                ->where('BU.business_unit_id', '=', $business_unit_id)
                                ->select(
                                    'BU.business_unit_id as businessUnitID',
                                    'BU.business_unit_name as businessUnitName',
                                    'BU.alias',
                                    'BU.combination_id as combinationID '
                                )->first();
                            if ($businessUnit) {
                                $resp->store->businessUnit = $businessUnit;
                            }

                        }
                    }
                }

            }

            return response()->json($response);

        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }
}
