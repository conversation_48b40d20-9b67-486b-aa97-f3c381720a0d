<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ActionLog
 * 
 * @property int $action_log_id
 * @property string $module
 * @property string $controller
 * @property string $action
 * @property Carbon $date
 * 
 *
 * @package App\Models
 */
class ActionLog extends Model
{
	protected $table = 'action_log';
	protected $primaryKey = 'action_log_id';
	public $timestamps = false;

	protected $dates = [
		'date'
	];

	protected $fillable = [
		'module',
		'controller',
		'action',
		'date'
	];

	public function action()
	{
		return $this->belongsTo(Action::class, 'module')
					->where('action.module', '=', 'action_log.module')
					->where('action.controller', '=', 'action_log.controller')
					->where('action.action', '=', 'action_log.action');
	}
}
