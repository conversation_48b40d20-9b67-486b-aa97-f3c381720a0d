<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Warehouse
 * 
 * @property int $warehouse_id
 * @property string $warehouse_name
 * @property string $alias
 * @property string|null $description
 * @property bool $incoming
 * @property bool $outgoing
 * @property bool $commercial_treatment
 * @property bool $frontend
 * @property string|null $acronyme
 * @property bool $status
 * @property bool $default
 * @property int $store_id
 * @property bool $store_default
 * @property string $warehouse_type
 * @property bool $is_defaultable
 * @property bool $is_store_defaultable
 * @property bool $web_enabled
 * @property bool $physical
 * @property bool $level_quantity
 * @property string $level1_name
 * @property string $level2_name
 * @property string $level3_name
 * @property string $level4_name
 * 
 * @property Store $store
 * @property Collection|ApiUserVariant[] $api_user_variants
 * @property Collection|BatchMovement[] $batch_movements
 * @property Collection|ExtraInfo[] $extra_infos
 * @property Collection|Inventory[] $inventories
 * @property Collection|Kardex[] $kardexes
 * @property Collection|KardexItem[] $kardex_items
 * @property Collection|KardexRange[] $kardex_ranges
 * @property Collection|MerchandiseMaster[] $merchandise_masters
 * @property Collection|ProductTypeSetting[] $product_type_settings
 * @property Collection|PurchaseOrder[] $purchase_orders
 * @property Collection|WarehouseArea[] $warehouse_areas
 * @property Collection|Movement[] $movements
 *
 * @package App\Models
 */
class Warehouse extends Model
{
	protected $table = 'warehouse';
	protected $primaryKey = 'warehouse_id';
	public $timestamps = false;

	protected $casts = [
		'incoming' => 'bool',
		'outgoing' => 'bool',
		'commercial_treatment' => 'bool',
		'frontend' => 'bool',
		'status' => 'bool',
		'default' => 'bool',
		'store_id' => 'int',
		'store_default' => 'bool',
		'is_defaultable' => 'bool',
		'is_store_defaultable' => 'bool',
		'web_enabled' => 'bool',
		'physical' => 'bool',
		'level_quantity' => 'bool'
	];

	protected $fillable = [
		'warehouse_name',
		'alias',
		'description',
		'incoming',
		'outgoing',
		'commercial_treatment',
		'frontend',
		'acronyme',
		'status',
		'default',
		'store_id',
		'store_default',
		'warehouse_type',
		'is_defaultable',
		'is_store_defaultable',
		'web_enabled',
		'physical',
		'level_quantity',
		'level1_name',
		'level2_name',
		'level3_name',
		'level4_name'
	];

	public function store()
	{
		return $this->belongsTo(Store::class);
	}

	public function api_user_variants()
	{
		return $this->hasMany(ApiUserVariant::class);
	}

	public function batch_movements()
	{
		return $this->hasMany(BatchMovement::class);
	}

	public function extra_infos()
	{
		return $this->hasMany(ExtraInfo::class);
	}

	public function inventories()
	{
		return $this->hasMany(Inventory::class);
	}

	public function kardexes()
	{
		return $this->hasMany(Kardex::class);
	}

	public function kardex_items()
	{
		return $this->hasMany(KardexItem::class);
	}

	public function kardex_ranges()
	{
		return $this->hasMany(KardexRange::class);
	}

	public function merchandise_masters()
	{
		return $this->hasMany(MerchandiseMaster::class);
	}

	public function product_type_settings()
	{
		return $this->hasMany(ProductTypeSetting::class);
	}

	public function purchase_orders()
	{
		return $this->hasMany(PurchaseOrder::class);
	}

	public function warehouse_areas()
	{
		return $this->hasMany(WarehouseArea::class);
	}

	public function movements()
	{
		return $this->belongsToMany(Movement::class, 'warehouse_movement')
					->withPivot('munit_quantity', 'munit_balance', 'bnet_pen', 'inet_pen', 'enet_pen', 'dnet_pen', 'net_pen', 'bnet_usd', 'inet_usd', 'enet_usd', 'dnet_usd', 'net_usd', 'allow_duplicate', 'transport_data', 'transport_company_id', 'transportist_id', 'fixed_asset_id', 'locker_request_id', 'inventory_id');
	}
}
