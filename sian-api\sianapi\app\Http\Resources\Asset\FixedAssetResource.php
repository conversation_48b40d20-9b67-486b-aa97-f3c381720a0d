<?php

namespace App\Http\Resources\Asset;

use App\Http\Resources\Administration\SimplePersonResource;
use App\Http\Resources\Accounting\SimpleAccountResource;
use App\Http\Resources\Administration\SimpleBusinessUnitResource;
use App\Http\Resources\Asset\SimpleDepreciationGroupResource;
use App\Http\Resources\Common\SimpleMovementResource;
use App\Http\Resources\Common\SimpleMultitableResource;
use App\Http\Resources\Human\SimpleAreaResource;
use App\Http\Resources\Logistic\SimpleMarkResource;
use Illuminate\Http\Resources\Json\JsonResource;

class FixedAssetResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->fixed_asset_id,
            'code' => $this->code,
            'description' => $this->description,
            'model' => $this->model,
            'serie' => $this->serie,
            'buyDate' => $this->buy_date,
            'initUsedDate' => $this->init_used_date,
            'requiredDepreciation' => $this->required_depreciation,
            'documentAuthorizationChangeMethod' => $this->document_authorization_change_method,
            'acquisitionCost' => $this->acquisition_cost,
            'balanceCost' => $this->balance_cost,
            'historicalDepreciation' => $this->historical_depreciation,
            'status' => $this->status,
            'usefulLife' => $this->useful_life,
            'lastFixedAssetMovementId' => $this->last_fixed_asset_movement_id,
            'ubication' => isset($this->fixedAssetMovement) ? $this->fixedAssetMovement->ubication : null,
            'accountAsset' => new SimpleAccountResource($this->accountAsset),
            'mark' => new SimpleMarkResource($this->mark),
            'depreciationGroup' =>  new SimpleDepreciationGroupResource($this->depreciationGroup),
            'typeSunat' => new SimpleMultitableResource($this->typeSunat),
            'statusSunat' =>  new SimpleMultitableResource($this->statusSunat),
            'responsible' => isset($this->fixedAssetMovement) ? new SimplePersonResource($this->fixedAssetMovement->responsible) : null,
            'area' => isset($this->fixedAssetMovement) ? new SimpleAreaResource($this->fixedAssetMovement->area) : null,
            'bussinessUnit' => isset($this->fixedAssetMovement) ? new SimpleBusinessUnitResource($this->fixedAssetMovement->businessUnit) : null,
            'movement' =>  new SimpleMovementResource($this->movement)
        ];
    }
}
