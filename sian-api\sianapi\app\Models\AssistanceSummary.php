<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AssistanceSummary
 * 
 * @property int $assistance_summary_id
 * @property int $year
 * @property int|null $week
 * @property bool $split_week
 * @property int $period
 * @property int $work_days
 * @property string $observation
 * @property bool $status
 * @property int $regime_id
 * 
 * @property Regime $regime
 * @property Collection|Employee[] $employees
 * @property Collection|PayrollMovement[] $payroll_movements
 *
 * @package App\Models
 */
class AssistanceSummary extends Model
{
	protected $table = 'assistance_summary';
	protected $primaryKey = 'assistance_summary_id';
	public $timestamps = false;

	protected $casts = [
		'year' => 'int',
		'week' => 'int',
		'split_week' => 'bool',
		'period' => 'int',
		'work_days' => 'int',
		'status' => 'bool',
		'regime_id' => 'int'
	];

	protected $fillable = [
		'year',
		'week',
		'split_week',
		'period',
		'work_days',
		'observation',
		'status',
		'regime_id'
	];

	public function regime()
	{
		return $this->belongsTo(Regime::class);
	}

	public function employees()
	{
		return $this->belongsToMany(Employee::class, 'assistance_summary_employee', 'assistance_summary_id', 'person_id')
					->withPivot('assistance_summary_employee_id', 'assistance_summary_employee_code', 'work_days_calc', 'work_days_employee', 'absences', 'work_hours', 'extrahours1', 'extrahours2', 'night_hours', 'holiday_hours', 'lateness');
	}

	public function payroll_movements()
	{
		return $this->hasMany(PayrollMovement::class);
	}
}
