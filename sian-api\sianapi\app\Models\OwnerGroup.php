<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class OwnerGroup
 * 
 * @property int $owner_group_id
 * @property string $owner_group_name
 * @property string $alias
 * @property bool $status
 * @property string $owner
 * @property int $owner_count
 * @property bool $is_visible
 * 
 *
 * @package App\Models
 */
class OwnerGroup extends Model
{
	protected $table = 'owner_group';
	protected $primaryKey = 'owner_group_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'owner_count' => 'int',
		'is_visible' => 'bool'
	];

	protected $fillable = [
		'owner_group_name',
		'alias',
		'status',
		'owner',
		'owner_count',
		'is_visible'
	];

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}
}
