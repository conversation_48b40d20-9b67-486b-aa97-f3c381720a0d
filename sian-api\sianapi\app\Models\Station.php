<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Station
 * 
 * @property int $station_id
 * @property string $station_name
 * @property string $alias
 * @property bool $status
 * @property bool $frontend
 * @property int $area_id
 * @property int $store_id
 * 
 * @property Area $area
 * @property Store $store
 * @property Collection|Employee[] $employees
 * @property Collection|JobOffer[] $job_offers
 *
 * @package App\Models
 */
class Station extends Model
{
	protected $table = 'station';
	protected $primaryKey = 'station_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'frontend' => 'bool',
		'area_id' => 'int',
		'store_id' => 'int'
	];

	protected $fillable = [
		'station_name',
		'alias',
		'status',
		'frontend',
		'area_id',
		'store_id'
	];

	public function area()
	{
		return $this->belongsTo(Area::class);
	}

	public function store()
	{
		return $this->belongsTo(Store::class);
	}

	public function employees()
	{
		return $this->hasMany(Employee::class);
	}

	public function job_offers()
	{
		return $this->hasMany(JobOffer::class);
	}
}
