<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AccountingPeriod
 * 
 * @property int $accounting_period_id
 * @property int $accounting_file_id
 * @property int $year
 * @property int $period
 * @property int $correlative
 * 
 * @property AccountingYear $accounting_year
 * @property Collection|AccountingDay[] $accounting_days
 * @property Collection|AccountingMovement[] $accounting_movements
 *
 * @package App\Models
 */
class AccountingPeriod extends Model
{
	protected $table = 'accounting_period';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'accounting_period_id' => 'int',
		'accounting_file_id' => 'int',
		'year' => 'int',
		'period' => 'int',
		'correlative' => 'int'
	];

	protected $fillable = [
		'accounting_period_id',
		'correlative'
	];

	public function accounting_year()
	{
		return $this->belongsTo(AccountingYear::class, 'accounting_file_id')
					->where('accounting_year.accounting_file_id', '=', 'accounting_period.accounting_file_id')
					->where('accounting_year.year', '=', 'accounting_period.year');
	}

	public function accounting_days()
	{
		return $this->hasMany(AccountingDay::class, 'accounting_file_id');
	}

	public function accounting_movements()
	{
		return $this->hasMany(AccountingMovement::class, 'accounting_file_id');
	}
}
