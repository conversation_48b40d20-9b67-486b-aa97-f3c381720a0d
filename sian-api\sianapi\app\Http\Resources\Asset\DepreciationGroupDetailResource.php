<?php

namespace App\Http\Resources\Asset;

use Illuminate\Http\Resources\Json\JsonResource;

class DepreciationGroupDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->depreciation_group_detail_id,
            'year' => $this->year,
            'annualRate' => $this->annual_rate,
            'period01' => $this->period01,
            'period02' => $this->period02,
            'period03' => $this->period03,
            'period04' => $this->period04,
            'period05' => $this->period05,
            'period06' => $this->period06,
            'period07' => $this->period07,
            'period08' => $this->period08,
            'period09' => $this->period09,
            'period10' => $this->period10,
            'period11' => $this->period11,
            'period12' => $this->period12
        ];
    }
}
