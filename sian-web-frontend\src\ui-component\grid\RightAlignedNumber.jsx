import { Typography } from '@mui/material';

const RightAlignedNumber = ({ value, decimals = 2, ...props }) => {
    const formattedValue = typeof value === 'number' ? value.toFixed(decimals) : parseFloat(value || 0).toFixed(decimals);

    return (
        <Typography sx={{ textAlign: 'right', px: 1 }} {...props}>
            {formattedValue}
        </Typography>
    );
};

export default RightAlignedNumber;
