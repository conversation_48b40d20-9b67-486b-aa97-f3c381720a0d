<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class MobilityPersonItem
 * 
 * @property int $movement_id
 * @property int $person_id
 * @property int $item_number
 * @property string $type
 * @property float $amount_pen
 * @property float $amount_usd
 * @property string $reason
 * @property string $displacement
 * @property Carbon $register_date
 * 
 * @property MobilityPerson $mobility_person
 *
 * @package App\Models
 */
class MobilityPersonItem extends Model
{
	protected $table = 'mobility_person_item';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'person_id' => 'int',
		'item_number' => 'int',
		'amount_pen' => 'float',
		'amount_usd' => 'float'
	];

	protected $dates = [
		'register_date'
	];

	protected $fillable = [
		'type',
		'amount_pen',
		'amount_usd',
		'reason',
		'displacement',
		'register_date'
	];

	public function mobility_person()
	{
		return $this->belongsTo(MobilityPerson::class, 'movement_id')
					->where('mobility_person.movement_id', '=', 'mobility_person_item.movement_id')
					->where('mobility_person.person_id', '=', 'mobility_person_item.person_id');
	}
}
