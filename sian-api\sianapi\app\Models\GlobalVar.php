<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class GlobalVar
 *
 * @property int $global_var_id
 * @property int $organization_id
 * @property string $color
 * @property float $igv
 * @property string $igv_list
 * @property string $display_currency
 * @property int $sunat_id
 * @property float $perception
 * @property float $retention
 * @property float $retention4
 * @property string|null $detraction
 * @property float $no_client_max_amount_pen
 * @property float $no_client_max_amount_usd
 * @property float $no_retention_max_amount_pen
 * @property float $no_retention_max_amount_usd
 * @property float $no_retention4_max_amount_pen
 * @property float $no_retention4_max_amount_usd
 * @property float $no_detraction_max_amount_pen
 * @property float $no_detraction_max_amount_usd
 * @property float $mobility_person_max_amount_pen
 * @property float $mobility_person_max_amount_usd
 * @property float $uit_amount_pen
 * @property float $uit_amount_usd
 * @property float $max_cash_amount_pen
 * @property float $max_cash_amount_usd
 * @property float $max_rounding_amount_pen
 * @property float $max_rounding_amount_usd
 * @property float $tolerance_pen
 * @property float $tolerance_usd
 * @property bool $commercial_ifixed
 * @property bool $warehouse_ifixed
 * @property float $familyAssigment
 * @property float $socialSecurity
 * @property float $minimumSalary
 * @property int $credit_validity
 * @property int $hours_to_declare
 * @property string $retention4_account_code
 * @property string $missing_account_code
 * @property string $surplus_account_code
 * @property int $account_group_level
 * @property bool $allowEntryOnlyDefaultWarehouse
 * @property bool $ifixed
 * @property bool $require_agreement
 * @property bool $require_model
 * @property bool $require_image
 * @property bool $require_profit_margin
 * @property string $barcode_validation
 * @property bool $enable_sellers
 * @property bool $enable_lockers
 * @property bool $not_scheduled_to_pay
 * @property bool $not_scheduled_to_redeem
 * @property string|null $accounts_by_nature
 * @property string|null $accounts_by_function
 * @property int $min_days_to_evaluate_clients
 * @property int $max_days_to_evaluate_clients
 * @property int|null $days_to_deactivate_client_credit
 * @property string|null $product_type_to_validate_prices
 * @property bool $product_type_setting_include_warehouse
 * @property string $cost_level_name1
 * @property string $cost_level_name2
 * @property string $cost_level_name3
 * @property string $cost_level_name4
 * @property string $cost_level_name5
 * @property int $max_attachment_size
 * @property int $max_attachments
 * @property bool $allow_edit_product_name
 * @property bool $set_last_cost_at_provision
 * @property int $minutes_to_verify_web_users
 * @property bool $print_sale_bill_detail
 * @property bool $replicate_web_name
 * @property bool $business_line_type
 * @property bool $use_batch
 * @property string $order_batch_output
 * @property int $days_to_alert_batch
 * @property float $margin_error_percentage
 * @property float $estimated_material_percentage
 * @property float $tax_percentage
 *
 * @property Account $account
 * @property Organization $organization
 * @property Person $person
 *
 * @package App\Models
 */
class GlobalVar extends Model {
    protected $table = 'global_var';
    protected $primaryKey = 'organization_id';
    public $incrementing = false;
    public $timestamps = false;

    protected $casts = [
        'global_var_id' => 'int',
        'organization_id' => 'int',
        'igv' => 'float',
        'sunat_id' => 'int',
        'perception' => 'float',
        'retention' => 'float',
        'retention4' => 'float',
        'no_client_max_amount_pen' => 'float',
        'no_client_max_amount_usd' => 'float',
        'no_retention_max_amount_pen' => 'float',
        'no_retention_max_amount_usd' => 'float',
        'no_retention4_max_amount_pen' => 'float',
        'no_retention4_max_amount_usd' => 'float',
        'no_detraction_max_amount_pen' => 'float',
        'no_detraction_max_amount_usd' => 'float',
        'mobility_person_max_amount_pen' => 'float',
        'mobility_person_max_amount_usd' => 'float',
        'uit_amount_pen' => 'float',
        'uit_amount_usd' => 'float',
        'max_cash_amount_pen' => 'float',
        'max_cash_amount_usd' => 'float',
        'max_rounding_amount_pen' => 'float',
        'max_rounding_amount_usd' => 'float',
        'tolerance_pen' => 'float',
        'tolerance_usd' => 'float',
        'commercial_ifixed' => 'bool',
        'warehouse_ifixed' => 'bool',
        'familyAssigment' => 'float',
        'socialSecurity' => 'float',
        'minimumSalary' => 'float',
        'credit_validity' => 'int',
        'hours_to_declare' => 'int',
        'account_group_level' => 'int',
        'allowEntryOnlyDefaultWarehouse' => 'bool',
        'ifixed' => 'bool',
        'require_agreement' => 'bool',
        'require_model' => 'bool',
        'require_image' => 'bool',
        'require_profit_margin' => 'bool',
        'enable_sellers' => 'bool',
        'enable_lockers' => 'bool',
        'not_scheduled_to_pay' => 'bool',
        'not_scheduled_to_redeem' => 'bool',
        'min_days_to_evaluate_clients' => 'int',
        'max_days_to_evaluate_clients' => 'int',
        'days_to_deactivate_client_credit' => 'int',
        'product_type_setting_include_warehouse' => 'bool',
        'max_attachment_size' => 'int',
        'max_attachments' => 'int',
        'allow_edit_product_name' => 'bool',
        'set_last_cost_at_provision' => 'bool',
        'minutes_to_verify_web_users' => 'int',
        'print_sale_bill_detail' => 'bool',
        'replicate_web_name' => 'bool',
        'business_line_type' => 'bool',
        'use_batch' => 'bool',
        'days_to_alert_batch' => 'int',
        'margin_error_percentage' => 'float',
        'estimated_material_percentage' => 'float',
        'tax_percentage' => 'float',
    ];

    protected $fillable = [
        'global_var_id',
        'color',
        'igv',
        'igv_list',
        'display_currency',
        'sunat_id',
        'perception',
        'retention',
        'retention4',
        'detraction',
        'no_client_max_amount_pen',
        'no_client_max_amount_usd',
        'no_retention_max_amount_pen',
        'no_retention_max_amount_usd',
        'no_retention4_max_amount_pen',
        'no_retention4_max_amount_usd',
        'no_detraction_max_amount_pen',
        'no_detraction_max_amount_usd',
        'mobility_person_max_amount_pen',
        'mobility_person_max_amount_usd',
        'uit_amount_pen',
        'uit_amount_usd',
        'max_cash_amount_pen',
        'max_cash_amount_usd',
        'max_rounding_amount_pen',
        'max_rounding_amount_usd',
        'tolerance_pen',
        'tolerance_usd',
        'commercial_ifixed',
        'warehouse_ifixed',
        'familyAssigment',
        'socialSecurity',
        'minimumSalary',
        'credit_validity',
        'hours_to_declare',
        'retention4_account_code',
        'missing_account_code',
        'surplus_account_code',
        'account_group_level',
        'allowEntryOnlyDefaultWarehouse',
        'ifixed',
        'require_agreement',
        'require_model',
        'require_image',
        'require_profit_margin',
        'barcode_validation',
        'enable_sellers',
        'enable_lockers',
        'not_scheduled_to_pay',
        'not_scheduled_to_redeem',
        'accounts_by_nature',
        'accounts_by_function',
        'min_days_to_evaluate_clients',
        'max_days_to_evaluate_clients',
        'days_to_deactivate_client_credit',
        'product_type_to_validate_prices',
        'product_type_setting_include_warehouse',
        'cost_level_name1',
        'cost_level_name2',
        'cost_level_name3',
        'cost_level_name4',
        'cost_level_name5',
        'max_attachment_size',
        'max_attachments',
        'allow_edit_product_name',
        'set_last_cost_at_provision',
        'minutes_to_verify_web_users',
        'print_sale_bill_detail',
        'replicate_web_name',
        'business_line_type',
        'use_batch',
        'order_batch_output',
        'days_to_alert_batch',
        'margin_error_percentage' => 'float',
        'estimated_material_percentage' => 'float',
        'tax_percentage' => 'float',
    ];

    public function account() {
        return $this->belongsTo(Account::class, 'surplus_account_code');
    }

    public function organization() {
        return $this->belongsTo(Organization::class);
    }

    public function person() {
        return $this->belongsTo(Person::class, 'sunat_id');
    }

    public static function getValue($columnName)
    {
        if (!\Schema::hasColumn('global_var', $columnName)) {
            throw new \InvalidArgumentException("La columna {$columnName} no existe en la tabla global_var.");
        }

        return self::query()->value($columnName);
    }
}
