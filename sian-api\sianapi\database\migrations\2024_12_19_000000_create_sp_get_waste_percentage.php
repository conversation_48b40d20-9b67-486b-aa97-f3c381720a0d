<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::unprepared('
            DROP PROCEDURE IF EXISTS `sp_get_waste_percentage`;
        ');

        DB::unprepared('
            CREATE PROCEDURE `sp_get_waste_percentage` (
              IN `in_product_ids` VARCHAR(255)
            )
            BEGIN

              SELECT 
                P.product_id,
                P.product_name,
                SUM(IQ.unit_quantity) AS normal_quantity,
                SUM(IW.unit_quantity) AS waste_quantity,
                SUM(IQ.unit_quantity) + SUM(IW.unit_quantity) AS total_quantity,
                ROUND((SUM(IW.unit_quantity) / (SUM(IQ.unit_quantity) + SUM(IW.unit_quantity))) * 100, 2) AS waste_percentage_total,
                ROUND(1 + (SUM(IW.unit_quantity) / SUM(IQ.unit_quantity)), 4) AS multiplier_to_total

              FROM movement M
              JOIN item IW 
                ON IW.movement_id = M.movement_id AND IW.is_waste = 1
              JOIN item IQ 
                ON IQ.movement_id = M.movement_id AND IQ.is_waste != 1 AND IW.product_id = IQ.product_id
              JOIN product P 
                ON P.product_id = IQ.product_id

              WHERE 
                (
                  in_product_ids = \'\'
                  OR FIND_IN_SET(P.product_id, in_product_ids) > 0
                )

              GROUP BY P.product_id, P.product_name;

            END
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::unprepared('DROP PROCEDURE IF EXISTS `sp_get_waste_percentage`');
    }
};
