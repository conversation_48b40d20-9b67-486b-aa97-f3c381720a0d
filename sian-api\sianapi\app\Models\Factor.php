<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Factor
 * 
 * @property int $factor_id
 * @property string $factor_name
 * @property string $short_name
 * @property string $type
 * @property bool $status
 * @property string|null $origin
 * 
 * @property Collection|FactorDetail[] $factor_details
 *
 * @package App\Models
 */
class Factor extends Model
{
	protected $table = 'factor';
	protected $primaryKey = 'factor_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool'
	];

	protected $fillable = [
		'factor_name',
		'short_name',
		'type',
		'status',
		'origin'
	];

	public function factor_details()
	{
		return $this->hasMany(FactorDetail::class);
	}
}
