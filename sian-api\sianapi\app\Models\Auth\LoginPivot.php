<?php

namespace App\Models\Auth;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;


class LoginPivot extends Model {
    use HasFactory;

    protected $table = 'login_pivot';

    protected $fillable = ['password', 'token'];

    public $timestamps = false;


    public static function savePivot($token, $hash) {
        $subToken = Str::substr($token, -200);
        //Guardamos la contraseña en la DB para utilizarlo cuando sea necesario // Se limita el token s 255 Caracteres para poder guardarlo en DB
        LoginPivot::create(['password' => $hash, 'token' => $subToken]);
    }

    public static function getPivotByToken($token) {
        $subToken = Str::substr($token, -200);
        return LoginPivot::where('token', $subToken)->first();
    }

    public static function geHashBytoken($token) {
        $pivot = LoginPivot::getPivotByToken($token);
        if ($pivot) {
            return $pivot->password;
        }
    }
    public static function getHashFromAuth($authorization) {
        $token = "";
        if (strpos($authorization, 'Bearer ') === 0) {
            $token = str_replace('Bearer ', '', $authorization);
        }

        $pivot = LoginPivot::getPivotByToken($token);

        if ($pivot) {
            return $pivot->password;
        }
    }


    public static function deletePivot($token) {
        $pivot = LoginPivot::getPivotByToken($token);
        if ($pivot) {
            $pivot->delete();
            return true;
        }
        return true;
    }

}
