<?php

namespace App\Http\Controllers\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\Person;
use App\Models\UserAccessToken;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;

class AccessController extends Controller {

    //public function __construct()
    //{
    //  $this->middleware('auth:api', ['except' => ['getMenu']]);
    //}

    public function getMenus(Request $request) {
        $request->validate([
            'username' => 'required|string',
            'module' => 'string',
        ]);
        $credentials = $request->only('username', 'module');

        $credentials['module'] = isset($credentials['module']) ? (($credentials['module'] == "0") ? "" : $credentials['module']) : "";

        $result = DB::select(
            "call sp_get_menu (:module, :username, :detail, :version)",
            [
                ':module' => $credentials['module'],
                ':username' => $credentials['username'],
                ':detail' => 1,
                ':version' => 2
            ]
        );

        $a_menus = [];

        $module_aux = "";
        $menu_aux = "";
        $submenu_aux = "";
        $i = 0;

        $module_temp = [];
        $menu_temp = [];
        $submenu_temp = [];

        if (count($result) > 0) {
            foreach ($result as $menu) {

                if ($i == 0) {
                    $module_aux = $menu->module;
                    $menu_aux = $menu->menu;
                    $submenu_aux = $menu->submenu;

                    $module_temp[$module_aux] = [
                        "menu" => $menu->module,
                        "title" => $menu->module_title,
                        "type" => "group",
                        "icon" => "IconUserCheck",
                        "children" => []
                    ];

                    $menu_temp[$menu_aux] = [
                        "menu" => $menu->menu,
                        "title" => $menu->menu_title,
                        "type" => "collapse",
                        "icon" => $menu->menu_icon,
                        "children" => []
                    ];
                    $submenu_temp[] = [
                        "menu" => $menu->submenu,
                        "title" => $menu->submenu_title,
                        "type" => "item",
                        "icon" => $menu->submenu_icon,
                        "url" => $menu->submenu_url,
                        "target" => false
                    ];
                }

                if ($module_aux != $menu->module) {
                    $menu_temp[$menu_aux]['children'] = $submenu_temp;
                    $submenu_temp = [];

                    $module_temp[$module_aux]['children'] = array_values($menu_temp);

                    $menu_temp = [];
                    $menu_aux = $menu->menu;
                    $menu_temp[$menu_aux] = [
                        "menu" => $menu->menu,
                        "title" => $menu->menu_title,
                        "type" => "collapse",
                        "icon" => $menu->menu_icon,
                        "children" => []
                    ];

                    $module_aux = $menu->module;
                    $module_temp[$module_aux] = [
                        "menu" => $menu->module,
                        "title" => $menu->module_title,
                        "type" => "group",
                        "icon" => "IconUserCheck",
                        "children" => []
                    ];
                } else {
                    if ($menu_aux != $menu->menu) {
                        $menu_temp[$menu_aux]['children'] = $submenu_temp;
                        $submenu_temp = [];

                        $menu_aux = $menu->menu;
                        $menu_temp[$menu_aux] = [
                            "menu" => $menu->menu,
                            "title" => $menu->menu_title,
                            "type" => "collapse",
                            "icon" => $menu->menu_icon,
                            "children" => []
                        ];
                    }
                }
                if ($i > 0) {
                    $submenu_temp[] = [
                        "menu" => $menu->submenu,
                        "title" => $menu->submenu_title,
                        "type" => "item",
                        "icon" => $menu->submenu_icon,
                        "url" => $menu->submenu_url,
                        "target" => false
                    ];
                }
                $i = $i + 1;
            }
            $menu_temp[$menu_aux]['children'] = $submenu_temp;
            $module_temp[$module_aux]['children'] = array_values($menu_temp);
        }

        $a_response = [
            'success' => true,
            'data' => [
                'menus' => array_values($module_temp)
            ]
        ];
        return response()->json($a_response);
    }

    public function getModules(Request $request) {
        $request->validate([
            'username' => 'required|string',
        ]);
        $credentials = $request->only('username');

        $result = DB::select(
            "call sp_get_menu (:module, :username, :detail, :version)",
            [
                ':module' => "",
                ':username' => $credentials['username'],
                ':detail' => 0,
                ':version' => 2
            ]
        );

        $a_module = [];
        $a_response = [];

        if (count($result) > 0) {

            foreach ($result as $menu) {

                $a_module[] = [
                    "module" => $menu->module,
                    "title" => $menu->module_title,
                    "icon" => $menu->module_icon
                ];
            }

            $a_response = [
                'success' => true,
                'data' => [
                    'modules' => $a_module
                ]
            ];
        }
        //$token = Auth::attempt($credentials);
        return response()->json($a_response);
    }
}
