<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Project
 * 
 * @property int $project_id
 * @property string $project_name
 * @property string $short_name
 * @property int $person_id
 * @property Carbon $begin_date
 * @property Carbon $end_date
 * @property string $project_status
 * @property int $combination_id
 * 
 * @property Person $person
 * @property Combination $combination
 * @property Collection|Movement[] $movements
 *
 * @package App\Models
 */
class Project extends Model
{
	protected $table = 'project';
	protected $primaryKey = 'project_id';
	public $timestamps = false;

	const STATE_ANNUL = 'Anulado';
    const STATE_PENDIENT = 'Pendiente';
    const STATE_FINALIZED = 'Concluido';

	protected $casts = [
		'person_id' => 'int',
		'combination_id' => 'int'
	];

	protected $dates = [
		'begin_date',
		'end_date'
	];

	protected $fillable = [
		'project_name',
		'short_name',
		'person_id',
		'begin_date',
		'end_date',
		'project_status',
		'combination_id'
	];

	public function person()
	{
		return $this->belongsTo(Person::class);
	}

	public function combination()
	{
		return $this->belongsTo(Combination::class);
	}

	public function movements()
	{
		return $this->hasMany(Movement::class);
	}
}
