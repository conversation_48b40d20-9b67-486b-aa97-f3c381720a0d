<?php

namespace App\Models\Procedures;

use Illuminate\Support\Facades\DB;

class SpGetPairedOwners
{
    public static function executeProcedure($params)
    {
        $results = DB::select('CALL sp_get_paired_owners(?, ?, ?, ?, ?, ?, ?)', [
            $params['type'],
            $params['parent_owner'] ?? null,
            $params['parent_owner_id'] ?? null,
            $params['child_owner'] ?? null,
            $params['child_owner_id'] ?? null,
            $params['child_owner_dynamic'] ?? null,
            $params['grandchild_owner_dynamic'] ?? null
        ]);

        return $results;
    }

    public static function rules()
    {
        return [
            'type' => 'required|string',
        ];
    }

    public static function attributeLabels()
    {
        return [
            'type' => 'Tipo',
            'parent_owner' => 'Propietario Padre',
            'parent_owner_id' => 'ID Propietario Padre',
            'child_owner' => 'Propietario Hijo',
            'child_owner_id' => 'ID Propietario Hijo',
            'value' => 'Valor',
            'default' => 'Predeterminado',
            'order' => 'Orden',
        ];
    }
}
