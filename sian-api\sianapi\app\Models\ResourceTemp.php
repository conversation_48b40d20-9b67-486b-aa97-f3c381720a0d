<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ResourceTemp
 * 
 * @property string $owner
 * @property int $owner_id
 * @property string $type
 * @property int $resource_number
 * @property string $url
 * @property string|null $title
 * @property string|null $description
 * @property string|null $target_url
 * @property int|null $width
 * @property int|null $height
 *
 * @package App\Models
 */
class ResourceTemp extends Model
{
	protected $table = 'resource_temp';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'owner_id' => 'int',
		'resource_number' => 'int',
		'width' => 'int',
		'height' => 'int'
	];

	protected $fillable = [
		'owner',
		'owner_id',
		'type',
		'resource_number',
		'url',
		'title',
		'description',
		'target_url',
		'width',
		'height'
	];
}
