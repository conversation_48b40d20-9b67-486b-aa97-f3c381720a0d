<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ScenarioSetting
 * 
 * @property int $scenario_setting_id
 * @property int $scenario_id
 * @property bool $send_mail_for_pending_delivery
 * @property int $email_count
 * 
 * @property Scenario $scenario
 *
 * @package App\Models
 */
class ScenarioSetting extends Model
{
	protected $table = 'scenario_setting';
	protected $primaryKey = 'scenario_setting_id';
	public $timestamps = false;

	protected $casts = [
		'scenario_id' => 'int',
		'send_mail_for_pending_delivery' => 'bool',
		'email_count' => 'int'
	];

	protected $fillable = [
		'scenario_id',
		'send_mail_for_pending_delivery',
		'email_count'
	];

	public function scenario()
	{
		return $this->belongsTo(Scenario::class, 'scenario_id', 'scenario_id');
	}
}
