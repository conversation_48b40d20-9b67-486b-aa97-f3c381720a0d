<?php

namespace App\Http\Resources\Human;

use App\Http\Resources\Administration\SimplePersonResource;
use Illuminate\Http\Resources\Json\JsonResource;

class SimpleUserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => isset($this->user_token) ? $this->user_token : $this->user_id,
            'user_id' => $this->user_id,
            'username' => $this->username,
            'status' => $this->status,
            'email' => $this->email_address,
            'phoneNumber' => $this->phone_number,
            'person' => new SimplePersonResource($this->person)
        ];
    }
}
