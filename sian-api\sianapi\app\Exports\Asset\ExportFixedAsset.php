<?php

namespace App\Exports\Asset;

use App\Models\FixedAsset;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

// class ExportFixedAsset implements FromCollection, ShouldAutoSize
class ExportFixedAsset implements FromView, ShouldAutoSize
{
    /**
    * @return \Illuminate\Support\Collection
    */
    // public function collection()
    // {
    //     return FixedAsset::all();
    // }

    public function view(): View {
        return view('modules.asset.classification.fixed-asset.export_excel', ['fixedAssets' => FixedAsset::all()]);
    }
}
