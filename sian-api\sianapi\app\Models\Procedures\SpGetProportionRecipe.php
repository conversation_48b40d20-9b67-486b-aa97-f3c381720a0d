<?php

namespace App\Models\Procedures;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class SpGetProportionRecipe {
    const MODE_DATE_AUTOMATIC = 0;
    const MODE_DATE_MANUAL = 1;

    public static function execute($product_id = '', $store_ids = [], $startDate = '', $endDate = '', $mode = self::MODE_DATE_AUTOMATIC) {

        $store_id_string = implode(',', $store_ids);

        $sql = "CALL sp_get_proportion_recipe(?, ?, ?, ?, ?)";
        $parameters = [
            $product_id,
            $store_id_string,
            $startDate,
            $endDate,
            $mode
        ];

        return DB::select($sql, $parameters);
    }
}
