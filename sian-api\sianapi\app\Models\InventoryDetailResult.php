<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class InventoryDetailResult
 * 
 * @property int $inventory_detail_result_id
 * @property int $inventory_detail_id
 * @property int $inventory_reason_id
 * @property string $action_type
 * @property float $real_stock
 * @property string|null $observation
 * @property int|null $movement_id
 * 
 * @property InventoryReason $inventory_reason
 * @property InventoryDetail $inventory_detail
 * @property Movement|null $movement
 *
 * @package App\Models
 */
class InventoryDetailResult extends Model
{
	protected $table = 'inventory_detail_result';
	protected $primaryKey = 'inventory_detail_result_id';
	public $timestamps = false;

	protected $casts = [
		'inventory_detail_id' => 'int',
		'inventory_reason_id' => 'int',
		'real_stock' => 'float',
		'movement_id' => 'int'
	];

	protected $fillable = [
		'inventory_detail_id',
		'inventory_reason_id',
		'action_type',
		'real_stock',
		'observation',
		'movement_id'
	];

	public function inventory_reason()
	{
		return $this->belongsTo(InventoryReason::class);
	}

	public function inventory_detail()
	{
		return $this->belongsTo(InventoryDetail::class);
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}
}
