<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class TransactionDetail
 * 
 * @property int $transaction_detail_id
 * @property int $transaction_id
 * @property string $owner
 * @property int $owner_id
 * @property int|null $integer_data
 * @property float|null $decimal_data
 * @property string|null $string_data
 * @property string|null $text_data
 * 
 * @property Transaction $transaction
 *
 * @package App\Models
 */
class TransactionDetail extends Model
{
	protected $table = 'transaction_detail';
	protected $primaryKey = 'transaction_detail_id';
	public $timestamps = false;

	protected $casts = [
		'transaction_id' => 'int',
		'owner_id' => 'int',
		'integer_data' => 'int',
		'decimal_data' => 'float'
	];

	protected $fillable = [
		'transaction_id',
		'owner',
		'owner_id',
		'integer_data',
		'decimal_data',
		'string_data',
		'text_data'
	];

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}

	public function transaction()
	{
		return $this->belongsTo(Transaction::class);
	}
}
