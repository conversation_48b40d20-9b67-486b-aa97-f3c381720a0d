import { Box, IconButton, TextField, Tooltip, Typography } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'store';
import StoreIcon from '@mui/icons-material/Store';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import RemoveCircleIcon from '@mui/icons-material/RemoveCircle';
import {
    addToCart,
    editToCart,
    getRepositionDataByProduct,
    getRepositionDataBySupply,
    openRotationModal,
    removeFromCart,
    setSelectedProduct,
    updateFormDataItem
} from 'store/slices/reposition/reposition';
import ErrorIcon from '@mui/icons-material/Error';
import { addDays, parseDateToLocaleString } from 'utils/dates';
import Grid from 'ui-component/grid/Grid';
import AutoModeIcon from '@mui/icons-material/AutoMode';
import useLoading from 'hooks/useLoading';
import { BlockLoader } from 'ui-component/loaders/loaders';
import SIANLink from 'ui-component/SIAN/SIANLink';

const NumberAlert = ({ condition = false, title = '' }) => {
    if (!condition) {
        return null;
    }
    return (
        <Tooltip title={title}>
            <IconButton color="error">
                <ErrorIcon />
            </IconButton>
        </Tooltip>
    );
};

const AlertRotation = ({ rotationScale }) => {
    switch (rotationScale) {
        case 'AR':
            return (
                <Tooltip title="El producto tiene ALTA ROTACIÓN">
                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#c6e0b4' }}>&nbsp;</Box>
                </Tooltip>
            );
        case 'MR':
            return (
                <Tooltip title="El producto tiene MEDIA ROTACIÓN">
                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#ffe699' }}>&nbsp;</Box>
                </Tooltip>
            );
        case 'BR':
            return (
                <Tooltip title="El producto tiene BAJA ROTACIÓN">
                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#b11226' }}>&nbsp;</Box>
                </Tooltip>
            );
        default:
            return (
                <Tooltip title="El producto NO tiene ROTACIÓN">
                    <Box
                        sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '99999px',
                            backgroundColor: 'white',
                            border: '1px solid'
                        }}
                    >
                        &nbsp;
                    </Box>
                </Tooltip>
            );
    }
};

const AlertBreak = ({ break_scale = 'QB' }) => {
    switch (break_scale) {
        case 'SS':
            return (
                <Tooltip title="El punto de Quiebre es SOBRE STOCK">
                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#a777dd' }}>&nbsp;</Box>
                </Tooltip>
            );
        case 'OPT':
            return (
                <Tooltip title="El punto de Quiebre es OPTIMO">
                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#c6e0b4' }}>&nbsp;</Box>
                </Tooltip>
            );
        case 'MOD':
            return (
                <Tooltip title="El punto de Quiebre es MODERADO">
                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#ffe699' }}>&nbsp;</Box>
                </Tooltip>
            );
        case 'CRI':
            return (
                <Tooltip title="El punto de Quiebre es CRÍTICO">
                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#b11226' }}>&nbsp;</Box>
                </Tooltip>
            );
        case 'QB':
            return (
                <Tooltip title="El punto de Quiebre esta QUEBRADO">
                    <Box
                        sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '99999px',
                            backgroundColor: 'white',
                            border: '1px solid'
                        }}
                    >
                        &nbsp;
                    </Box>
                </Tooltip>
            );
        default:
            return (
                <Tooltip title="El punto de Quiebre esta QUEBRADO">
                    <Box
                        sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '99999px',
                            backgroundColor: 'white',
                            border: '1px solid'
                        }}
                    >
                        &nbsp;
                    </Box>
                </Tooltip>
            );
    }
};

export default function SupplyDetail({ row }) {
    const dispatch = useDispatch();
    const { data, formData, cart, filters, supplyData } = useSelector((state) => state.reposition);
    const { data: storeData } = useSelector((state) => state.store);

    const [loading, startLoading, endLoading] = useLoading(true);

    const [repositionProduct, setRepositionProduct] = useState([]);

    const openModal = () => dispatch(openRotationModal());
    const setSelected = (data) => dispatch(setSelectedProduct(data));
    console.log({ row });
    console.log({ supplyData });
    const reload = () => {
        startLoading();
        dispatch(getRepositionDataBySupply(row[0], storeData, filters.daysMinStock, filters.daysOfReposition)).then((data) => {
            setRepositionProduct(data);
            endLoading();
        });
    };

    const handleAddToCart = (item) => {
        dispatch(addToCart(item));
    };

    const handleRemoveFromCart = (pk) => {
        dispatch(removeFromCart(pk));
    };

    const handleEditCart = (pk, updatedData) => {
        dispatch(editToCart(pk, updatedData));
    };

    useEffect(() => {
        reload();
    }, []);

    const QuantityInput = ({ tableMeta: { rowData: rowMetadata }, keyword = 'quantity_oc' }) => {
        const pk = rowMetadata[0];
        const rowData = formData[pk];
        const cartRowData = cart[pk];
        const repositionProductData = repositionProduct.find((item) => item.pk === pk);

        const getQuantity = (cartRowData, rowData, keyword) => {
            if (cartRowData && cartRowData[keyword] !== undefined) {
                return cartRowData[keyword];
            }
            if (rowData && rowData[keyword] !== undefined) {
                return rowData[keyword];
            }
            return 0;
        };

        const [numberInput, setNumberInput] = useState(getQuantity(cartRowData, rowData, keyword));

        useEffect(() => {
            const quantity = getQuantity(cartRowData, rowData, keyword);
            setNumberInput(parseFloat(quantity).toFixed(2));
        }, [rowData?.[keyword], cartRowData?.[keyword]]);

        const handleBlur = () => {
            const newValue = repositionProductData?.presentation?.allowDecimals === 1 ? numberInput : parseFloat(numberInput);
            const fixedValue = Math.floor(newValue).toFixed(2) || 0;
            setNumberInput(fixedValue);
            dispatch(updateFormDataItem(pk, { [keyword]: fixedValue, hasTouch: true }));
            if (cartRowData) {
                handleEditCart(pk, { [keyword]: fixedValue });
            }
        };

        const handleChange = ({ target: { value } }) => {
            const newValue = parseFloat(value) || 0;
            setNumberInput(newValue);
        };

        const autocomplete = () => {
            const productData = data.find((item) => item.pk === row[0]);
            const repositionRowData = repositionProduct.find((item) => item.pk === pk);
            const estimated = parseFloat(repositionRowData.unit_quantity) / parseFloat(productData?.presentation?.equivalence ?? 1);

            if (estimated > 0) {
                let keyOtherValue = '';
                let newValue = estimated;

                if (rowData) {
                    if (keyword === 'quantity_oc') {
                        keyOtherValue = 'quantity_ota';
                    } else {
                        keyOtherValue = 'quantity_oc';
                    }

                    newValue = estimated - (rowData[keyOtherValue] ?? 0);
                }

                if (newValue > 0) {
                    dispatch(updateFormDataItem(pk, { [keyword]: newValue, hasTouch: true }));

                    if (cartRowData) {
                        handleEditCart(pk, { [keyword]: newValue });
                    }
                }
            }
        };

        return (
            <Box>
                <TextField
                    variant="outlined"
                    sx={{ width: '8rem' }}
                    size="small"
                    type="number"
                    value={numberInput}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    InputProps={{
                        inputProps: {
                            style: { textAlign: 'right' },
                            step: rowData?.presentation?.allowDecimals === 1 ? 0.1 : 1,
                            min: 0,
                            inputMode: rowData?.presentation?.allowDecimals === 1 ? 'decimal' : 'numeric',
                            pattern: '[0-9]*'
                        }
                    }}
                />

                {repositionProductData.notAvailable ? null : (
                    <Tooltip title="Volver al Valor Sugerido">
                        <IconButton color="primary" aria-label="Volver al Valor Original" onClick={autocomplete}>
                            <AutoModeIcon />
                        </IconButton>
                    </Tooltip>
                )}
            </Box>
        );
    };

    const SelectProduct = ({ tableMeta }) => {
        const pk = tableMeta.rowData[0];
        const product_id = row[0];
        const rowItem = formData[pk];
        const productData = data.find((item) => item.pk === product_id);
        const cartSelected = cart[pk];
        const sum_quantity = parseFloat(rowItem?.quantity_oc ?? 0) + parseFloat(rowItem?.quantity_ota ?? 0);

        const handleClick = () => {
            if (cartSelected) {
                handleRemoveFromCart(pk);
            } else {
                handleAddToCart({
                    ...rowItem,
                    store_id: tableMeta.rowData[1],
                    store_name: tableMeta.rowData[2],
                    product_name: productData.product_name,
                    provider: productData.provider,
                    provider_id: productData.provider_id,
                    provider_number: productData.provider_number,
                    equivalence: productData.presentation.equivalence,
                    presentation: productData.presentation,
                    product_id: row[0],
                    unit_price: productData.unit_price
                });
            }
        };

        useEffect(() => {
            if (cartSelected && sum_quantity <= 0) {
                handleRemoveFromCart(pk);
            }
        }, [sum_quantity, cartSelected]);

        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                {rowItem && sum_quantity > 0 ? (
                    <>
                        {cartSelected ? (
                            <RemoveCircleIcon color="error" fontSize="large" onClick={handleClick} style={{ cursor: 'pointer' }} />
                        ) : (
                            <AddCircleIcon color="primary" fontSize="large" onClick={handleClick} style={{ cursor: 'pointer' }} />
                        )}
                    </>
                ) : (
                    <Tooltip title="La cantidad es insuficiente para añadir al carrito" sx={{ color: '#bdbdbd' }}>
                        <AddCircleIcon color="inherit" fontSize="large" style={{ cursor: 'pointer' }} />
                    </Tooltip>
                )}
            </Box>
        );
    };

    const columns = [
        {
            name: 'pk',
            label: 'PK',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'store_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'store_name',
            label: 'TIENDA',
            options: {
                filter: true,
                sort: true
            }
        },
        {
            name: 'stock',
            label: 'STOCK LOCAL',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {value} <NumberAlert condition={parseFloat(value) < 1} title="No hay stock en el Local" />
                        </Box>
                    );
                }
            }
        },
        {
            name: 'to_enter',
            label: 'CANT X ING',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return value;
                }
            }
        },
        {
            name: 'purchase_stock',
            label: 'STOCK DISPON',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => parseFloat(value).toFixed(2)
            }
        },
        {
            name: 'unit_quantity',
            label: 'SUGERIDO',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = row[0];
                    const productData = data.find((item) => item.pk === pk);
                    const result = value / parseFloat(productData?.presentation?.equivalence ?? 1);

                    const product_id = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === product_id);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return Math.ceil(result).toFixed(2);
                }
            }
        },
        {
            name: 'quantity_oc',
            label: 'CANT A PEDIR',
            options: {
                filter: true,
                sort: false,
                display: true,
                customBodyRender: (value, tableMeta) => <QuantityInput tableMeta={tableMeta} />
            }
        },
        {
            name: 'quantity_ota',
            label: 'CANT A TRANSFERIR',
            options: {
                filter: true,
                sort: false,
                display: false,
                customBodyRender: (value, tableMeta) => <QuantityInput tableMeta={tableMeta} keyword="quantity_ota" />
            }
        },
        {
            name: 'measure_name',
            label: 'PRES',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: () => {
                    const pk = row[0];
                    const rowData = data.find((item) => item.pk === pk);
                    return rowData?.presentation?.measure_name ?? '';
                }
            }
        },
        {
            name: 'other',
            label: '-',
            options: {
                filter: false,
                sort: false,
                customBodyRender: (_, tableMeta) => <SelectProduct tableMeta={tableMeta} />
            }
        }
    ];

    return (
        <Box sx={{ pt: 2, pl: 2, pr: 2, pb: 4, backgroundColor: '#f5f5f5' }}>
            <BlockLoader loading={loading}>
                <Box sx={{ p: 1, backgroundColor: 'white', borderRadius: '1rem', border: '1px solid #e0e0e0' }}>
                    <Grid
                        columns={columns}
                        data={repositionProduct}
                        options={{
                            search: false,
                            download: false,
                            print: false,
                            sort: false,
                            viewColumns: true,
                            filter: false,
                            filterType: 'multiselect',
                            responsive: 'vertical',
                            fixedHeader: true,
                            fixedSelectColumn: true,
                            jumpToPage: false,
                            resizableColumns: false,
                            draggableColumns: {
                                enabled: true
                            },
                            serverSide: true,
                            selectableRows: 'none',
                            selectableRowsOnClick: false,
                            pagination: false,
                            confirmFilters: false,
                            rowHover: true,
                            toolbar: false,

                            setTableProps: () => ({
                                size: 'small'
                            })
                        }}
                    />
                </Box>
            </BlockLoader>
        </Box>
    );
}
