<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

/**
 * Class Payment Schedule
 *
 * @property int $payment_schedule_detail_id
 * @property int $payment_schedule_id
 * @property int $entry_group_id
 * @property int $movement_id
 * @property string $document_origin
 * @property float $total_amount
 * @property float $balance_amount
 * @property int $order
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @package App\Models
 */
class PaymentScheduleDetail extends Model
{
    protected $table = 'payment_schedule_detail';

    protected $primaryKey = 'payment_schedule_detail_id';

    protected $fillable = [
        'payment_schedule_id',
        'entry_group_id',
        'movement_id',
        'document_origin',
        'total_amount',
        'balance_amount',
        'order',
    ];

    public $timestamps = false;

    public function paymentSchedule()
    {
        return $this->belongsTo(PaymentSchedule::class, 'payment_schedule_id');
    }

    // Relación con EntryGroup
    public function entryGroup()
    {
        return $this->belongsTo(EntryGroup::class, 'entry_group_id');
    }

    public function movement()
    {
        return $this->belongsTo(Movement::class, 'movement_id');
    }

}
