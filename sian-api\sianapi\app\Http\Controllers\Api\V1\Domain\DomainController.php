<?php

namespace App\Http\Controllers\Api\V1\Domain;

use App\Models\ScheduleSetting;
use App\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;



class DomainController extends Controller {

    /**
     *
     *
     * @return \Illuminate\Http\Response
     */

    public function index(Request $request) {

        $host = '';

        $originHeader = $request->headers->get('Origin');

        if ($originHeader) {
            $parsedUrl = parse_url($originHeader);
            $host = $parsedUrl['host'] ?? '';
        }


        $response = [
            'success' => false,
            'message' => "No se encontro el dominio asociado"
        ];

        if ($host !== '') {
            try {
                $tenant = Tenant::whereDomain($host)->first();

                return response()->json($tenant['sian_domain'],200);

            } catch (\Exception $e) {


                return response()->json($response, 404);
            }
        }else {
            return response()->json($response, 404);

        }

    }
}
