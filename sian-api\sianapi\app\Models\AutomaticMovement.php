<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class AutomaticMovement
 * 
 * @property int $movement_id
 * 
 * @property Movement $movement
 *
 * @package App\Models
 */
class AutomaticMovement extends Model
{
	protected $table = 'automatic_movement';
	protected $primaryKey = 'movement_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int'
	];

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}
}
