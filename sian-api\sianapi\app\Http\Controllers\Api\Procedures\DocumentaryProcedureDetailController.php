<?php

namespace App\Http\Controllers\Api\Procedures;

use App\Http\Controllers\Controller;
use App\Models\Procedures\DocumentaryProcedureDetail;
use Illuminate\Http\Request;

class DocumentaryProcedureDetailController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // return new DocumentaryProcedureCollection(DocumentaryProcedureDetail::latest()->paginate(10));
        return DocumentaryProcedureDetail::latest()->paginate(10);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Procedures\DocumentaryProcedureDetail  $documentaryProcedureDetail
     * @return \Illuminate\Http\Response
     */
    public function show(DocumentaryProcedureDetail $documentaryProcedureDetail)
    {
        // return new DocumentaryProcedureResource($documentaryProcedureDetail);
        return $documentaryProcedureDetail;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Procedures\DocumentaryProcedureDetail  $documentaryProcedureDetail
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, DocumentaryProcedureDetail $documentaryProcedureDetail)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Procedures\DocumentaryProcedureDetail  $documentaryProcedureDetail
     * @return \Illuminate\Http\Response
     */
    public function destroy(DocumentaryProcedureDetail $documentaryProcedureDetail)
    {
        //
    }
}
