<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class PaymentLink
 * 
 * @property int $payment_link_id
 * @property int $movement_id
 * @property int $api_user_id
 * @property string $link
 * 
 * @property ApiUser $api_user
 * @property Movement $movement
 *
 * @package App\Models
 */
class PaymentLink extends Model
{
	protected $table = 'payment_link';
	protected $primaryKey = 'payment_link_id';
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'api_user_id' => 'int'
	];

	protected $fillable = [
		'movement_id',
		'api_user_id',
		'link'
	];

	public function api_user()
	{
		return $this->belongsTo(ApiUser::class);
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}
}
