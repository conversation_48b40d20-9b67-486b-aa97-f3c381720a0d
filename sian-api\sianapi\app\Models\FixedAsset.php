<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;

/**
 * Class FixedAsset
 * 
 * @property int $fixed_asset_id
 * @property string $code
 * @property string $description
 * @property string $account_code
 * @property int|null $mark_id
 * @property string|null $model
 * @property string|null $serie
 * @property Carbon $buy_date
 * @property Carbon $init_used_date
 * @property bool $required_depreciation
 * @property string|null $document_authorization_change_method
 * @property bool $status
 * @property int $type_sunat
 * @property int $status_sunat
 * @property int|null $useful_life
 * @property int $depreciation_group_id
 * @property float $acquisition_cost
 * @property float $balance_cost
 * @property float $historical_depreciation
 * @property int|null $last_fixed_asset_movement_id

 * @property DepreciationGroup|null $depreciation_group
 *
 * @package App\Models
 */
class FixedAsset extends Model
{
	use HasFactory, Notifiable;

	protected $table = 'fixed_asset';
	protected $primaryKey = 'fixed_asset_id';
	public $timestamps = false;

	const AMOUNT = 'AMOUNT';
	const PERCENTAGE = 'PERCENTAGE';
	const AMOUNT_LABEL = 'Monto';
	const PERCENTAGE_LABEL = 'Porcentaje';
	const AMOUNT_SIMBOL = 'S/';
	const PERCENTAGE_SIMBOL = '%';
	const MONTHLY = 'MONTHLY';
	const MONTHLY_LABEL = 'Mensual';
	const YEARLY = 'YEARLY';
	const YEARLY_LABEL = 'Anual';

	protected $casts = [
		'mark_id' => 'int',
		'required_depreciation' => 'bool',
		'depreciation_method' => 'int',
		'amount_depreciation' => 'float',
		'type_sunat' => 'int',
		'status_sunat' => 'int',
		'status' => 'bool',
		'used_warehouse' => 'bool',
		'useful_life' => 'int',
		'acquisition_cost' => 'float',
		'last_fixed_asset_movement_id' => 'int'
	];

	protected $dates = [
		'buy_date',
		'init_used_date'
	];

	protected $fillable = [
		'code',
		'description',
		'account_code',
		'mark_id',
		'model',
		'serie',
		'buy_date',
		'init_used_date',
		'required_depreciation',
		'document_authorization_change_method',
		'period_depreciation',
		'type_depreciation',
		'historical_depreciation',
		'type_sunat',
		'status_sunat',
		'status',
		'useful_life',
		'acquisition_cost',
		'balance_cost',
		'depreciation_group_id',
		'last_fixed_asset_movement_id'
	];

	public function accountAsset()
	{
		return $this->belongsTo(Account::class, 'account_code', 'account_code');
	}

	public function typeSunat()
	{
		return $this->belongsTo(Multitable::class, 'type_sunat', 'multi_id');
	}

	public function statusSunat()
	{
		return $this->belongsTo(Multitable::class, 'status_sunat', 'multi_id');
	}

	public function fixedAssetMovement()
	{
		return $this->belongsTo(FixedAssetMovement::class, 'last_fixed_asset_movement_id', 'fixed_asset_movement_id');
	}

	public function depreciationGroup()
	{
		return $this->belongsTo(DepreciationGroup::class, 'depreciation_group_id', 'depreciation_group_id');
	}

	public function mark()
	{
		return $this->belongsTo(Mark::class, 'mark_id', 'mark_id');
	}

	public function area()
	{
		return $this->belongsTo(Area::class, 'area_id', 'area_id');
	}

	public function businessUnit()
	{
		return $this->belongsTo(BusinessUnit::class, 'business_unit_id', 'business_unit_id');
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class, 'movement_id', 'movement_id');
	}

	public static function getDepreciationPeriodItems()
	{
		return [
			Self::MONTHLY => Self::MONTHLY_LABEL,
			Self::YEARLY => Self::YEARLY_LABEL
		];
	}

	public static function getDepreciationTypeItems()
	{
		return [
			Self::AMOUNT => Self::AMOUNT_LABEL,
			Self::PERCENTAGE => Self::PERCENTAGE_LABEL
		];
	}

	public static function getDepreciationTypeLabel($s_code)
	{
		if (isset($s_code)) {
			$a_items = Self::getDepreciationTypeItems();
			return $a_items[$s_code];
		}
	}

	public function getPurchaseDateAttribute()
	{
		return $this->buy_date->format('d/m/Y');
	}

	public function getInitUseDateAttribute()
	{
		return $this->init_used_date->format('d/m/Y');
	}
}
