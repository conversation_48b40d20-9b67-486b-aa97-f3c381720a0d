<?php

namespace App\Http\Controllers\Api\V1\Merchandise;

use App\Models\Presentation;
use App\Models\Fake\Currency;
use App\Models\GlobalVar;
use App\Models\Procedures\SpGetProductPresentations;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use stdClass;

class MerchandiseController extends Controller {
    /**
     *
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        $validate = Validator::make($request->all(), [
            'page' => 'required|integer',
            'pageSize' => 'required|integer',
            'isSupply' => 'required|string',
            'productName' => 'sometimes|string',
            'subline' => 'sometimes|string',
            'line' => 'sometimes|string',
            'mark' => 'sometimes|string',
        ]);

        if ($validate->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validate->errors()
            ], 400);
        }

        $isSupply = filter_var($request->query('isSupply'), FILTER_VALIDATE_BOOLEAN);

        return $this->getProducts($request, $isSupply);
    }

    private static function getQueryProduct(Request $request, $onlySupplys = false, $notIncludeDisabledProducts = true) {
        $query = DB::table('merchandise as ME')
            ->join('product as P', 'P.product_id', '=', 'ME.product_id')
            ->join('subline as S', 'S.subline_id', '=', 'ME.subline_id')
            ->join('line as L', 'L.line_id', '=', 'S.line_id')
            ->join('division as D', 'D.division_id', '=', 'L.division_id')
            ->join('presentation AS PR', function ($join) {
                $join->on('PR.product_id', '=', 'P.product_id')
                    ->where('PR.equivalence', '=', 1);
            })
            ->join('presentation AS DPR', function ($join) {
                $join->on('DPR.product_id', '=', 'P.product_id')
                    ->where('DPR.default', '=', 1);
            })
            ->leftJoin('mark as MA', 'MA.mark_id', '=', 'ME.mark_id')
            ->leftJoin('recipe as RE', 'RE.product_id', '=', 'P.product_id');


        if ($onlySupplys) {
            $query->where('P.is_supply', '=', 1);
        } else {
            $query->where('D.division_id', '=', 122)
                ->whereNotIn('P.product_id', function ($query) {
                    $query->select('PL.product_parent_id')
                        ->from('product_link as PL');
                });
        }

        if ($notIncludeDisabledProducts) {
            $query->where('P.status', '=', 1);
        }

        if ($request->has('productName')) {
            $productName = $request->input('productName');
            $query->where('P.product_name', 'like', '%' . addcslashes($productName, '%_') . '%');
        }

        if ($request->has('subline')) {
            $sublineString = $request->input('subline');
            $sublineArray = explode(',', $sublineString);
            $query->whereIn('S.subline_id', $sublineArray);
        }

        if ($request->has('line')) {
            $lineString = $request->input('line');
            $lineArray = explode(',', $lineString);
            $query->whereIn('L.line_id', $lineArray);
        }

        if ($request->has('mark')) {
            $markString = $request->input('mark');
            $markArray = explode(',', $markString);
            $query->whereIn('MA.mark_id', $markArray);
        }
        return $query;
    }

    private function getProducts(Request $request, $withPresentations = false, $notIncludeDisabledProducts = true) {
        try {
            $page = intval($request->query('page'));
            $pageSize = intval($request->query('pageSize'));
            $startIndex = ($page - 1) * $pageSize;

            $query = self::getQueryProduct($request, $withPresentations, $notIncludeDisabledProducts);

            $totalItems = $query->count();

            $queryResults = $query->select(
                'ME.product_id as productID',
                'P.product_name as productName',
                'S.subline_name as sublineName',
                'L.line_name as lineName',
                'MA.mark_name as markName',
                'RE.recipe_id as recipeID',
                'RE.total_material_cost as totalMaterialCost',
                'RE.pres_quantity as presQuantity',
                'PR.equivalence',
                'DPR.equivalence as defaultEquivalence',
                DB::raw("(SELECT IFNULL(
                                ROUND(CAST(K.unit_cost as DECIMAL(10,4)) * CAST(PR.equivalence as DECIMAL(10,4)),4),
                                0)
                          FROM kardex as K
                          WHERE
                            K.product_id = ME.product_id AND
                            K.unit_cost > 0
                          ORDER BY K.kardex_id desc LIMIT 1) AS unitCost")
            )->groupBy(
                    'ME.product_id',
                    'P.product_name',
                    'S.subline_name',
                    'L.line_name',
                    'MA.mark_name',
                    'RE.recipe_id',
                    'RE.total_material_cost',
                    'RE.pres_quantity',
                    'PR.equivalence',
                    'DPR.equivalence'
                )->offset($startIndex)->limit($pageSize)->get();

            $response = [
                'success' => true,
                'message' => 'Productos obtenidos correctamente',
                'pagination' => [
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'totalRecords' => $totalItems,
                    'totalPages' => ceil($totalItems / $pageSize),
                ],
                'data' => $queryResults,
            ];


            $ids = [];

            foreach ($queryResults as $result) {
                $ids[] = $result->productID;
            }

            $productPresentations = SpGetProductPresentations::getAssociative(
                !$withPresentations ? SpGetProductPresentations::MODE_COMBOBOX_WITH_PRICES : SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES,
                $ids,
                Currency::PEN_CAP,
                1
            );

            self::processProductPresentations($queryResults, $productPresentations, $withPresentations);

            $response['data'] = $queryResults;

            return response()->json($response);

        } catch (\Exception $ex) {
            Log::error('Query error: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Query error: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public static function processProductPresentations(&$queryResults, $productPresentations, $withPresentations = true, $firstPresentation = null) {
        foreach ($queryResults as $row) {

            $row->productID = intval($row->productID);
            $row->presentations = $productPresentations[$row->productID] ?? [];

            foreach ($row->presentations as &$presentation) {
                $presentation->cost = floatval($row->unitCost) * floatval($presentation->equivalence);
                $presentation->icost = $presentation->cost * 1.18;

                if (!$withPresentations) {
                    $presentation->mprice = floatval($presentation->mprice);
                    $presentation->imprice = floatval($presentation->imprice);
                    $presentation->aprice = floatval($presentation->aprice);
                    $presentation->iaprice = floatval($presentation->iaprice);
                    $presentation->wprice = floatval($presentation->wprice);
                    $presentation->iwprice = floatval($presentation->iwprice);
                }

                if (isset($row->defaultEquivalence) && floatval($row->defaultEquivalence) == floatval($presentation->equivalence)) {
                    $row->presentation = $presentation;
                }
            }

            if (isset($row->presentation) && isset($row->totalMaterialCost) && isset($row->presQuantity)) {
                $unit_material_cost = floatval($row->totalMaterialCost) / floatval($row->presQuantity);
                $row->presentation->cost = round(floatval($unit_material_cost) / 1.18, 2);
                $row->presentation->icost = floatval($unit_material_cost);
            }

        }
    }

    public static function proccessPresentations($ids, $productPresentations) {
        $result = [];

        $igv = GlobalVar::getValue('igv');
        $unit_igv = 1 + $igv;

        foreach ($ids as $product_id) {

            $row = new stdClass;
            $row->presentations = $productPresentations[$product_id] ?? [];

            $requiredValuesQuery = "
                    SELECT
                        P.product_id as productID,
                        P.product_name as productName,
                        R.recipe_id as recipeID,
                        P.uref_cost_pen as refCost,
                        R.total_material_cost as totalMaterialCost,
                        R.pres_quantity as presQuantity,
                        UP.equivalence,
                        DP.equivalence as defaultEquivalence,
                        M.lpcost_pen AS unitCost
                    FROM merchandise as M
                    STRAIGHT_JOIN product as P on P.product_id = M.product_id
                    STRAIGHT_JOIN presentation as DP on DP.product_id = M.product_id AND DP.default = 1
                    STRAIGHT_JOIN presentation as UP on UP.product_id = M.product_id AND UP.equivalence = 1
                    LEFT JOIN recipe as R on R.product_id = M.product_id
                    WHERE
                        M.product_id = :product_id
                ";

            $requiredValuesResult = DB::select(DB::raw($requiredValuesQuery), ['product_id' => $product_id]);
            $unitCost = $requiredValuesResult[0]->unitCost && $requiredValuesResult[0]->unitCost > 0
                ? $requiredValuesResult[0]->unitCost
                : $requiredValuesResult[0]->refCost;
            $defaultEquivalence = $requiredValuesResult[0]->defaultEquivalence ?? Presentation::UNIT_EQUIVALENCE;
            $equivalence = $requiredValuesResult[0]->equivalence ?? Presentation::UNIT_EQUIVALENCE;
            $totalMaterialCost = $requiredValuesResult[0]->totalMaterialCost ?? 0;
            $presQuantity = $requiredValuesResult[0]->presQuantity ?? 1;
            $productID = $requiredValuesResult[0]->productID ?? 0;
            $productName = $requiredValuesResult[0]->productName ?? 1;

            $row->unitCost = $unitCost;
            $row->defaultEquivalence = $defaultEquivalence;
            $row->equivalence = $equivalence;
            $row->totalMaterialCost = $totalMaterialCost;
            $row->presQuantity = $presQuantity;
            $row->productID = $productID;
            $row->productName = $productName;

            foreach ($row->presentations as &$presentation) {

                $presentation->cost = floatval($unitCost) * floatval($presentation->equivalence);
                $presentation->icost = $presentation->cost * $unit_igv;
                $presentation->mprice = floatval($presentation->mprice);
                $presentation->imprice = floatval($presentation->imprice);
                $presentation->aprice = floatval($presentation->aprice);
                $presentation->iaprice = floatval($presentation->iaprice);
                $presentation->wprice = floatval($presentation->wprice);
                $presentation->iwprice = floatval($presentation->iwprice);

                if (isset($defaultEquivalence) && floatval($defaultEquivalence) == floatval($presentation->equivalence)) {
                    $row->presentation = $presentation;
                }
            }

            if (isset($row->presentation) && isset($row->totalMaterialCost) && $row->totalMaterialCost > 0 && isset($row->presQuantity)) {
                $unit_material_cost = floatval($row->totalMaterialCost) / floatval($row->presQuantity);
                $row->presentation->cost = floatval($unit_material_cost);
                $row->presentation->icost = floatval($unit_material_cost) * $unit_igv;
            }

            $result[$product_id] = $row;
        }
        return $result;
    }

    public function getFoodItems(Request $request) {
        try {

            $query = self::getQueryProduct($request, false, true);

            if ($request->has('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(function ($q) use ($keyword) {
                    $q->where('P.product_name', 'like', '%' . $keyword . '%')
                        ->orWhere('P.product_id', 'like', '%' . $keyword . '%');
                });
            }

            if ($request->has('products')) {
                $productsSelected = $request->input('products');
                $arrayDivision = explode(",", $productsSelected);
                $query->whereNotIn("ME.product_id", $arrayDivision);
            }

            $query->select('ME.product_id', 'P.product_name')
                ->groupBy('ME.product_id', 'P.product_name');

            if ($request->has('limit')) {
                $limit = $request->input('limit');
                $query->limit($limit);
            } else {
                $query->limit(50);
            }

            return response()->json([
                'success' => true,
                'data' => $query->get(),

            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ], 500);
        }
    }
    public function getSupplyItems(Request $request) {
        try {

            $query = self::getQueryProduct($request, true, true);

            if ($request->has('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(function ($q) use ($keyword) {
                    $q->where('P.product_name', 'like', '%' . $keyword . '%')
                        ->orWhere('P.product_id', 'like', '%' . $keyword . '%');
                });
            }

            if ($request->has('products')) {
                $productsSelected = $request->input('products');
                $arrayDivision = explode(",", $productsSelected);
                $query->whereNotIn("ME.product_id", $arrayDivision);
            }

            $query->select('ME.product_id', 'P.product_name')
                ->groupBy('ME.product_id', 'P.product_name');

            if ($request->has('limit')) {
                $limit = $request->input('limit');
                $query->limit($limit);
            } else {
                $query->limit(50);
            }

            return response()->json([
                'success' => true,
                'data' => $query->get(),

            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ], 500);
        }
    }

    public function getPresentations(Request $request) {
        try {

            $data = SpGetProductPresentations::getAssociative(
                SpGetProductPresentations::MODE_COMBOBOX_WITH_PRICES,
                $request->get('ids'),
                Currency::PEN_CAP,
                1
            );

            return response()->json(self::proccessPresentations(explode(',', $request->get('ids')), $data));
        } catch (\Exception $ex) {
            Log::error('Query error: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Query error: ' . $ex->getMessage(),
            ], 500);
        }
    }
}
