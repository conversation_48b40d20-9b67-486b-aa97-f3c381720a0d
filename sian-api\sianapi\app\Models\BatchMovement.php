<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class BatchMovement
 * 
 * @property int $batch_movement_id
 * @property int $batch_id
 * @property int $warehouse_id
 * @property Carbon $movement_date
 * @property float $unit_quantity
 * @property float $pres_quantity
 * @property float $equivalence
 * @property string $direction
 * @property bool $status
 * @property string $type
 * @property int|null $parent_movement_batch_id
 * @property int|null $item_id
 * 
 * @property Batch $batch
 * @property Item|null $item
 * @property Warehouse $warehouse
 *
 * @package App\Models
 */
class BatchMovement extends Model
{
	protected $table = 'batch_movement';
	protected $primaryKey = 'batch_movement_id';
	public $timestamps = false;

	protected $casts = [
		'batch_id' => 'int',
		'warehouse_id' => 'int',
		'unit_quantity' => 'float',
		'pres_quantity' => 'float',
		'equivalence' => 'float',
		'status' => 'bool',
		'parent_movement_batch_id' => 'int',
		'item_id' => 'int'
	];

	protected $dates = [
		'movement_date'
	];

	protected $fillable = [
		'batch_id',
		'warehouse_id',
		'movement_date',
		'unit_quantity',
		'pres_quantity',
		'equivalence',
		'direction',
		'status',
		'type',
		'parent_movement_batch_id',
		'item_id'
	];

	public function batch()
	{
		return $this->belongsTo(Batch::class);
	}

	public function item()
	{
		return $this->belongsTo(Item::class);
	}

	public function warehouse()
	{
		return $this->belongsTo(Warehouse::class);
	}
}
