<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Link
 *
 * @property int $link_id
 * @property string $url
 * @property int $object_id
 *
 * @property ObjectModel $object
 *
 * @package App\Models
 */
class Link extends Model
{
	protected $table = 'link';
	protected $primaryKey = 'link_id';
	public $timestamps = false;

	protected $casts = [
		'object_id' => 'int'
	];

	protected $fillable = [
		'url',
		'object_id'
	];

	public function object()
	{
		return $this->belongsTo(ObjectModel::class);
	}
}
