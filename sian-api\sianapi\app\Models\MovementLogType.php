<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class MovementLogType
 * 
 * @property int $movement_log_type_id
 * @property string $route
 * @property string $title
 * @property string $alias
 * @property string $long_title
 * @property string $required
 * @property int $order
 * 
 * @property Scenario $scenario
 * @property Collection|MovementLog[] $movement_logs
 *
 * @package App\Models
 */
class MovementLogType extends Model
{
	protected $table = 'movement_log_type';
	protected $primaryKey = 'movement_log_type_id';
	public $timestamps = false;

	protected $casts = [
		'order' => 'int'
	];

	protected $fillable = [
		'route',
		'title',
		'alias',
		'long_title',
		'required',
		'order'
	];

	public function scenario()
	{
		return $this->belongsTo(Scenario::class, 'route');
	}

	public function movement_logs()
	{
		return $this->hasMany(MovementLog::class);
	}
}
