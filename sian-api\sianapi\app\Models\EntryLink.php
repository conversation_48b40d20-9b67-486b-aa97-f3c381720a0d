<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class EntryLink
 * 
 * @property int $entry_id
 * @property int $entry_parent_id
 * @property bool $status
 * @property float $amount_pen
 * @property float $amount_usd
 * 
 * @property Entry $entry
 *
 * @package App\Models
 */
class EntryLink extends Model
{
	protected $table = 'entry_link';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'entry_id' => 'int',
		'entry_parent_id' => 'int',
		'status' => 'bool',
		'amount_pen' => 'float',
		'amount_usd' => 'float'
	];

	protected $fillable = [
		'status',
		'amount_pen',
		'amount_usd'
	];

	public function entry()
	{
		return $this->belongsTo(Entry::class, 'entry_parent_id');
	}
}
