<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ServiceOrder
 * 
 * @property int $movement_id
 * @property string $type
 * @property string $product
 * @property int $quantity
 * @property string $model
 * @property string $mark
 * @property string $serie
 * @property Carbon $begin_date
 * @property Carbon|null $end_date
 * @property Carbon $estimated_date
 * @property string $service_status
 * 
 * @property CommercialMovement $commercial_movement
 *
 * @package App\Models
 */
class ServiceOrder extends Model
{
	protected $table = 'service_order';
	protected $primaryKey = 'movement_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'quantity' => 'int'
	];

	protected $dates = [
		'begin_date',
		'end_date',
		'estimated_date'
	];

	protected $fillable = [
		'type',
		'product',
		'quantity',
		'model',
		'mark',
		'serie',
		'begin_date',
		'end_date',
		'estimated_date',
		'service_status'
	];

	public function commercial_movement()
	{
		return $this->belongsTo(CommercialMovement::class, 'movement_id');
	}
}
