<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class LoanMovementFee
 * 
 * @property int $movement_id
 * @property int $fee_number
 * @property Carbon $expiration_date
 * @property float $debt_pen
 * @property float $factor_pen
 * @property float $amortization_pen
 * @property float $interest_pen
 * @property float $desgravamen_pen
 * @property float $oae_adm_pen
 * @property float $oae_financial_pen
 * @property float $oae_sales_pen
 * @property float $porte_pen
 * @property float $financing_pen
 * @property float $itf_pen
 * @property float $fee_pen
 * @property float $debt_usd
 * @property float $factor_usd
 * @property float $amortization_usd
 * @property float $interest_usd
 * @property float $desgravamen_usd
 * @property float $oae_adm_usd
 * @property float $oae_financial_usd
 * @property float $oae_sales_usd
 * @property float $porte_usd
 * @property float $financing_usd
 * @property float $itf_usd
 * @property float $fee_usd
 * 
 * @property LoanMovement $loan_movement
 *
 * @package App\Models
 */
class LoanMovementFee extends Model
{
	protected $table = 'loan_movement_fee';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'fee_number' => 'int',
		'debt_pen' => 'float',
		'factor_pen' => 'float',
		'amortization_pen' => 'float',
		'interest_pen' => 'float',
		'desgravamen_pen' => 'float',
		'oae_adm_pen' => 'float',
		'oae_financial_pen' => 'float',
		'oae_sales_pen' => 'float',
		'porte_pen' => 'float',
		'financing_pen' => 'float',
		'itf_pen' => 'float',
		'fee_pen' => 'float',
		'debt_usd' => 'float',
		'factor_usd' => 'float',
		'amortization_usd' => 'float',
		'interest_usd' => 'float',
		'desgravamen_usd' => 'float',
		'oae_adm_usd' => 'float',
		'oae_financial_usd' => 'float',
		'oae_sales_usd' => 'float',
		'porte_usd' => 'float',
		'financing_usd' => 'float',
		'itf_usd' => 'float',
		'fee_usd' => 'float'
	];

	protected $dates = [
		'expiration_date'
	];

	protected $fillable = [
		'expiration_date',
		'debt_pen',
		'factor_pen',
		'amortization_pen',
		'interest_pen',
		'desgravamen_pen',
		'oae_adm_pen',
		'oae_financial_pen',
		'oae_sales_pen',
		'porte_pen',
		'financing_pen',
		'itf_pen',
		'fee_pen',
		'debt_usd',
		'factor_usd',
		'amortization_usd',
		'interest_usd',
		'desgravamen_usd',
		'oae_adm_usd',
		'oae_financial_usd',
		'oae_sales_usd',
		'porte_usd',
		'financing_usd',
		'itf_usd',
		'fee_usd'
	];

	public function loan_movement()
	{
		return $this->belongsTo(LoanMovement::class, 'movement_id');
	}
}
