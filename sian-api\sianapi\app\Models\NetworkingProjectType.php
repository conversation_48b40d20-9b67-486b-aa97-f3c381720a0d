<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class NetworkingProjectType
 * 
 * @property int $networking_project_type_id
 * @property string $networking_project_type_name
 * @property bool $status
 * 
 * @property Collection|NetworkingActivityTemplate[] $networking_activity_templates
 * @property Collection|NetworkingProject[] $networking_projects
 *
 * @package App\Models
 */
class NetworkingProjectType extends Model
{
	protected $table = 'networking_project_type';
	protected $primaryKey = 'networking_project_type_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool'
	];

	protected $fillable = [
		'networking_project_type_name',
		'status'
	];

	public function networking_activity_templates()
	{
		return $this->hasMany(NetworkingActivityTemplate::class);
	}

	public function networking_projects()
	{
		return $this->hasMany(NetworkingProject::class);
	}
}
