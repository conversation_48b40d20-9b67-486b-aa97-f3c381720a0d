<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Newsletter
 * 
 * @property int $newsletter_id
 * @property string $newsletter_name
 * @property string $alias
 * @property string|null $description
 * @property bool $status
 * @property bool $frontend
 * @property int $email_count
 *
 * @package App\Models
 */
class Newsletter extends Model
{
	protected $table = 'newsletter';
	protected $primaryKey = 'newsletter_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'frontend' => 'bool',
		'email_count' => 'int'
	];

	protected $fillable = [
		'newsletter_name',
		'alias',
		'description',
		'status',
		'frontend',
		'email_count'
	];
}
