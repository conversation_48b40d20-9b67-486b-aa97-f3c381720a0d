<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ApiUserCashboxVariant
 * 
 * @property int $api_user_cashbox_variant_id
 * @property string $type
 * @property string $currency
 * @property string $card_type
 * @property int $cashbox_id
 * @property int $api_user_variant_id
 * @property int $order
 * 
 * @property ApiUserVariant $api_user_variant
 * @property Cashbox $cashbox
 *
 * @package App\Models
 */
class ApiUserCashboxVariant extends Model
{
	protected $table = 'api_user_cashbox_variant';
	protected $primaryKey = 'api_user_cashbox_variant_id';
	public $timestamps = false;

	protected $casts = [
		'cashbox_id' => 'int',
		'api_user_variant_id' => 'int',
		'order' => 'int'
	];

	protected $fillable = [
		'type',
		'currency',
		'card_type',
		'cashbox_id',
		'api_user_variant_id',
		'order'
	];

	public function api_user_variant()
	{
		return $this->belongsTo(ApiUserVariant::class);
	}

	public function cashbox()
	{
		return $this->belongsTo(Cashbox::class);
	}
}
