<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class EventLog
 * 
 * @property int $event_log_id
 * @property string $event_name
 * @property Carbon $date
 *
 * @package App\Models
 */
class EventLog extends Model
{
	protected $table = 'event_log';
	protected $primaryKey = 'event_log_id';
	public $timestamps = false;

	protected $dates = [
		'date'
	];

	protected $fillable = [
		'event_name',
		'date'
	];
}
