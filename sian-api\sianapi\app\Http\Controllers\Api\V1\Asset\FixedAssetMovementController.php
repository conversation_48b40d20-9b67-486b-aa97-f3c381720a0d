<?php

namespace App\Http\Controllers\Api\V1\Asset;

use Illuminate\Http\Request;
use App\Models\FixedAssetMovement;
use App\Models\FixedAsset;
use App\Models\Person;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Resources\Asset\FixedAssetMovementCollection;
use App\Http\Resources\Asset\FixedAssetMovementResource;
use Illuminate\Support\Facades\Validator;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;

class FixedAssetMovementController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $request->validate([
            'page' => 'int',
            'pageSize' => 'int',
            'sortField' => 'String',
            'direction' => 'String',
            'searchTerm' => 'String',
            'id' => 'int',
            'fixedAssetId' => 'int',
        ]);

        $i_page = (isset($request->page) && $request->page > 0) ? $request->page : 1;
        $i_pageSize = (isset($request->pageSize) && $request->pageSize > 0) ? $request->pageSize : 10;
        $s_sortField = isset($request->sortField) ? $request->sortField : 'fixed_asset_movement_id';
        $s_direction = isset($request->direction) ? $request->direction : 'DESC';
        $s_searchTerm = isset($request->searchTerm) ? $request->searchTerm : '';
        $i_skip = ($i_page - 1) * $i_pageSize;

        //Query
        $s_query = FixedAssetMovement::skip($i_skip);
        if (isset($request->id))
            $s_query->where('fixed_asset_movement_id', $request->id);
        if (isset($request->fixedAssetId))
            $s_query->where('fixed_asset_id', $request->fixedAssetId);
        $s_query->orderBy($s_sortField, $s_direction);

        $fixedAssetMovements = $s_query->paginate($i_pageSize);

        $paginator =  new FixedAssetMovementCollection($fixedAssetMovements);
        $a_response = [
            'success' => true,
            'data' => [
                'count' => $paginator->count(),
                'totalPages' => $paginator->lastPage(),
                'page' => $paginator->currentPage(),
                'pageSize' => $paginator->perPage(),
                'totalItems' => $paginator->total(),
                'items' => $paginator->items(),
            ]
        ];
        return $a_response;
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\FixedAssetMovement  $fixedAssetMovement
     * @return \Illuminate\Http\Response
     */
    public function show(FixedAssetMovement $fixedAssetMovement)
    {
        $result["success"]  = true;
        $result["data"]  = new FixedAssetMovementResource($fixedAssetMovement);
        return $result;
    }

    /**
     * create a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request): JsonResponse
    {
        $validate = Validator::make($request->all(), [
            'fixedAssetId' => 'required|integer',
            'movementDate' => 'required',
            'movementTypeId' => 'required',
            'businessUnitId' => 'required'
        ], [
            'fixedAssetId.required' => "El ID del activo fijo es obligatorio.",
            'movementDate.required' => 'La fecha del movimiento es obligatorio.',
            'movementTypeId.required' => 'El tipo de movimiento es obligatorio.',
            'businessUnitId.required' => 'La unidad de negocio es obligatorio.',
        ]);

        $a_response = [];

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {

            $a_movementType = FixedAssetMovement::getMovementTypeItems();
            $i_changeValue = $a_movementType[$request->movementTypeId]['changeValue'];

            $o_fixedAssetMovement = new FixedAssetMovement();
            $o_fixedAssetMovement->fixed_asset_id = $request->fixedAssetId;
            $o_fixedAssetMovement->movement_date = $request->movementDate;
            $o_fixedAssetMovement->movement_type = $request->movementTypeId;
            $o_fixedAssetMovement->change_value = $i_changeValue;
            $o_fixedAssetMovement->business_unit_id = $request->businessUnitId;
            $o_fixedAssetMovement->last = 1;
            $o_fixedAssetMovement->area_id = isset($request->areaId) ? $request->areaId : null;
            $o_fixedAssetMovement->responsible_id = isset($request->responsibleId) ? $request->responsibleId : null;
            $o_fixedAssetMovement->ubication = isset($request->ubication) ? $request->ubication : null;
            $o_fixedAssetMovement->observation = isset($request->observation) ? $request->observation : null;

            $o_fixedAsset = FixedAsset::find($request->fixedAssetId);

            $d_historicalDecrease = 0;
            $d_historicalIncrease = 0;
            $d_historicalDepreciation = 0;
            $d_actualValue = 0;

            if (isset($o_fixedAsset->fixedAssetMovement)) {
                $d_historicalDecrease = $o_fixedAsset->fixedAssetMovement->historical_decrease;
                $d_historicalIncrease = $o_fixedAsset->fixedAssetMovement->historical_increase;
                $d_historicalDepreciation = $o_fixedAsset->fixedAssetMovement->historical_depreciation;
                $d_actualValue = $o_fixedAsset->fixedAssetMovement->actual_value;
                $o_lastfixedAssetMovement = FixedAssetMovement::find($o_fixedAsset->fixedAssetMovement->fixed_asset_movement_id);
                $o_lastfixedAssetMovement->last = 0;
            }

            if ($i_changeValue == 1) {

                $o_fixedAssetMovement->increase = isset($request->increase) ? $request->increase : 0;
                $o_fixedAssetMovement->decrease = isset($request->decrease) ? $request->decrease : 0;
                $o_fixedAssetMovement->movement_id = isset($request->movement_id) ? $request->movement_id : null;

                if (isset($request->decrease) && $request->decrease > 0) {
                    $o_fixedAssetMovement->historical_depreciation = $o_fixedAssetMovement->movement_type  == FixedAssetMovement::MOVEMENT_TYPE_DEPRECIATION ? $d_historicalDepreciation + $o_fixedAssetMovement->decrease : $d_historicalDepreciation;
                    $o_fixedAssetMovement->historical_decrease = $d_historicalDecrease + $o_fixedAssetMovement->decrease;
                } else {
                    $o_fixedAssetMovement->historical_depreciation = $d_historicalDepreciation;
                    $o_fixedAssetMovement->historical_decrease =  $d_historicalDecrease;
                }

                if (isset($request->increase) && $request->increase > 0) {
                    $o_fixedAssetMovement->historical_increase = $d_historicalIncrease + $o_fixedAssetMovement->increase;
                } else {
                    $o_fixedAssetMovement->historical_increase = $d_historicalIncrease;
                }
                $o_fixedAssetMovement->actual_value  = $o_fixedAssetMovement->historical_increase - $o_fixedAssetMovement->historical_decrease;
                $o_fixedAsset->balance_cost = $o_fixedAssetMovement->actual_value;
                $o_fixedAsset->historical_depreciation = $o_fixedAssetMovement->historical_depreciation;
            } else {
                $o_fixedAssetMovement->increase = 0;
                $o_fixedAssetMovement->decrease = 0;
                $o_fixedAssetMovement->historical_depreciation = $d_historicalDepreciation;
                $o_fixedAssetMovement->historical_decrease =  $d_historicalDecrease;
                $o_fixedAssetMovement->historical_increase = $d_historicalIncrease;
                $o_fixedAssetMovement->actual_value = $d_actualValue;
            }

            try {
                DB::beginTransaction();

                if (isset($o_lastfixedAssetMovement)) {
                    $o_lastfixedAssetMovement->save();
                }
                $o_fixedAssetMovement->save();
                $o_fixedAsset->last_fixed_asset_movement_id = $o_fixedAssetMovement->fixed_asset_movement_id;
                $o_fixedAsset->save();

                DB::commit();

                $a_response = [
                    'success' => true,
                    'message' => 'Se registró correctamente.',
                ];
            } catch (QueryException $ex) {
                DB::rollBack();
                $a_response = [
                    'success' => false,
                    'message' => $ex->getMessage(),
                ];
            }
        }
        return response()->json($a_response);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $i_deleted = FixedAssetMovement::destroy($id);

            if ($i_deleted > 0) {
                $a_response = [
                    'success' => true,
                    'message' => 'Se eliminó correctamente.',
                ];
            } else {
                $a_response = [
                    'success' => false,
                    'message' => 'El objeto no existe.',
                ];
            }
        } catch (QueryException $ex) {
            $a_response = [
                'success' => false,
                'message' => $ex->getMessage(),
            ];
        }
        return response()->json($a_response);
    }

    public function getManualMovementTypeItems()
    {
        $a_types = FixedAssetMovement::getManualMovementTypeItems();
        $a_result = [];

        foreach ($a_types as $code => $a_value) {
            $a_result[] = [
                'id' => $code,
                'name' => $a_value['label']
            ];
        }

        $a_response = [
            'success' => true,
            'data' => [
                'items' => $a_result
            ]
        ];

        return response()->json($a_response);
    }
}
