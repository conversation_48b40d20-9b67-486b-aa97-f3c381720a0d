<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ECommerceProvider
 * 
 * @property int $e_commerce_provider_id
 * @property int $person_id
 * @property string $currency
 * @property bool $include_igv
 * @property bool $include_insurance
 * @property bool $use_volumetric_weight
 * @property float $additional_percentage
 * @property bool $by_item
 * @property bool $status
 * @property bool $default
 * @property bool $is_defaultable
 * 
 * @property Person $person
 * @property Collection|ECommerceDestinyType[] $e_commerce_destiny_types
 *
 * @package App\Models
 */
class ECommerceProvider extends Model
{
	protected $table = 'e_commerce_provider';
	protected $primaryKey = 'person_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'e_commerce_provider_id' => 'int',
		'person_id' => 'int',
		'include_igv' => 'bool',
		'include_insurance' => 'bool',
		'use_volumetric_weight' => 'bool',
		'additional_percentage' => 'float',
		'by_item' => 'bool',
		'status' => 'bool',
		'default' => 'bool',
		'is_defaultable' => 'bool'
	];

	protected $fillable = [
		'e_commerce_provider_id',
		'currency',
		'include_igv',
		'include_insurance',
		'use_volumetric_weight',
		'additional_percentage',
		'by_item',
		'status',
		'default',
		'is_defaultable'
	];

	public function person()
	{
		return $this->belongsTo(Person::class);
	}

	public function e_commerce_destiny_types()
	{
		return $this->hasMany(ECommerceDestinyType::class, 'person_id');
	}
}
