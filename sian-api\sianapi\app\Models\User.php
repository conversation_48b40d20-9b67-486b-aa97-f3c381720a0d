<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use PHPOpenSourceSaver\JWTAuth\Contracts\JWTSubject;
/**
 * Class User
 * 
 * @property int $user_id
 * @property string $username
 * @property string $password
 * @property string $hash
 * @property bool $status
 * @property string $email_address
 * @property string $phone_number
 * @property bool $level
 * @property int $person_id
 * @property bool $remember_filters
 * @property string|null $restore_id
 * 
 * @property Person $person
 *
 * @package App\Models
 */
class User extends Authenticatable implements JWTSubject
{
	use HasFactory, Notifiable;

	protected $table = 'user';
	protected $primaryKey = 'user_id';
	public $timestamps = false;
	const OWNER = 'User';

	public $user_token = null;

	protected $casts = [
		'status' => 'bool',
		'level' => 'bool',
		'person_id' => 'int',
		'remember_filters' => 'bool'
	];

	protected $hidden = [
		'password',
		'hash',
	];

	protected $fillable = [
		'username',
		'password',
		'hash',
		'status',
		'email_address',
		'phone_number',
		'level',
		'person_id',
		'remember_filters',
		'restore_id'
	];

	public function person()
	{
		return $this->belongsTo(Person::class, 'person_id', 'person_id');
	}

	/**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

	/*
		public function username()
		{
			return $this->username;
		}
	*/

	public function getAuthPassword()
    {
        return $this->attributes['hash'];
    }
}
