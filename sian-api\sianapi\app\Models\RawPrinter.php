<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class RawPrinter
 * 
 * @property int $raw_printer_id
 * @property string $raw_printer_name
 * @property string $alias
 * @property string $raw_printer_serie
 * @property string|null $authorization_number
 * @property bool $status
 * @property bool $columns
 * @property string $line_spacing
 * @property string $default_font
 * @property string $finish_mode
 * @property string $type
 * @property bool $open_cashbox
 * @property bool $default
 * @property string|null $owner
 * @property int|null $owner_id
 * @property bool $is_defaultable
 * 
 *
 * @package App\Models
 */
class RawPrinter extends Model
{
	protected $table = 'raw_printer';
	protected $primaryKey = 'raw_printer_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'columns' => 'bool',
		'open_cashbox' => 'bool',
		'default' => 'bool',
		'owner_id' => 'int',
		'is_defaultable' => 'bool'
	];

	protected $fillable = [
		'raw_printer_name',
		'alias',
		'raw_printer_serie',
		'authorization_number',
		'status',
		'columns',
		'line_spacing',
		'default_font',
		'finish_mode',
		'type',
		'open_cashbox',
		'default',
		'owner',
		'owner_id',
		'is_defaultable'
	];

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}
}
