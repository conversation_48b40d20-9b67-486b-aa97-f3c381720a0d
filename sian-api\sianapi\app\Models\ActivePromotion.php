<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ActivePromotion
 * 
 * @property int $product_id
 * @property float $equivalence
 * @property int $store_id
 * @property int $promotion_item_id
 * @property Carbon $begin_date
 * @property Carbon $end_date
 * 
 * @property Presentation $presentation
 * @property PromotionItem $promotion_item
 * @property Store $store
 *
 * @package App\Models
 */
class ActivePromotion extends Model
{
	protected $table = 'active_promotion';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'product_id' => 'int',
		'equivalence' => 'float',
		'store_id' => 'int',
		'promotion_item_id' => 'int'
	];

	protected $dates = [
		'begin_date',
		'end_date'
	];

	protected $fillable = [
		'promotion_item_id',
		'begin_date',
		'end_date'
	];

	public function presentation()
	{
		return $this->belongsTo(Presentation::class, 'product_id')
					->where('presentation.product_id', '=', 'active_promotion.product_id')
					->where('presentation.equivalence', '=', 'active_promotion.equivalence');
	}

	public function promotion_item()
	{
		return $this->belongsTo(PromotionItem::class);
	}

	public function store()
	{
		return $this->belongsTo(Store::class);
	}
}
