<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FactorDetail
 * 
 * @property int $factor_detail_id
 * @property int $factor_id
 * @property Carbon|null $date_ini
 * @property Carbon|null $date_end
 * @property string $value
 * 
 * @property Factor $factor
 *
 * @package App\Models
 */
class FactorDetail extends Model
{
	protected $table = 'factor_detail';
	protected $primaryKey = 'factor_detail_id';
	public $timestamps = false;

	protected $casts = [
		'factor_id' => 'int'
	];

	protected $dates = [
		'date_ini',
		'date_end'
	];

	protected $fillable = [
		'factor_id',
		'date_ini',
		'date_end',
		'value'
	];

	public function factor()
	{
		return $this->belongsTo(Factor::class);
	}
}
