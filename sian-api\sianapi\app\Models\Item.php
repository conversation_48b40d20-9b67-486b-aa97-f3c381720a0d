<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Item
 *
 * @property int $item_id
 * @property int $movement_id
 * @property string $owner
 * @property int|null $product_id
 * @property int|null $item_type_id
 * @property string $product_type
 * @property string $product_name
 * @property float $pres_quantity
 * @property float $equivalence
 * @property float $unit_quantity
 * @property float $unit_balance
 * @property float $total_pen
 * @property float $total_usd
 * @property float $balance_pen
 * @property float $balance_usd
 * @property bool $allow_decimals
 * @property int $item_number
 * @property int|null $promotion_item_id
 * @property int|null $promotion_gift_option_id
 * @property int|null $combination_id
 *
 * @property Combination|null $combination
 * @property Movement $movement
 * @property Multitable|null $multitable
 * @property Presentation|null $presentation
 * @property PromotionGiftOption|null $promotion_gift_option
 * @property PromotionItem|null $promotion_item
 * @property Collection|BatchMovement[] $batch_movements
 * @property CommercialMovementProduct $commercial_movement_product
 * @property ItemLink $item_link
 * @property Collection|Kardex[] $kardexes
 * @property Collection|KardexItem[] $kardex_items
 * @property Collection|ProductList[] $product_lists
 * @property TransformationResult $transformation_result
 * @property WarehouseMerchandise $warehouse_merchandise
 *
 * @package App\Models
 */
class Item extends Model
{
    const PRODUCT_TYPE_MERCHANDISE = 'M';
    const PRODUCT_TYPE_SERVICE = 'S';
    const PRODUCT_TYPE_GROUP = 'G';
	protected $table = 'item';
	protected $primaryKey = 'item_id';
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'product_id' => 'int',
		'item_type_id' => 'int',
		'pres_quantity' => 'float',
		'equivalence' => 'float',
		'unit_quantity' => 'float',
		'unit_balance' => 'float',
		'total_pen' => 'float',
		'total_usd' => 'float',
		'balance_pen' => 'float',
		'balance_usd' => 'float',
		'allow_decimals' => 'bool',
		'item_number' => 'int',
		'promotion_item_id' => 'int',
		'promotion_gift_option_id' => 'int',
		'combination_id' => 'int'
	];

	protected $fillable = [
		'movement_id',
		'owner',
		'product_id',
		'item_type_id',
		'product_type',
		'product_name',
		'pres_quantity',
		'equivalence',
		'unit_quantity',
		'unit_balance',
		'total_pen',
		'total_usd',
		'balance_pen',
		'balance_usd',
		'allow_decimals',
		'item_number',
		'promotion_item_id',
		'promotion_gift_option_id',
		'combination_id'
	];

	public function combination()
	{
		return $this->belongsTo(Combination::class);
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function multitable()
	{
		return $this->belongsTo(Multitable::class, 'item_type_id');
	}

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}

	public function presentation()
	{
		return $this->belongsTo(Presentation::class, 'product_id')
					->where('presentation.product_id', '=', 'item.product_id')
					->where('presentation.equivalence', '=', 'item.equivalence');
	}

	public function promotion_gift_option()
	{
		return $this->belongsTo(PromotionGiftOption::class);
	}

	public function promotion_item()
	{
		return $this->belongsTo(PromotionItem::class);
	}

	public function batch_movements()
	{
		return $this->hasMany(BatchMovement::class);
	}

	public function commercial_movement_product()
	{
		return $this->hasOne(CommercialMovementProduct::class);
	}

	public function item_link()
	{
		return $this->hasOne(ItemLink::class, 'parent_item_id');
	}

	public function kardexes()
	{
		return $this->hasMany(Kardex::class);
	}

	public function kardex_items()
	{
		return $this->hasMany(KardexItem::class);
	}

	public function product_lists()
	{
		return $this->belongsToMany(ProductList::class, 'product_list_item', 'item_id', 'owner')
					->withPivot('owner_id', 'amount_pen', 'amount_usd', 'total_pen', 'total_usd');
	}

	public function transformation_result()
	{
		return $this->hasOne(TransformationResult::class);
	}

	public function warehouse_merchandise()
	{
		return $this->hasOne(WarehouseMerchandise::class);
	}
}
