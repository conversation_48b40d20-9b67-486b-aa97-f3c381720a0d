<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Product
 *
 * @property int $product_link_id
 * @property int $product_parent_id
 * @property int $product_id
 * @property float $quantity
 * @property float $equivalence
 * @property float $unit_cost
 * @property float $waste_percentage
 *
 * @property Product $productParent
 * @property Product $product
 * @property Merchandise $merchandise
 *
 * @package App\Models
 **/
class ProductLink extends Model {
    protected $table = 'product_link';
    protected $primaryKey = 'product_link_id';
    public $timestamps = false;
    protected $fillable = [
        'product_parent_id',
        'product_id',
        'quantity',
        'equivalence',
        'unit_cost',
        'waste_percentage',

    ];

    protected $casts = [
        'quantity' => 'float',
        'equivalence' => 'float',
        'unit_cost' => 'float',
        'waste_percentage' => 'float',
    ];

    public function productParent() {
        return $this->belongsTo(Product::class, 'product_parent_id', 'product_id');
    }

    public function product() {
        return $this->belongsTo(Product::class, 'product_id', 'product_id');
    }

    public function merchandise() {
        return $this->belongsTo(Merchandise::class, 'product_id', 'product_id');
    }



}
