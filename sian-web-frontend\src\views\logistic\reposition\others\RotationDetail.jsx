import {
    Box,
    IconButton,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TextField,
    Tooltip,
    Typography
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'store';
import StoreIcon from '@mui/icons-material/Store';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import RemoveCircleIcon from '@mui/icons-material/RemoveCircle';
import {
    addToCart,
    editToCart,
    getRepositionDataByProduct,
    openRotationModal,
    removeFromCart,
    setSelectedProduct,
    updateFormDataItem,
    updateFormSupplyDataItem
} from 'store/slices/reposition/reposition';
import ErrorIcon from '@mui/icons-material/Error';
import { addDays, parseDateToLocaleString } from 'utils/dates';
import Grid from 'ui-component/grid/Grid';
import AutoModeIcon from '@mui/icons-material/AutoMode';
import useLoading from 'hooks/useLoading';
import { BlockLoader } from 'ui-component/loaders/loaders';
import SIANLink from 'ui-component/SIAN/SIANLink';
import { FOOD_VALUE, RAW_MATERIAL } from 'models/Reposition';
import RightAlignedNumber from 'ui-component/grid/RightAlignedNumber';
import { stickyColumn } from 'ui-component/grid/Grid';
import DisplayCurrency from 'ui-component/display/DisplayCurrency';
import { UNIT_EQUIVALENCE } from 'models/Presentation';
import { MenuItem } from '@mui/material';
import NestedGrid from 'ui-component/grid/NestedGrid';
import { WashTwoTone } from '@mui/icons-material';

const findDerivedProductRecursively = (derivedProducts, targetProductId) => {
    if (!derivedProducts || !Array.isArray(derivedProducts)) return null;

    for (const derived of derivedProducts) {
        if (derived.product_id === targetProductId) {
            return derived;
        }

        if (derived.derivedProducts && derived.derivedProducts.length > 0) {
            const found = findDerivedProductRecursively(derived.derivedProducts, targetProductId);
            if (found) return found;
        }
    }

    return null;
};

const findMainProductWithDerivedProduct = (data, targetProductId) => {
    if (!data || !Array.isArray(data)) return null;

    for (const item of data) {
        const found = findDerivedProductRecursively(item.derivedProducts, targetProductId);
        if (found) return item;
    }

    return null;
};

const NumberAlert = ({ condition = false, title = '' }) => {
    if (!condition) {
        return null;
    }
    return (
        <Tooltip title={title}>
            <IconButton color="error">
                <ErrorIcon />
            </IconButton>
        </Tooltip>
    );
};

const AlertRotation = ({ rotationScale }) => {
    switch (rotationScale) {
        case 'AR':
            return (
                <Tooltip title="El producto tiene ALTA ROTACIÓN">
                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#c6e0b4' }}>&nbsp;</Box>
                </Tooltip>
            );
        case 'MR':
            return (
                <Tooltip title="El producto tiene MEDIA ROTACIÓN">
                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#ffe699' }}>&nbsp;</Box>
                </Tooltip>
            );
        case 'BR':
            return (
                <Tooltip title="El producto tiene BAJA ROTACIÓN">
                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#b11226' }}>&nbsp;</Box>
                </Tooltip>
            );
        default:
            return (
                <Tooltip title="El producto NO tiene ROTACIÓN">
                    <Box
                        sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '99999px',
                            backgroundColor: 'white',
                            border: '1px solid'
                        }}
                    >
                        &nbsp;
                    </Box>
                </Tooltip>
            );
    }
};

const AlertBreak = ({ break_scale = 'QB' }) => {
    switch (break_scale) {
        case 'SS':
            return (
                <Tooltip title="El punto de Quiebre es SOBRE STOCK">
                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#a777dd' }}>&nbsp;</Box>
                </Tooltip>
            );
        case 'OPT':
            return (
                <Tooltip title="El punto de Quiebre es OPTIMO">
                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#c6e0b4' }}>&nbsp;</Box>
                </Tooltip>
            );
        case 'MOD':
            return (
                <Tooltip title="El punto de Quiebre es MODERADO">
                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#ffe699' }}>&nbsp;</Box>
                </Tooltip>
            );
        case 'CRI':
            return (
                <Tooltip title="El punto de Quiebre es CRÍTICO">
                    <Box sx={{ width: 20, height: 20, borderRadius: '99999px', backgroundColor: '#b11226' }}>&nbsp;</Box>
                </Tooltip>
            );
        case 'QB':
            return (
                <Tooltip title="El punto de Quiebre esta QUEBRADO">
                    <Box
                        sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '99999px',
                            backgroundColor: 'white',
                            border: '1px solid'
                        }}
                    >
                        &nbsp;
                    </Box>
                </Tooltip>
            );
        default:
            return (
                <Tooltip title="El punto de Quiebre esta QUEBRADO">
                    <Box
                        sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '99999px',
                            backgroundColor: 'white',
                            border: '1px solid'
                        }}
                    >
                        &nbsp;
                    </Box>
                </Tooltip>
            );
    }
};

const processAnalisys = (productData, stores) => {
    if (!productData || !productData.analisys) return null;

    const listData = productData.analisys.map((item, index) => {
        const pk = `${item.product_id}-${item.store_id}`;
        return {
            ...item,
            rowIndex: index,
            quantity: parseFloat(item.unit_quantity_order).toFixed(4),
            unit_quantity: parseFloat(item.unit_quantity_order),
            unit_price: parseFloat(item.unit_price),
            default_unit_price: parseFloat(item.unit_price),
            provider: item.provider,
            pk
        };
    });

    stores.forEach((store) => {
        const existingItem = listData.find((item) => item.store_id === store.store_id);

        if (!existingItem) {
            listData.push({
                notAvailable: true,
                indicator_calculation_id: 4,
                store_id: store.store_id,
                store_name: store.store_name,
                product_id: productData.product_id,
                product_name: productData.product_name,
                measure_name: productData.measure_name,
                equivalence_default: productData.equivalence_default,
                measure_default: productData.measure_default,
                provider_id: productData.provider_id,
                provider_number: productData.provider_number,
                provider: productData.provider,
                warehouse_id: store.warehouse_id,
                unit_price: productData.unit_price,
                expires: 0,
                vcto_alert: '-',
                rotation_scale: '-',
                rotation_value: 0,
                rotation_indicator: 'NR(0)',
                rotation_color: '',
                obsolete: 0,
                obsolete_indicator: '-',
                pareto_percentage_sale: '0.0',
                pareto_percentage_utility: '0.0',
                stock: '0.00',
                to_enter: '0.00',
                to_dispatch: '0.00',
                purchase_stock: '0.00',
                average_quantity: '0.00',
                average_diary: '0.0000',
                inventory_days: 0,
                break_value: 0,
                break_scale: '-',
                min_stock: 0,
                reposition_stock: 0,
                unit_quantity_order: 0,
                rowIndex: 0,
                quantity: '0',
                unit_quantity: 0,
                default_unit_price: productData.default_unit_price,
                pk: `${productData.product_id}-${store.store_name}`
            });
        }
    });

    listData.sort((a, b) => a.warehouse_id - b.warehouse_id);

    return listData;
};

const getDerivedProducts = (productData) => {
    if (!productData || !productData.derivedProducts) return [];

    return productData.derivedProducts.map((item, index) => ({
        ...item,
        pk: item.product_id,
        globalIndex: index
    }));
};

const NestedCard = ({ children, width = '50%' }) => (
    <Box sx={{ p: 1, backgroundColor: 'white', borderRadius: '1rem', border: '1px solid #e0e0e0', width }}>{children}</Box>
);

const DerivedProductAnalysis = ({ row, columns }) => {
    const productId = row[0];
    const { data } = useSelector((state) => state.reposition);
    const { data: storeData } = useSelector((state) => state.store);
    const mainProductData = findMainProductWithDerivedProduct(data, productId);
    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);
    const derivedAnalysis = derivedProduct?.analisys ? processAnalisys(derivedProduct, storeData) : null;

    if (!derivedAnalysis || derivedAnalysis.length === 0) {
        return (
            <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                <Typography variant="body2">No hay análisis disponible para este producto derivado</Typography>
            </Box>
        );
    }

    const DerivedAnalysisGrid = () => {
        const [repositionDerivedProduct] = useState(derivedAnalysis);

        return (
            <Grid
                columns={columns}
                title="Análisis por Tienda"
                data={repositionDerivedProduct}
                options={{
                    search: false,
                    download: false,
                    print: false,
                    sort: false,
                    viewColumns: false,
                    filter: false,
                    responsive: 'vertical',
                    fixedHeader: false,
                    selectableRows: 'none',
                    pagination: false,
                    toolbar: false,
                    setTableProps: () => ({
                        size: 'small',
                        sx: {
                            '& .MuiTableHead-root .MuiTableCell-root': {
                                fontSize: '0.875rem',
                                fontWeight: 'bold',
                                padding: '8px 16px'
                            },
                            '& .MuiTableBody-root .MuiTableCell-root': {
                                fontSize: '1.2rem',
                                padding: '36px 16px'
                            }
                        }
                    })
                }}
            />
        );
    };

    return (
        <Box sx={{ width: '100%', height: 'fit-content' }}>
            <Box
                sx={{
                    p: 2,
                    backgroundColor: 'white',
                    borderRadius: '1rem',
                    border: '1px solid #e0e0e0',
                    '& .MuiTableHead-root .MuiTableCell-root': {
                        fontSize: '0.875rem',
                        fontWeight: 'bold',
                        padding: '8px 16px'
                    },
                    '& .MuiTableBody-root .MuiTableCell-root': {
                        fontSize: '1.2rem',
                        padding: '36px 16px'
                    }
                }}
            >
                <DerivedAnalysisGrid />
            </Box>
        </Box>
    );
};

const DerivedProductNestedContent = ({ row, data, derivedAnalysisColumns, simplifiedDerivedProductColumns }) => {
    const productId = row[0];
    const mainProductData = findMainProductWithDerivedProduct(data, productId);
    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);
    const hasSubDerived = derivedProduct?.derivedProducts && derivedProduct.derivedProducts.length > 0;

    return (
        <Box sx={{ display: 'flex', flexDirection: 'row', gap: 2, py: 1 }}>
            <Box sx={{ width: hasSubDerived ? '25%' : '100%' }}>
                <DerivedProductAnalysis row={row} columns={derivedAnalysisColumns} />
            </Box>
            {hasSubDerived && (
                <Box sx={{ width: '75%' }}>
                    <SubDerivedProducts row={row} columns={simplifiedDerivedProductColumns} analysisColumns={derivedAnalysisColumns} />
                </Box>
            )}
        </Box>
    );
};

const SubDerivedProducts = ({ row, columns, analysisColumns }) => {
    const productId = row[0];
    const { data } = useSelector((state) => state.reposition);
    const mainProductData = findMainProductWithDerivedProduct(data, productId);
    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);
    const rawSubDerivedProducts = derivedProduct?.derivedProducts || [];

    const subDerivedProducts = rawSubDerivedProducts.map((item, index) => ({
        ...item,
        pk: item.product_id,
        globalIndex: index
    }));

    if (subDerivedProducts.length === 0) {
        return (
            <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                <Typography variant="body2">No hay productos derivados adicionales</Typography>
            </Box>
        );
    }

    return (
        <Box sx={{ width: '100%', height: 'fit-content' }}>
            <Box
                sx={{
                    p: 1,
                    backgroundColor: 'white',
                    borderRadius: '1rem',
                    border: '1px solid #e0e0e0',
                    width: '100%',
                    '& .MuiTable-root': {
                        width: '100% !important',
                        tableLayout: 'fixed'
                    },
                    '& .MuiTableCell-root': {
                        padding: '8px 16px'
                    }
                }}
            >
                <NestedGrid
                    columns={columns}
                    data={subDerivedProducts}
                    RenderNestedContent={(props) => <DerivedProductAnalysis {...props} columns={analysisColumns || columns} />}
                    options={{
                        search: false,
                        download: false,
                        print: false,
                        sort: false,
                        viewColumns: false,
                        filter: false,
                        responsive: 'vertical',
                        fixedHeader: false,
                        selectableRows: 'none',
                        pagination: false,
                        toolbar: false,
                        setTableProps: () => ({
                            size: 'small'
                        })
                    }}
                />
            </Box>
        </Box>
    );
};

export default function RotationDetail({ row, isFromProyection = null, foodMode = null }) {
    const dispatch = useDispatch();
    const { data, formData, cart, filters, merchandiseFoodData, formSupplyData } = useSelector((state) => state.reposition);
    const { data: storeData } = useSelector((state) => state.store);

    const isRowDataAvailable = (rowData) => {
        return rowData && !rowData.notAvailable;
    };

    const getRowDataSafely = (pk) => {
        try {
            const rowData = repositionProduct.find((item) => item.pk === pk);
            const result = rowData || { notAvailable: true };
            return result;
        } catch (error) {
            return { notAvailable: true };
        }
    };

    const renderSafeContent = (pk, renderFunction) => {
        try {
            const rowData = getRowDataSafely(pk);
            if (!isRowDataAvailable(rowData)) {
                return <Typography color="gray">-</Typography>;
            }
            return renderFunction(rowData);
        } catch (error) {
            console.warn('Error al renderizar contenido para pk:', pk, error);
            return <Typography color="gray">-</Typography>;
        }
    };

    const supplyAnalisys = processAnalisys(
        isFromProyection ? merchandiseFoodData.find((item) => item.product_id === row[0]) : data.find((item) => item.product_id === row[0]),
        storeData
    );
    const productData = isFromProyection
        ? merchandiseFoodData.find((item) => item.product_id === row[0])
        : data.find((item) => item.product_id === row[0]);
    const derivedProducts = getDerivedProducts(productData);

    const [repositionProduct, setRepositionProduct] = useState(supplyAnalisys || []);
    const [isAsync] = useState(!supplyAnalisys);
    const [loading, startLoading, endLoading] = useLoading(isAsync);

    const openModal = () => dispatch(openRotationModal());
    const setSelected = (data) => dispatch(setSelectedProduct(data));

    const reload = () => {
        if (isAsync) {
            startLoading();
            dispatch(getRepositionDataByProduct(row[0], { ...filters, mode: isFromProyection ? 'Merc2' : filters.mode }, storeData)).then(
                (data) => {
                    setRepositionProduct(data);
                    endLoading();
                }
            );
        }
    };

    const handleAddToCart = (item) => {
        dispatch(addToCart(item));
    };

    const handleRemoveFromCart = (pk) => {
        dispatch(removeFromCart(pk));
    };

    const handleEditCart = (pk, updatedData) => {
        dispatch(editToCart(pk, updatedData));
    };

    useEffect(() => {
        reload();
    }, []);

    const QuantityInput = ({ tableMeta: { rowData: rowMetadata }, keyword = 'quantity_oc' }) => {
        const pk = rowMetadata[0];

        const rowData = filters.mode === FOOD_VALUE ? formSupplyData[pk] : formData[pk];
        const cartRowData = cart[pk];
        const repositionProductData = repositionProduct.find((item) => item.pk === pk);

        const getQuantity = (cartRowData, rowData, keyword) => {
            if (cartRowData && cartRowData[keyword] !== undefined) {
                return cartRowData[keyword];
            }
            if (rowData && rowData[keyword] !== undefined) {
                return rowData[keyword];
            }
            return 0;
        };

        const [numberInput, setNumberInput] = useState(getQuantity(cartRowData, rowData, keyword));

        useEffect(() => {
            const quantity = getQuantity(cartRowData, rowData, keyword);
            setNumberInput(parseFloat(quantity).toFixed(2));
        }, [rowData?.[keyword], cartRowData?.[keyword]]);

        const handleBlur = () => {
            const newValue = repositionProductData?.presentation?.allowDecimals === 1 ? numberInput : parseFloat(numberInput);
            const fixedValue = Math.floor(newValue).toFixed(2) || 0;
            setNumberInput(fixedValue);

            const updateAction = filters.mode === FOOD_VALUE ? updateFormSupplyDataItem : updateFormDataItem;
            dispatch(
                updateAction(pk, {
                    [keyword]: fixedValue,
                    hasTouch: true,
                    product_id: repositionProductData.product_id,
                    store_id: repositionProductData.store_id
                })
            );

            if (cartRowData) {
                handleEditCart(pk, { [keyword]: fixedValue });
            }
        };

        const handleChange = ({ target: { value } }) => {
            const newValue = parseFloat(value) || 0;
            setNumberInput(newValue);
        };

        const autocomplete = () => {
            const productData = data.find((item) => item.pk === row[0]);
            const repositionRowData = repositionProduct.find((item) => item.pk === pk);
            const estimated = parseFloat(repositionRowData.unit_quantity) / parseFloat(productData?.presentation?.equivalence ?? 1);

            if (estimated > 0) {
                let keyOtherValue = '';
                let newValue = estimated;

                if (rowData) {
                    if (keyword === 'quantity_oc') {
                        keyOtherValue = 'quantity_ota';
                    } else {
                        keyOtherValue = 'quantity_oc';
                    }

                    newValue = estimated - (rowData[keyOtherValue] ?? 0);
                }

                if (newValue > 0) {
                    const updateAction = filters.mode === FOOD_VALUE ? updateFormSupplyDataItem : updateFormDataItem;
                    dispatch(updateAction(pk, { [keyword]: newValue, hasTouch: true, store_id: repositionProductData.store_id }));

                    if (cartRowData) {
                        handleEditCart(pk, { [keyword]: newValue });
                    }
                }
            }
        };

        return (
            <Box>
                <TextField
                    variant="outlined"
                    sx={{ width: '8rem' }}
                    size="small"
                    type="number"
                    value={numberInput}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    InputProps={{
                        inputProps: {
                            style: { textAlign: 'right' },
                            step: rowData?.presentation?.allowDecimals === 1 ? 0.1 : 1,
                            min: 0,
                            inputMode: rowData?.presentation?.allowDecimals === 1 ? 'decimal' : 'numeric',
                            pattern: '[0-9]*'
                        }
                    }}
                />
                {filters.mode !== FOOD_VALUE && !repositionProductData.notAvailable && (
                    <Tooltip title="Volver al Valor Sugerido">
                        <IconButton color="primary" aria-label="Volver al Valor Original" onClick={autocomplete}>
                            <AutoModeIcon />
                        </IconButton>
                    </Tooltip>
                )}
            </Box>
        );
    };

    const SelectProduct = ({ tableMeta }) => {
        const pk = tableMeta.rowData[0];
        const product_id = row[0];

        const rowItem = filters.mode === FOOD_VALUE ? formSupplyData[pk] : formData[pk];
        const productData = data.find((item) => item.pk === product_id);
        const cartSelected = cart[pk];
        const sum_quantity = parseFloat(rowItem?.quantity_oc ?? 0) + parseFloat(rowItem?.quantity_ota ?? 0);

        const handleClick = () => {
            if (cartSelected) {
                handleRemoveFromCart(pk);
            } else {
                handleAddToCart({
                    ...rowItem,
                    store_id: tableMeta.rowData[1],
                    store_name: tableMeta.rowData[2],
                    product_name: productData.product_name,
                    provider: productData.provider,
                    provider_id: productData.provider_id,
                    provider_number: productData.provider_number,
                    equivalence: productData.presentation.equivalence,
                    presentation: productData.presentation,
                    product_id: row[0],
                    unit_price: productData.unit_price
                });
            }
        };

        useEffect(() => {
            if (cartSelected && sum_quantity <= 0) {
                handleRemoveFromCart(pk);
            }
        }, [sum_quantity, cartSelected]);

        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                {rowItem && sum_quantity > 0 ? (
                    <>
                        {cartSelected ? (
                            <RemoveCircleIcon color="error" fontSize="large" onClick={handleClick} style={{ cursor: 'pointer' }} />
                        ) : (
                            <AddCircleIcon color="primary" fontSize="large" onClick={handleClick} style={{ cursor: 'pointer' }} />
                        )}
                    </>
                ) : (
                    <Tooltip title="La cantidad es insuficiente para añadir al carrito" sx={{ color: '#bdbdbd' }}>
                        <AddCircleIcon color="inherit" fontSize="large" style={{ cursor: 'pointer' }} />
                    </Tooltip>
                )}
            </Box>
        );
    };

    const columns = [
        {
            name: 'pk',
            label: 'PK',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'store_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'store_name',
            label: 'TIENDA',
            options: {
                filter: true,
                sort: true
            }
        },
        {
            name: 'inventory_days',
            label: 'DIAS INV',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (!isRowDataAvailable(rowData)) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return (
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            {value}
                            <NumberAlert condition={parseFloat(value) < 1} title="No hay stock para cumplir con la demanda requerida" />
                        </Box>
                    );
                }
            }
        },
        {
            name: 'break_value',
            label: 'P.QUIEBRE',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);

                    if (!isRowDataAvailable(rowData)) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return (
                        <Box
                            sx={{
                                display: 'flex',
                                gap: 1,
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '100%',
                                cursor: 'pointer'
                            }}
                        >
                            {`${rowData?.break_scale ?? 'QB'}(${parseFloat(value) > 0 ? value : '-'})`}
                            <AlertBreak break_scale={rowData?.break_scale ?? 'QB'} />
                        </Box>
                    );
                }
            }
        },
        {
            name: 'inventory_days',
            label: 'F.QUIEBRE',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = getRowDataSafely(pk);
                    if (!isRowDataAvailable(rowData)) {
                        return <Typography color="gray">-</Typography>;
                    }
                    return (
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            {parseDateToLocaleString(addDays(new Date(), parseInt(value, 10)))}
                            <NumberAlert condition={parseInt(value, 10) < 1} title="El quiebre es Crítico" />
                        </Box>
                    );
                }
            }
        },
        {
            name: 'break_value',
            label: 'F.LIMITE PEDIDO',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    return renderSafeContent(pk, (rowData) => {
                        return (
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                {parseDateToLocaleString(addDays(new Date(), parseInt(value, 10)))}
                                <NumberAlert condition={parseInt(value, 10) < 1} title="El quiebre es Crítico" />
                            </Box>
                        );
                    });
                }
            }
        },
        {
            name: 'vcto_alert',
            label: 'VCTO',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData && rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }
                    return value;
                }
            }
        },
        {
            name: 'rotation_scale',
            label: 'ROTACIÓN',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }
                    return value;
                }
            }
        },
        {
            name: 'rotation_value',
            label: 'INDICE DE ROTACIÓN',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const store = tableMeta.rowData[1];
                    const rowData = repositionProduct.find((item) => item.pk === pk);

                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return (
                        <Box
                            sx={{
                                display: 'flex',
                                gap: 1,
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '100%',
                                cursor: 'pointer'
                            }}
                            onClick={() => {
                                setSelected({ ...rowData, store });
                                openModal();
                            }}
                        >
                            {rowData?.rotation_indicator ?? '-'}
                            <AlertRotation rotationScale={rowData?.rotation_scale ?? 'NR'} />
                        </Box>
                    );
                }
            }
        },
        {
            name: 'obsolete_indicator',
            label: '% OBSOLETO',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return `${value}%`;
                }
            }
        },
        {
            name: 'pareto_percentage_sale',
            label: 'PRTO % VOL',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return `${value}%`;
                }
            }
        },
        {
            name: 'pareto_percentage_utility',
            label: 'PRTO % UTL',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return `${value}%`;
                }
            }
        },
        {
            name: 'stock',
            label: 'STOCK LOCAL',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {value} <NumberAlert condition={parseFloat(value) < 1} title="No hay stock en el Local" />
                        </Box>
                    );
                }
            }
        },
        {
            name: 'to_enter',
            label: 'CANT X ING',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return value;
                }
            }
        },
        {
            name: 'to_dispatch',
            label: 'CANT X DES',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return value;
                }
            }
        },
        {
            name: 'purchase_stock',
            label: 'STOCK DISPON',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => parseFloat(value).toFixed(2)
            }
        },
        {
            name: 'min_stock',
            label: 'STOCK MIN',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }
                    return parseFloat(value).toFixed(2);
                }
            }
        },
        {
            name: 'average_quantity',
            label: 'PROM X 30 DÍAS',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }
                    return parseFloat(value).toFixed(2);
                }
            }
        },
        {
            name: 'unit_quantity',
            label: 'SUGERIDO',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = row[0];
                    const productData = data.find((item) => item.pk === pk);
                    const result = value / parseFloat(productData?.presentation?.equivalence ?? 1);

                    const product_id = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === product_id);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return Math.ceil(result).toFixed(2);
                }
            }
        },
        {
            name: 'quantity_oc',
            label: 'CANT A PEDIR',
            options: {
                filter: true,
                sort: false,
                display: true,
                customBodyRender: (value, tableMeta) => <QuantityInput tableMeta={tableMeta} />
            }
        },
        {
            name: 'quantity_ota',
            label: 'CANT A TRANSFERIR',
            options: {
                filter: true,
                sort: false,
                display: false,
                customBodyRender: (value, tableMeta) => <QuantityInput tableMeta={tableMeta} keyword="quantity_ota" />
            }
        },
        {
            name: 'measure_name',
            label: 'PRES',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: () => {
                    const pk = row[0];
                    const rowData = data.find((item) => item.pk === pk);
                    return rowData?.presentation?.measure_name ?? '';
                }
            }
        },
        {
            name: 'other',
            label: '-',
            options: {
                filter: false,
                sort: false,
                customBodyRender: (_, tableMeta) => <SelectProduct tableMeta={tableMeta} />
            }
        }
    ];

    const merchandiseFoodColumns = [
        {
            name: 'pk',
            label: 'PK',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'store_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'store_name',
            label: 'TIENDA',
            options: {
                filter: true,
                sort: true
            }
        },
        {
            name: 'rotation_scale',
            label: 'ROTACIÓN',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }
                    return value;
                }
            }
        },
        {
            name: 'rotation_value',
            label: 'INDICE DE ROTACIÓN',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const store = tableMeta.rowData[1];
                    const rowData = repositionProduct.find((item) => item.pk === pk);

                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return (
                        <Box
                            sx={{
                                display: 'flex',
                                gap: 1,
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '100%',
                                cursor: 'pointer'
                            }}
                            onClick={() => {
                                setSelected({ ...rowData, store });
                                openModal();
                            }}
                        >
                            {rowData?.rotation_indicator ?? '-'}
                            <AlertRotation rotationScale={rowData?.rotation_scale ?? 'NR'} />
                        </Box>
                    );
                }
            }
        },
        {
            name: 'obsolete_indicator',
            label: '% OBSOLETO',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return `${value}%`;
                }
            }
        },
        {
            name: 'pareto_percentage_sale',
            label: 'PRTO % VOL',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return `${value}%`;
                }
            }
        },
        {
            name: 'pareto_percentage_utility',
            label: 'PRTO % UTL',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return `${value}%`;
                }
            }
        },
        {
            name: 'to_enter',
            label: 'CANT X ING',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return value;
                }
            }
        },
        {
            name: 'to_dispatch',
            label: 'CANT X DES',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return value;
                }
            }
        },
        {
            name: 'average_quantity',
            label: 'PROM X 30 DÍAS',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }
                    return parseFloat(value).toFixed(2);
                }
            }
        },
        {
            name: 'unit_quantity',
            label: 'PROYECTADO A VENDER',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = row[0];
                    const productData = data.find((item) => item.pk === pk);
                    const result = value / parseFloat(productData?.presentation?.equivalence ?? 1);
                    const rowData = merchandiseFoodData.find((item) => item.pk === pk);
                    const measure_name = rowData?.presentation?.measure_name ?? '';
                    return `${Math.ceil(result).toFixed(2)} ${measure_name}`;
                }
            }
        }
    ];

    const supplyColumns = [
        {
            name: 'pk',
            label: 'PK',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'store_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'store_name',
            label: 'TIENDA',
            options: {
                filter: true,
                sort: true
            }
        },
        {
            name: 'unit_quantity_proyected',
            label: 'C.PROYECTADA',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'purchase_stock',
            label: 'STOCK',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = getRowDataSafely(pk);
                    if (!isRowDataAvailable(rowData)) {
                        return <Typography color="gray">-</Typography>;
                    }
                    return (
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end', gap: 1 }}>
                            <RightAlignedNumber value={value} />
                            <NumberAlert condition={parseFloat(value) < 1} title="No hay stock en el Local" />
                        </Box>
                    );
                }
            }
        },
        {
            name: 'to_enter',
            label: 'CANT X ING',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = getRowDataSafely(pk);
                    if (!isRowDataAvailable(rowData)) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return <RightAlignedNumber value={value} />;
                }
            }
        },
        {
            name: 'to_dispatch',
            label: 'CANT X DES',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = repositionProduct.find((item) => item.pk === pk);
                    if (rowData.notAvailable) {
                        return <Typography color="gray">-</Typography>;
                    }

                    return <RightAlignedNumber value={value} />;
                }
            }
        },
        {
            name: 'quantity_oc',
            label: 'CANTIDAD REPONER',
            options: {
                filter: true,
                sort: false,
                display: true,
                customBodyRender: (value, tableMeta) => <QuantityInput tableMeta={tableMeta} keyword="quantity_ota" />
            }
        }
    ];

    const recipeColumns = [
        {
            name: 'recipe_id',
            label: 'ID',
            options: {
                display: false
            }
        },
        {
            name: 'recipe_name',
            label: 'RECETA'
        },
        {
            name: 'recipe_quantity',
            label: 'CANTIDAD DE RECETA'
        },
        {
            name: 'supply_quantity',
            label: 'CANTIDAD DEL INSUMO'
        }
    ];

    const simplifiedDerivedProductColumns = [
        {
            name: 'product_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { width: '80px', maxWidth: '80px' } }),
                setCellProps: () => ({ style: { width: '80px', maxWidth: '80px' } })
            }
        },
        {
            name: 'product_name',
            label: 'PRODUCTO',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { minWidth: '450px', whiteSpace: 'nowrap' } }),
                setCellProps: () => ({ style: { minWidth: '450px', whiteSpace: 'nowrap' } }),
                customBodyRender: (value) => (
                    <Typography sx={{ whiteSpace: 'nowrap' }}>
                        <strong>{value}</strong>
                    </Typography>
                )
            }
        },
        {
            name: 'waste_info',
            label: (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <WashTwoTone sx={{ fontSize: '16px', color: '#ff9800' }} />
                    MERMA
                </Box>
            ),
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { minWidth: '200px', whiteSpace: 'nowrap' } }),
                setCellProps: () => ({ style: { minWidth: '200px', whiteSpace: 'nowrap' } }),
                customBodyRender: (value, tableMeta) => {
                    const wasteInfo = value;

                    if (!wasteInfo || !wasteInfo.waste_percentage_total) {
                        return (
                            <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center', color: '#999' }}>
                                Sin merma
                            </Typography>
                        );
                    }

                    const percentage = parseFloat(wasteInfo.waste_percentage_total);
                    const multiplier = parseFloat(wasteInfo.multiplier_to_total);

                    // Color basado en el porcentaje de merma
                    const getColor = (pct) => {
                        if (pct < 2) return '#4caf50'; // Verde para merma baja
                        if (pct < 5) return '#ff9800'; // Naranja para merma media
                        return '#f44336'; // Rojo para merma alta
                    };

                    return (
                        <Box sx={{ textAlign: 'center', whiteSpace: 'nowrap' }}>
                            <Typography
                                sx={{
                                    fontWeight: 'bold',
                                    color: getColor(percentage),
                                    fontSize: '0.9rem'
                                }}
                            >
                                {percentage.toFixed(2)}%
                            </Typography>
                            <Typography
                                sx={{
                                    fontSize: '0.75rem',
                                    color: '#666',
                                    lineHeight: 1
                                }}
                            >
                                x{multiplier.toFixed(3)}
                            </Typography>
                        </Box>
                    );
                }
            }
        },
        {
            name: 'purchase_stock',
            label: 'STOCK EN TIENDAS',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        // {
        //     name: 'purchase_stock',
        //     label: 'STOCK DE UNIDADES EN TIENDAS',
        //     options: {
        //         filter: true,
        //         sort: true,
        //         customBodyRender: (value, { rowData }) => {
        //             const product_id = rowData[0];
        //             const productData = data.find((item) => item.pk === product_id);
        //             const result = value / parseFloat(productData?.equivalence_default ?? 1);
        //             return <RightAlignedNumber value={result} />;
        //         }
        //     }
        // },
        {
            name: 'supplying_stock',
            label: 'STOCK A.PRINCIPAL',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'measure_name',
            label: 'PRESENTACIÓN',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <Typography>{value}</Typography>
            }
        },
        {
            name: 'measure_default',
            label: 'PRESENTACIÓN UNIDAD',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <Typography>{value}</Typography>
            }
        }
    ];

    const derivedAnalysisColumns = [
        {
            name: 'pk',
            label: 'PK',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'store_name',
            label: 'TIENDA',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <Typography sx={{ fontSize: '1.2rem', fontWeight: 'bold' }}>{value}</Typography>
            }
        },
        {
            name: 'stock',
            label: 'STOCK',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => (
                    <Typography sx={{ fontSize: '1.2rem', fontWeight: 'medium', textAlign: 'right' }}>
                        {parseFloat(value).toFixed(2)}
                    </Typography>
                )
            }
        }
    ];

    const derivedProductColumns = [
        {
            name: 'pk',
            label: 'PK',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'product_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true
            }
        },
        {
            name: 'product_name',
            label: 'PRODUCTO',
            options: {
                filter: true,
                sort: true,
                ...stickyColumn,
                customBodyRender: (value) => (
                    <Typography>
                        <strong>{value}</strong>
                    </Typography>
                )
            }
        },
        {
            name: 'provider_number',
            label: 'RUC',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'provider',
            label: 'PROVEEDOR',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'to_enter',
            label: 'CANT X ING',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'to_dispatch',
            label: 'CANT X DES',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        // {
        //     name: 'unit_quantity_proyected',
        //     label: 'C.PROYECTADA',
        //     options: {
        //         filter: false,
        //         sort: false,
        //         display: true,
        //         customBodyRender: (value, tableMeta) => <RightAlignedNumber value={value} />
        //     }
        // },
        {
            name: 'purchase_stock',
            label: 'STOCK TIENDAS',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'supplying_stock',
            label: 'STOCK A.PRINCIPAL',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'unit_quantity',
            label: 'REPONER TIENDAS',
            options: {
                filter: false,
                sort: false,
                display: true,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'presentations',
            label: 'PRES',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (value) => <Typography>{value || '-'}</Typography>
            }
        }
        // {
        //     name: 'unit_price',
        //     label: 'P.UNITARIO',
        //     options: {
        //         filter: true,
        //         sort: false,
        //         customBodyRender: (value) => <DisplayCurrency value={value} currency="pen" maxDecimals={4} />
        //     }
        // },
        // {
        //     name: 'total_price',
        //     label: 'P.TOTAL',
        //     options: {
        //         filter: true,
        //         sort: false,
        //         customBodyRender: (value) => <DisplayCurrency value={value} currency="pen" maxDecimals={4} />
        //     }
        // }
    ];

    const getColumns = () => {
        if (isFromProyection) {
            return merchandiseFoodColumns;
        }
        if (isAsync) {
            return columns;
        }
        return supplyColumns;
    };

    const getCardWidth = () => {
        if (isFromProyection && isAsync) {
            return '100%';
        }
        if (isFromProyection && !isAsync) {
            return '60%';
        }
        if (!isFromProyection && isAsync) {
            return '90%';
        }
        return '45%';
    };

    return (
        <Box sx={{ pt: 2, pl: 2, pr: 2, pb: 4, backgroundColor: '#f5f5f5' }}>
            <BlockLoader loading={loading}>
                <Box sx={{ display: 'flex', gap: 1, flexDirection: 'row', width: '100%', justifyContent: 'center' }}>
                    <NestedCard
                        width={foodMode === RAW_MATERIAL ? '30%' : getCardWidth()}
                        sx={{
                            '& .MuiTable-root': {
                                width: '100% !important',
                                tableLayout: 'fixed'
                            },
                            '& .MuiTableCell-root': {
                                padding: '8px 16px'
                            }
                        }}
                    >
                        <Grid
                            columns={getColumns()}
                            data={repositionProduct}
                            title="Análisis por Tienda"
                            options={{
                                search: false,
                                download: false,
                                print: false,
                                sort: false,
                                viewColumns: true,
                                filter: false,
                                filterType: 'multiselect',
                                responsive: 'vertical',
                                fixedHeader: true,
                                fixedSelectColumn: true,
                                jumpToPage: false,
                                resizableColumns: false,
                                draggableColumns: {
                                    enabled: true
                                },
                                serverSide: true,
                                selectableRows: 'none',
                                selectableRowsOnClick: false,
                                pagination: false,
                                confirmFilters: false,
                                rowHover: true,
                                toolbar: false
                            }}
                        />
                    </NestedCard>
                    {foodMode === RAW_MATERIAL && (
                        <NestedCard
                            width="70%"
                            sx={{
                                '& .MuiTable-root': {
                                    width: '100% !important',
                                    tableLayout: 'fixed'
                                },
                                '& .MuiTableCell-root': {
                                    padding: '8px 16px'
                                }
                            }}
                        >
                            <NestedGrid
                                columns={simplifiedDerivedProductColumns}
                                data={derivedProducts}
                                title="Productos Derivados"
                                RenderNestedContent={(props) => (
                                    <DerivedProductNestedContent
                                        {...props}
                                        data={data}
                                        derivedAnalysisColumns={derivedAnalysisColumns}
                                        simplifiedDerivedProductColumns={simplifiedDerivedProductColumns}
                                    />
                                )}
                                options={{
                                    search: false,
                                    download: false,
                                    print: false,
                                    sort: false,
                                    viewColumns: true,
                                    filter: false,
                                    filterType: 'multiselect',
                                    responsive: 'vertical',
                                    fixedHeader: true,
                                    fixedSelectColumn: true,
                                    jumpToPage: false,
                                    resizableColumns: false,
                                    draggableColumns: {
                                        enabled: true
                                    },
                                    serverSide: false,
                                    selectableRows: 'none',
                                    selectableRowsOnClick: false,
                                    pagination: false,
                                    confirmFilters: false,
                                    rowHover: true,
                                    toolbar: false,
                                    setTableProps: () => ({
                                        size: 'small'
                                    })
                                }}
                            />
                        </NestedCard>
                    )}
                </Box>
            </BlockLoader>
        </Box>
    );
}
