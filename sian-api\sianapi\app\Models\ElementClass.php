<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ElementClass
 * 
 * @property bool $element_class_code
 * @property string $element_class_name
 * 
 * @property Collection|Element[] $elements
 *
 * @package App\Models
 */
class ElementClass extends Model
{
	protected $table = 'element_class';
	protected $primaryKey = 'element_class_code';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'element_class_code' => 'bool'
	];

	protected $fillable = [
		'element_class_name'
	];

	public function elements()
	{
		return $this->hasMany(Element::class, 'element_class_code');
	}
}
