<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ComboItem
 * 
 * @property int $product_id
 * @property int $sub_product_id
 * @property float $sub_equivalence
 * @property string $product_type
 * @property float $pres_quantity
 * @property float $unit_quantity
 * @property float $mprice_pen
 * @property float $aprice_pen
 * @property float $wprice_pen
 * @property float $mprice_usd
 * @property float $aprice_usd
 * @property float $wprice_usd
 * @property float $proportion
 * @property int $item_number
 * 
 * @property Combo $combo
 * @property Presentation $presentation
 *
 * @package App\Models
 */
class ComboItem extends Model
{
	protected $table = 'combo_item';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'product_id' => 'int',
		'sub_product_id' => 'int',
		'sub_equivalence' => 'float',
		'pres_quantity' => 'float',
		'unit_quantity' => 'float',
		'mprice_pen' => 'float',
		'aprice_pen' => 'float',
		'wprice_pen' => 'float',
		'mprice_usd' => 'float',
		'aprice_usd' => 'float',
		'wprice_usd' => 'float',
		'proportion' => 'float',
		'item_number' => 'int'
	];

	protected $fillable = [
		'product_type',
		'pres_quantity',
		'unit_quantity',
		'mprice_pen',
		'aprice_pen',
		'wprice_pen',
		'mprice_usd',
		'aprice_usd',
		'wprice_usd',
		'proportion',
		'item_number'
	];

	public function combo()
	{
		return $this->belongsTo(Combo::class, 'product_id');
	}

	public function presentation()
	{
		return $this->belongsTo(Presentation::class, 'sub_product_id')
					->where('presentation.product_id', '=', 'combo_item.sub_product_id')
					->where('presentation.equivalence', '=', 'combo_item.sub_equivalence');
	}
}
