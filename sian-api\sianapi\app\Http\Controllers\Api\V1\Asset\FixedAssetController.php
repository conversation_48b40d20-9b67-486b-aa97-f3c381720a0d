<?php

namespace App\Http\Controllers\Api\V1\Asset;

use App\Http\Resources\Asset\FixedAssetCollection;
use Illuminate\Http\Request;
use App\Models\FixedAsset;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Exports\Asset\ExportFixedAsset;
use Illuminate\Support\Facades\Validator;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Maatwebsite\Excel\Facades\Excel;

use App\Http\Resources\Asset\FixedAssetResource;
use App\Http\Resources\Asset\SimpleFixedAssetResource;
use App\Http\Resources\Sp\PreCalcDepreciationResource;
use GrahamCampbell\ResultType\Success;

class FixedAssetController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $request->validate([
            'page' => 'int',
            'pageSize' => 'int',
            'sortField' => 'String',
            'direction' => 'String',
            'searchTerm' => 'String',
            'id' => 'int',
            'code' => 'String',
            'name' => 'String'
        ]);

        $i_page = (isset($request->page) && $request->page > 0) ? $request->page : 1;
        $i_pageSize = (isset($request->pageSize) && $request->pageSize > 0) ? $request->pageSize : 10;
        $s_sortField = isset($request->sortField) ? $request->sortField : 'fixed_asset_id';
        $s_direction = isset($request->direction) ? $request->direction : 'DESC';
        $s_searchTerm = isset($request->searchTerm) ? $request->searchTerm : '';
        $i_skip = ($i_page - 1) * $i_pageSize;

        //Query
        $s_query = FixedAsset::skip($i_skip);
        if (isset($request->id))
            $s_query->where('fixed_asset_id', $request->id);
        if (isset($request->code))
            $s_query->where('code', 'like', '%' . $request->code . '%');
        if (isset($request->name))
            $s_query->where('description', 'like', '%' . $request->name . '%');
        $s_query->orderBy($s_sortField, $s_direction);

        $fixedAssets = $s_query->paginate($i_pageSize);

        //$fixedAssets->appends(['pageSize' => $i_pageSize])->appends(['sortField' => $s_sortField])->appends(['direction' => $s_direction]);
        //if (isset($request->id))
        //    $fixedAssets->appends(['id' => $request->id]);
        //if (isset($request->code))
        //    $fixedAssets->appends(['code' => $request->code]);
        //if (isset($request->name))
        //    $fixedAssets->appends(['name' => $request->name]);

        $paginator =  new FixedAssetCollection($fixedAssets);
        $a_response = [
            'success' => true,
            'data' => [
                'count' => $paginator->count(),
                'totalPages' => $paginator->lastPage(),
                'page' => $paginator->currentPage(),
                'pageSize' => $paginator->perPage(),
                'totalItems' => $paginator->total(),
                'items' => $paginator->items(),
            ]
        ];
        return $a_response;
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\FixedAsset  $fixedAsset
     * @return \Illuminate\Http\Response
     */
    public function show(FixedAsset $fixedAsset)
    {
        $result["success"]  = true;
        $result["data"]  = new FixedAssetResource($fixedAsset);
        return $result;
    }

    /**
     * create a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request): JsonResponse
    {
        $validate = Validator::make($request->all(), [
            'code' => 'required|string|max:25',
            'description' => 'required|string|max:100',
            'account' => 'required|string|max:9',
        ], [
            'code.required' => 'El campo código es obligatorio.',
            'description.required' => 'El campo descripción es obligatorio.',
            'account.required' => 'La cuenta contable es obligatoria.'
        ]);

        $a_response = [];

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {

            $o_fixedAsset = new FixedAsset();
            $o_fixedAsset->code = $request->code;
            $o_fixedAsset->description = $request->description;
            $o_fixedAsset->account_code = $request->account;
            $o_fixedAsset->model = $request->model;
            $o_fixedAsset->serie = $request->serie;
            $o_fixedAsset->status = 1;
            $o_fixedAsset->buy_date = $request->buyDate;
            $o_fixedAsset->init_used_date = $request->initUsedDate;
            $o_fixedAsset->type_sunat = $request->typeSunat;
            $o_fixedAsset->status_sunat = $request->statusSunat;
            $o_fixedAsset->required_depreciation = $request->requiredDepreciation;
            $o_fixedAsset->mark_id = isset($request->markId) ? $request->markId : null;
            $o_fixedAsset->buy_date = isset($request->buyDate) ? $request->buyDate : null;
            $o_fixedAsset->init_used_date = isset($request->initUsedDate) ? $request->initUsedDate : null;
            if ($o_fixedAsset->required_depreciation == 1) {
                $o_fixedAsset->depreciation_group_id = isset($request->requiredDepreciation) ? $request->depreciationGroupId : null;
                $o_fixedAsset->document_authorization_change_method = isset($request->documentAuthorizationChangeMethod) ? $request->documentAuthorizationChangeMethod : null;
                $o_fixedAsset->useful_life = isset($request->usefulLife) ? $request->usefulLife : null;
            }

            try {
                if ($o_fixedAsset->save()) {
                    $a_response = [
                        'success' => true,
                        'message' => 'Se registró correctamente.',
                    ];
                }
            } catch (QueryException $ex) {
                $a_response = [
                    'success' => false,
                    'message' => $ex->getMessage(),
                ];
            }
        }
        return response()->json($a_response);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validate = Validator::make($request->all(), [
            'description' => 'required|string|max:100',
            'account' => 'required|string|max:9',
        ], [
            'description.required' => 'El campo descripción es obligatorio.'
        ]);

        $a_response = [];

        if ($validate->fails()) {
            $a_response = [
                'success' => false,
                'message' => $validate->errors()->first()
            ];
        } else {

            $o_fixedAsset = FixedAsset::find($id);

            if (isset($o_fixedAsset)) {

                $o_fixedAsset->description = isset($request->description) ? $request->description : $o_fixedAsset->description;
                $o_fixedAsset->account_code = isset($request->account) ? $request->account : $o_fixedAsset->account_code;
                $o_fixedAsset->model = isset($request->model) ? $request->model : $o_fixedAsset->model;
                $o_fixedAsset->serie = isset($request->serie) ? $request->serie : $o_fixedAsset->serie;
                $o_fixedAsset->status = isset($request->status) ? $request->status : $o_fixedAsset->status;
                $o_fixedAsset->buy_date = isset($request->buyDate) ? $request->buyDate : $o_fixedAsset->buy_date;
                $o_fixedAsset->init_used_date = isset($request->initUsedDate) ? $request->initUsedDate : $o_fixedAsset->init_used_date;
                $o_fixedAsset->type_sunat = isset($request->typeSunat) ? $request->typeSunat : $o_fixedAsset->type_sunat;
                $o_fixedAsset->status_sunat = isset($request->statusSunat) ? $request->statusSunat :  $o_fixedAsset->status_sunat;
                $o_fixedAsset->required_depreciation = isset($request->requiredDepreciation) ? $request->requiredDepreciation :   $o_fixedAsset->required_depreciation;
                $o_fixedAsset->mark_id = isset($request->markId) ? $request->markId : $o_fixedAsset->mark_id;
                $o_fixedAsset->buy_date = isset($request->buyDate) ? $request->buyDate :   $o_fixedAsset->buy_date;
                $o_fixedAsset->init_used_date = isset($request->initUsedDate) ? $request->initUsedDate :  $o_fixedAsset->init_used_date;
                if ($o_fixedAsset->required_depreciation == 1) {
                    $o_fixedAsset->depreciation_group_id = isset($request->requiredDepreciation) ? $request->depreciationGroupId : $o_fixedAsset->depreciation_group_id;
                    $o_fixedAsset->document_authorization_change_method = isset($request->documentAuthorizationChangeMethod) ? $request->documentAuthorizationChangeMethod : $o_fixedAsset->document_authorization_change_method;
                    $o_fixedAsset->useful_life = isset($request->usefulLife) ? $request->usefulLife : $o_fixedAsset->useful_life;
                } else {
                    $o_fixedAsset->depreciation_group_id = null;
                    $o_fixedAsset->document_authorization_change_method = null;
                    $o_fixedAsset->useful_life = null;
                }

                try {
                    if ($o_fixedAsset->save()) {
                        $a_response = [
                            'success' => true,
                            'message' => 'Se actualizó correctamente.',
                        ];
                    }
                } catch (QueryException $ex) {
                    $a_response = [
                        'success' => false,
                        'message' => $ex->getMessage(),
                    ];
                }
            } else {
                $a_response = [
                    'success' => false,
                    'message' => 'No se pudo recuperar el activo.',
                ];
            }
        }
        return response()->json($a_response);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $i_deleted = FixedAsset::destroy($id);

            if ($i_deleted > 0) {
                $a_response = [
                    'success' => true,
                    'message' => 'Se eliminó correctamente.',
                ];
            } else {
                $a_response = [
                    'success' => false,
                    'message' => 'El objeto no existe.',
                ];
            }
        } catch (QueryException $ex) {
            $a_response = [
                'success' => false,
                'message' => $ex->getMessage(),
            ];
        }
        return response()->json($a_response);
    }

    public function getDepreciationTypeItems()
    {
        $a_types = FixedAsset::getDepreciationTypeItems();
        $a_result = [];

        foreach ($a_types as $codigo => $value) {
            $a_result[] = [
                'code' => $codigo,
                'value' => $value
            ];
        }

        $a_response = [
            'success' => true,
            'data' => [
                'items' => $a_result
            ]
        ];

        return response()->json($a_response);
    }

    public function getDepreciationPeriodItems()
    {
        $a_types = FixedAsset::getDepreciationPeriodItems();
        $a_result = [];

        foreach ($a_types as $code => $value) {
            $a_result[] = [
                'code' => $code,
                'value' => $value
            ];
        }

        $a_response = [
            'success' => true,
            'data' => [
                'items' => $a_result
            ]
        ];

        return response()->json($a_response);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getItems(Request $request)
    {
        $a_response = [
            'success' => true,
            'data' => [
                'items' => SimpleFixedAssetResource::collection(FixedAsset::where('status', 1)->get())
            ]
        ];
        return response()->json($a_response);
    }

    public function report(Request $request)
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Headers: Origin, Content-Type, Authorization, X-Auth-Token, x-xsrf-token');
        header('Access-Control-Allow-Methods: GET, POST, PUT, PATCH, DELETE, HEAD, OPTIONS');

        $page = 1;
        $limit = 10;
        $request->validate([
            'page' => 'int',
            'limit' => 'int',
        ]);

        $params = $request->only('page', 'limit');

        if (isset($params['limit']))
            $limit = $params['limit'];
        if (isset($params['page'])) {
            $page = $params['page'];
        }
        $skip = ($page - 1) * $limit;
        $count = FixedAsset::count();
        $i_last = 1;
        $result = DB::table('fixed_asset', 'F')
            ->join('multitable as TS', 'TS.multi_id', '=', 'F.type_sunat')
            ->join('multitable as SS', 'SS.multi_id', '=', 'F.status_sunat')
            ->join('account as AA', 'AA.account_code', '=', 'F.account_code')
            ->leftJoin('mark as M', 'M.mark_id', '=', 'F.mark_id')
            ->leftJoin('fixed_asset_movement as FAM', 'FAM.fixed_asset_id', '=', 'F.fixed_asset_id')
            ->leftJoin('business_unit as BU', 'BU.business_unit_id', '=', 'FAM.business_unit_id')
            ->leftJoin('area as A', 'A.area_id', '=', 'FAM.area_id')
            ->leftJoin('movement as MO', 'MO.movement_id', '=', 'FAM.movement_id')
            ->leftJoin('person as P', 'P.person_id', '=', 'FAM.responsible_id')
            ->leftJoin('depreciation_group as DG', 'DG.depreciation_group_id', '=', 'F.depreciation_group_id')
            ->select(
                'F.*',
                'FAM.ubication',
                'FAM.responsible_id',
                'FAM.area_id',
                'FAM.business_unit_id',
                'FAM.movement_id',
                'TS.description AS type_sunat_name',
                'SS.description AS status_sunat_name',
                'AA.account_name AS fixed_asset_account_name',
                'M.mark_name',
                'BU.business_unit_name',
                'A.area_name',
                'P.person_name',
                'P.identification_type',
                'P.identification_number',
                'MO.document_code',
                'MO.document_serie',
                'MO.document_correlative',
                'DG.depreciation_group_name'
            )
            ->where('FAM.last', '=', $i_last)
            ->orWhereNull('FAM.last')
            ->skip($skip)->take($limit)
            ->get();

        try {
            $mpdf = new \Mpdf\Mpdf(['defaultPageNumStyle' => '1', 'margin_top' => 31]);
            $mpdf->showImageErrors = true;

            // Buffer the following html with PHP so we can store it to a variable later
            ob_start();

            $stylesheet = file_get_contents(__DIR__ . "/report.css");
            $mpdf->WriteHTML($stylesheet, \Mpdf\HTMLParserMode::HEADER_CSS);
            require_once(__DIR__ . "/reportHTML.php");
            // Now collect the output buffer into a variable
            $html = ob_get_contents();
            ob_end_clean();

            // send the captured HTML from the output buffer to the mPDF class for processing
            $mpdf->WriteHTML($html);
            // $mpdf->WriteHTML($html,\Mpdf\HTMLParserMode::HTML_BODY);
            $mpdf->Output('activos-fijos.pdf', \Mpdf\Output\Destination::INLINE);
        } catch (\Mpdf\MpdfException $e) {
            // Note: safer fully qualified exception name used for catch
            // Process the exception, log, print etc.
            echo $e->getMessage();
        }
    }

    public function exportToExcel(Request $request)
    {
        return Excel::download(new ExportFixedAsset, 'fixed-asset.xlsx');
    }
}
