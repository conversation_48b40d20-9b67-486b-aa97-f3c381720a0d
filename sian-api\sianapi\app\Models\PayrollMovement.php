<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PayrollMovement
 * 
 * @property int $movement_id
 * @property int $dictionary_id
 * @property int|null $assistance_summary_id
 * 
 * @property AssistanceSummary|null $assistance_summary
 * @property Dictionary $dictionary
 * @property Movement $movement
 * @property Collection|PayrollMovementHeader[] $payroll_movement_headers
 *
 * @package App\Models
 */
class PayrollMovement extends Model
{
	protected $table = 'payroll_movement';
	protected $primaryKey = 'movement_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'dictionary_id' => 'int',
		'assistance_summary_id' => 'int'
	];

	protected $fillable = [
		'dictionary_id',
		'assistance_summary_id'
	];

	public function assistance_summary()
	{
		return $this->belongsTo(AssistanceSummary::class);
	}

	public function dictionary()
	{
		return $this->belongsTo(Dictionary::class);
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function payroll_movement_headers()
	{
		return $this->hasMany(PayrollMovementHeader::class, 'movement_id');
	}
}
