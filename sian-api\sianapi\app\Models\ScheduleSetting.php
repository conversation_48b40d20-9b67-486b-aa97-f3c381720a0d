<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;


/**
 * Class Schedule Setting
 *
 * @property int $schedule_setting_id
 * @property array $day
 * @property bool $status
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property int $default_time
 *
 * @package  App\Models
 */


class ScheduleSetting extends Model {
    protected $table = 'schedule_setting';
    protected $primaryKey = 'schedule_setting_id';
    public $timestamps = false;

    protected $casts = [
        'day' => 'array',
        'default_time', 'int',
        'status' => 'bool'
    ];

    protected $fillable = [
        'day',
        'default_time',
        'status',
    ];

    protected $dates = ['created_at', 'updated_at'];

}
