<?php

namespace App\Http\Controllers\Api\V1\BusinessUnit;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use App\Models\BusinessUnit;

class BusinessUnitController extends Controller {
    /**
     *
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        try {
            $a_models = BusinessUnit::where('status', 1)->get();

            return response()->json([
                'success' => true,
                'data' => $a_models
            ]);


        } catch (\Throwable $th) {
            Log::error($th->getMessage());
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }
}
