<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class CommercialCaseDetail
 * 
 * @property int $commercial_case_detail_id
 * @property int $commercial_case_id
 * @property int $contact_mode
 * @property string $modality
 * @property Carbon $date
 * @property string|null $observation
 * @property string $state
 * @property Carbon|null $register_date
 * @property int|null $user_register
 * @property bool $last_of_day
 * @property bool $last_case
 * 
 * @property CommercialCase $commercial_case
 * @property Multitable $multitable
 * @property Person|null $person
 *
 * @package App\Models
 */
class CommercialCaseDetail extends Model
{
	protected $table = 'commercial_case_detail';
	protected $primaryKey = 'commercial_case_detail_id';
	public $timestamps = false;

	protected $casts = [
		'commercial_case_id' => 'int',
		'contact_mode' => 'int',
		'user_register' => 'int',
		'last_of_day' => 'bool',
		'last_case' => 'bool'
	];

	protected $dates = [
		'date',
		'register_date'
	];

	protected $fillable = [
		'commercial_case_id',
		'contact_mode',
		'modality',
		'date',
		'observation',
		'state',
		'register_date',
		'user_register',
		'last_of_day',
		'last_case'
	];

	public function commercial_case()
	{
		return $this->belongsTo(CommercialCase::class);
	}

	public function multitable()
	{
		return $this->belongsTo(Multitable::class, 'contact_mode');
	}

	public function person()
	{
		return $this->belongsTo(Person::class, 'user_register');
	}
}
