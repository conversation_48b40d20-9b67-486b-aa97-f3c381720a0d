<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Merchandise
 *
 * @property int $product_id
 * @property string|null $model
 * @property string|null $part_number
 * @property bool $serialized
 * @property int $subline_id
 * @property int $mark_id
 * @property string|null $characteristic
 * @property bool $show_without_stock
 * @property int|null $stock_id
 * @property float $lpcost_pen
 * @property float $lpcost_usd
 * @property float $uref_cost_pen
 * @property float $uref_cost_usd
 * @property bool $use_batch
 *
 * @property Mark $mark
 * @property Product $product
 * @property Stock|null $stock
 * @property Subline $subline
 * @property Collection|Batch[] $batches
 * @property Collection|Kardex[] $kardexes
 * @property Collection|KardexRange[] $kardex_ranges
 * @property Collection|MerchandiseMaster[] $merchandise_masters
 * @property Collection|Stock[] $stocks
 * @property Collection|StockRange[] $stock_ranges
 *
 * @package App\Models
 */
class Merchandise extends Model {
    protected $table = 'merchandise';
    protected $primaryKey = 'product_id';
    public $incrementing = false;
    public $timestamps = false;

    protected $casts = [
        'product_id' => 'int',
        'serialized' => 'bool',
        'subline_id' => 'int',
        'mark_id' => 'int',
        'show_without_stock' => 'bool',
        'stock_id' => 'int',
        'lpcost_pen' => 'float',
        'lpcost_usd' => 'float',
        'uref_cost_pen' => 'float',
        'uref_cost_usd' => 'float',
        'use_batch' => 'bool'
    ];

    protected $fillable = [
        'model',
        'part_number',
        'serialized',
        'subline_id',
        'mark_id',
        'characteristic',
        'show_without_stock',
        'stock_id',
        'lpcost_pen',
        'lpcost_usd',
        'uref_cost_pen',
        'uref_cost_usd',
        'use_batch'
    ];

    public function mark() {
        return $this->belongsTo(Mark::class, 'mark_id', 'mark_id');
    }

    public function product() {
        return $this->belongsTo(Product::class,'product_id','product_id');
    }

    public function stock() {
        return $this->belongsTo(Stock::class);
    }

    public function subline() {
        return $this->belongsTo(Subline::class,'subline_id', 'subline_id');
    }

    public function batches() {
        return $this->hasMany(Batch::class, 'product_id');
    }

    public function kardexes() {
        return $this->hasMany(Kardex::class, 'product_id');
    }

    public function kardex_ranges() {
        return $this->hasMany(KardexRange::class, 'product_id');
    }

    public function merchandise_masters() {
        return $this->hasMany(MerchandiseMaster::class, 'product_id');
    }

    public function stocks() {
        return $this->hasMany(Stock::class, 'product_id');
    }

    public function stock_ranges() {
        return $this->hasMany(StockRange::class, 'product_id');
    }

}
