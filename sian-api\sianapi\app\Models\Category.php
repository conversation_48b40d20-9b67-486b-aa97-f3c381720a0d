<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Category
 * 
 * @property int $category_id
 * @property string $category_name
 * @property string $alias
 * @property bool $status
 * @property bool $web_enabled
 * @property string|null $transaction_code
 * 
 * @property Collection|Subcategory[] $subcategories
 *
 * @package App\Models
 */
class Category extends Model
{
	protected $table = 'category';
	protected $primaryKey = 'category_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'web_enabled' => 'bool'
	];

	protected $fillable = [
		'category_name',
		'alias',
		'status',
		'web_enabled',
		'transaction_code'
	];

	public function subcategories()
	{
		return $this->hasMany(Subcategory::class);
	}
}
