<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ProductList
 *
 * @property string $owner
 * @property int $owner_id
 * @property string $currency
 * @property float $exchange_rate
 * @property float $munit_quantity
 * @property float $munit_balance
 * @property float $sunit_quantity
 * @property int $item_count
 * @property float $total_pen
 * @property float $total_usd
 * @property bool $allow_duplicate
 *
 * @property Collection|Item[] $items
 *
 * @package App\Models
 */
class ProductList extends Model
{
	protected $table = 'product_list';
	public $incrementing = false;
	public $timestamps = false;

    const LABEL_TYPE_PRE_PAY = 'Entrega a Rendir Cuenta';
    const LABEL_TYPE_REFUND = 'Desembolso / Reembolso';

	protected $casts = [
		'owner_id' => 'int',
		'exchange_rate' => 'float',
		'munit_quantity' => 'float',
		'munit_balance' => 'float',
		'sunit_quantity' => 'float',
		'item_count' => 'int',
		'total_pen' => 'float',
		'total_usd' => 'float',
		'allow_duplicate' => 'bool'
	];

	protected $fillable = [
		'currency',
		'exchange_rate',
		'munit_quantity',
		'munit_balance',
		'sunit_quantity',
		'item_count',
		'total_pen',
		'total_usd',
		'allow_duplicate'
	];

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}

	public function items()
	{
		return $this->belongsToMany(Item::class, 'product_list_item', 'owner')
					->withPivot('owner_id', 'amount_pen', 'amount_usd', 'total_pen', 'total_usd');
	}
}
