<?php

namespace App\Http\Controllers\Api\V1\Financial;

use App\Models\ScheduleSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;


class ScheduleSettingController extends Controller {

    /**
     *
     *
     * @return \Illuminate\Http\Response
     */

    public function show(Request $request) {

        try {
            $results = DB::table("schedule_setting as SS")->select('*')->where('SS.status', '=', 1)->first();

            if ($results) {
                $response = [
                    'success' => true,
                    'data' => $results,
                ];
            } else {

                $response = [
                    'success' => true,
                    'data' => null,
                    'message' => 'No schedule configuration records.',
                ];
            }

            return response()->json($response);

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'error' => 'query error: ' . $e->getMessage()
            ];

            return response()->json($response, 500);
        }

    }

    public function store(Request $request) {
        $requestData = $request->json()->all();


        $validator = Validator::make($requestData, [
            'day' => 'required|array',
            'default_time' => 'required|integer'
        ]);


        if ($validator->fails()) {
            $response = [
                'success' => false,
                'errors' => $validator->errors(),
            ];
            return response()->json($response, 400);
        }
        try {

            $days = json_encode($requestData['day'], true);
            $default_time = json_encode($requestData['default_time'], true);

            $insertedId = DB::table('schedule_setting')->insertGetId([
                'day' => $days,
                'default_time' => $default_time,
                'status' => true,
            ]);

            $response = [
                'success' => true,
                'message' => 'Schedule Setting Inserted!.',
                'data' => ['schedule_setting_id' => $insertedId],
            ];

            return response()->json($response, 200);

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'error' => '' . $e->getMessage()
            ];
            return response()->json($response, 400);
        }
        ;
    }

    public function update(Request $request) {
        $requestData = $request->json()->all();
        $validator = Validator::make($requestData, [
            'day' => 'required|array',
            'default_time' => 'required|integer'
        ]);

        if ($validator->fails()) {
            $response = [
                'success' => false,
                'errors' => $validator->errors(),
            ];
            return response()->json($response, 400);
        }

        try {
            $scheduleSetting = ScheduleSetting::findOrFail($requestData['schedule_setting_id']);
            $scheduleSetting->update([
                'day' => $requestData['day'],
                'default_time' => $requestData['default_time'],
                'status' => true,
            ]);

            $response = [
                'success' => true,
                'message' => 'Schedule Setting Updated!'
            ];

            return response()->json($response, 200);

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'error' => '' . $e->getMessage()
            ];
            return response()->json($response, 400);
        }
        ;
    }
}
