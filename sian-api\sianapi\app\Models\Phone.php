<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Phone
 * 
 * @property string $owner
 * @property int $owner_id
 * @property string $phone_number
 * @property string $phone_type
 * @property int $order
 * 
 *
 * @package App\Models
 */
class Phone extends Model
{
	protected $table = 'phone';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'owner_id' => 'int',
		'order' => 'int'
	];

	protected $fillable = [
		'phone_type',
		'order'
	];

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}
}
