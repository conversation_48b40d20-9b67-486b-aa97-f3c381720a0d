<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AccountingYear
 * 
 * @property int $accounting_year_id
 * @property int $accounting_file_id
 * @property int $year
 * 
 * @property AccountingFile $accounting_file
 * @property Collection|AccountingPeriod[] $accounting_periods
 *
 * @package App\Models
 */
class AccountingYear extends Model
{
	protected $table = 'accounting_year';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'accounting_year_id' => 'int',
		'accounting_file_id' => 'int',
		'year' => 'int'
	];

	protected $fillable = [
		'accounting_year_id'
	];

	public function accountingFile()
	{
		return $this->belongsTo(AccountingFile::class);
	}

	public function accountingPeriods()
	{
		return $this->hasMany(AccountingPeriod::class, ['year', 'accounting_file_id'], ['year', 'accounting_file_id']);
	}

	public function accountingDays()
	{
		return $this->hasMany(AccountingDay::class, ['year', 'accounting_file_id'], ['year', 'accounting_file_id']);
	}
}
