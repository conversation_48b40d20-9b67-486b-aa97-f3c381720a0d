<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SianController extends Controller {


    public static function authenticateAPI($sianDomain) {
        $logs = array();

        try {

            // $protocol = (env('IS_HTTPS') == true) ? 'https://' : 'http://';
            // $sianDomain = $protocol . config('app.globals.sian_url');

            $apiPassword = env('SIAN_API_YII_PASSWORD');

            $url = $sianDomain . '/admin/apiSian/signin/login';

            $data = [
                "username" => "ApiSIAN",
                "password" => '123456',
                "lifetime" => "800000"
            ];

            Log::info('Url SIAN ' . $url);


            $response = Http::withoutVerifying()->withOptions([
                'curl' => [
                    CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2, // Forzar TLS 1.2
                ]
            ])->post($url, $data);

            if ($response->successful()) {

                $responseData = $response->json();

                if (isset($responseData['data']['token'])) {

                    $appToken = $responseData['data']['token'];

                    $messageLog = 'Token de la aplicación obtenido correctamente';
                    $dataLog = ['appToken' => $appToken];

                    Log::info($messageLog, $dataLog);

                    $logs[] = ['message' => $messageLog, 'data' => $dataLog];

                    return [
                        'appToken' => $appToken,
                        'logs' => $logs,
                        'url' => $url,
                        'data' => $data
                    ];

                } else {

                    $messageErrorLog = 'Error al autenticar la aplicación en SIAN: Token no presente en la respuesta';

                    $logs[] = ['message' => $messageErrorLog];

                    throw new \Exception(json_encode([
                        'messageErrorLog' => $messageErrorLog,
                        'logs' => $logs
                    ]));

                }

            }

            $messageLog = 'Respuesta completa del servidor: ';
            $dataLog = $response->body();

            $logs[] = ['message' => $messageLog, 'data' => $dataLog];

            Log::error($messageLog . $dataLog);



            $messageLog = 'URL de la solicitud a SIAN: ';
            $dataLog = $url;

            $logs[] = ['message' => $messageLog, 'data' => $dataLog];

            Log::error($messageLog . $dataLog);


            $messageLog = 'Error al autenticar la aplicación en SIAN: ';
            $dataLog = $response;

            $logs[] = ['message' => $messageLog, 'data' => $dataLog];

            throw new \Exception($messageLog . [
                "message" => $response->json('message'),
                "logs" => $logs
            ]);

        } catch (\Exception $e) {
            $logs[] = $e->getMessage();
            Log::error($e->getMessage());
            throw $e;
        }
    }

    public static function authenticateUser($token, $username, $hash, $sianDomain) {
        $logs = array();

        $paramenters = ['app-token' => $token, 'username' => $username, 'password' => $hash];

        try {


            // $protocol = (env('IS_HTTPS') == true) ? 'https://' : 'http://';
            // $sianDomain = $protocol . config('app.globals.sian_url');
            $url = $sianDomain . '/admin/apiSian/user/login';

            $data = [
                "username" => $username,
                "password" => $hash,
            ];


            $response = Http::withoutVerifying()->withHeaders([
                'Content-Type' => 'application/json',
                'App-Authorization' => $token,
            ])->post($url, $data);


            $logs[] = ['message' => 'URL de la solicitud a SIAN para autenticar usuario: ', 'data' => $url];
            Log::info('URL de la solicitud a SIAN para autenticar usuario: ' . $url);

            $logs[] = ['message' => 'Datos de la solicitud a SIAN para autenticar usuario: ', 'data' => $data];

            Log::info('Datos de la solicitud a SIAN para autenticar usuario: ' . json_encode($data));

            $logs[] = ['message' => 'Respuesta completa del servidor para autenticar usuario: ', 'data' => json_decode($response->body())];

            Log::info('Respuesta completa del servidor para autenticar usuario: ' . $response->body());


            if (!$response->successful()) {
                return ['response' => $response->json(), 'logs' => $logs, 'parameters' => $paramenters];
            }

            $responseData = $response->json();

            if (isset($responseData['data']['token'])) {
                return ['token' => $responseData['data']['token'], 'logs' => $logs, 'parameters' => $paramenters];
            }

            $logs[] = 'Token no presente en la respuesta al autenticar usuario en SIAN';

            Log::error('Token no presente en la respuesta al autenticar usuario en SIAN');

            return response()->json(['message' => 'Error al autenticar el usuario en SIAN', 'logs' => $logs], 500);
        } catch (\Exception $e) {

            $logs[] = 'Error en la función autenticarUsuario: ' . $e->getMessage();
            Log::error('Error en la función autenticarUsuario: ' . $e->getMessage());

            return ['message' => $e->getMessage(), "logs" => $logs, 'parameters' => $paramenters];
        }
    }

    public static function savePayOnSian($data, $username, $password, $sianDomain) {
        $logs = array();
        $parameters = [
            'username' => $username,
            'data' => $data
        ];
        try {
            $appResponse = SianController::authenticateAPI($sianDomain);
            $appToken = $appResponse['appToken'];
            $userResponse = SianController::authenticateUser($appToken, $username, $password, $sianDomain);
            $userToken = $userResponse['token'];

            $url = $sianDomain . '/admin/apiSian/pay';

            Log::info('URL de la solicitud a SIAN', ['url' => $url]);

            $logs[] = ['message' => 'URL de la solicitud a SIAN', ['url' => $url]];


            $response = Http::withoutVerifying()->withHeaders([
                'Content-Type' => 'application/json',
                'App-Authorization' => $appToken,
                'Authorization' => $userToken,
            ])->post($url, $data);

            Log::info('Data de SIAN ', ['data' => $data]);

            Log::info('Response de SIAN ', ['response' => $response]);

            $logs[] = ['parameters' => $parameters];

            $jsonResponse = json_decode($response->body());

            if ($jsonResponse !== null) {
                $logs[] = ['message' => 'Respuesta completa de la solicitud a SIAN', 'response' => $jsonResponse];
            } else {
                $logs[] = ['message' => 'Respuesta completa de la solicitud a SIAN (no es JSON)', 'response' => $response->body()];
            }

            Log::info('Respuesta completa de la solicitud a SIAN', ['response' => $response->body()]);

            if (!($response instanceof \Illuminate\Http\Client\Response)) {
                throw new \Exception('Error al realizar la solicitud a SIAN: Respuesta inválida' . json_encode($logs) . json_encode($parameters));
            }

            if (!$response->successful()) {

                if ($response->status() == 404) {
                    throw new \Exception('Error al realizar la solicitud a SIAN: Recurso no encontrado' . json_encode($logs));
                }
                throw new \Exception('Error al realizar la solicitud a SIAN: ' . ['response' => $response->status(), 'logs' => $logs]);
            }

            $logs[] = ['message' => 'Respuesta JSON de la solicitud a SIAN', 'response' => $response->json()];

            Log::info('Respuesta JSON de la solicitud a SIAN', ['json' => $response->json(), 'logs' => $logs]);

            return ['response' => $response->json(), 'logs' => $logs];

        } catch (\Exception $e) {

            $logs[] = ['message' => 'Error al generar facturación en SIAN', 'error' => $e->getMessage()];

            Log::error('Error al generar facturación en SIAN', ['error' => $e->getMessage()]);

            return ['message' => $e->getMessage(), 'logs' => $logs];
        }
    }

    public static function saveORTOnSIAN($data, $username, $password, $sianDomain) {
        $parameters = [
            'username' => $username,
            'data' => $data
        ];

        $appResponse = SianController::authenticateAPI($sianDomain);
        $appToken = $appResponse['appToken'];
        $userResponse = SianController::authenticateUser($appToken, $username, $password, $sianDomain);
        $userToken = $userResponse['token'];

        $url = $sianDomain . '/admin/apiSian/transformationOrder';

        Log::info('URL de la solicitud a SIAN', ['url' => $url]);

        $response = Http::withoutVerifying()->withHeaders([
            'Content-Type' => 'application/json',
            'App-Authorization' => $appToken,
            'Authorization' => $userToken,
        ])->post($url, $data);

        Log::info('Data de SIAN ', ['data' => $data]);

        Log::info('Response de SIAN ', ['response' => $response]);

        if (!($response instanceof \Illuminate\Http\Client\Response)) {
            return ['message' => 'Error al realizar la solicitud a SIAN, Respuesta inválida', 'data' => $parameters, 'sucess' => false];
        }

        $response_data = json_decode($response->body());

        $response_data->success = false;

        if (!$response->successful()) {

            if ($response->status() == 404) {
                return $response_data;

            }
            return $response_data;
        }

        Log::info('Respuesta JSON de la solicitud a SIAN', ['data' => json_decode($response->body()), 'sucess' => false]);

        $response_data->success = true;
        return $response_data;
    }

    public static function authenticateSavePay($data, $username, $hash, $sianDomain) {
        try {
            $responseAppToken = self::authenticateAPI($sianDomain);
            $appToken = $responseAppToken['appToken'];

            $responseToken = self::authenticateUser($responseAppToken['appToken'], $username, $hash, $sianDomain);
            $userToken = $responseToken['token'];


            Log::info('Tokens Obtenidos correctamente', ['appToken' => $appToken, 'userToken' => $userToken]);
            $facturacionSianResponse = self::savePayOnSian($appToken, $userToken, $data, $sianDomain);

            Log::info('Respuesta de Guardado en SIAN', ['facturacionSianResponse' => $facturacionSianResponse]);



            return [
                'response' => $facturacionSianResponse['response'],
                "logs" => [
                    'save' => $facturacionSianResponse['logs'],
                    'authenticateAPI' => $responseAppToken['logs'],
                    'authenticateUser' => $responseToken['logs']
                ]
            ];



        } catch (\Exception $e) {

            Log::error('Error en autenticación y facturación SIAN', ['error' => $e->getMessage()]);

            return [
                'response' => [
                    'code' => 500,
                    'message' => 'Error en autenticación y facturación SIAN',
                    'data' => [
                        'success' => false
                    ]
                ],
                'logs' => [
                    'pay' => $facturacionSianResponse['logs'],
                    'authenticateAPI' => $responseAppToken['logs'],
                    'authenticateUser' => $responseToken['logs']
                ],
                'error' => $e->getMessage()
            ];
        }
    }

    public static function getApiTokens($credentials, $sianDomain = '') {
        $responseApiSIAN = SianController::authenticateAPI($sianDomain);
        $appToken = $responseApiSIAN['appToken'];

        $username = $credentials['username'];
        $password = $credentials['hash'];

        $responseUserApiSIAN = SianController::authenticateUser($appToken, $username, $password, $sianDomain);

        $userToken = $responseUserApiSIAN['token'];

        return ["appToken" => $appToken, "userToken" => $userToken];
    }
}
