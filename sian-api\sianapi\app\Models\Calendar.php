<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Calendar
 * 
 * @property int $calendar_id
 * @property Carbon $date
 * @property int $year
 * @property int $period
 * @property int $year_week
 * @property int $week
 * @property string $day_name
 * @property bool $is_holiday
 * @property bool $opened
 * 
 *
 * @package App\Models
 */
class Calendar extends Model
{
	protected $table = 'calendar';
	protected $primaryKey = 'date';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'calendar_id' => 'int',
		'year' => 'int',
		'period' => 'int',
		'year_week' => 'int',
		'week' => 'int',
		'is_holiday' => 'bool',
		'opened' => 'bool'
	];

	protected $dates = [
		'date'
	];

	protected $fillable = [
		'calendar_id',
		'year',
		'period',
		'year_week',
		'week',
		'day_name',
		'is_holiday',
		'opened'
	];

	public function period()
	{
		return $this->belongsTo(Period::class, 'year')
					->where('period.year', '=', 'calendar.year')
					->where('period.period', '=', 'calendar.period');
	}
}
