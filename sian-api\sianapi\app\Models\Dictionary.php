<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Dictionary
 * 
 * @property int $dictionary_id
 * @property string $dictionary_name
 * @property int $regime_id
 * @property string $type
 * @property bool $status
 * 
 * @property Regime $regime
 * @property Collection|Concept[] $concepts
 * @property Collection|Operation[] $operations
 * @property Collection|PayrollMovement[] $payroll_movements
 *
 * @package App\Models
 */
class Dictionary extends Model
{
	protected $table = 'dictionary';
	protected $primaryKey = 'dictionary_id';
	public $timestamps = false;

	protected $casts = [
		'regime_id' => 'int',
		'status' => 'bool'
	];

	protected $fillable = [
		'dictionary_name',
		'regime_id',
		'type',
		'status'
	];

	public function regime()
	{
		return $this->belongsTo(Regime::class);
	}

	public function concepts()
	{
		return $this->hasMany(Concept::class);
	}

	public function operations()
	{
		return $this->hasMany(Operation::class);
	}

	public function payroll_movements()
	{
		return $this->hasMany(PayrollMovement::class);
	}
}
