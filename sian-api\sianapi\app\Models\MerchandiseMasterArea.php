<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class MerchandiseMasterArea
 * 
 * @property int $product_id
 * @property int $warehouse_id
 * @property int $warehouse_area_id
 * @property bool $default
 * 
 * @property MerchandiseMaster $merchandise_master
 * @property WarehouseArea $warehouse_area
 *
 * @package App\Models
 */
class MerchandiseMasterArea extends Model
{
	protected $table = 'merchandise_master_area';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'product_id' => 'int',
		'warehouse_id' => 'int',
		'warehouse_area_id' => 'int',
		'default' => 'bool'
	];

	protected $fillable = [
		'default'
	];

	public function merchandise_master()
	{
		return $this->belongsTo(MerchandiseMaster::class, 'product_id')
					->where('merchandise_master.product_id', '=', 'merchandise_master_area.product_id')
					->where('merchandise_master.warehouse_id', '=', 'merchandise_master_area.warehouse_id');
	}

	public function warehouse_area()
	{
		return $this->belongsTo(WarehouseArea::class);
	}
}
