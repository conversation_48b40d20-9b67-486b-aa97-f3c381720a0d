<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class HumanCategory
 * 
 * @property int $human_category_id
 * @property string $human_category_name
 * @property bool $status
 * 
 * @property Collection|Employee[] $employees
 * @property Collection|HumanCategoryRemuneration[] $human_category_remunerations
 * @property Collection|PayrollMovementHeader[] $payroll_movement_headers
 *
 * @package App\Models
 */
class HumanCategory extends Model
{
	protected $table = 'human_category';
	protected $primaryKey = 'human_category_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool'
	];

	protected $fillable = [
		'human_category_name',
		'status'
	];

	public function employees()
	{
		return $this->hasMany(Employee::class);
	}

	public function human_category_remunerations()
	{
		return $this->hasMany(HumanCategoryRemuneration::class);
	}

	public function payroll_movement_headers()
	{
		return $this->hasMany(PayrollMovementHeader::class);
	}
}
