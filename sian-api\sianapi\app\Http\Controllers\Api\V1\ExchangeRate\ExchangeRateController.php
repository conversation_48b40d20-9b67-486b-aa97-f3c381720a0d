<?php

namespace App\Http\Controllers\Api\V1\ExchangeRate;

use App\Http\Controllers\Controller;
use App\Models\ExchangeRate;
use Illuminate\Http\Request;
use Carbon\Carbon;

class ExchangeRateController extends Controller {


    /**
     *
     *
     * @return \Illuminate\Http\Response
     */


    public function hasExchangeRate(Request $request) {

        $date = Carbon::today();

        if ($request->has('date')) {
            $date = Carbon::parse($request->query('date'));

        }

        $hasExchangeRate = ExchangeRate::whereDate('date', $date)->exists();

        if ($hasExchangeRate) {
            return response()->json([
                'success' => true,
                'data' => [
                    'hasExchangeRate' => $hasExchangeRate,
                    'date' => $date
                ]
            ], 200);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'No se encontró un tipo de cambio para la fecha de hoy'
            ], 200);
        }

    }
}
