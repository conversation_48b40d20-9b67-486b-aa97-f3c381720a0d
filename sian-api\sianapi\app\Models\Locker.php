<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Locker
 * 
 * @property int $locker_id
 * @property string $locker_code
 * @property string $locker_name
 * @property string $alias
 * @property bool $status
 * @property int $sync_frequency
 * @property string $process
 * @property Carbon|null $last_sync
 * @property int $box_count
 * @property int $available_boxes
 * @property int $person_id
 * 
 * @property Person $person
 * @property Collection|CommercialMovement[] $commercial_movements
 * @property Collection|LockerBox[] $locker_boxes
 * @property Collection|LockerRequest[] $locker_requests
 *
 * @package App\Models
 */
class Locker extends Model
{
	protected $table = 'locker';
	protected $primaryKey = 'locker_id';
	public $timestamps = false;

	protected $casts = [
		'status' => 'bool',
		'sync_frequency' => 'int',
		'box_count' => 'int',
		'available_boxes' => 'int',
		'person_id' => 'int'
	];

	protected $dates = [
		'last_sync'
	];

	protected $fillable = [
		'locker_code',
		'locker_name',
		'alias',
		'status',
		'sync_frequency',
		'process',
		'last_sync',
		'box_count',
		'available_boxes',
		'person_id'
	];

	public function person()
	{
		return $this->belongsTo(Person::class);
	}

	public function commercial_movements()
	{
		return $this->hasMany(CommercialMovement::class);
	}

	public function locker_boxes()
	{
		return $this->hasMany(LockerBox::class);
	}

	public function locker_requests()
	{
		return $this->hasMany(LockerRequest::class);
	}
}
