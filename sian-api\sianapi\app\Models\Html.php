<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Html
 *
 * @property int $html_id
 * @property string $html_content
 * @property bool $show_title
 * @property int $object_id
 *
 * @property ObjectModel $object
 *
 * @package App\Models
 */
class Html extends Model
{
	protected $table = 'html';
	protected $primaryKey = 'html_id';
	public $timestamps = false;

	protected $casts = [
		'show_title' => 'bool',
		'object_id' => 'int'
	];

	protected $fillable = [
		'html_content',
		'show_title',
		'object_id'
	];

	public function object()
	{
		return $this->belongsTo(ObjectModel::class);
	}
}
