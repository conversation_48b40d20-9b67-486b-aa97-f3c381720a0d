<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class TransformationResult
 * 
 * @property int $item_id
 * @property int $movement_id
 * 
 * @property Movement $movement
 * @property Item $item
 *
 * @package App\Models
 */
class TransformationResult extends Model
{
	protected $table = 'transformation_result';
	protected $primaryKey = 'item_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'item_id' => 'int',
		'movement_id' => 'int'
	];

	protected $fillable = [
		'movement_id'
	];

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function item()
	{
		return $this->belongsTo(Item::class);
	}
}
