<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class StockRange
 * 
 * @property int $product_id
 * @property Carbon $begin_date
 * @property Carbon $end_date
 * @property int $stock_id
 * 
 * @property Merchandise $merchandise
 * @property Stock $stock
 *
 * @package App\Models
 */
class StockRange extends Model
{
	protected $table = 'stock_range';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'product_id' => 'int',
		'stock_id' => 'int'
	];

	protected $dates = [
		'begin_date',
		'end_date'
	];

	protected $fillable = [
		'stock_id'
	];

	public function merchandise()
	{
		return $this->belongsTo(Merchandise::class, 'product_id');
	}

	public function stock()
	{
		return $this->belongsTo(Stock::class);
	}
}
