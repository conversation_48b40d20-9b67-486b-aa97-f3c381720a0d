<?php

namespace App\Models\Procedures;

use Illuminate\Support\Facades\DB;

class SpGetProductPresentations {
    const MODE_FORM = 0;
    const MODE_COMBOBOX_WITH_PRICES = 1;
    const MODE_COMBOBOX_WITHOUT_PRICES = 2;
    const MODE_PREVIEW = 3;
    const MODE_INTERNAL_API = 4;

    public static function getAssociative($mode = self::MODE_COMBOBOX_WITHOUT_PRICES, $productIds, $currency, $getCost = 0, $storeId = null, $date = null, $excludeId = null) {
        $data = [];
        $dataStocks = [];

        if (!is_array($productIds)) {
            $productIds = explode(',', $productIds);
        }

        if (count($productIds) > 0) {
            $productIdsString = implode(',', $productIds);

            $items = DB::select('CALL sp_get_product_presentations(?, ?, ?, ?, ?, ?, ?)', [
                $mode,
                $productIdsString,
                $currency,
                $getCost,
                $storeId,
                $date ? now()->parse($date)->format('Y-m-d') : null,
                $excludeId,
            ]);

            $data = collect($items)->groupBy('product_id')->toArray();

            if (config('your_config.allow_dispatch_multi_origin')) {
                $dataStocks = SpGetProductPresentationsStocks::getAssociative($productIds, now()->format('Y-m-d'), $excludeId, $storeId);
            }
        }

        $finalData = [];
        foreach ($data as $productId => $presentations) {
            foreach ($presentations as $presentation) {
                $key = $productId . '-' . number_format($presentation->equivalence, 2);
                $stocks = $dataStocks[$key] ?? [];
                $presentation->stocks = $stocks;
                $finalData[$productId][number_format($presentation->equivalence, 2)] = $presentation;
            }
        }

        return $finalData;
    }
}
