<?php

namespace App\Http\Controllers\Api\V1\Project;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use App\Models\Project;

class ProjectController extends Controller {
    /**
     *
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        try {
            $a_models = Project::get();

            return response()->json([
                'success' => true,
                'data' => $a_models
            ]);


        } catch (\Throwable $th) {
            Log::error($th->getMessage());
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }
}
